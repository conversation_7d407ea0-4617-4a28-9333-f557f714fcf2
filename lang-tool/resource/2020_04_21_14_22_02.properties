#Tue Apr 21 14:22:02 CST 2020
MIX_UBL_ALL_UBL_FE_LOC_00050663=问题十：
MIX_UBL_ALL_UBL_FE_LOC_00050662=请选择IP地址
MIX_UBL_ALL_UBL_FE_LOC_00050659=获取IP地址
MIX_UBL_ALL_UBL_FE_LOC_00050661=1、为什么要选择网关IP？
MIX_UBL_ALL_UBL_FE_LOC_00050660=部署网关的服务器要求既可以访问内网，又可以访问外网，所以该服务器会安装配置多个网卡， 且在混合云模式下，ERP和网关的通信是双向的，ERP服务器须选择配置正确的网关服务器IP。
MIX_UBL_ALL_UBL_FE_LOC_00050672=无法获取正确的IP，请确认网关服务器里连接状态
MIX_UBL_ALL_UBL_FE_LOC_00050671=网关服务器IP
MIX_UBL_ALL_UBL_FE_LOC_00050669=如何确定正确的网关IP？
MIX_UBL_ALL_UBL_FE_LOC_00050670=网关IP的必须是一个能够被ERP所处的服务器访问的IP地址，建议选择内网IP即可。 如果不能确认网关ID能够被ERP所在服务器访问，可以在ERP服务器通过 "ping" 命令确认，如果连接失败，说明两台机器网络连接不通，需要联系对应的服务器管理人员解决网络连接问题。
MIX_UBL_ALL_UBL_FE_LOC_00050668=确认网关所在的服务器主机
MIX_UBL_ALL_UBL_FE_LOC_00050667=2、如何确定正确的网关IP？
MIX_UBL_ALL_UBL_FE_LOC_00050666=数据同步任务
MIX_UBL_ALL_UBL_FE_LOC_00050665=编号
MIX_UBL_ALL_UBL_FE_LOC_00050664=为了保证ERP能够和网关正确通信，需要先确认一下网关正确的IP地址； 如果您不知道怎么选择，请查看最下面的常见问题-如何选择网关IP。
