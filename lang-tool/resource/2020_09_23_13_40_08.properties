#Wed Sep 23 13:40:08 CST 2020
MIX_UBL_ALL_UBL_FE_LOC_00050692=若用户使用新网关同步数据，请先设置默认网关（ERP连接配置中点击设置默认）后全量执行人员任务
MIX_UBL_ALL_UBL_FE_LOC_00050691=耗时\:
MIX_UBL_ALL_UBL_FE_LOC_00050690=状态\:
MIX_UBL_ALL_UBL_FE_LOC_00050726=移动审批无法获取正确的NC/NCC用户关联关系
MIX_UBL_ALL_UBL_FE_LOC_00050725=友户通关联关系问题
MIX_UBL_ALL_UBL_FE_LOC_00050724=执行的视图
MIX_UBL_ALL_UBL_FE_LOC_00050723=3、若关联关系为一条，同样单独同步一下对应员工的数据，再确定一下关联关系
MIX_UBL_ALL_UBL_FE_LOC_00050722=运行结束时间
MIX_UBL_ALL_UBL_FE_LOC_00050721=定时触发
MIX_UBL_ALL_UBL_FE_LOC_00050720=问题说明
MIX_UBL_ALL_UBL_FE_LOC_00050689=启用状态
MIX_UBL_ALL_UBL_FE_LOC_00050688=总数量
MIX_UBL_ALL_UBL_FE_LOC_00050687=查看执行详情
MIX_UBL_ALL_UBL_FE_LOC_00050686=未运行：
MIX_UBL_ALL_UBL_FE_LOC_00050685=跳过
MIX_UBL_ALL_UBL_FE_LOC_00050684=日志详情\:
MIX_UBL_ALL_UBL_FE_LOC_00050683=触发类型
MIX_UBL_ALL_UBL_FE_LOC_00050682=移动审批打开白页或者提示无法获取友户通关联关系，其他情况请找移动审批确认
MIX_UBL_ALL_UBL_FE_LOC_00050681=补偿类型
MIX_UBL_ALL_UBL_FE_LOC_00050680=操作说明：
MIX_UBL_ALL_UBL_FE_LOC_00050719=查看详情
MIX_UBL_ALL_UBL_FE_LOC_00050718=注：查询语句如下（NC/NCC/U8C）
MIX_UBL_ALL_UBL_FE_LOC_00050717=结束时间\:
MIX_UBL_ALL_UBL_FE_LOC_00050716=运行开始时间
MIX_UBL_ALL_UBL_FE_LOC_00050715=同步任务执行日志
MIX_UBL_ALL_UBL_FE_LOC_00050714=视图
MIX_UBL_ALL_UBL_FE_LOC_00050713=任务执行状态
MIX_UBL_ALL_UBL_FE_LOC_00050712=执行详情
MIX_UBL_ALL_UBL_FE_LOC_00050711=友户通ID
MIX_UBL_ALL_UBL_FE_LOC_00050710=失败补偿
MIX_UBL_ALL_UBL_FE_LOC_00050679=姓名
MIX_UBL_ALL_UBL_FE_LOC_00050678=详情
MIX_UBL_ALL_UBL_FE_LOC_00050677=查看出厂SQL
MIX_UBL_ALL_UBL_FE_LOC_00050676=执行中
MIX_UBL_ALL_UBL_FE_LOC_00050675=开始时间\:
MIX_UBL_ALL_UBL_FE_LOC_00050674=4、若干关联关系为多条，则确认移动审批所要使用的关联关系数据（即友户通和ERP用户主键对应关系）是否为最新的数据，若不为则单独同步一下对应员工的数据
MIX_UBL_ALL_UBL_FE_LOC_00050673=同步时间
MIX_UBL_ALL_UBL_FE_LOC_00050709=运行中：
MIX_UBL_ALL_UBL_FE_LOC_00050708=1、输入用户信息查询用户关联关系详情数据
MIX_UBL_ALL_UBL_FE_LOC_00050707=手动触发
MIX_UBL_ALL_UBL_FE_LOC_00050706=按照用户名查询：select cuserid as id, user_code, user_name from sm_user where user_name \= '用户名'
MIX_UBL_ALL_UBL_FE_LOC_00050705=执行状态
MIX_UBL_ALL_UBL_FE_LOC_00050704=用户ID
MIX_UBL_ALL_UBL_FE_LOC_00050703=网关ID\:
MIX_UBL_ALL_UBL_FE_LOC_00050702=视图编码
MIX_UBL_ALL_UBL_FE_LOC_00050701=搜索字段类型
MIX_UBL_ALL_UBL_FE_LOC_00050700=刷新
MIX_UBL_ALL_UBL_FE_LOC_00050699=出厂SQL
MIX_UBL_ALL_UBL_FE_LOC_00050698=任务数量
MIX_UBL_ALL_UBL_FE_LOC_00050697=2、若关联关系数据若为空，则单独同步一下员工的数据
MIX_UBL_ALL_UBL_FE_LOC_00050696=序号
MIX_UBL_ALL_UBL_FE_LOC_00050695=复制
MIX_UBL_ALL_UBL_FE_LOC_00050694=跳过：
MIX_UBL_ALL_UBL_FE_LOC_00050693=执行结束
