const fs = require('fs');
const path = require('path');
const moduleConfig = require('./appModuleConfig.js');

let alias = {
  "moment-timezone": 'moment', //将 cron-parser 中引入的 moment-timezone 覆盖改写为 moment
  components: path.resolve('ucf-common/src/components/'),
  utils: path.resolve('ucf-common/src/utils/'),
  static: path.resolve('ucf-common/src/static/'),
  styles: path.resolve('ucf-common/src/styles/'),
  images: path.resolve('ucf-common/src/images/'),
  decorator: path.resolve('ucf-common/src/decorator/'),
  hooks: path.resolve('ucf-common/src/hooks/'),
  constants: path.resolve('ucf-common/src/constants/'),
  services: path.resolve('ucf-common/src/services/'),
  core: path.resolve('ucf-common/src/core/'),
  ucfapps: path.resolve('ucf-apps/'),
  "ucf-apps": path.resolve('ucf-apps/'),
  "@data": path.resolve('ucf-apps/data/'),
}

const nodeModules =
  fs.readdirSync(path.resolve('node_modules'));

nodeModules.forEach(moduleName => {
  if (moduleName.startsWith('bee-')) {
    alias[moduleName] = path.resolve('node_modules/' + moduleName)
  }
})

let appModuleList = moduleConfig.appModuleList;

appModuleList.forEach(module => {
  let { path: modulePath } = module;
  let aliasKey = modulePath;
  alias[aliasKey] = path.resolve(`ucf-apps/${modulePath}/src/`)
})


module.exports = {
  resolve: {
    alias: alias
  }
}



