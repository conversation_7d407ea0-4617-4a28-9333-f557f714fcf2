// let fs = require("fs")
// let UglifyJS = require("uglify-js")
// let CleanCSS = require("clean-css")
// let cssMinify = new CleanCSS()
// let merge = require('webpack-merge')
const htmlWebpackPlugins = require('html-webpack-plugin')
function HtmlWebpackPluginBefore(cb) {
  this.pluginName = 'HtmlWebpackPluginBefore';
  this.cb = cb
}

// HtmlWebpackPluginBefore.prototype.getRes = function () {
//     let {paths, uglify} = this.option
//
//     let jsSors = "", cssSors = "";
//     paths.forEach((path) => {
//         if (/\.css$/.test(path)) {
//             cssSors += fs.readFileSync(path, "utf-8")
//         }
//         if (/\.js$/.test(path)) {
//             jsSors += fs.readFileSync(path, "utf-8")
//         }
//     })
//     if (uglify) {
//         cssSors = cssMinify.minify(cssSors).styles;
//         jsSors = UglifyJS.minify(jsSors).code;
//     }
//
//     return {
//         css: cssSors ? "<style>" + cssSors + "</style>" : "",
//         js: jsSors ? "<script>" + jsSors + "</script>" : ""
//     }
//
// }

HtmlWebpackPluginBefore.prototype.apply = function (compiler) {
  if ('hooks' in compiler) {
    compiler.hooks.compilation.tap(this.pluginName, this.applyCompilation.bind(this))
  }else {
    compiler.plugin('compilation', this.applyCompilation.bind(this))
  }

};

HtmlWebpackPluginBefore.prototype.applyCompilation = function(compilation) {
  let self = this;
  let isNew = false;
  if ('hooks' in compilation) {
    let hooks = compilation.hooks;
    if (hooks.htmlWebpackPluginBeforeHtmlProcessing) {
      compilation.hooks.htmlWebpackPluginBeforeHtmlProcessing.tapAsync(this.pluginName, registerCb)
    }else {
      isNew = true;
      let hooks = htmlWebpackPlugins.getHooks(compilation);
      hooks.afterTemplateExecution.tapAsync(this.pluginName, registerCb)
    }
  }else {
    compilation.plugin('html-webpack-plugin-before-html-processing', registerCb)
  }
  function registerCb(htmlPluginData, callback) {
    let htmlCode = htmlPluginData.html;
    if (self.cb) {
      htmlCode = self.cb(htmlCode, isNew)
    }
    htmlPluginData.html = htmlCode
    if (callback) {
      callback(null, htmlPluginData)
    }else {
      return Promise.resolve(htmlPluginData)
    }
  }
}

module.exports = HtmlWebpackPluginBefore;
