const argv = require("minimist")(process.argv.slice(2));
const commands = argv;

/**
 * 前端资源上下文路径
 * */
const CONTEXT = commands.context ;

//资源产出目录
const DIST_ROOT = 'ucf-publish';

/**
 * 后端接口上下文路径
 * */
const SERVER_CONTEXT = '';

/**
 * react，react-dom, prop-types, axios, mobx, mobx-react, moment
 * 等项目基础框架库提取包名称
 * */
const BASE_COMMON_NAME = 'baseCommon';


/**
 * 是否私有化
 * */
const PRIVATE = commands.private ?  JSON.parse(commands.private) : false;

module.exports = {
  CONTEXT,
  DIST_ROOT,
  BASE_COMMON_NAME,
  SERVER_CONTEXT,
  PRIVATE,
}
