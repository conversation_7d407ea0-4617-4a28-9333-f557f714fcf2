/*
 * @Author: your name
 * @Date: 2021-04-08 14:13:25
 * @LastEditTime: 2021-04-15 11:02:11
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \ublinker-fe\config\appModuleConfig.js
 */
const argv = require("minimist")(process.argv.slice(2));
const commands = argv;
const utils = require("./utils");
const { PRIVATE } = utils;
/**
 * 定义模块列表
 * path 模块路径
 * type 模块类型 page -该模块单独构建  -module该模块会打包进portal
 * */

let appModuleList = [
    { path: "data/sync-task", type: "page", from: "bip" },
    { path: "data/map-relations", type: "page", from: "bip" },
    { path: "data/exchange", type: "page", from: "bip" },
    // 集成方案配置
    { path: "integrate/programme-config", type: "page", from: "bip" },
    // 网关管理
    { path: "gateway/manage", type: "page", from: "bip" },
    // aksk密钥管理
    { path: "key/aksk-manage", type: "page", from: "bip" },
    //数据集成任务监控
    { path: "task-monitoring", type: "page", from: "bip" },
    { path: "system/warning/set", type: "page", from: "bip" },
    { path: "connector/ec", type: "page", from: "bip" },

    //管理端
    { path: "data/timing", type: "page" }, // ??????
    { path: "data/migration", type: "page" }, // 数据迁移

    { path: "event/view-pub2pri/log", type: "page" },
    { path: "event/view-pub2pri/subion", type: "page" },
    { path: "event/view-pub2pri/compensate", type: "page" },
    { path: "event/task-class", type: "page" }, // TODO:
    { path: "event/view-register", type: "page" }, // TODO
    { path: "event/init-record", type: "page" }, // TODO:

    // {path: 'system/warning', type: 'page'},
    // {path: 'system/connector', type: 'page'},
    { path: "system/msg-notify/set", type: "page" },
    { path: "system/msg-notify", type: "page" },

    // { path: "connector/erp", type: "page" },
    // {path: 'connector/common', type: 'page'},
    // {path: 'connector/common2', type: 'page'},
    { path: "connector/task-init", type: "page" },
    { path: "monitor/gateway", type: "module" },
    { path: "monitor/data", type: "page" }, //好像没用
    { path: "monitor/task", type: "page" }, //管理端页面 //TODO
    // {path: 'monitor/api-call', type: 'module'},
    // {path: 'monitor/active-tenant', type: 'module'},
    { path: "monitor/tenant", type: "page" }, //bip页面
    { path: "monitor", type: "page" }, //管理端页面
    { path: "design-connector", type: "page" }, // TODO:
    //首页
    { path: "home-page", type: "page" },
    //管理端，修改数据映射
    { path: "modify-data", type: "page" }, // TODO
    { path: "maintain/special-logic", type: "page" }, // TODO
    //管理端-删除回调配置维护
    { path: "maintain/del-callback", type: "page" }, // TODO
    //管理端-后续操作维护
    { path: "maintain/follow-operation", type: "page" }, // TODO
    //管理端-后续操作排除配置
    { path: "maintain/operation-exclusion", type: "page" }, // TODO
    //解绑初始化
    { path: "unbindInit", type: "page" },

    // {path: 'key2/aksk-manage', type: "page"},
    // 集成系统配置
    // {path: 'integrate/system-config', type: "page"},
    // 数据对象配置
    // {path: 'data/object-config', type: "page"},
    // 启动方案配置
    // {path: 'start/start-config', type: "page" },
    //集成方案包
    // {path: 'integration-package', type: "page"},
    //集成实施工具
    // {path: 'integrated-implementation', type: "page"},
    //管理端-特殊逻辑配置维护
    //集成应用
    // {path: 'integrated-application', type: "page"},
];

/**
 * 读取对应模块中的 config.js， 并将config.js中的配置扩展到appModuleList中的每一个成员
 * 并通过ucf.config.js global_env 参数 注入全局变量 MODULE_LIST
 * 当 模块以路由的形式集成到portal中的，跳转模块中的对应子路由时，需要根据config中的key在此列表中查找对应的模块信息
 * 可调用 ucf-common/decorator/index.js 中的 navTo装饰器
 * 拿到主路由 进行 拼接
 * */
appModuleList.forEach((item) => {
    let moduleConfig = null;
    try {
        moduleConfig = require(`../ucf-apps/${item.path}/src/config.js`);
    } catch (e) {
        moduleConfig = null;
    }
    if (moduleConfig) {
        //如果该模块在portal中通过路由加载 routePath 为该模块父路由路径
        item.routePath = moduleConfig.path;
        item.moduleKey = moduleConfig.key;
    }
});

const getModulePath = function (type) {
    let list = null;
    if (type) {
        list = appModuleList.filter((item) => item.type === type);
    } else {
        list = appModuleList;
    }
    return list.map((item) => item.path);
};

module.exports = {
    appModuleList,
    getModulePath,
};
