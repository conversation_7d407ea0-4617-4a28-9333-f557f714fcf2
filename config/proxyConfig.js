/*
 * @Author: your name
 * @Date: 2021-04-15 17:40:03
 * @LastEditTime: 2021-06-08 20:05:31
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \ublinker-fe\config\proxyConfig.js
 */
const argv = require("minimist")(process.argv.slice(2));
/**
 * 代理配置
 * */

const TARGET = "https://bip-daily.yonyoucloud.com";

const COOKIE = ``;
const urlMap = new URL(TARGET);

const TARGET_HOST = urlMap.host;

module.exports = [
    {
        enable: false,
        //要代理访问的对方路由
        router: [
            "/gwmanage/gwportal/mygwapp/integrated/system/findByPage",
            "/gwmanage/gwportal/mygwapp/integrated/system/add",
            "/gwmanage/gwportal/mygwapp/integrated/system/update",
            "/gwmanage/gwportal/diwork/integscheme/getSchemesBySysId",
            "/gwmanage/gwportal/diwork/integscheme/deleteSchemeInfo/",
            "/gwmanage/gwportal/diwork/integscheme/getSchemeInfo/",
            "/gwmanage/gwportal/diwork/integscheme/transformationRuleList",
            "/gwmanage/gwportal/diwork/integscheme/typeList",
            "/gwmanage/gwportal/mygwapp/integrated/system/findAll",
            "/gwmanage/gwportal/diwork/integscheme/addSchemeInfo",
            "/gwmanage/gwportal/diwork/integscheme/updateSchemeInfo",
            "/gwmanage/gwportal/diwork/integscheme/getBothObjectAttributes",
            "/gwmanage/gwportal/diwork/integscheme/getObjectAttributes",
            "/gwmanage/gwportal/mygwapp/integrated/system/findById",
            "/gwmanage/gwportal/diwork/integscheme/listObjectsBySysId",
            "/gwmanage/gwportal/diwork/integscheme/listObjectOperation",
        ],
        url: "https://mock.yonyoucloud.com/mock/17450",
    },
    {
        enable: false,
        headers: {
            Host: "************:8081",
            Cookie: "gr_user_id=0b471c4c-319d-42e0-acf3-25154f40f239; grwng_uid=111b0fb2-5514-47b5-9031-0e6b850aa7bc; yht_username_diwork=ST-3505-qhdvenCtvAcgi0DkHnct-idtest.yyuap.com__1b7bc676-35c1-461e-9aeb-d741471dd722; yht_usertoken_diwork=RVauaNUUEJ3sd3JVf%2FjVRZlIUxd3xt0r1nY18B8GNQthhuU%2FmBA5jwc1YdibZh1o9YxSmPXD482k9SN6cl8QGA%3D%3D; yht_access_token=bttczZWRnhMcVROV3hVWG84eGM2UWxFaVdmb2V6Vk9ySlduR1dWL0gwMU1nNzFpMXlUc01YVGxVK3l2R1Yzd0U2aVJaZnFxbGp5Q0xmZUVEdmcxQVE5MmdiemMxMVRaOVRXMDJ3Y0pVTDZySzQ9X19pZHRlc3QueXl1YXAuY29t__dc4fe19692220095e1ec68a852f28156_1605491361338; wb_at=LMjprmrjpgcudm5su3bfhm6jAmbsjhcsdrskxxtZokbnl; ARK_STARTUP=eyJTVEFSVFVQIjp0cnVlLCJTVEFSVFVQVElNRSI6IjIwMjAtMTEtMTYgMDk6NDk6MjEuNjU0In0%3D; PHPSESSID=f00n449f67dvknginpb4np0gn4; ck_safe_chaoke_csrf_token=2dedb607a7db805d88ec7d43240bfe6d; YKJ_IS_DIWORK=1; YKJ_DIWORK_DATA=%7B%22data%22%3A%7B%22is_diwork%22%3A1%2C%22cur_qzid%22%3A%2218273%22%7D%2C%22key%22%3A%220ce5d12c00be4226e7d6293b1a6a4bb3%22%7D; jwt_token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fucQON1XtYwBg0mXwu4hYSYWUJCbdgz-oP3xOqZcT7o; ARK_ID=JS81ea1bf47dadc62dd73977a61e4760ca81ea; FZ_STROAGE.yyuap.com=eyJBUktTVVBFUiI6eyJ0ZW5hbnRfaWQiOiJubm1mdGswYyIsImNvbXBhbnkiOiLnjovosarpmLPpo5%2Flk4Hlhazlj7giLCJ1c2VyX2lkIjoiMWI3YmM2NzYtMzVjMS00NjFlLTlhZWItZDc0MTQ3MWRkNzIyIiwidXNlcl9uYW1lIjoic2hpcGwiLCJwcm9kdWN0X2lkIjoiZGl3b3JrIiwicHJvZHVjdF9uYW1lIjoiZGl3b3JrIn0sIlNFRVNJT05JRCI6IjJjNmRlYWViYmNjMWY5NjciLCJTRUVTSU9OREFURSI6MTYwNTQ5MTM5NTIwNCwiQU5TQVBQSUQiOiJhZDliOTEwZjA3MTA5NTJmIiwiQU5TJERFQlVHIjoyLCJBTlNVUExPQURVUkwiOiJodHRwczovL2FydC5kaXdvcmsuY29tLyIsIkZSSVNUREFZIjoiMjAyMDExMTYiLCJGUklTVElNRSI6ZmFsc2UsIkFSS19MT0dJTklEIjoiMWI3YmM2NzYtMzVjMS00NjFlLTlhZWItZDc0MTQ3MWRkNzIyIiwiQVJLX0lEIjoiSlM4MWVhMWJmNDdkYWRjNjJkZDczOTc3YTYxZTQ3NjBjYTgxZWEiLCJBUktGUklTVFBST0ZJTEUiOiIyMDIwLTExLTE2IDA5OjQ5OjIxLjY0NyIsIkFOU1NFUlZFUlRJTUUiOi01ODd9",
        },
        //要代理访问的对方路由
        router: ["/gwmanage/gwportal", "/gwmanage/gwapp", "/gwmanage/erpdata"],
        url: "http://************:8081",
    },
    {
        enable: false,
        headers: {
            Host: "ucg-test.yyuap.com",
            Cookie: "NTKF_T2D_CLIENTID=guest487B7BB8-B439-5874-618C-35D71D9E1512; nTalk_CACHE_DATA={uid:yu_1000_ISME9754_guest487B7BB8-B439-58,tid:1590041189790015}; yht_access_token=bttbFhPZS9rRkRtZW5BcHF6MWtwcWxpdlVKeUxWK2tFa3VkeUljZTM0d2VBcS9QcUhScXVyYTNiekFjL3E4Um4vVFFqcnk2NjRmVTMzNVFLMkEzdVRqZjM0UVFIbWNBM2Z5Q01hbzNJeXdJMElnT1F2S0tpMFpmOGdBWWE4WG80Vm9fX3U4Yy1zc28tZGFpbHkueXl1YXAuY29t__44117c1f9ce74fbc501b39e3ba6cc362_1590817609229; wb_at=LMjtpnmjnteMGGsxqeCL6nBgtyihjtubjtrdqjcZhkxkxxtZokbnl; ARK_STARTUP=eyJTVEFSVFVQIjp0cnVlLCJTVEFSVFVQVElNRSI6IjIwMjAtMDUtMzAgMTM6NDY6NDkuOTQ5In0%3D; ARK_ID=JS2a2f48418170ea5b17ce1dbeaae9217b2a2f; FZ_STROAGE.yyuap.com=eyJBUktTVVBFUiI6eyJ0ZW5hbnRfaWQiOiJzZWN4MG1reCIsImNvbXBhbnkiOiLljJfkuqzph5HoipLmnpznp5HmioDmnInpmZDlhazlj7giLCJ1c2VyX2lkIjoiYWFjYTMwMjYtNWM0ZC00MWE1LTg5ZjMtZTM2YTJmYWMyNDg4IiwidXNlcl9uYW1lIjoiIiwicHJvZHVjdF9pZCI6ImRpd29yayIsInByb2R1Y3RfbmFtZSI6ImRpd29yayJ9LCJTRUVTSU9OSUQiOiIyNWIzZTI1YjI4MDg5OTk5IiwiU0VFU0lPTkRBVEUiOjE1OTA4MTc2MDk5NjUsIkFOU0FQUElEIjoiYWQ5YjkxMGYwNzEwOTUyZiIsIkFOUyRERUJVRyI6MiwiQU5TVVBMT0FEVVJMIjoiaHR0cHM6Ly9hcnQuZGl3b3JrLmNvbS8iLCJGUklTVERBWSI6IjIwMjAwNTE1IiwiRlJJU1RJTUUiOmZhbHNlLCJBUktfTE9HSU5JRCI6ImFhY2EzMDI2LTVjNGQtNDFhNS04OWYzLWUzNmEyZmFjMjQ4OCIsIkFSS19JRCI6IkpTMmEyZjQ4NDE4MTcwZWE1YjE3Y2UxZGJlYWFlOTIxN2IyYTJmIiwiQVJLRlJJU1RQUk9GSUxFIjoiMjAyMC0wNS0xNSAxMTozMjo1MS45MjEiLCJBTlNTRVJWRVJUSU1FIjotOTkyfQ%3D%3D; gr_user_id=94a99f90-7104-420a-a4e7-2a2f2d7dbe3d; grwng_uid=5a2b0bea-6339-4158-9cd1-71001df935ed; at=a3d91917-3f57-498e-bc04-0b73ad955783; yonyou_uid=237c8832-9bbe-48fe-8c21-c24563cce1f0; yonyou_uname=%25E7%2589%259B%25E7%25BA%25A2; JSESSIONID=1D4A730B5AB4EA66B7E4BAB07002E7D8; yht_usertoken_yyopen=K1y4BAh8YnxkSZc4D%2BETb5pIBlY%2F2tbhXnGgu5En%2FIscKLmlPn5UnR4YoliwLT%2Fg%2FD6YQ%2Fhc9peHG5g%2FsREL0w%3D%3D; yht_username_yyopen=ST-3269-SleqK9spLHbwcgIFGam3-idtest.yyuap.com__237c8832-9bbe-48fe-8c21-c24563cce1f0; u_appid=developer; userName=%E7%89%9B%E7%BA%A2; userId=237c8832-9bbe-48fe-8c21-c24563cce1f0; userCode=niuhongk7ft5fu7; u_providerid=237c8832-9bbe-48fe-8c21-c24563cce1f0; SESSION_KEY=4688982e-6085-41c2-829c-af966ea73799; open_private=1; ucg_tenant=75c13acc-ae53-4694-9a6e-3ddcce26d7f3; isv=75c13acc-ae53-4694-9a6e-3ddcce26d7f3; SERVERID=2651794d8e79974471ac0037cf5a859d|**********|**********",
        },
        //要代理访问的对方路由
        router: ["/gwmanage/gwportal/mygwapp/monitor/", "/gwmanage/gwportal/mygwapp/dashboard/"],
        url: "http://ucg-test.yyuap.com",
    },
    {
        enable: false,
        headers: {
            Host: TARGET_HOST,
            Cookie: "sysid=diwork; eudiqz=khkzr494e3a157ab-8f52-4d8a-9c2a-a4f144bf0135; PHPSESSID=ojd4adrpm70ulcoeiis8mgslcj; NTKF_T2D_CLIENTID=guest8158D11E-2B9C-DD5C-B541-C1BACBCF7D26; nTalk_CACHE_DATA={uid:yu_1000_ISME9754_guest8158D11E-2B9C-DD,tid:1596683111375647}; YHT_UGC=eyJhbGciOiJIUzI1NiJ9.eyJ0Z3QiOiJUR1QtMzc1MC1KMWROWUhhZFliR1Z4RE5sczFNM2V4WVk1U256cGxQZnFPbGdlZXNaREttTE0xMzN2RS11OGMtdXNlci1kYWlseS55eXVhcC5jb20iLCJmbGFnIjp0cnVlLCJjb3VudCI6MCwidXNlcklkIjoiYWFjYTMwMjYtNWM0ZC00MWE1LTg5ZjMtZTM2YTJmYWMyNDg4IiwidXJsIjoidThjLXNzby1kYWlseS55eXVhcC5jb20ifQ.LmhATXPraU6wa53_7_QqOL0iwykjUgtiF1J9qmcTdCw; ARK_ID=JS2a2f48418170ea5b17ce1dbeaae9217b2a2f; yht_username_diwork=ST-15295-ejR3eU6cgkG6Rk4FkQMc-u8c-user-daily.yyuap.com__aaca3026-5c4d-41a5-89f3-e36a2fac2488; yht_usertoken_diwork=2BMFTRgUA9E%2Fm%2BBtdeE7kkVbPPqyLmG%2FHYTnNgppVYkOXLZ42frqglA1iqgHna1DO5w62JtU4dVGOaOpZ5Vdmg%3D%3D; yht_access_token=bttYWh6bGJjNHhaZUc5cmw4ZE40Y2QvVEJWYi9lSm1SL2xCZUZ3aHh0dExlRE4wRVdjUERhQXRLQXNUUENVUUlkYW5oRGJPRUVwbzlIT1d4a1RiOWRXVURHRnk0a0RyQXp5c2FFN21DcDUwbStIUUJEN1c4OFF6cmlCY1RpYmhpbHpfX3U4Yy1zc28tZGFpbHkueXl1YXAuY29t__44117c1f9ce74fbc501b39e3ba6cc362_1598321964577; wb_at=LMjnrovrjdiKpdNsbfj9sKjq8jJFbjtubjtrdqjcZhkxkxxtZokbnl; ARK_STARTUP=eyJTVEFSVFVQIjp0cnVlLCJTVEFSVFVQVElNRSI6IjIwMjAtMDgtMjUgMTQ6MjY6NDcuNTg3In0%3D; at=1c3e1581-5ae0-490c-b798-33e2892779e8; yonyou_uid=1b7bc676-35c1-461e-9aeb-d741471dd722; yonyou_uname=shipl; yht_username=ST-4878-RQoVONeoyLdmcc5JyEXs-idtest.yyuap.com__1b7bc676-35c1-461e-9aeb-d741471dd722; yht_usertoken=7quDphoVlxT7J4AUULNOD9qmqR3IaK%2B1qTjE1p3mtlCnHqZGkEdXlU3ghhyWd8EiJSHodA8hHi2MQkkSCD02IA%3D%3D; FZ_STROAGE.yyuap.com=eyJBUktTVVBFUiI6eyJ0ZW5hbnRfaWQiOiJzZWN4MG1reCIsImNvbXBhbnkiOiLljJfkuqzph5HoipLmnpznp5HmioDmnInpmZDlhazlj7giLCJ1c2VyX2lkIjoiYWFjYTMwMjYtNWM0ZC00MWE1LTg5ZjMtZTM2YTJmYWMyNDg4IiwidXNlcl9uYW1lIjoiMTM0Mzk0MjcwNDgiLCJwcm9kdWN0X2lkIjoiZGl3b3JrIiwicHJvZHVjdF9uYW1lIjoiZGl3b3JrIn0sIlNFRVNJT05JRCI6IjRhNDYyODAzNjNhNGU1NTQiLCJTRUVTSU9OREFURSI6MTU5ODQwNjQ5Nzk1MiwiQU5TQVBQSUQiOiJhZDliOTEwZjA3MTA5NTJmIiwiQU5TJERFQlVHIjoyLCJBTlNVUExPQURVUkwiOiJodHRwczovL2FydC5kaXdvcmsuY29tLyIsIkZSSVNUREFZIjoiMjAyMDA3MDciLCJGUklTVElNRSI6ZmFsc2UsIkFSS19JRCI6IkpTMmEyZjQ4NDE4MTcwZWE1YjE3Y2UxZGJlYWFlOTIxN2IyYTJmIiwiQVJLX0xPR0lOSUQiOiJhYWNhMzAyNi01YzRkLTQxYTUtODlmMy1lMzZhMmZhYzI0ODgiLCJBUktGUklTVFBST0ZJTEUiOiIyMDIwLTA3LTA3IDIxOjE2OjQ0LjI1MSIsIkFOU1NFUlZFUlRJTUUiOi0xMDIwfQ%3D%3D; acw_tc=2760827415984211413047301eac1beb160e42b2bf8a1b9a2ca02f6836063e; SESSION=469616ee-aa11-4b52-8b98-a8698599ed10",
        },
        //要代理访问的对方路由
        router: ["/gwmanage/gwapp"],
        url: TARGET,
    },
    {
        enable: true,
        router: ["/iuap-ipaas-dataintegration/gwmanage/fastMockAssetPack"],
        url: "https://www.fastmock.site/mock/dd130717c9712fa63fb31b6bff11945b",
        pathRewrite: {
            "^/iuap-ipaas-dataintegration/gwmanage/fastMockAssetPack": "/fastMockAssetPack",
        },
    },
    {
        enable: true,
        headers: {
            Host: TARGET_HOST,
            "X-XSRF-TOKEN": "AX_VB2FVA66F4EM22LT1V1HG5Y1G!192433",
        },
        //要代理访问的对方路由
        router: [
            "/iuap-ipaas-dataintegration/gwmanage/gwportal",
            "/iuap-ipaas-dataintegration/gwmanage/erpdata",
            "/iuap-ipaas-dataintegration/gwmanage/si",
            "/iuap-ipaas-dataintegration/gwmanage",
            "/iuap-tns",
            "/iuap-tns/ucf-wh",
            "/iuap-ipaas-dataintegration/formula",
            "/iuap-ipaas-base/baseconsole",
            "/iuap-ipaas-designer/console",
            "/iuap-apcom-workbench",
            "/iuap-tinper",
            "/iuap-apcom-coderule",
            "/tne2nd",
            "/iuap-fe-webhome",
            "/iuap-apcom-i18n",
        ],
        url: TARGET,
    },
    {
        // 本地联调 base-fe
        enable: true,
        router: ["/iuap-ipaas-base/ucf-wh/base-fe"],
        url: "http://localhost:8384", //   NOSONAR
        pathRewrite: {
            "^/iuap-ipaas-base/ucf-wh/base-fe": "/",
        },
    },
];
