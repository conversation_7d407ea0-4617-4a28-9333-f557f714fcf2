export default {
    zhcn: {
        MIX_UBL_ALL_UBL_FE_LOC_2021831946: "下载网关",
        MIX_UBL_ALL_UBL_FE_LOC_2021831947: "新建网关",
        MIX_UBL_ALL_UBL_FE_LOC_2021831948: "网关ID",
        MIX_UBL_ALL_UBL_FE_LOC_2021831949: "网关编码",
        MIX_UBL_ALL_UBL_FE_LOC_2021831950: "连接器部署",
        MIX_UBL_ALL_UBL_FE_LOC_2021831951: "设为默认",
        MIX_UBL_ALL_UBL_FE_LOC_2021831952: "编辑",
        MIX_UBL_ALL_UBL_FE_LOC_2021831953: "请选择IP地址",
        MIX_UBL_ALL_UBL_FE_LOC_2021831954: "编号",
        MIX_UBL_ALL_UBL_FE_LOC_2021831955: "IP地址",
        MIX_UBL_ALL_UBL_FE_LOC_2021831956: "取消",
        MIX_UBL_ALL_UBL_FE_LOC_2021831957: "确认",
        MIX_UBL_ALL_UBL_FE_LOC_2021831958: "连接器配置",
        MIX_UBL_ALL_UBL_FE_LOC_2021831959: "网关服务器IP",
        MIX_UBL_ALL_UBL_FE_LOC_2021831960: "获取IP地址",
        MIX_UBL_ALL_UBL_FE_LOC_2021832000: "白名单",
        MIX_UBL_ALL_UBL_FE_LOC_2021832001: "出网端口号",
        MIX_UBL_ALL_UBL_FE_LOC_2021832002: "连接器实例ID",
        MIX_UBL_ALL_UBL_FE_LOC_2021832003: "连接器编码",
        MIX_UBL_ALL_UBL_FE_LOC_2021832004: "连接适配器编码",
        MIX_UBL_ALL_UBL_FE_LOC_2021832005: "连接适配器名称",
        MIX_UBL_ALL_UBL_FE_LOC_2021832006: "连接编码",
        MIX_UBL_ALL_UBL_FE_LOC_2021832007: "连接名称",
        MIX_UBL_ALL_UBL_FE_LOC_2021832008: "连接适配器状态",
        MIX_UBL_ALL_UBL_FE_LOC_2021832009: "连接适配器版本",
        MIX_UBL_ALL_UBL_FE_LOC_2021832010: "更新适配器",
        MIX_UBL_ALL_UBL_FE_LOC_2021832011: "卸载适配器",
        MIX_UBL_ALL_UBL_FE_LOC_2021832012: "网关健康检查",
        MIX_UBL_ALL_UBL_FE_LOC_2021832013: "名称",
        MIX_UBL_ALL_UBL_FE_LOC_2021832014: "网关健康检查成功",
        MIX_UBL_ALL_UBL_FE_LOC_2021832015: "默认",
        MIX_UBL_ALL_UBL_FE_LOC_2021832016: "已启动",
        MIX_UBL_ALL_UBL_FE_LOC_2021832017: "已离线",
        MIX_UBL_ALL_UBL_FE_LOC_2021832018: "确定删除此网关？",
        MIX_UBL_ALL_UBL_FE_LOC_2021832019: "下载密钥",
        MIX_UBL_ALL_UBL_FE_LOC_2021832020: "删除",
        MIX_UBL_ALL_UBL_FE_LOC_2021832021: "请输入",
        MIX_UBL_ALL_UBL_FE_LOC_2021832022: "请输入正确的ip",
        MIX_UBL_ALL_UBL_FE_LOC_2021832023: "编辑网关",
        MIX_UBL_ALL_UBL_FE_LOC_2021832024: "请输入网关编码",
        MIX_UBL_ALL_UBL_FE_LOC_2021832025: "网关编码由英文、数字、下划线组成",
        MIX_UBL_ALL_UBL_FE_LOC_2021832026: "请输入网关名称",
        MIX_UBL_ALL_UBL_FE_LOC_20218231446: "重置网关状态",
        MIX_UBL_ALL_UBL_FE_LOC_20221191127: "操作成功",

        MIX_UBL_ALL_UBL_FE_LOC_20225161702: "主",
        MIX_UBL_ALL_UBL_FE_LOC_202205311132: "编码",
        MIX_UBL_ALL_UBL_FE_LOC_202205311133: "描述",
        MIX_UBL_ALL_UBL_FE_LOC_202205311134: "网关服务器IP",
        MIX_UBL_ALL_UBL_FE_LOC_202205311135: "出网端口号",

        MIX_UBL_ALL_UBL_FE_LOC_202206061427: "安装说明",
        MIX_UBL_ALL_UBL_FE_LOC_202206061428: "按照下面步骤完成网关创建、客户端下载及安装",
        MIX_UBL_ALL_UBL_FE_LOC_202206061429: "第一步：下載网关客户端",
        MIX_UBL_ALL_UBL_FE_LOC_202206061430: "1. 找一台7x24小时都能访问您企业内部系统，且能访问互联网的机器，安装网关客户端。",
        MIX_UBL_ALL_UBL_FE_LOC_202206061431: "2.下载完成后解压，需保证解压后的文件目录不存在中文字符。",
        MIX_UBL_ALL_UBL_FE_LOC_202206061432: "供4种主流操作系统版本。即：Windows x64、Windows x86、Linux x64、Linux x86",
        MIX_UBL_ALL_UBL_FE_LOC_202206061433: "第二步：下载密钥",
        MIX_UBL_ALL_UBL_FE_LOC_202206061434: "请将下载的密钥解压至网关客户端解压目录的config文件夹下",
        MIX_UBL_ALL_UBL_FE_LOC_202206061435: "第三步：启动网关客户端（本地）",
        MIX_UBL_ALL_UBL_FE_LOC_202206061436: "在网关客户端解压目录的bin文件夹，运行startup.bat",
        MIX_UBL_ALL_UBL_FE_LOC_202206061437: "Linux环境：在网关客户端解压目录的bin文件夹，运行startup.sh",
        MIX_UBL_ALL_UBL_FE_LOC_202206061438: "第四步：测试连接",
        MIX_UBL_ALL_UBL_FE_LOC_202206061439: "在线",
        MIX_UBL_ALL_UBL_FE_LOC_202206061440: "离线",
        MIX_UBL_ALL_UBL_FE_LOC_202206061441: "编辑",
        MIX_UBL_ALL_UBL_FE_LOC_202206061442: "混合云网关",
        MIX_UBL_ALL_UBL_FE_LOC_202206061443: "通过YonLinker实现与内网服务集成的代理系统。",
        MIX_UBL_ALL_UBL_FE_LOC_202206061444: "如何使用",
        MIX_UBL_ALL_UBL_FE_LOC_202206061445: "混合云网关由 Server 和 Client 构成：",
        MIX_UBL_ALL_UBL_FE_LOC_202206061446: "1、Server: 支持网关管理、客户端配置、连接器部署等功能操作。",
        MIX_UBL_ALL_UBL_FE_LOC_202206061447: "2、Client: 实现YonLinker与用户内网服务的交互。",
        MIX_UBL_ALL_UBL_FE_LOC_202206061448: "连接器",
        MIX_UBL_ALL_UBL_FE_LOC_202206061449: "下载终端",
        MIX_UBL_ALL_UBL_FE_LOC_202206061450: "状态刷新",
        MIX_UBL_ALL_UBL_FE_LOC_202206061451: "网关名称",
        MIX_UBL_ALL_UBL_FE_LOC_202206061452: "创建人",
        MIX_UBL_ALL_UBL_FE_LOC_202206061453: "创建时间",
        MIX_UBL_ALL_UBL_FE_LOC_202206061454: "最后修改人",
        MIX_UBL_ALL_UBL_FE_LOC_202206061455: "最后修改时间",
        MIX_UBL_ALL_UBL_FE_LOC_202206061456: "Client版本",
        MIX_UBL_ALL_UBL_FE_LOC_202206061457: "IP白名单",
        MIX_UBL_ALL_UBL_FE_LOC_202206061458: "内网访问IP",
        MIX_UBL_ALL_UBL_FE_LOC_202206061459: "内网访问端口",
        MIX_UBL_ALL_UBL_FE_LOC_202206061460: "下载客户端",
        MIX_UBL_ALL_UBL_FE_LOC_202206061461: "按照步骤完成网关创建、客户端下载及安装",
        MIX_UBL_ALL_UBL_FE_LOC_202206061462: "基本信息",
        MIX_UBL_ALL_UBL_FE_LOC_202206061463: "客户端连接信息",
        MIX_UBL_ALL_UBL_FE_LOC_202206061464: "添加",
        MIX_UBL_ALL_UBL_FE_LOC_202206061465: "高度封装的系统交互协议/业务系统；分为通用型、业务型。",
        MIX_UBL_ALL_UBL_FE_LOC_202206061466: "连接器部署到对应的混合云网关上，即可连接到内网特定的业务系统和中间件。",
        MIX_UBL_ALL_UBL_FE_LOC_202206061467: "序号",
        MIX_UBL_ALL_UBL_FE_LOC_202206061468: "连接器名称",
        MIX_UBL_ALL_UBL_FE_LOC_202206061469: "连接产品",
        MIX_UBL_ALL_UBL_FE_LOC_202206061470: "产品归属厂商",
        MIX_UBL_ALL_UBL_FE_LOC_202206061471: "连接配置数量",
        MIX_UBL_ALL_UBL_FE_LOC_202206061472: "卸载",
        MIX_UBL_ALL_UBL_FE_LOC_202206061473: "添加连接器",
        MIX_UBL_ALL_UBL_FE_LOC_202206061474: "暂无",
        MIX_UBL_ALL_UBL_FE_LOC_202206061475: "请搜索",
        MIX_UBL_ALL_UBL_FE_LOC_202206061476: "确定卸载【<%= name %>】吗？卸载后内网将无法使用，请谨慎操作。",
        MIX_UBL_ALL_UBL_FE_LOC_202206061477: "获取",
        MIX_UBL_ALL_UBL_FE_LOC_202206061478: "基本信息保存成功",
        MIX_UBL_ALL_UBL_FE_LOC_202206061479: "客户端连接信息保存成功",
        MIX_UBL_ALL_UBL_FE_LOC_202206061480: "部署",
        MIX_UBL_ALL_UBL_FE_LOC_202206061481: "正在部署，暂不支持操作",
        MIX_UBL_ALL_UBL_FE_LOC_202206061482: "请输入正确的端口号",
        MIX_UBL_ALL_UBL_FE_LOC_202206061483: "请录入允许访问Client端的IP，英文逗号分隔",
        MIX_UBL_ALL_UBL_FE_LOC_202206061484: "公有云server和Client端连接方式",
        MIX_UBL_ALL_UBL_FE_LOC_202206061485: "请输入完整的外网可访问的网关地址，包含http(s)://",
        MIX_UBL_ALL_UBL_FE_LOC_202206061486: "Client直连地址",
        MIX_UBL_ALL_UBL_FE_LOC_202206061487: "Client直连端口",
        MIX_UBL_ALL_UBL_FE_LOC_202206061488:
            "若公有云Server端和Client端通过HTTP直连，则需要录入【Client直连地址】，此地址必须是外网可访问的，且是包含http(s)://的完整地址，例如：例如：http://***********:8888，可以是nginx或者Client端所在服务器的外网地址",
        MIX_UBL_ALL_UBL_FE_LOC_202206061489:
            "Client端启动时会分配一个用于被公有云Server端连接的端口号，若通过nginx直连Client端，需要在nginx上增加跳转到此端口的配置",
        MIX_UBL_ALL_UBL_FE_LOC_202206061490:
            "Client端提供接口可以让客户内部系统通过Client端访问公有云服务，此接口的IP和端口就是设置的【内网访问IP】和【内网访问端口】",
        MIX_UBL_ALL_UBL_FE_LOC_202206061491: "确定删除该网关吗",
        MIX_UBL_ALL_UBL_FE_LOC_202206061492: "WEBSOCKET长连接",
        MIX_UBL_ALL_UBL_FE_LOC_202206061493: "HTTP直连",

        MIX_UBL_ALL_UBL_FE_LOC_202208221359:
            "WEBSOCKET长连接，指的是客户端启动的时候会向公有云Server端发起连接请求，连接上后此通道将长时间生效，公有云Server端和客户端可直接通过此通道进行通信，无需实时建立HTTP连接。此方式安全性更高，只需客户端所在服务器开通访问公有云Server端的网络权限即可，无需开通外网访问客户端所在服务器的网络权限",
        MIX_UBL_ALL_UBL_FE_LOC_202208221360: "HTTP直连：需要开通外网访问客户端所在服务器的权限，公有云和客户端的访问请求需要实时通过HTTP协议进行连接",
        MIX_UBL_ALL_UBL_FE_LOC_202208221361:
            "若公有云Server端和Client端通过HTTP直连，则需要录入【Client直连地址】，此地址必须是外网可访问的，且是包含http(s)://的完整地址，例如：http://***********:8888，可以是nginx或者Client端所在服务器的外网地址",
        MIX_UBL_ALL_UBL_FE_LOC_202208221362: "Nginx访问地址",
        MIX_UBL_ALL_UBL_FE_LOC_202208221363:
            "集群部署模式需要提前安装nginx，由nginx负责路由转发Client端。步骤如下：1.下载和解压nginx到服务器，请根据nginx安装服务器选择下载linux版或windows版。下载地址：http://nginx.org/en/download.html 2.在【网关详情页】维护网关client端实例，并下载nginx配置文件 3.将nginx配置文件覆盖到nginx服务器上（nginx安装目录/conf）4.启动nginx",

        MIX_UBL_ALL_UBL_FE_LOC_202208311350: "部署模式",
        MIX_UBL_ALL_UBL_FE_LOC_202208311351: "单一部署",
        MIX_UBL_ALL_UBL_FE_LOC_202208311352: "集群部署",
        MIX_UBL_ALL_UBL_FE_LOC_202208311353: "服务器IP",
        MIX_UBL_ALL_UBL_FE_LOC_202208311354: "换行输入多个IP",
        MIX_UBL_ALL_UBL_FE_LOC_202208311355: "集群部署模式需要提前安装nginx，由nginx负责路由转发Client端。步骤如下：",
        MIX_UBL_ALL_UBL_FE_LOC_202208311356: "1.下载和解压nginx到服务器，请根据nginx安装服务器选择下载linux版或windows版。",
        MIX_UBL_ALL_UBL_FE_LOC_202208311357: "下载地址：http://nginx.org/en/download.html",
        MIX_UBL_ALL_UBL_FE_LOC_202208311358: "2.在【网关详情页】维护网关client端实例，并下载nginx配置文件",
        MIX_UBL_ALL_UBL_FE_LOC_202208311359: "3.将nginx配置文件覆盖到nginx服务器上（nginx安装目录/conf）",
        MIX_UBL_ALL_UBL_FE_LOC_202208311360: "4.启动nginx",
        MIX_UBL_ALL_UBL_FE_LOC_202208311361: "基础配置",
        MIX_UBL_ALL_UBL_FE_LOC_202208311362: "保存",
        MIX_UBL_ALL_UBL_FE_LOC_202208311363: "http直连端口",
        MIX_UBL_ALL_UBL_FE_LOC_202208311364: "保存成功",
        MIX_UBL_ALL_UBL_FE_LOC_202208311365: "刷新",
        MIX_UBL_ALL_UBL_FE_LOC_202208311366: "实例IP",
        MIX_UBL_ALL_UBL_FE_LOC_202208311367: "实例端口号",
        MIX_UBL_ALL_UBL_FE_LOC_202208311368: "状态",
        MIX_UBL_ALL_UBL_FE_LOC_202208311369: "运行实例",
        MIX_UBL_ALL_UBL_FE_LOC_202208311370: "获取实例",
        MIX_UBL_ALL_UBL_FE_LOC_202208311371: "下载nginx配置文件",
        MIX_UBL_ALL_UBL_FE_LOC_202208311372: "获取ip",
        MIX_UBL_ALL_UBL_FE_LOC_202208311373: "高级设置",

        MIX_UBL_ALL_UBL_FE_LOC_202209151728: "集群",
        MIX_UBL_ALL_UBL_FE_LOC_202209151729: "网关出网IP",
        MIX_UBL_ALL_UBL_FE_LOC_202209151730: "集群部署：支持下载多个客户端安装至同一/多个服务器",
        MIX_UBL_ALL_UBL_FE_LOC_202209151731: "集群部署：请使用同一网关内的密钥文件",
        MIX_UBL_ALL_UBL_FE_LOC_202209151732: "集群部署：请先添加客户端所在服务器IP，再启动网关客户端",
        MIX_UBL_ALL_UBL_FE_LOC_202209151733: "第五步：下载和启动nginx（集群部署时需要）",
        MIX_UBL_ALL_UBL_FE_LOC_202209151734: "集群部署需提前安装nginx（由nginx负责路由转发网关客户端）步骤如下：",
        MIX_UBL_ALL_UBL_FE_LOC_202209151735:
            "1、下载和解压nginx到服务器，请根据nginx安装服务器选择下载linux版或windows版。下载地址：:http://nginx.org/en/download.html",
        MIX_UBL_ALL_UBL_FE_LOC_202209151736: "2、在【网关详情页】维护网关客户端实例，并下载nginx配置文件",
        MIX_UBL_ALL_UBL_FE_LOC_202209151737: "3、将nginx配置文件覆盖到nginx服务器上（nginx安装目录/conf）",
        MIX_UBL_ALL_UBL_FE_LOC_202209151738: "4、启动nginx",

        MIX_UBL_ALL_UBL_FE_LOC_202209221654: "仅安装一个客户端",
        MIX_UBL_ALL_UBL_FE_LOC_202209221655: "根据需要安装多个客户端组成一个集群。具体集群部署的安装方式详见网关首页右上角的【安装说明】",
        MIX_UBL_ALL_UBL_FE_LOC_202209221656:
            "对于集群部署的客户端，需要前置安装一个nginx进行路由跳转。若是长连接方式，则【nginx访问地址】输入集群nginx所在内网地址即可；如果是直连方式，客户可以直接将集群nginx开通外网访问权限，输入集群nginx外网可访问的地址，客户还可以将集群nginx放在内网，单独安装一个外网可以访问的nginx，此nginx上配置跳转到集群nginx上，【nginx访问地址】输入此外网nginx即可。访问地址必须是包含http(s)://的完整地址，例如：http://***********:8888",

        MIX_UBL_ALL_UBL_FE_LOC_202211221020: "连接方式",
        MIX_UBL_ALL_UBL_FE_LOC_202211221021: "适配器",
    },
    zhtw: {
        MIX_UBL_ALL_UBL_FE_LOC_2021831946: "下載網關",
        MIX_UBL_ALL_UBL_FE_LOC_2021831947: "新建網關",
        MIX_UBL_ALL_UBL_FE_LOC_2021831948: "網關ID",
        MIX_UBL_ALL_UBL_FE_LOC_2021831949: "網關編碼",
        MIX_UBL_ALL_UBL_FE_LOC_2021831950: "連接器部署",
        MIX_UBL_ALL_UBL_FE_LOC_2021831951: "設為默認",
        MIX_UBL_ALL_UBL_FE_LOC_2021831952: "編輯",
        MIX_UBL_ALL_UBL_FE_LOC_2021831953: "請選擇IP地址",
        MIX_UBL_ALL_UBL_FE_LOC_2021831954: "編號",
        MIX_UBL_ALL_UBL_FE_LOC_2021831955: "IP地址",
        MIX_UBL_ALL_UBL_FE_LOC_2021831956: "取消",
        MIX_UBL_ALL_UBL_FE_LOC_2021831957: "確認",
        MIX_UBL_ALL_UBL_FE_LOC_2021831958: "連接器配置",
        MIX_UBL_ALL_UBL_FE_LOC_2021831959: "網關服務器IP",
        MIX_UBL_ALL_UBL_FE_LOC_2021831960: "獲取IP地址",
        MIX_UBL_ALL_UBL_FE_LOC_2021832000: "白名單",
        MIX_UBL_ALL_UBL_FE_LOC_2021832001: "出網端口號",
        MIX_UBL_ALL_UBL_FE_LOC_2021832002: "連接器實例ID",
        MIX_UBL_ALL_UBL_FE_LOC_2021832003: "連接器編碼",
        MIX_UBL_ALL_UBL_FE_LOC_2021832004: "連接適配器編碼",
        MIX_UBL_ALL_UBL_FE_LOC_2021832005: "連接適配器名稱",
        MIX_UBL_ALL_UBL_FE_LOC_2021832006: "連接編碼",
        MIX_UBL_ALL_UBL_FE_LOC_2021832007: "連接名稱",
        MIX_UBL_ALL_UBL_FE_LOC_2021832008: "連接適配器狀態",
        MIX_UBL_ALL_UBL_FE_LOC_2021832009: "連接適配器版本",
        MIX_UBL_ALL_UBL_FE_LOC_2021832010: "更新適配器",
        MIX_UBL_ALL_UBL_FE_LOC_2021832011: "卸載適配器",
        MIX_UBL_ALL_UBL_FE_LOC_2021832012: "網關健康檢查",
        MIX_UBL_ALL_UBL_FE_LOC_2021832013: "名稱",
        MIX_UBL_ALL_UBL_FE_LOC_2021832014: "網關健康檢查成功",
        MIX_UBL_ALL_UBL_FE_LOC_2021832015: "默認",
        MIX_UBL_ALL_UBL_FE_LOC_2021832016: "已啟動",
        MIX_UBL_ALL_UBL_FE_LOC_2021832017: "已離線",
        MIX_UBL_ALL_UBL_FE_LOC_2021832018: "確定刪除此網關？",
        MIX_UBL_ALL_UBL_FE_LOC_2021832019: "下載密鑰",
        MIX_UBL_ALL_UBL_FE_LOC_2021832020: "刪除",
        MIX_UBL_ALL_UBL_FE_LOC_2021832021: "請輸入",
        MIX_UBL_ALL_UBL_FE_LOC_2021832022: "請輸入正確的ip",
        MIX_UBL_ALL_UBL_FE_LOC_2021832023: "編輯網關",
        MIX_UBL_ALL_UBL_FE_LOC_2021832024: "請輸入網關編碼",
        MIX_UBL_ALL_UBL_FE_LOC_2021832025: "網關編碼由英文、數字、下劃線組成",
        MIX_UBL_ALL_UBL_FE_LOC_2021832026: "請輸入網關名稱",
        MIX_UBL_ALL_UBL_FE_LOC_20218231446: "重置網關狀態",
        MIX_UBL_ALL_UBL_FE_LOC_20221191127: "操作成功",

        MIX_UBL_ALL_UBL_FE_LOC_20225161702: "主",
        MIX_UBL_ALL_UBL_FE_LOC_202205311132: "編碼",
        MIX_UBL_ALL_UBL_FE_LOC_202205311133: "描述",
        MIX_UBL_ALL_UBL_FE_LOC_202205311134: "網關服務器IP",
        MIX_UBL_ALL_UBL_FE_LOC_202205311135: "出網端口號",

        MIX_UBL_ALL_UBL_FE_LOC_202206061427: "安裝說明",
        MIX_UBL_ALL_UBL_FE_LOC_202206061428: "按照下面步驟完成網關創建、客戶端下載及安裝",
        MIX_UBL_ALL_UBL_FE_LOC_202206061429: "第一步：下载網關客户端",
        MIX_UBL_ALL_UBL_FE_LOC_202206061430: "1. 找一台7x24小時都能訪問您企業內部系統，且能訪問互聯網的機器，安裝網關客户端。",
        MIX_UBL_ALL_UBL_FE_LOC_202206061431: "2.下載完成後解壓，需保證解壓後的文件目錄不存在中文字符。",
        MIX_UBL_ALL_UBL_FE_LOC_202206061432: "供4種主流操作系統版本。即：Windows x64、Windows x86、Linux x64、Linux x86",
        MIX_UBL_ALL_UBL_FE_LOC_202206061433: "第二步：下載密鑰",
        MIX_UBL_ALL_UBL_FE_LOC_202206061434: "請將下載的密鑰解壓至網關客户端解壓目錄的config文件夾下",
        MIX_UBL_ALL_UBL_FE_LOC_202206061435: "第三步：啟動網關客户端（本地）",
        MIX_UBL_ALL_UBL_FE_LOC_202206061436: "在網關客戶端解壓目錄的bin文件夾，運行startup.bat",
        MIX_UBL_ALL_UBL_FE_LOC_202206061437: "Linux環境：在網關客戶端解壓目錄的bin文件夾，運行startup.sh",
        MIX_UBL_ALL_UBL_FE_LOC_202206061438: "第四步：測試連接",
        MIX_UBL_ALL_UBL_FE_LOC_202206061439: "在線",
        MIX_UBL_ALL_UBL_FE_LOC_202206061440: "離線",
        MIX_UBL_ALL_UBL_FE_LOC_202206061441: "編輯",
        MIX_UBL_ALL_UBL_FE_LOC_202206061442: "混合云網關",
        MIX_UBL_ALL_UBL_FE_LOC_202206061443: "通過YonLinker實現與內網服務集成的代理系統。",
        MIX_UBL_ALL_UBL_FE_LOC_202206061444: "如何使用",
        MIX_UBL_ALL_UBL_FE_LOC_202206061445: "混合云網關由 Server 和 Client 構成：",
        MIX_UBL_ALL_UBL_FE_LOC_202206061446: "1、Server: 支持網關管理、客户端配置、連接器部署等功能操作。",
        MIX_UBL_ALL_UBL_FE_LOC_202206061447: "2、Client: 实现YonLinker与用户内网服务的交互。",
        MIX_UBL_ALL_UBL_FE_LOC_202206061448: "連接器",
        MIX_UBL_ALL_UBL_FE_LOC_202206061449: "下載終端",
        MIX_UBL_ALL_UBL_FE_LOC_202206061450: "狀態刷新",
        MIX_UBL_ALL_UBL_FE_LOC_202206061451: "網關名稱",
        MIX_UBL_ALL_UBL_FE_LOC_202206061452: "創建人",
        MIX_UBL_ALL_UBL_FE_LOC_202206061453: "創建時間",
        MIX_UBL_ALL_UBL_FE_LOC_202206061454: "最後修改人",
        MIX_UBL_ALL_UBL_FE_LOC_202206061455: "最後修改時間",
        MIX_UBL_ALL_UBL_FE_LOC_202206061456: "Client版本",
        MIX_UBL_ALL_UBL_FE_LOC_202206061457: "IP白名單",
        MIX_UBL_ALL_UBL_FE_LOC_202206061458: "內網訪問IP",
        MIX_UBL_ALL_UBL_FE_LOC_202206061459: "內網訪問端口",
        MIX_UBL_ALL_UBL_FE_LOC_202206061460: "下載客户端",
        MIX_UBL_ALL_UBL_FE_LOC_202206061461: "按照步驟完成網關創建、客戶端下載及安裝",
        MIX_UBL_ALL_UBL_FE_LOC_202206061462: "基本信息",
        MIX_UBL_ALL_UBL_FE_LOC_202206061463: "客户端連接信息",
        MIX_UBL_ALL_UBL_FE_LOC_202206061464: "添加",
        MIX_UBL_ALL_UBL_FE_LOC_202206061465: "高度封裝的系統交互協議/業務系統；分為通用型、業務型。",
        MIX_UBL_ALL_UBL_FE_LOC_202206061466: "連接器部署到對應的混合云網關上，即可連接到內網特定的業務系統和中間件。",
        MIX_UBL_ALL_UBL_FE_LOC_202206061467: "序號",
        MIX_UBL_ALL_UBL_FE_LOC_202206061468: "連接器名稱",
        MIX_UBL_ALL_UBL_FE_LOC_202206061469: "連接產品",
        MIX_UBL_ALL_UBL_FE_LOC_202206061470: "產品歸屬廠商",
        MIX_UBL_ALL_UBL_FE_LOC_202206061471: "連接配置數量",
        MIX_UBL_ALL_UBL_FE_LOC_202206061472: "卸載",
        MIX_UBL_ALL_UBL_FE_LOC_202206061473: "添加連接器",
        MIX_UBL_ALL_UBL_FE_LOC_202206061474: "暫無",
        MIX_UBL_ALL_UBL_FE_LOC_202206061475: "請搜索",
        MIX_UBL_ALL_UBL_FE_LOC_202206061476: "確定卸載【<%= name %>】嗎？卸載後內網將無法使用，請謹慎操作。",
        MIX_UBL_ALL_UBL_FE_LOC_202206061477: "獲取",
        MIX_UBL_ALL_UBL_FE_LOC_202206061478: "基本信息保存成功",
        MIX_UBL_ALL_UBL_FE_LOC_202206061479: "客户端連接信息保存成功",
        MIX_UBL_ALL_UBL_FE_LOC_202206061480: "部署",
        MIX_UBL_ALL_UBL_FE_LOC_202206061481: "正在部署，暫不支持操作",
        MIX_UBL_ALL_UBL_FE_LOC_202206061482: "請輸入正確的端口號",
        MIX_UBL_ALL_UBL_FE_LOC_202206061483: "請錄入允許訪問Client端的IP，英文逗號分隔",
        MIX_UBL_ALL_UBL_FE_LOC_202206061484: "公有云server和Client端連接方式",
        MIX_UBL_ALL_UBL_FE_LOC_202206061485: "請輸入完整的外網可訪問的網關地址，包含http(s)://",
        MIX_UBL_ALL_UBL_FE_LOC_202206061486: "Client直連地址",
        MIX_UBL_ALL_UBL_FE_LOC_202206061487: "Client直連端口",
        MIX_UBL_ALL_UBL_FE_LOC_202206061488:
            "若公有云Server端和Client端通過HTTP直連，則需要錄入【Client直連地址】，此地址必須是外網可訪問的，且是包含http(s)://的完整地址，例如：例如：http://***********:8888，可以是nginx或者Client端所在服務器的外網地址",
        MIX_UBL_ALL_UBL_FE_LOC_202206061489:
            "Client端啟動時會分配一個用於被公有云Server端連接的端口號，若通過nginx直連Client端，需要在nginx上增加跳轉到此端口的配置",
        MIX_UBL_ALL_UBL_FE_LOC_202206061490:
            "Client端提供接口可以讓客戶內部系統通過Client端訪問公有云服務，此接口的IP和端口就是設置的【內網訪問IP】和【內網訪問端口】",
        MIX_UBL_ALL_UBL_FE_LOC_202206061491: "確定刪除該網關嗎",
        MIX_UBL_ALL_UBL_FE_LOC_202206061492: "WEBSOCKET長連接",
        MIX_UBL_ALL_UBL_FE_LOC_202206061493: "HTTP直連",

        MIX_UBL_ALL_UBL_FE_LOC_202208221359:
            "WEBSOCKET長連接，指的是客戶端啟動的時候會向公有云Server端發起連接請求，連接上後此通道將長時間生效，公有云Server端和客戶端可直接通過此通道進行通信，無需實時建立HTTP連接。此方式安全性更高，只需客戶端所在服務器開通訪問公有云Server端的網絡權限即可，無需開通外網訪問客戶端所在服務器的網絡權限",
        MIX_UBL_ALL_UBL_FE_LOC_202208221360: "HTTP直連：需要開通外網訪問客戶端所在服務器的權限，公有云和客戶端的訪問請求需要實時通過HTTP協議進行連接",
        MIX_UBL_ALL_UBL_FE_LOC_202208221361:
            "若公有云Server端和Client端通過HTTP直連，則需要錄入【Client直連地址】，此地址必須是外網可訪問的，且是包含http(s)://的完整地址，例如：http://***********:8888，可以是nginx或者Client端所在服務器的外網地址",
        MIX_UBL_ALL_UBL_FE_LOC_202208221362: "Nginx訪問地址",
        MIX_UBL_ALL_UBL_FE_LOC_202208221363:
            "集群部署模式需要提前安裝nginx，由nginx負責路由轉發Client端。步驟如下：1.下載和解壓nginx到服務器，請根據nginx安裝服務器選擇下載linux版或windows版。下載地址：http://nginx.org/en/download.html 2.在【網關詳情頁】維護網關client端實例，並下載nginx配置文件 3.將nginx配置文件覆蓋到nginx服務器上（nginx安裝目錄/conf）4.啓動nginx",

        MIX_UBL_ALL_UBL_FE_LOC_202208311350: "部署模式",
        MIX_UBL_ALL_UBL_FE_LOC_202208311351: "單壹部署",
        MIX_UBL_ALL_UBL_FE_LOC_202208311352: "集群部署",
        MIX_UBL_ALL_UBL_FE_LOC_202208311353: "服務器IP",
        MIX_UBL_ALL_UBL_FE_LOC_202208311354: "換行輸入多個IP",
        MIX_UBL_ALL_UBL_FE_LOC_202208311355: "集群部署模式需要提前安裝nginx，由nginx負責路由轉發Client端。步驟如下：",
        MIX_UBL_ALL_UBL_FE_LOC_202208311356: "1.下載和解壓nginx到服務器，請根據nginx安裝服務器選擇下載linux版或windows版。",
        MIX_UBL_ALL_UBL_FE_LOC_202208311357: "下載地址：http://nginx.org/en/download.html",
        MIX_UBL_ALL_UBL_FE_LOC_202208311358: "2.在【網關詳情頁】維護網關client端實例，並下載nginx配置文件",
        MIX_UBL_ALL_UBL_FE_LOC_202208311359: "3.將nginx配置文件覆蓋到nginx服務器上（nginx安裝目錄/conf）",
        MIX_UBL_ALL_UBL_FE_LOC_202208311360: "4.啓動nginx",
        MIX_UBL_ALL_UBL_FE_LOC_202208311361: "基礎配置",
        MIX_UBL_ALL_UBL_FE_LOC_202208311362: "保存",
        MIX_UBL_ALL_UBL_FE_LOC_202208311363: "http直連端口",
        MIX_UBL_ALL_UBL_FE_LOC_202208311364: "保存成功",
        MIX_UBL_ALL_UBL_FE_LOC_202208311365: "刷新",
        MIX_UBL_ALL_UBL_FE_LOC_202208311366: "實例IP",
        MIX_UBL_ALL_UBL_FE_LOC_202208311367: "實例端口號",
        MIX_UBL_ALL_UBL_FE_LOC_202208311368: "狀態",
        MIX_UBL_ALL_UBL_FE_LOC_202208311369: "運行實例",
        MIX_UBL_ALL_UBL_FE_LOC_202208311370: "獲取實例",
        MIX_UBL_ALL_UBL_FE_LOC_202208311371: "下載nginx配置文件",
        MIX_UBL_ALL_UBL_FE_LOC_202208311372: "獲取ip",
        MIX_UBL_ALL_UBL_FE_LOC_202208311373: "高級設置",

        MIX_UBL_ALL_UBL_FE_LOC_202209151728: "集群",
        MIX_UBL_ALL_UBL_FE_LOC_202209151729: "網關出網IP",
        MIX_UBL_ALL_UBL_FE_LOC_202209151730: "集群部署：支持下載多個客戶端安裝至同壹/多個服務器",
        MIX_UBL_ALL_UBL_FE_LOC_202209151731: "集群部署：請使用同壹網關內的密鑰文件",
        MIX_UBL_ALL_UBL_FE_LOC_202209151732: "集群部署：請先添加客戶端所在服務器IP，再啓動網關客戶端",
        MIX_UBL_ALL_UBL_FE_LOC_202209151733: "第五步：下載和啓動nginx（集群部署時需要）",
        MIX_UBL_ALL_UBL_FE_LOC_202209151734: "集群部署需提前安裝nginx（由nginx負責路由轉發網關客戶端）步驟如下：",
        MIX_UBL_ALL_UBL_FE_LOC_202209151735:
            "1、下載和解壓nginx到服務器，請根據nginx安裝服務器選擇下載linux版或windows版。下載地址:http://nginx.org/en/download.html",
        MIX_UBL_ALL_UBL_FE_LOC_202209151736: "2、在【網關詳情頁】維護網關客戶端實例，並下載nginx配置文件",
        MIX_UBL_ALL_UBL_FE_LOC_202209151737: "3、將nginx配置文件覆蓋到nginx服務器上（nginx安裝目錄/conf）",
        MIX_UBL_ALL_UBL_FE_LOC_202209151738: "4、啓動nginx",

        MIX_UBL_ALL_UBL_FE_LOC_202209221654: "僅安裝一個客戶端",
        MIX_UBL_ALL_UBL_FE_LOC_202209221655: "根據需要安裝多個客戶端組成一個集群。具體集群部署的安裝方式詳見網關首頁右上角的【安裝說明】",
        MIX_UBL_ALL_UBL_FE_LOC_202209221656:
            "對於集群部署的客戶端，需要前置安裝一個nginx進行路由跳轉。若是長連接方式，則【nginx訪問地址】輸入集群nginx所在內網地址即可；如果是直連方式，客戶可以直接將集群nginx開通外網訪問權限，輸入集群nginx外網可訪問的地址，客戶還可以將集群nginx放在內網，單獨安裝一個外網可以訪問的nginx，此nginx上配置跳轉到集群nginx上，【nginx訪問地址】輸入此外網nginx即可。訪問地址必須是包含http(s)://的完整地址，例如：http://***********:8888",

        MIX_UBL_ALL_UBL_FE_LOC_202211221020: "連接方式",
        MIX_UBL_ALL_UBL_FE_LOC_202211221021: "適配器",
    },
    enus: {
        MIX_UBL_ALL_UBL_FE_LOC_2021831946: "Download gateway",
        MIX_UBL_ALL_UBL_FE_LOC_2021831947: "New gateway",
        MIX_UBL_ALL_UBL_FE_LOC_2021831948: "Gateway ID",
        MIX_UBL_ALL_UBL_FE_LOC_2021831949: "Gateway code",
        MIX_UBL_ALL_UBL_FE_LOC_2021831950: "Connector deployment",
        MIX_UBL_ALL_UBL_FE_LOC_2021831951: "Set as default",
        MIX_UBL_ALL_UBL_FE_LOC_2021831952: "Edit",
        MIX_UBL_ALL_UBL_FE_LOC_2021831953: "Please select an IP address",
        MIX_UBL_ALL_UBL_FE_LOC_2021831954: "Number",
        MIX_UBL_ALL_UBL_FE_LOC_2021831955: "IP address",
        MIX_UBL_ALL_UBL_FE_LOC_2021831956: "Cancel",
        MIX_UBL_ALL_UBL_FE_LOC_2021831957: "Confirm",
        MIX_UBL_ALL_UBL_FE_LOC_2021831958: "Connector configuration",
        MIX_UBL_ALL_UBL_FE_LOC_2021831959: "Gateway server IP",
        MIX_UBL_ALL_UBL_FE_LOC_2021831960: "Get IP address",
        MIX_UBL_ALL_UBL_FE_LOC_2021832000: "White list",
        MIX_UBL_ALL_UBL_FE_LOC_2021832001: "Outgoing port number",
        MIX_UBL_ALL_UBL_FE_LOC_2021832002: "Connector instance ID",
        MIX_UBL_ALL_UBL_FE_LOC_2021832003: "Connector code",
        MIX_UBL_ALL_UBL_FE_LOC_2021832004: "Connection adapter code",
        MIX_UBL_ALL_UBL_FE_LOC_2021832005: "Connection adapter name",
        MIX_UBL_ALL_UBL_FE_LOC_2021832006: "Connection code",
        MIX_UBL_ALL_UBL_FE_LOC_2021832007: "Connection name",
        MIX_UBL_ALL_UBL_FE_LOC_2021832008: "Connection adapter status",
        MIX_UBL_ALL_UBL_FE_LOC_2021832009: "Connection adapter version",
        MIX_UBL_ALL_UBL_FE_LOC_2021832010: "Update adapter",
        MIX_UBL_ALL_UBL_FE_LOC_2021832011: "Uninstall adapter",
        MIX_UBL_ALL_UBL_FE_LOC_2021832012: "Gateway health check",
        MIX_UBL_ALL_UBL_FE_LOC_2021832013: "Name",
        MIX_UBL_ALL_UBL_FE_LOC_2021832014: "Gateway health check succeeded",
        MIX_UBL_ALL_UBL_FE_LOC_2021832015: "Default",
        MIX_UBL_ALL_UBL_FE_LOC_2021832016: "Started",
        MIX_UBL_ALL_UBL_FE_LOC_2021832017: "Offline",
        MIX_UBL_ALL_UBL_FE_LOC_2021832018: "Are you sure to delete this gateway?",
        MIX_UBL_ALL_UBL_FE_LOC_2021832019: "Download key",
        MIX_UBL_ALL_UBL_FE_LOC_2021832020: "Delete",
        MIX_UBL_ALL_UBL_FE_LOC_2021832021: "Please enter",
        MIX_UBL_ALL_UBL_FE_LOC_2021832022: "Please enter the correct IP address",
        MIX_UBL_ALL_UBL_FE_LOC_2021832023: "Edit gateway",
        MIX_UBL_ALL_UBL_FE_LOC_2021832024: "Please enter the gateway code",
        MIX_UBL_ALL_UBL_FE_LOC_2021832025: "The gateway code consists of English, numbers and underscores",
        MIX_UBL_ALL_UBL_FE_LOC_2021832026: "Please enter the gateway name",
        MIX_UBL_ALL_UBL_FE_LOC_20218231446: "Reset gateway status",
        MIX_UBL_ALL_UBL_FE_LOC_20221191127: "Operation successful",

        MIX_UBL_ALL_UBL_FE_LOC_20225161702: "Main",
        MIX_UBL_ALL_UBL_FE_LOC_202205311132: "Coding",
        MIX_UBL_ALL_UBL_FE_LOC_202205311133: "Describe",
        MIX_UBL_ALL_UBL_FE_LOC_202205311134: "Gateway server IP",
        MIX_UBL_ALL_UBL_FE_LOC_202205311135: "Outgoing port number",

        MIX_UBL_ALL_UBL_FE_LOC_202206061427: "Installation Notes",
        MIX_UBL_ALL_UBL_FE_LOC_202206061428: "Follow the steps below to complete gateway creation, client download and installation",
        MIX_UBL_ALL_UBL_FE_LOC_202206061429: "Step 1: Download the Client",
        MIX_UBL_ALL_UBL_FE_LOC_202206061430:
            "1. Find a machine that can access your internal system and the Internet 7x24 hours a day, and install the client.",
        MIX_UBL_ALL_UBL_FE_LOC_202206061431:
            "2. After the download is complete, decompress it, and make sure that there are no Chinese characters in the decompressed file directory.",
        MIX_UBL_ALL_UBL_FE_LOC_202206061432: "For 4 mainstream operating system versions. ie: Windows x64, Windows x86, Linux x64, Linux x86",
        MIX_UBL_ALL_UBL_FE_LOC_202206061433: "Step 2: Download the key",
        MIX_UBL_ALL_UBL_FE_LOC_202206061434: "Please unzip the downloaded key to the config folder of the unzip directory on the client side",
        MIX_UBL_ALL_UBL_FE_LOC_202206061435: "Step 3: Start the Client (local)",
        MIX_UBL_ALL_UBL_FE_LOC_202206061436: "In the bin folder of the decompressed directory of the gateway client, run startup.bat",
        MIX_UBL_ALL_UBL_FE_LOC_202206061437: "Linux environment: run startup.sh in the bin folder of the decompressed directory on the gateway client",
        MIX_UBL_ALL_UBL_FE_LOC_202206061438: "Step 4: Test the connection",
        MIX_UBL_ALL_UBL_FE_LOC_202206061439: "Online",
        MIX_UBL_ALL_UBL_FE_LOC_202206061440: "Offline",
        MIX_UBL_ALL_UBL_FE_LOC_202206061441: "Edit",
        MIX_UBL_ALL_UBL_FE_LOC_202206061442: "Hybrid Cloud Gateway",
        MIX_UBL_ALL_UBL_FE_LOC_202206061443: "A proxy system integrated with intranet services through YonLinker.",
        MIX_UBL_ALL_UBL_FE_LOC_202206061444: "How to use",
        MIX_UBL_ALL_UBL_FE_LOC_202206061445: "The hybrid cloud gateway consists of Server and Client:",
        MIX_UBL_ALL_UBL_FE_LOC_202206061446:
            "1. Server: supports gateway management, client configuration, connector deployment and other functional operations.",
        MIX_UBL_ALL_UBL_FE_LOC_202206061447: `2. Client: Realize the interaction between YonLinker and the user's intranet service.`,
        MIX_UBL_ALL_UBL_FE_LOC_202206061448: "Connector",
        MIX_UBL_ALL_UBL_FE_LOC_202206061449: "Download terminal",
        MIX_UBL_ALL_UBL_FE_LOC_202206061450: "Status refresh",
        MIX_UBL_ALL_UBL_FE_LOC_202206061451: "Gateway name",
        MIX_UBL_ALL_UBL_FE_LOC_202206061452: "Founder",
        MIX_UBL_ALL_UBL_FE_LOC_202206061453: "Creation time",
        MIX_UBL_ALL_UBL_FE_LOC_202206061454: "Last modified by",
        MIX_UBL_ALL_UBL_FE_LOC_202206061455: "Last Modified",
        MIX_UBL_ALL_UBL_FE_LOC_202206061456: "Client version",
        MIX_UBL_ALL_UBL_FE_LOC_202206061457: "IP whitelist",
        MIX_UBL_ALL_UBL_FE_LOC_202206061458: "Intranet access IP",
        MIX_UBL_ALL_UBL_FE_LOC_202206061459: "Intranet access port",
        MIX_UBL_ALL_UBL_FE_LOC_202206061460: "Download Client",
        MIX_UBL_ALL_UBL_FE_LOC_202206061461: "Follow the steps to complete gateway creation, client download and installation",
        MIX_UBL_ALL_UBL_FE_LOC_202206061462: "Basic Information",
        MIX_UBL_ALL_UBL_FE_LOC_202206061463: "Client connection information",
        MIX_UBL_ALL_UBL_FE_LOC_202206061464: "Add to",
        MIX_UBL_ALL_UBL_FE_LOC_202206061465: "Highly encapsulated system interaction protocol/business system; divided into general type and business type.",
        MIX_UBL_ALL_UBL_FE_LOC_202206061466:
            "The connector is deployed on the corresponding hybrid cloud gateway to connect to the specific business system and middleware of the intranet.",
        MIX_UBL_ALL_UBL_FE_LOC_202206061467: "Serial number",
        MIX_UBL_ALL_UBL_FE_LOC_202206061468: "Connector name",
        MIX_UBL_ALL_UBL_FE_LOC_202206061469: "Connected Products",
        MIX_UBL_ALL_UBL_FE_LOC_202206061470: "The product belongs to the manufacturer",
        MIX_UBL_ALL_UBL_FE_LOC_202206061471: "Number of connection configurations",
        MIX_UBL_ALL_UBL_FE_LOC_202206061472: "Uninstall",
        MIX_UBL_ALL_UBL_FE_LOC_202206061473: "Add connector",
        MIX_UBL_ALL_UBL_FE_LOC_202206061474: "Temporarily no",
        MIX_UBL_ALL_UBL_FE_LOC_202206061475: "please search",
        MIX_UBL_ALL_UBL_FE_LOC_202206061476:
            "Are you sure you want to uninstall [<%= name %>]? After uninstalling, the intranet will not be available, please operate with caution.",
        MIX_UBL_ALL_UBL_FE_LOC_202206061477: "Obtain",
        MIX_UBL_ALL_UBL_FE_LOC_202206061478: "Basic information saved successfully",
        MIX_UBL_ALL_UBL_FE_LOC_202206061479: "Client connection information saved successfully",
        MIX_UBL_ALL_UBL_FE_LOC_202206061480: "deploy",
        MIX_UBL_ALL_UBL_FE_LOC_202206061481: "Deploying, operation not currently supported",
        MIX_UBL_ALL_UBL_FE_LOC_202206061482: "Please enter the correct port number",
        MIX_UBL_ALL_UBL_FE_LOC_202206061483: "Please enter the IP that is allowed to access the client, separated by commas",
        MIX_UBL_ALL_UBL_FE_LOC_202206061484: "Public cloud server and client connection method",
        MIX_UBL_ALL_UBL_FE_LOC_202206061485: "Please enter the complete gateway address accessible from the external network, including http(s)://",
        MIX_UBL_ALL_UBL_FE_LOC_202206061486: "Client direct connection address",
        MIX_UBL_ALL_UBL_FE_LOC_202206061487: "Client direct connection port",
        MIX_UBL_ALL_UBL_FE_LOC_202206061488:
            "If the public cloud server and client are directly connected through HTTP, you need to enter [Client direct connection address], this address must be accessible from the external network, and it must be a complete address including http(s)://, for example: For example: : http://***********:8888, which can be the external network address of nginx or the server where the client is located",
        MIX_UBL_ALL_UBL_FE_LOC_202206061489:
            "When the client is started, a port number for connecting to the public cloud server will be assigned. If the client is directly connected to the client through nginx, the configuration of jumping to this port needs to be added on nginx.",
        MIX_UBL_ALL_UBL_FE_LOC_202206061490: `The client terminal provides an interface to allow the customer's internal system to access public cloud services through the client terminal. The IP and port of this interface are the set [Intranet Access IP] and [Intranet Access Port]`,
        MIX_UBL_ALL_UBL_FE_LOC_202206061491: `Are you sure you want to delete this gateway`,
        MIX_UBL_ALL_UBL_FE_LOC_202206061492: `WEBSOCKET long connection`,
        MIX_UBL_ALL_UBL_FE_LOC_202206061493: `HTTP direct connection`,

        MIX_UBL_ALL_UBL_FE_LOC_202208221359: `WEBSOCKET long connection means that when the client starts, it will initiate a connection request to the public cloud server. After the connection is made, this channel will take effect for a long time. The public cloud server and the client can communicate directly through this channel without real-time establishment. HTTP connection. This method is more secure, as long as the server where the client is located has the network permission to access the public cloud server, there is no need to enable the network permission of the server where the client is located from the external network`,
        MIX_UBL_ALL_UBL_FE_LOC_202208221360: `HTTP direct connection: The permission to access the server where the client is located on the external network needs to be opened, and the access request of the public cloud and the client needs to be connected through the HTTP protocol in real time`,
        MIX_UBL_ALL_UBL_FE_LOC_202208221361: `If the public cloud server and client are directly connected through HTTP, you need to enter the [Client direct connection address], this address must be accessible from the external network, and it must be a complete address including http(s)://, for example: http ://***********:8888, which can be the external network address of nginx or the server where the client is located`,
        MIX_UBL_ALL_UBL_FE_LOC_202208221362: `Nginx access address`,
        MIX_UBL_ALL_UBL_FE_LOC_202208221363: `In the cluster deployment mode, nginx needs to be installed in advance, and nginx is responsible for routing and forwarding the client. Proceed as follows:

    1. Download and decompress nginx to the server, please choose to download the linux version or the windows version according to the nginx installation server. Download address: http://nginx.org/en/download.html
    2. Maintain the gateway client instance on the [Gateway Details Page] and download the nginx configuration file
    3. Overwrite the nginx configuration file on the nginx server (nginx installation directory/conf)
    4. Start nginx`,

        MIX_UBL_ALL_UBL_FE_LOC_202208311350: "Deployment mode",
        MIX_UBL_ALL_UBL_FE_LOC_202208311351: "Single deployment",
        MIX_UBL_ALL_UBL_FE_LOC_202208311352: "Cluster deployment",
        MIX_UBL_ALL_UBL_FE_LOC_202208311353: "Server IP",
        MIX_UBL_ALL_UBL_FE_LOC_202208311354: "Line feed: enter multiple IPS",
        MIX_UBL_ALL_UBL_FE_LOC_202208311355:
            "In the cluster deployment mode, nginx needs to be installed in advance, and nginx is responsible for routing and forwarding the client side. The steps are as follows:",
        MIX_UBL_ALL_UBL_FE_LOC_202208311356:
            "1. Download and unzip nginx to the server. Please choose to download Linux version or Windows version according to the nginx installation server.",
        MIX_UBL_ALL_UBL_FE_LOC_202208311357: "Next page: http://nginx.org/en/download.html",
        MIX_UBL_ALL_UBL_FE_LOC_202208311358: "2. Maintain the gateway client instance on the [gateway details page] and download the nginx configuration file",
        MIX_UBL_ALL_UBL_FE_LOC_202208311359: "3. Overwrite the nginx configuration file on the nginx server (nginx installation directory / CONF)",
        MIX_UBL_ALL_UBL_FE_LOC_202208311360: "4. Start nginx",
        MIX_UBL_ALL_UBL_FE_LOC_202208311361: "Basic configuration",
        MIX_UBL_ALL_UBL_FE_LOC_202208311362: "Save",
        MIX_UBL_ALL_UBL_FE_LOC_202208311363: "HTTP direct port",
        MIX_UBL_ALL_UBL_FE_LOC_202208311364: "Saved successfully",
        MIX_UBL_ALL_UBL_FE_LOC_202208311365: "Refresh",
        MIX_UBL_ALL_UBL_FE_LOC_202208311366: "Instance IP",
        MIX_UBL_ALL_UBL_FE_LOC_202208311367: "Instance port number",
        MIX_UBL_ALL_UBL_FE_LOC_202208311368: "State",
        MIX_UBL_ALL_UBL_FE_LOC_202208311369: "Running instance",
        MIX_UBL_ALL_UBL_FE_LOC_202208311370: "Get instance",
        MIX_UBL_ALL_UBL_FE_LOC_202208311371: "Download nginx configuration file",
        MIX_UBL_ALL_UBL_FE_LOC_202208311372: "Get IP",
        MIX_UBL_ALL_UBL_FE_LOC_202208311373: "Advanced setting",

        MIX_UBL_ALL_UBL_FE_LOC_202209151728: "Colony",
        MIX_UBL_ALL_UBL_FE_LOC_202209151729: "Gateway outbound IP",
        MIX_UBL_ALL_UBL_FE_LOC_202209151730: "Cluster deployment: supports downloading multiple clients and installing them to the same/multiple servers",
        MIX_UBL_ALL_UBL_FE_LOC_202209151731: "Cluster deployment: Please use the key file in the same gateway",
        MIX_UBL_ALL_UBL_FE_LOC_202209151732: "Cluster deployment: Please add the server IP of the client first, and then start the gateway client",
        MIX_UBL_ALL_UBL_FE_LOC_202209151733: "Step 5: Download and start nginx (required for cluster deployment)",
        MIX_UBL_ALL_UBL_FE_LOC_202209151734:
            "The cluster deployment needs to install nginx in advance (nginx is responsible for the routing and forwarding gateway client). The steps are as follows:",
        MIX_UBL_ALL_UBL_FE_LOC_202209151735:
            "1. Download and unzip nginx to the server. Please choose to download Linux version or Windows version according to nginx installation server. Download address:http://nginx.org/en/download.html",
        MIX_UBL_ALL_UBL_FE_LOC_202209151736: "2. Maintain the gateway client instance on the [Gateway Details Page] and download the nginx configuration file",
        MIX_UBL_ALL_UBL_FE_LOC_202209151737: "3. Overwrite the nginx configuration file to the nginx server (nginx installation directory/conf）",
        MIX_UBL_ALL_UBL_FE_LOC_202209151738: "4. Start nginx",

        MIX_UBL_ALL_UBL_FE_LOC_202209221654: "Install only one client",
        MIX_UBL_ALL_UBL_FE_LOC_202209221655:
            "Install multiple clients as needed to form a cluster. Please refer to the [Installation Instructions] at the top right corner of the gateway home page for the specific cluster deployment installation method",
        MIX_UBL_ALL_UBL_FE_LOC_202209221656:
            "For cluster deployed clients, you need to pre install a nginx for routing jump. For the long connection mode, enter the intranet address of the cluster nginx in the [nginx Access Address]; If it is a direct connection mode, the customer can directly open the external network access permission of the cluster nginx, and enter the address accessible to the external network of the cluster nginx. The customer can also place the cluster nginx on the internal network and install a nginx that can be accessed by the external network separately. The configuration on this nginx jumps to the cluster nginx, and [nginx access address] can be entered into this external network nginx. The access address must be a complete address containing http (s)://, for example: http://***********:8888",

        MIX_UBL_ALL_UBL_FE_LOC_202211221020: "Connection mode",
        MIX_UBL_ALL_UBL_FE_LOC_202211221021: "Adapter",
    },
};
