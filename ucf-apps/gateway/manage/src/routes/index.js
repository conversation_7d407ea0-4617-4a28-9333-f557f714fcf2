import React from "react";
import { RoutesRender } from "core";

import ListRoute from "./list";
// import ConnectorDeploy from './connector-deploy';

import getConfigProvider from "components/ConfigProvider";
import config from "../config";
const ConfigProvider = getConfigProvider(config);

const routes = [ListRoute];

const Routes = () => {
    return (
        <ConfigProvider fieldid="UCG-FE-gateway-manage-src-routes-index-8350205-ConfigProvider">
            <RoutesRender routes={routes} />
        </ConfigProvider>
    );
};
export default Routes;
