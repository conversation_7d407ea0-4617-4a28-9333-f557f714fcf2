import { action, observable, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { defaultListMap } from "utils/pageListUtils";
import { autoServiceMessage } from "utils/service";
import * as ownerService from "./service";
import { a_download } from "utils/index";
import { createLoading } from "utils/feedback";

const initState = {
    dataSource: [],
    // 适配器列表
    connectorList: { ...defaultListMap },
    pagination: null,
    // 选中的列表
    selectedConnectList: [],
    // 选中网关
    selectedGateway: undefined,
    connectsByType: undefined,
    configName: "-",
    navLinks: [],
    questionShow: false,
    gatewayConnectorsNum: [],
    serviceCodeDiwork: "kflj_wggl",
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;
    @observable searchInfo = {}; // 调整为后端查询;

    @action
    resetState = () => {
        this.state = initState;
        this.searchInfo = {};
    };
    @action
    setSearchInfo = (data) => {
        this.searchInfo = { ...this.searchInfo, ...data };
    };
    init = () => {
        this.state = initState;
    };
    setGateway = (data) => {
        this.changeState({
            selectedGateway: data,
        });
    };

    getDataSource = async () => {
        let _loading = createLoading();
        let [dataSource, B] = await Promise.all([this.getGatewayList(this.searchInfo), this.getGatewayConnectors()]);
        dataSource = dataSource?.map((aItem) => {
            const bItem = B?.find((bItem) => bItem?.gatewayId === aItem?.gatewayID);
            return { ...aItem, ...bItem };
        });
        if (_loading) {
            _loading.hide();
            _loading = null;
        }
        this.changeState({
            dataSource,
        });
    };
    // 获取网关列表
    getGatewayList = async (params) => {
        let res = await autoServiceMessage({
            service: ownerService.listService({
                order: "asc",
                isAll: true,
                ...params,
            }),
        });
        if (res) {
            return this.handleSortData(res.data || []);
        }
    };
    handleSortData = (list) => {
        const index = list.sort((a, b) => (a.createtime > b.createtime ? -1 : 1)).findIndex((item) => item.isdefault);
        if (index > 0) {
            let first = list.splice(index, 1)[0];
            list.unshift(first);
        }

        return list;
    };
    getGatewayConnectors = async () => {
        let res = await autoServiceMessage({
            service: ownerService.connectCountService(
                {
                    order: "asc",
                    isAll: true,
                },
                { serviceCode: this.state.serviceCodeDiwork }
            ),
        });
        if (res) {
            return res?.data || [];
        }
    };
    formatDataSource = async (gatewayConnectors) => {
        let { dataSource } = this.state;
        let numArr = [];
        for (let i = 0; i < dataSource.length; i++) {
            const item1 = dataSource[i];
            const match = gatewayConnectors.find((item2) => item2.gatewayId === item1.gatewayID);
            if (match) {
                numArr.push(match.num);
            }
        }
        this.changeState({
            gatewayConnectorsNum: numArr,
        });
    };
    // 新建网关
    addGateWay = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.addGateWay(data, { serviceCode: this.state.serviceCodeDiwork }),
        });
        if (res) {
            this.getDataSource();
        }
    };

    // 编辑网关
    editGateWay = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.editGateWay(data, { serviceCode: this.state.serviceCodeDiwork }),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180451", "基本信息保存成功", undefined, {
                returnStr: true,
            }) /* "基本信息保存成功" */,
        });
        if (res) {
            this.setGateway(res.data);
        }
    };

    // 删除网关
    deleteGateWay = async (gatewayId) => {
        let res = await autoServiceMessage({
            service: ownerService.deleteGateWay(gatewayId, { serviceCode: this.state.serviceCodeDiwork }),
        });
        if (res) {
            this.getDataSource();
            this.changeState({
                selectedGateway: undefined,
            });
        }
        return res;
    };

    // 设置默认网关
    setDefaultGateWay = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.setDefaultGateWay(data, { serviceCode: this.state.serviceCodeDiwork }),
        });
        if (res) {
            this.getDataSource();
        }
    };

    // 重置网关
    resetGateWay = async (gatewayId) => {
        let res = await autoServiceMessage({
            service: ownerService.resetGateWay(gatewayId, { serviceCode: this.state.serviceCodeDiwork }),
            // success:'操作成功'
        });
        return res;
    };

    // 获取下载网关url
    getGateWayDownloadUrl = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.getGateWayDownloadUrl(data, { serviceCode: this.state.serviceCodeDiwork }),
        });
        return res;
    };

    // 下载密钥
    downloadSecretKey = async (gatewayId) => {
        ownerService.downloadGatewayKey(gatewayId, { serviceCode: this.state.serviceCodeDiwork });
    };

    // 数据集成-连接配置: 根据分类获取连接列表
    getConnectsByType = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.getConnectsByType(data, { serviceCode: this.state.serviceCodeDiwork }),
        });
        if (res && res.data) {
            this.changeState({
                connectsByType: res.data,
            });
        }
    };
    // 适配器模板: 租户部署(更新/卸载)适配器
    adapterOperation = async (adapterCode, type) => {
        let res = await autoServiceMessage({
            service: ownerService.adapterOperation(
                {
                    adapterId: adapterCode,
                    gatewayId: this.state.selectedGateway.gatewayID,
                    action: type,
                },
                { serviceCode: this.state.serviceCodeDiwork }
            ),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418044F", "操作成功", undefined, {
                returnStr: true,
            }) /* "操作成功" */,
        });

        return res;
    };
    // 适配器表格勾选
    gridSelectFunc = (selectedList) => {
        // let newIdList = selectedList.map((item)=>{ return item.id });
        this.state.selectedConnectList = selectedList;
        // this.changeState({
        //     selectedConnectList: newIdList
        // });
    };
    to = (promise) => {
        return promise
            .then((data) => {
                return [null, data];
            })
            .catch((err) => [err]);
    };
    // 连接器点击确定
    connectCommit = async (gatewayId, gateWayData) => {
        let results = await Promise.all([
            autoServiceMessage({
                service: ownerService.saveGateWayConfig(
                    {
                        gatewayId,
                        ...gateWayData,
                    },
                    { serviceCode: this.state.serviceCodeDiwork }
                ),
            }),
            autoServiceMessage({
                service: ownerService.bindConnect(
                    gatewayId,
                    this.state.selectedConnectList.map((item) => {
                        return item.id;
                    }),
                    { serviceCode: this.state.serviceCodeDiwork }
                ),
            }),
        ]);
    };
    bindConnectors = async (gatewayId, bindIds) => {
        let res = autoServiceMessage({
            service: ownerService.bindConnect(gatewayId, bindIds, { serviceCode: this.state.serviceCodeDiwork }),
        });
        return res;
    };
    // 获取网关配置
    getGateWayConfig = async (gatewayId) => {
        let res = await autoServiceMessage({
            service: ownerService.getGateWayConfig(gatewayId, { serviceCode: this.state.serviceCodeDiwork }),
        });
        return res;
    };

    // 网关健康检查
    healthCheck = async (gatewayId, isCluster) => {
        let res = await autoServiceMessage({
            service: isCluster
                ? ownerService.ClusterhealthCheck({ gatewayId }, { serviceCode: this.state.serviceCodeDiwork })
                : ownerService.healthCheck({ gatewayId }, { serviceCode: this.state.serviceCodeDiwork }),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180450", "网关健康检查成功", undefined, {
                returnStr: true,
            }) /* "网关健康检查成功" */,
        });
        return res;
    };
    setConfigName = (configName) => {
        this.state.configName = configName;
    };

    setNavLinks = (nav) => {
        this.state.navLinks.push(nav);
    };

    setQuestionShow = () => {
        this.state.questionShow = true;
    };
    getLightAppGatewayMsg = async (gatewayId) => {
        let res = await autoServiceMessage({
            service: ownerService.getLightAppGatewayMsgService({ gatewayId }, { serviceCode: this.state.serviceCodeDiwork }),
        });
        if (res) {
            return res.data || {};
        }
    };
    // getLightAppGatewayList = async (gatewayId) => {
    //     let res = await autoServiceMessage({
    //         service: ownerService.getLightAppGatewayListService({gatewayId}, { serviceCode: this.state.serviceCodeDiwork })
    //     });
    //     if(res){

    //         return res.data;
    //     }
    // }

    testLightAppGateway = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.TestLightAppGatewayService(data, { serviceCode: this.state.serviceCodeDiwork }),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180553", "测试连接成功", undefined, {
                returnStr: true,
            }) /* "测试连接成功" */,
        });
    };
    saveLightAppGateway = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.SaveLightAppGatewayService(data, { serviceCode: this.state.serviceCodeDiwork }),
            success: lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F8005A", "保存成功", undefined, {
                returnStr: true,
            }) /* "保存成功" */,
        });
    };
    handleSetDefault = async (connectId) => {
        let res = await autoServiceMessage({
            service: ownerService.setDefaultService({ connectId }, { serviceCode: this.state.serviceCodeDiwork }),
            success: lang.templateByUuid("UID:P_UBL-FE_1982A55A04D00035", "设置成功", undefined, {
                returnStr: true,
            }) /* "设置成功" */,
        });
        if (res) {
            this.getConnectsByType({ gatewayId: this.state.selectedGateway.gatewayID });
        }
        return res;
    };
    handleUpdateConnector = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.updateConnectorService(data, { serviceCode: this.state.serviceCodeDiwork }),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418041E", "更新成功！", undefined, {
                returnStr: true,
            }),
        });
        if (res) {
            // this.getConnectsByType({ gatewayId: this.state.selectedGateway.gatewayID });
        }
        return res;
    };
}
/**
 * 根据具体模块修改 storeKey 为保证唯一性
 * storeKey 由 模块文件夹名称 + 页面名称 + 'Store' 小驼峰
 *
 * storeKey是作为当前 Store 的唯一键， storeKey与Store的实例会在 rootStore或者StoreMap中以 键值对的 形式存在
 * 1. 用于在模块的 storeMap（模块routes/store.js）中注册并初始化当前Store
 * 2. 用于在container的 inject 中关联当前Store的实例时，从模块的storeMap或者rootStore中获取对应store实例
 * */
export const storeKey = "gatewayManageListStore";

export default Store;
