import React, { useEffect, useMemo, useRef, useState, Fragment } from "react";
import { Drawer, Space, Button, Dropdown, Menu, Popconfirm, Tabs, Table, Form, Col, Icon, Input, Row, Modal, Progress, Select } from "@tinper/next-ui";
import AddConnector from "../AddConnector";
import { autoServiceMessage } from "utils/service";
import {
    getGateWayConfig,
    saveGateWayConfig,
    healthCheck,
    ClusterhealthCheck,
    downloadGatewayKey,
    getGateWayDownloadUrl,
    getClientIpList,
    saveEditGateWay,
    downloadNG,
    deleteInstance,
    reloadGatewayAddressService,
    getGatewayAddressService,
} from "../../service";
import Logo from "components/Card/Logo";
import EditContent from "./EditContent";
import onlineSvg from "static/images/gateway/online.svg";
import outlineSvg from "static/images/gateway/outline.svg";
import ConnectorGrid from "./ConnectorGrid";
import GatewayHelp from "../IndexView/GatewayHelp";
import AdaptersView from "../Adapters";
import { getLocalImg, downloadOss } from "utils/index";
import { codeReg, codeMessage, ipReg, portReg, ipWhiteReg, ipsEnterReg } from "utils/regExp";
import "./index.less";
import { observer, inject } from "mobx-react";
import { storeKey } from "../../store";
import { gateWayOssVersion } from "../IndexView";
import Grid from "components/TinperBee/Grid";
const { TabPane } = Tabs;
const GatewayStatus = {
    online: 1,
    offline: 2,
};
export const GatewayBindStatus = {
    bind: 1,
    unbound: 0,
};

const alignCenter = { display: "flex", alignItems: "center" };
const InfoDrawer = (props) => {
    const { ownerStore, ownerState, onClose, locale } = props;
    const { selectedGateway: gateway = {} } = ownerState;
    const [gatewayConfig, setGatewayConfig] = useState(undefined);
    const [ipList, setIpList] = useState([]); // ipList
    const [addShow, setAddShow] = useState(false);
    const [isHttpConnect, setHttpConnect] = useState(gateway.channelProtocol === "http");
    const [isCluster, setCluster] = useState(gateway.isCluster || false);
    const [isEditing, setEditing] = useState(false); // BUG  删除 false
    const [instanceIndex, setInstanceIndex] = useState(-1); // 集群模式运行实例获取ip Modal
    const [selectedInstanceIpRecord, setSelectedInstanceIpRecord] = useState({}); // 选择ip回填表格
    const currentGetIp = useRef(); // 当前表格点击获取所填的ip
    const [instanceForm] = Form.useForm(); // 运行实例表格编辑
    const [serviceCode, setServiceCode] = useState("root");
    const [activeTab, setActiveTab] = useState("root");
    const basicRef = useRef();
    const configRef = useRef();
    const heighRef = useRef();
    const [currentTab, setCurrentTab] = useState("1");
    const adapterViewRef = useRef(null);
    const isFirst = useRef(true); // 是否首次切换tab, 决定是否获取连接器
    let canEdit = useMemo(() => (ownerState.selectedGateway || {}).status !== GatewayStatus.offline, [ownerState.selectedGateway]);
    const [showDownloadBadge, setShowDownloadBadge] = useState(false);

    const [clickedDownloadClient, setClickedDownloadClient] = useState(false);
    const [percent, setPercent] = useState(0);

    const handleDownloadClient = async ({ key }) => {
        setClickedDownloadClient(true);
        let res = await autoServiceMessage({
            service: getGateWayDownloadUrl({
                sysType: gateWayOssVersion[key].sysType,
                bit: gateWayOssVersion[key].bit,
            }),
        });
        if (res) {
            downloadOss(res);
        }
        setClickedDownloadClient(false);
    };
    const menu = (
        <Menu fieldid="ublinker-routes-list-components-InfoDrawer-index-3914176-Menu" onClick={handleDownloadClient}>
            <Menu.Item fieldid="UCG-FE-routes-list-components-InfoDrawer-index-7077761-Menu.Item" key={0}>
                windows x64
            </Menu.Item>
            {/* <Menu.Item fieldid="UCG-FE-routes-list-components-InfoDrawer-index-4051415-Menu.Item" key={1}>windows x86</Menu.Item> */}
            <Menu.Item fieldid="UCG-FE-routes-list-components-InfoDrawer-index-164250-Menu.Item" key={2}>
                linux x64
            </Menu.Item>
            <Menu.Item key={4}>arrch64</Menu.Item>
        </Menu>
    );
    const fetchGatewayConfig = async (values) => {
        Promise.all([
            autoServiceMessage({
                service: getGateWayConfig(gateway.gatewayID),
                // showLoading: false
            }),
            autoServiceMessage({
                service: getClientIpList(gateway.gatewayID),
                showLoading: false,
            }),
            autoServiceMessage({
                service: getGatewayAddressService(gateway.gatewayID),
                showLoading: false,
            }),
        ]).then(([res, ipListRes, addressRes]) => {
            let ipList = [];
            let gatewayAddress = "";
            if (res && res.data) {
                setGatewayConfig(res.data);
            }
            if (ipListRes?.data) {
                ipList = ipListRes.data?.localAddress?.map((item, index) => ({
                    index: index,
                    ip: item,
                }));
                setIpList(ipList);
            }

            if (addressRes?.data) {
                const { ip, location } = addressRes.data;
                gatewayAddress = `${ip || ""} ${location || ""}`;
                ownerStore.setGateway({ ...gateway, gatewayAddress });
            }
        });
    };
    useEffect(() => {
        if (window.jDiwork) {
            window.jDiwork.getState &&
                window.jDiwork.getState((data) => {
                    setServiceCode(data.serviceInfo.serviceCode);
                    setActiveTab(data.activeTab);
                });
        }
    }, []);
    useEffect(() => {
        if (gateway.gatewayID) {
            fetchGatewayConfig(); // XXX-2 网关在线获取连接信息
        }
    }, [gateway.gatewayID]);

    // 网关基本信息保存
    const handleBasicInfoSave = async (values) => {
        await ownerStore.editGateWay({ id: gateway.gatewayID, ...values });
    };
    // 连接信息保存
    const handleConfigSave = async (values) => {
        let res = await autoServiceMessage({
            service: saveGateWayConfig({ gatewayId: gateway.gatewayID, ...values }, { serviceCode: props.ownerState.serviceCodeDiwork }),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180311", "客户端连接信息保存成功", undefined, {
                returnStr: true,
            }) /* "客户端连接信息保存成功" */,
        });
        if (res && res.status === 1) {
            setGatewayConfig({ ...gatewayConfig, ...values });
        }
    };

    const handleOpenAdd = () => {
        setAddShow(true);
    };

    const handleBindConnects = () => {
        setAddShow(false);
        // ownerStore.bindConnectors(gateway.gatewayID, []); //todo去处理绑定的连接器
    };
    // 抽屉内的网关刷新
    const handleRefresh = async () => {
        const res = await autoServiceMessage({
            service: gateway.isCluster
                ? ClusterhealthCheck({ gatewayId: (props.ownerState.selectedGateway || {}).gatewayID }, { serviceCode: props.ownerState.serviceCodeDiwork })
                : healthCheck({ gatewayId: (props.ownerState.selectedGateway || {}).gatewayID }, { serviceCode: props.ownerState.serviceCodeDiwork }),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418031B", "网关健康检查成功", undefined, {
                returnStr: true,
            }) /* "网关健康检查成功" */,
        });
        if (res) {
            // XXX-3 刷新网关重置store.selectedgateway.status，并且请求连接信息
            ownerStore.setGateway({ ...gateway, status: GatewayStatus.online });
            fetchGatewayConfig();
        } else {
            ownerStore.setGateway({ ...gateway, status: GatewayStatus.offline });
        }
        gateway.isCluster && !res && fetchGatewayConfig(); //集群状态下不管第一个接口成功与否都调config接口，单一模式还是第一个接口成功再调第二个
    };

    const handleDelete = () => {
        props.onDelete(ownerState.selectedGateway);
    };
    const handleCloseDrawer = () => {
        // 数据还原
        setGatewayConfig(undefined); //TODO 清空数据
        isFirst.current = true;
        setEditing(false);
        onClose();
    };
    const handleRefreshDeploy = async () => {
        const res = await autoServiceMessage({
            service: reloadGatewayAddressService(gateway.gatewayID),
        });
        if (res?.data) {
            const { ip, location } = res.data;
            const gatewayAddress = `${ip || ""} ${location || ""}`;
            ownerStore.setGateway({ ...gateway, gatewayAddress });
        }
    };
    // 基本信息formItem配置
    let basicInfoItems = [
        {
            editable: true,
            formItemProps: {
                label: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180326", "网关编码") /* "网关编码" */,
                name: "code",
                validateTrigger: "onChange",
                rules: [
                    { required: true, message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418030E", "请输入") /* "请输入" */ },
                    {
                        pattern: codeReg,
                        message: lang.templateByUuid(
                            "UID:P_UBL-FE_18D8CEF60418032A",
                            "网关编码由英文、数字、下划线组成" //@notranslate
                        ) /* "网关编码由英文、数字、下划线组成" */,
                    },
                ],
            },
            span: 12,
            formElementProps: {
                // formItem包裹的表单元素属性
                disabled: true,
                placeholder: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418030E", "请输入") /* "请输入" */,
            },
        },
        {
            editable: true,
            formItemProps: {
                label: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418032F", "网关名称") /* "网关名称" */,
                name: "name",
                validateTrigger: "onChange",
                rules: [{ required: true, message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418030E", "请输入") /* "请输入" */ }],
            },
            span: 12,
            formElementProps: {
                // formItem包裹的表单元素属性
                disabled: false,
                placeholder: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418030E", "请输入") /* "请输入" */,
                maxLength: "128",
            },
        },
        {
            formItemProps: {
                label: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418033A", "网关ID") /* "网关ID" */,
                name: "gatewayID",
            },
            span: 12,
            formElementProps: {
                // formItem包裹的表单元素属性
                disabled: true,
            },
        },
        {
            formItemProps: {
                label: lang.templateByUuid("UID:P_UBL-FE_1D798ECC04200008", "网关部署路径") /* "网关部署路径" */,
                name: "gatewayAddress",
            },
            span: 12,
            formElementProps: {
                disabled: true,
            },
            textExtraNodes: [
                <Icon
                    key={2}
                    onClick={async (e) => {
                        e.stopPropagation();
                        handleRefreshDeploy();
                    }}
                    style={{ cursor: "pointer" }}
                    type="uf-shuaxin"
                />,
            ],
        },

        {
            type: "textarea",
            editable: true,
            formItemProps: {
                label: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418033F", "描述") /* "描述" */,
                name: "describe",
                labelCol: { span: 3 },
                wrapperCol: { span: 20 },
            },
            formElementProps: {
                // formItem包裹的表单元素属性
                style: { height: 60 },
                disabled: false,
                placeholder: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418030E", "请输入") /* "请输入" */,
            },
            span: 24,
        },
        {
            type: "radioGroup",
            editable: true,
            direction: "horizontal", // 垂直的 Radio.Group
            formItemProps: {
                label: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180347", "部署模式") /* "部署模式" */,
                name: "isCluster",
                labelCol: { span: 3 },
                wrapperCol: { span: 21 },
                initialValue: false,
            },
            formElementProps: {
                disabled: !canEdit,
                // formItem包裹的表单元素属性
                options1: [
                    {
                        label: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418034F", "单一部署") /* "单一部署" */,
                        value: false,
                        // desc: '支持下载一个Client端，且安装在一个服务器内'
                        toolTip: {
                            overlayStyle: {},
                            arrowPointAtCenter: true,
                            overlay: (
                                <div>
                                    {
                                        lang.templateByUuid(
                                            "UID:P_UBL-FE_1CC8AF2004980028",
                                            "部署一个网关应用实例并启动，部署简单，推荐使用"//@notranslate
                                        ) /* "部署一个网关应用实例并启动，部署简单，推荐使用" */
                                    }
                                </div>
                            ),
                        },
                    },
                    {
                        label: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180312", "集群部署") /* "集群部署" */,
                        value: true,
                        // desc: '支持下载一个Client端，且安装在多个服务器内',
                        toolTip: {
                            overlayStyle: {},
                            arrowPointAtCenter: true,
                            overlay: (
                                <div>
                                    {
                                        lang.templateByUuid(
                                            "UID:P_UBL-FE_1CC8AF2004980024",
                                            "部署多个网关应用实例并启动，部署复杂度高但高可用，安装方式详见主页【安装说明】"//@notranslate
                                        ) /* "部署多个网关应用实例并启动，部署复杂度高但高可用，安装方式详见主页【安装说明】" */
                                    }
                                </div>
                            ),
                        },
                    },
                ],
                onChange: (e) => {
                    setCluster(e);
                },
            },
            span: 24,
        },
        isCluster
            ? {
                  type: "textarea",
                  editable: true,
                  formItemProps: {
                      label: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418031D", "服务器IP") /* "服务器IP" */,
                      name: "clusterIPs",
                      labelCol: { span: 3 },
                      wrapperCol: { span: 20 },
                      validateTrigger: "onChange",
                      rules: [
                          { required: true, message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418030E", "请输入") /* "请输入" */ },
                          { pattern: ipsEnterReg, message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180320", "请输入正确的ip") /* "请输入正确的ip" */ },
                      ],
                  },
                  formElementProps: {
                      disabled: !canEdit,
                      // formItem包裹的表单元素属性
                      style: { height: 80 },
                      placeholder: lang.templateByUuid(
                          "UID:P_UBL-FE_18D8CEF604180322",
                          "请输入允许访问客户端的IP；换行输入多个" //@notranslate
                      ) /* "请输入允许访问客户端的IP；换行输入多个" */,
                  },
                  span: 24,
              }
            : {
                  hidden: true,
                  formItemProps: {},
                  formElementProps: {},
              },
    ];

    // 获取ip的modal table Columns
    const fillModalTableColumns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180342", "编号") /* "编号" */,
            dataIndex: "index",
            key: "index",
            render: (text, record, index) => index,
        },
        { title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180343", "IP地址") /* "IP地址" */, dataIndex: "ip", key: "ip" },
    ];
    // 连接方式
    const formItemConnectionMethod = {
        type: "radioGroup",
        needTrans: true,
        formItemProps: {
            // formItem元素属性
            label: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180345", "连接方式") /* "连接方式" */,
            name: "channelProtocol",
            labelCol: { span: 3 },
            wrapperCol: { span: 20 },
            initialValue: "ws",
        },
        span: 24,
        formElementProps: {
            // formItem包裹的表单元素属性
            disabled: !canEdit,
            onChange: (value) => {
                setHttpConnect(value === "http");
            },
            options1: [
                {
                    label:
                        "WEBSOCKET" +
                        lang.templateByUuid("UID:P_UBL-FE_18E725900468004E", "长连接", undefined, {
                            returnStr: true,
                        }) /* "长连接" */,
                    value: "ws",
                    toolTip: {
                        overlay: lang.templateByUuid(
                            "UID:P_UBL-FE_1CC8AF2004980025",
                            "该连接方式无需开通外网端口，网络安全性高，需网关所在服务器支持WS协议"//@notranslate
                        ) /* "该连接方式无需开通外网端口，网络安全性高，需网关所在服务器支持WS协议" */,
                    },
                },
                {
                    label: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418030D", "HTTP直连") /* "HTTP直连" */,
                    value: "http",
                    toolTip: {
                        overlay: lang.templateByUuid(
                            "UID:P_UBL-FE_1CC8AF2004980026",
                            "该连接方式需要开通外网端口。业务如需附件预览，推荐使用此方式"//@notranslate
                        ) /* "该连接方式需要开通外网端口。业务如需附件预览，推荐使用此方式" */,
                    },
                },
            ],
        },
    };
    // 外网可访问的nginx地址
    const formItemExternalNginx = {
        editable: true,
        formItemProps: {
            label: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180316", "Nginx访问地址") /* "Nginx访问地址" */,
            name: !isCluster ? "extranetGatewayAddress" : "clusterNgAddress",
            validateTrigger: "onChange",
            rules: [{ required: true, message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418030E", "请输入") /* "请输入" */ }],
            labelCol: { span: 3 },
            wrapperCol: { span: 20 },
        },
        formElementProps: {
            // formItem包裹的表单元素属性
            disabled: !canEdit,
            placeholder: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418031C", "请输入外网可访问的地址") /* "请输入外网可访问的地址" */,
        },
        span: 24,
        toolTip: lang.templateByUuid(
            "UID:P_UBL-FE_18D8CEF60418031E",
            "对于集群部署的客户端，需要前置安装一个nginx进行路由跳转。若是长连接方式，则【nginx访问地址】输入集群nginx所在内网地址即可；如果是直连方式，客户可以直接将集群nginx开通外网访问权限，输入集群nginx外网可访问的地址，客户还可以将集群nginx放在内网，单独安装一个外网可以访问的nginx，此nginx上配置跳转到集群nginx上，【nginx访问地址】输入此外网nginx即可。访问地址必须是包含http(s)://的完整地址，例如：http://***********:8888" //@notranslate
        ) /* "对于集群部署的客户端，需要前置安装一个nginx进行路由跳转。若是长连接方式，则【nginx访问地址】输入集群nginx所在内网地址即可；如果是直连方式，客户可以直接将集群nginx开通外网访问权限，输入集群nginx外网可访问的地址，客户还可以将集群nginx放在内网，单独安装一个外网可以访问的nginx，此nginx上配置跳转到集群nginx上，【nginx访问地址】输入此外网nginx即可。访问地址必须是包含http(s)://的完整地址，例如：http://***********:8888" */,
    };
    // 集群nginx地址
    const formItemClusterNginx = {
        editable: true,
        formItemProps: {
            label: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180316", "Nginx访问地址") /* "Nginx访问地址" */,
            name: "clusterNgAddress",
            validateTrigger: "onChange",
            rules: [{ required: true, message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418030E", "请输入") /* "请输入" */ }],
            labelCol: { span: 3 },
            wrapperCol: { span: 20 },
        },
        formElementProps: {
            // formItem包裹的表单元素属性
            disabled: !canEdit,
            placeholder: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180324", "请输入集群Nginx地址，如http(s)://") /* "请输入集群Nginx地址，如http(s)://" */,
        },
        span: 24,
        toolTip: lang.templateByUuid(
            "UID:P_UBL-FE_18D8CEF60418031E",
            "对于集群部署的客户端，需要前置安装一个nginx进行路由跳转。若是长连接方式，则【nginx访问地址】输入集群nginx所在内网地址即可；如果是直连方式，客户可以直接将集群nginx开通外网访问权限，输入集群nginx外网可访问的地址，客户还可以将集群nginx放在内网，单独安装一个外网可以访问的nginx，此nginx上配置跳转到集群nginx上，【nginx访问地址】输入此外网nginx即可。访问地址必须是包含http(s)://的完整地址，例如：http://***********:8888" //@notranslate
        ) /* "对于集群部署的客户端，需要前置安装一个nginx进行路由跳转。若是长连接方式，则【nginx访问地址】输入集群nginx所在内网地址即可；如果是直连方式，客户可以直接将集群nginx开通外网访问权限，输入集群nginx外网可访问的地址，客户还可以将集群nginx放在内网，单独安装一个外网可以访问的nginx，此nginx上配置跳转到集群nginx上，【nginx访问地址】输入此外网nginx即可。访问地址必须是包含http(s)://的完整地址，例如：http://***********:8888" */,
    };
    const fetchIpList = async () => {
        // debugger
        if (ipList.length) {
            setSelectedInstanceIpRecord({ index: ipList.findIndex((f) => f.ip === currentGetIp.ip) });
            return;
        }
        let res = await autoServiceMessage({
            service: getClientIpList(gateway.gatewayID, { serviceCode: props.ownerState.serviceCodeDiwork }),
            showLoading: false,
        });
        if (res && res.data) {
            const ipList = (((res || {}).data || {}).localAddress || []).map((item, index) => ({
                index: index,
                ip: item,
            }));
            // debugger
            setSelectedInstanceIpRecord({ index: ipList.findIndex((f) => f.ip === currentGetIp.ip) });
            setIpList(ipList);
        }
    };
    // 内网地址
    const formItemIntranetAddress = {
        type: "select", // 填充数据类型
        editable: true,
        formItemProps: {
            label: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180334", "内网访问IP") /* "内网访问IP" */,
            name: "ip",
            validateTrigger: "onChange",
            rules: [
                { required: true, message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418030E", "请输入") /* "请输入" */ },
                { pattern: ipReg, message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180338", "请输入正确的ip地址") /* "请输入正确的ip地址" */ },
            ],
            tooltip: lang.templateByUuid("UID:P_UBL-FE_1CC8AF2004980023", "客户端所在服务器的内网访问IP") /* "客户端所在服务器的内网访问IP" */,
        },
        formElementProps: {
            // formItem包裹的表单元素属性
            disabled: !canEdit,
            placeholder: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418030E", "请输入") /* "请输入" */,
            options: ipList,
            fieldNames: {
                label: "ip",
                value: "ip",
            },
        },
        // otherElementProps: {
        //     eventHandler: fetchIpList,
        //     buttonText: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418033B", "获取") /* "获取" */, // 填充按钮文本（只有类型为table才需设置）
        //     modalTitle: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418033C", "获取IP地址") /* "获取IP地址" */, // 填充弹窗标题（只有类型为table才需设置）
        //     modalTableColumns: fillModalTableColumns || [],
        //     data: ipList,
        // },
        span: 12,
    };
    // 内网端口
    const formItemIntranetPort = {
        editable: true,
        formItemProps: {
            label: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180341", "内网访问端口") /* "内网访问端口" */,
            name: "outPort",
            validateTrigger: "onChange",
            rules: [
                { required: true, message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418030E", "请输入") /* "请输入" */ },
                { pattern: portReg, message: lang.templateByUuid("UID:P_UBL-FE_1A6A374205E80019", "端口号范围为0-65535") /* "端口号范围为0-65535" */ },
            ],
            tooltip: lang.templateByUuid("UID:P_UBL-FE_1CC8AF2004980021", "客户端所在服务器的内网访问端口") /* "客户端所在服务器的内网访问端口" */,
        },
        formElementProps: {
            // formItem包裹的表单元素属性
            disabled: !canEdit,
            placeholder: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418030E", "请输入") /* "请输入" */,
        },
        span: 12,
    };
    // http直连端口
    const formItemIntranetHttpPort = {
        editable: true,
        formItemProps: {
            label: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418034D", "http直连端口") /* "http直连端口" */,
            name: "mobileLightPort",
            validateTrigger: "onChange",
            rules: [
                { required: true, message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418030E", "请输入") /* "请输入" */ },
                { pattern: portReg, message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180338", "请输入正确的ip地址") /* "请输入正确的ip地址" */ }, //
            ],
        },
        formElementProps: {
            // formItem包裹的表单元素属性
            disabled: !canEdit,
            placeholder: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418030E", "请输入") /* "请输入" */,
        },
        span: 12,
    };
    // 客户端连接信息forItem组合
    let clientInfoColumns = useMemo(() => {
        let columns;
        if (!isCluster && !isHttpConnect) {
            // 单一部署 + ws
            columns = [formItemConnectionMethod, formItemIntranetAddress, formItemIntranetPort];
            return columns;
        } else if (!isCluster && isHttpConnect) {
            // 单一部署 + http
            columns = [formItemConnectionMethod, formItemExternalNginx, formItemIntranetAddress, formItemIntranetPort, formItemIntranetHttpPort];
        } else if (isCluster && isHttpConnect) {
            // 集群部署 + http
            columns = [formItemConnectionMethod, formItemExternalNginx];
        } else if (isCluster && !isHttpConnect) {
            // 集群部署 + ws
            columns = [formItemConnectionMethod, formItemClusterNginx];
        }
        return columns;
    }, [isCluster, isHttpConnect, ipList]);
    const advancedColumns = [
        {
            // ip白名单
            type: "textarea",
            editable: true,
            formItemProps: {
                labelCol: { span: 3 },
                wrapperCol: { span: 20 },
                label: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180323", "IP白名单") /* "IP白名单" */,
                name: "whiteList",
                validateTrigger: "onChange",
                rules: [
                    {
                        pattern: ipsEnterReg,
                        message: lang.templateByUuid("UID:P_UBL-FE_1A6A374205E80018", "格式为X.X.X.X，X为0-255之间") /* "格式为X.X.X.X，X为0-255之间" */,
                    },
                ],
                tooltip: lang.templateByUuid(
                    "UID:P_UBL-FE_1CC8AF2004980022",
                    "ERP能访问网关的IP， 如NCC或高级版IP为 192.XXX.X.1 ,设置后网关只允许192.XXX.X.1的NCC或高级版访问，如不设置，则默认全部可访问"//@notranslate
                ) /* "ERP能访问网关的IP， 如NCC或高级版IP为 192.XXX.X.1 ,设置后网关只允许192.XXX.X.1的NCC或高级版访问，如不设置，则默认全部可访问" */,
            },
            formElementProps: {
                // formItem包裹的表单元素属性
                style: { height: 80 },
                disabled: !canEdit,
                placeholder: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180328", "换行输入多个IP") /* "换行输入多个IP" */,
            },
            span: 24,
        },
        {
            type: "radioGroup",
            editable: true,
            direction: "horizontal", // 垂直的 Radio.Group
            formItemProps: {
                label: lang.templateByUuid("UID:P_UBL-FE_1B86E5BA04A00011", "链路日志") /* "链路日志" */,
                name: "enableLinkMonitor",
                labelCol: { span: 3 },
                wrapperCol: { span: 21 },
                initialValue: false,
                tooltip: lang.templateByUuid(
                    "UID:P_UBL-FE_1CC8AF2004980027",
                    "是否同意链路日志记录，启动链路日志后，数据集成日志将会被记录且仅用于链路报错问题排查。"//@notranslate
                ) /* "是否同意链路日志记录，启动链路日志后，数据集成日志将会被记录且仅用于链路报错问题排查。" */,
            },
            formElementProps: {
                // formItem包裹的表单元素属性
                disabled: !canEdit,
                options1: [
                    {
                        label: lang.templateByUuid("UID:P_UBL-FE_1B86E5BA04A00012", "开启") /* "开启" */,
                        value: true,
                    },
                    {
                        label: lang.templateByUuid("UID:P_UBL-FE_1B86E5BA04A00010", "关闭") /* "关闭" */,
                        value: false,
                    },
                ],
                onChange: (e) => {
                    // setEnableLinkMonitor(e);
                },
            },
            span: 24,
        },
    ];
    const handleIpChange = (value, oldValue) => {
        setShowDownloadBadge(value !== oldValue);
    };
    //  集群模式下-连接实例table Columns
    let commonInsColumns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418032D", "序号") /* "序号" */,
            width: 50,
            dataIndex: "$$index",
            render: (value, record, index) => {
                return index + 1;
            },
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180332", "实例IP") /* "实例IP" */,
            dataIndex: "clientIp",
            width: 200,
            render: (text, record, index) => {
                if (canEdit && isEditing) {
                    return (
                        <Space fieldid="ublinker-routes-list-components-InfoDrawer-index-5853720-Space" align="center">
                            <Form.Item
                                name={[index, "clientIp"]} //[index, 'clientIp']
                                validateTrigger="onChange"
                                rules={[{ required: true, message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418030E", "请输入") /* "请输入" */ }]}
                            >
                                <Input
                                    fieldid="ublinker-routes-list-components-InfoDrawer-index-751979-Input"
                                    onChange={(value) => handleIpChange(value, record.clientIp)}
                                />
                            </Form.Item>
                            {/* <a fieldid="ublinker-routes-list-components-InfoDrawer-index-398579-a" onClick={() => {
                            currentGetIp.current = record.clientIp;
                            setInstanceIndex(index);
                            fetchIpList();
                        }}>获取</a> */}
                        </Space>
                    );
                }
                return <span>{text}</span>;
            },
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418033E", "实例端口号") /* "实例端口号" */,
            dataIndex: "clientPort",
            render: (text, record, index) => {
                return <span>{text}</span>;
            },
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180348", "状态") /* "状态" */,
            dataIndex: "clientState",
            render: (text, record, index) => {
                return text == 1 ? (
                    <div className="instance-state online">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418032C", "在线") /* "在线" */}</div>
                ) : (
                    <div className="instance-state offline">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418032E", "离线") /* "离线" */}</div>
                );
            },
        },
        {
            title: "",
            dataIndex: "serverId",
            // isShow:false,
            render: (text, record, index) => {
                if (!canEdit || !isEditing) return <span>{text}</span>;
                return (
                    <Space fieldid="ublinker-routes-list-components-InfoDrawer-index-5397519-Space" align="center">
                        <Form.Item
                            hidden
                            name={[index, "serverId"]} //[index, 'clientIp']
                        >
                            <Input fieldid="ublinker-routes-list-components-InfoDrawer-index-580854-Input" />
                        </Form.Item>
                    </Space>
                );
            },
        },
    ];
    //  集群模式下-连接实例Column2根据集群模式决定
    const hoverContent = (record) => {
        return (
            <Fragment>
                <Button
                    fieldid="ublinker-routes-list-components-InfoDrawer-index-7316121-Button"
                    //   {...Table.hoverButtonPorps}
                    size="sm"
                    colors="dark"
                    className="ucg-mar-r-5"
                    onClick={handleHoverRefresh.bind(null, record)}
                >
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180318", "刷新") /* "刷新" */}
                </Button>
                <Button
                    fieldid="ublinker-routes-list-components-InfoDrawer-index-281849-Button"
                    //   {...Table.hoverButtonPorps}
                    size="sm"
                    colors="dark"
                    className="ucg-mar-r-5"
                    onClick={handleHoverDelete.bind(null, record)}
                >
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180319", "删除") /* "删除" */}
                </Button>
            </Fragment>
        );
    };
    const handleHoverRefresh = async () => {
        fetchGatewayConfig(); //大刷
    };
    const handleHoverDelete = async (record) => {
        let res = await autoServiceMessage(
            {
                service: deleteInstance({
                    gatewayId: gateway.gatewayID,
                    serverId: record.serverId,
                }),
            },
            { serviceCode: props.ownerState.serviceCodeDiwork }
        );
        if (res) {
            fetchGatewayConfig(); //大刷
        }
    };
    let instanceColumns = useMemo(() => {
        if (isHttpConnect) {
            commonInsColumns.splice(3, 0, {
                title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418032B", "直连端口号") /* "直连端口号" */,
                dataIndex: "mobilePort",
                render: (text, record, index) => {
                    return <span>{text}</span>;
                },
            });
        }
        return commonInsColumns;
    }, [isHttpConnect, canEdit, isEditing]);
    const handleCancelEdit = async () => {
        setEditing(false);
        setCluster(gateway.isCluster); //部署模式恢复 有可能需要再调一次客户端接口
        setHttpConnect(gateway.channelProtocol === "http"); //连接方式恢复
    };
    const handleSave = async () => {
        try {
            // 获取基本信息
            const basicInfo = await basicRef.current.validateFields();
            // 获取客户端信息
            const configInfo = await configRef.current.validateFields();
            const highInfo = await heighRef.current.validateFields();
            // 获取连接实例表格数据
            let tableInfo = [];
            tableInfo = await instanceForm.validateFields();
            let res = await autoServiceMessage({
                service: saveEditGateWay(
                    { gatewayId: gateway.gatewayID, ...basicInfo, ...configInfo, ...highInfo, clientInfos: Object.values(tableInfo) },
                    { serviceCode: props.ownerState.serviceCodeDiwork }
                ),
                success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418034C", "保存成功", undefined, {
                    returnStr: true,
                }) /* "保存成功" */,
            });
            if (res && res.status === 1) {
                // setGatewayConfig({ ...gatewayConfig, ...values });
                ownerStore.setGateway(undefined);
                ownerStore.getDataSource();
            }
            // else{
            //     ownerStore.setGateway(undefined);
            //     ownerStore.getDataSource();
            // }
        } catch (error) {
            console.log(
                lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180310", "验证失败", undefined, {
                    returnStr: true,
                }) /* "验证失败" */,
                error
            );
        }
    };
    let drawerFooter = useMemo(() => {
        let node;
        if (currentTab === "1" && isEditing) {
            node = (
                <Space fieldid="UCG-FE-routes-list-components-InfoDrawer-index-4818366-Space">
                    <Button fieldid="ublinker-routes-list-components-InfoDrawer-index-6601498-Button" colors="secondary" onClick={handleCancelEdit}>
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180315", "取消") /* "取消" */}
                    </Button>
                    <Button fieldid="ublinker-routes-list-components-InfoDrawer-index-6439179-Button" colors="primary" onClick={handleSave}>
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180317", "保存") /* "保存" */}
                    </Button>
                </Space>
            );
        } else {
            node = null;
        }
        return node;
    }, [canEdit, isEditing, currentTab]);

    const handleInstanceIpModalCancel = () => {
        setInstanceIndex(-1);
        setSelectedInstanceIpRecord({});
    };
    // 集群模式--获取ip弹窗确认
    const handleInstanceIpModalConfirm = () => {
        const values = {
            [instanceIndex]: { ip: selectedInstanceIpRecord.ip },
        };
        // 两个表格都要同步设置ip
        instanceForm.setFieldsValue(values);
        setInstanceIndex(-1);
    };
    const getSelectedInstanceIpFunc = (record, index) => {
        setSelectedInstanceIpRecord({ ...record, index });
    };
    //
    const handleEdit = async () => {
        setCurrentTab("1");
        console.log("999----", gatewayConfig);
        await configRef.current.setFieldsValue(gatewayConfig);
        await basicRef.current.setFieldsValue(gateway);
        await heighRef.current.setFieldsValue(gatewayConfig);
        await instanceForm.setFieldsValue(gatewayConfig?.clientInfos);
        setEditing(true);
    };
    const headerNode = (
        <Space size={8} align="center" style={{ height: "100%" }}>
            <img className="gateway-logo" src={gateway.status === 1 ? onlineSvg : outlineSvg} alt="logo" />
            <div title={gateway.name} className="InfoDrawer-gatewayName">
                {gateway.name}
            </div>
            <div
                fieldid="ublinker-routes-list-components-InfoDrawer-index-************-Dropdown"
                className={`gateway-status ${(ownerState.selectedGateway || {}).status !== GatewayStatus.offline ? "gateway-status-able" : "gateway-status-disable"}`}
            >
                {
                    (ownerState.selectedGateway || {}).status !== GatewayStatus.offline
                        ? lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418032C", "在线") /* "在线" */
                        : lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418032E", "离线") /* "离线" */
                }
            </div>
            {!isEditing && (
                <Button fieldid="ublinker-routes-list-components-InfoDrawer-index-9462593-Button" onClick={handleEdit} type="primary">
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180331", "编辑") /* "编辑" */}
                </Button>
            )}
            <Dropdown fieldid="ublinker-routes-list-components-InfoDrawer-index-3474640-Dropdown" overlay={menu}>
                <Button fieldid="ublinker-routes-list-components-InfoDrawer-index-2211212047-Button" disabled={clickedDownloadClient}>
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180333", "下载客户端") /* "下载客户端" */}
                </Button>
            </Dropdown>
            <Button fieldid="ublinker-routes-list-components-InfoDrawer-index-1148145-Button" onClick={() => downloadGatewayKey(gateway.gatewayID)}>
                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180335", "下载密钥") /* "下载密钥" */}
            </Button>
            <Button fieldid="ublinker-routes-list-components-InfoDrawer-index-5570753-Button" onClick={handleRefresh}>
                {lang.templateByUuid("UID:P_UBL-FE_1C9BB73404C00002", "测试连接") /* "测试连接" */}
            </Button>
            {!gateway?.num && gateway.status === GatewayStatus.offline ? (
                <Popconfirm
                    fieldid="ublinker-routes-list-components-InfoDrawer-index-1315127-Popconfirm"
                    trigger="click"
                    placement="bottom"
                    content={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180337", "确定删除该网关吗") /* "确定删除该网关吗" */}
                    cancelText={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180315", "取消") /* "取消" */}
                    okText={lang.templateByUuid("UID:P_UBL-FE_1CCD7D9A05F00002", "确定") /* "确定" */}
                    onClose={handleDelete}
                >
                    <Button fieldid="ublinker-routes-list-components-InfoDrawer-index-2211212048-Button">
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180319", "删除") /* "删除" */}
                    </Button>
                </Popconfirm>
            ) : null}
        </Space>
    );
    // 下载nginx配置文件
    const handleLoadNginx = async () => {
        let res = await autoServiceMessage(
            {
                service: downloadNG({
                    gatewayId: gateway.gatewayID,
                    address: gatewayConfig.clientInfos
                        .map((item) => {
                            return `${item.clientIp}:${item.clientPort}`;
                        })
                        .join(","),
                    httpAddress: gatewayConfig.clientInfos
                        .map((item) => {
                            return `${item.clientIp}:${item.mobilePort}`;
                        })
                        .join(","),
                }),
            },
            { serviceCode: props.ownerState.serviceCodeDiwork }
        );
        if (res) {
            const blob = res.data;
            const reader = new FileReader();
            reader.readAsDataURL(blob);
            reader.onload = (e) => {
                const a = document.createElement("a");
                a.download = res.headers["content-disposition"].split("=")[1];
                // a.download = `文件名称.zip`;
                // 后端设置的文件名称在res.headers的 "content-disposition": "form-data; name=\"attachment\"; filename=\"20181211191944.zip\"",
                a.href = e.target.result;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
            };
        }
        // if (res) {
        // 	a_download(res.data,true)
        // }
    };

    return (
        <Drawer
            style={{ background: "transparent" }}
            fieldid="ublinker-routes-list-components-InfoDrawer-index-5281703-Drawer"
            className="gateway-drawer"
            drawerStyle={{ borderRadius: 0, height: "calc(100% - 16px)", marginTop: "8px", marginBottom: "8px" }}
            bodyStyle={{ padding: 0, overflow: "hidden", flex: 1 }}
            width={1100}
            title={headerNode}
            onClose={handleCloseDrawer}
            closable={true}
            footer={drawerFooter}
            footerStyle={{ textAlign: "right" }}
            getPopupContainer={document.getElementById(activeTab)?.firstChild ?? "body"}
            visible={!!ownerState.selectedGateway}
        >
            {!isEditing && (
                <div className="gateway-drawer-desc">
                    <img
                        fieldid="ublinker-routes-list-components-InfoDrawer-index-3421675-img"
                        className="gateway-drawer-desc-icon"
                        src={getLocalImg("assetPack/help-hint.png")}
                    />
                    <span className="gateway-drawer-desc-text">
                        {lang.templateByUuid(
                            "UID:P_UBL-FE_18D8CEF604180313",
                            "按照步骤完成网关创建、客户端下载及安装" //@notranslate
                        )}
                    </span>
                    <GatewayHelp overlayMaxHeight={true} autoAdjustOverflow={false} locale={locale}></GatewayHelp>
                </div>
            )}
            <Tabs
                fieldid="ublinker-routes-list-components-InfoDrawer-index-2425646-Tabs"
                // defaultActiveKey="1"
                activeKey={currentTab}
                className="gateway-drawer-tabs"
                type="card"
                onChange={(key) => {
                    setCurrentTab(key);
                    if (key === "2" && isFirst.current) {
                        ownerStore.getConnectsByType({ gatewayId: gateway.gatewayID }); // 获取连接器
                        isFirst.current = false;
                    }
                }}
            >
                <TabPane
                    fieldid="UCG-FE-routes-list-components-InfoDrawer-index-9064698-TabPane"
                    tab={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418031A", "基础配置") /* "基础配置" */}
                    key="1"
                    style={{ overflow: "auto" }}
                >
                    <EditContent
                        ref={basicRef}
                        isEditing={isEditing}
                        title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418031F", "基本信息", undefined, {
                            returnStr: true,
                        })}
                        fields={basicInfoItems}
                        data={gateway}
                        onSave={handleBasicInfoSave}
                    />
                    <EditContent
                        ref={configRef}
                        isEditing={isEditing}
                        title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180321", "客户端连接信息", undefined, {
                            returnStr: true,
                        })}
                        fields={clientInfoColumns}
                        data={{ ...gatewayConfig }}
                        onSave={handleConfigSave}
                    />
                    {isCluster && (
                        <Row>
                            <Col span={23}>
                                <div className="cluster-wrap">
                                    <div className="instance-header">
                                        <span>
                                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180325", "运行实例") /* "运行实例" */}(
                                            {(gatewayConfig && gatewayConfig.clientInfos && gatewayConfig.clientInfos.length) || 0})
                                        </span>
                                        {canEdit && isEditing && (
                                            <Space fieldid="UCG-FE-routes-list-components-InfoDrawer-index-1714957-Space">
                                                <Button
                                                    fieldid="ublinker-routes-list-components-InfoDrawer-index-8867766-Button"
                                                    onClick={() => {
                                                        fetchGatewayConfig();
                                                    }}
                                                >
                                                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180327", "获取实例") /* "获取实例" */}
                                                </Button>
                                                {/* <Badge fieldid="ublinker-routes-list-components-InfoDrawer-index-5751380-Badge" size='large' dot count={+showDownloadBadge} title='文件可更新'> */}
                                                {gatewayConfig?.clientInfos?.length > 0 ? (
                                                    <Button fieldid="ublinker-routes-list-components-InfoDrawer-index-6965516-Button" onClick={handleLoadNginx}>
                                                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180329", "下载nginx配置文件") /* "下载nginx配置文件" */}
                                                    </Button>
                                                ) : null}

                                                {/* </Badge> */}
                                                {/* <Tooltip fieldid="ublinker-routes-list-components-InfoDrawer-index-1920638-Tooltip" arrowPointAtCenter overlay={<div></div>}>
                                                    <img fieldid="ublinker-routes-list-components-InfoDrawer-index-8976128-img" style={{ cursor: 'pointer', width: '14px', height: '14px', marginLeft: '5px', marginRight: '5px' }} src={getLocalImg('gateway/help.png')} alt="" />
                                                </Tooltip> */}
                                            </Space>
                                        )}
                                    </div>
                                    <Form fieldid="ublinker-routes-list-components-InfoDrawer-index-4062257-Form" form={instanceForm}>
                                        <Table
                                            fieldid="ublinker-routes-list-components-InfoDrawer-index-1747975-Table"
                                            columns={instanceColumns}
                                            data={gatewayConfig?.clientInfos || []}
                                            dataSource={gatewayConfig?.clientInfos || []}
                                            hoverContent={canEdit && isEditing && hoverContent}
                                        />
                                    </Form>

                                    <Modal
                                        fieldid="ublinker-routes-list-components-InfoDrawer-index-1633409-Modal"
                                        show={instanceIndex > -1}
                                        title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180330", "获取ip", undefined, {
                                            returnStr: true,
                                        })}
                                        onCancel={handleInstanceIpModalCancel}
                                        onOk={handleInstanceIpModalConfirm}
                                        width={800}
                                        height={600}
                                    >
                                        <Grid
                                            fieldid="ublinker-routes-ObjectSetting-components-IndexView-index-9043665-Grid"
                                            columns={fillModalTableColumns}
                                            data={ipList}
                                            radioSelect
                                            selectedRowIndex={selectedInstanceIpRecord.index}
                                            getSelectedDataFunc={getSelectedInstanceIpFunc}
                                        />
                                    </Modal>
                                </div>
                            </Col>
                            <Col span={1}></Col>
                        </Row>
                    )}
                    <EditContent
                        ref={heighRef}
                        isEditing={isEditing}
                        title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180340", "高级设置", undefined, {
                            returnStr: true,
                        })}
                        fields={advancedColumns}
                        data={{ ...gatewayConfig }}
                        onSave={handleConfigSave}
                    />
                </TabPane>
                {!isEditing && (
                    <TabPane
                        fieldid="UCG-FE-routes-list-components-InfoDrawer-index-4973907-TabPane"
                        tab={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180344", "连接器") /* "连接器" */}
                        key="2"
                        style={{ overflow: "hidden" }}
                    >
                        <ConnectorGrid
                            data={ownerState.connectsByType}
                            ownerStore={ownerStore}
                            ownerState={ownerState}
                            // onOperate={handleOperate}
                            onAdd={handleOpenAdd}
                        />
                    </TabPane>
                )}
                {!isEditing && (
                    <TabPane
                        fieldid="UCG-FE-routes-list-components-InfoDrawer-index-4133949-TabPane"
                        tab={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418034B", "适配器") /* "适配器" */}
                        key="3"
                        style={{ overflow: "hidden" }}
                    >
                        <AdaptersView
                            ref={adapterViewRef}
                            instanceInfo={{ gatewayId: gateway.gatewayID, useMainTenantGateway: "" }} //只用到网关ID
                            getGatewayConfig={() => {}}
                            ownerState={ownerState}
                        />
                    </TabPane>
                )}
            </Tabs>

            <AddConnector
                ownerStore={ownerStore}
                ownerState={ownerState}
                show={addShow}
                data={ownerState.connectsByType || []}
                onCancel={() => setAddShow(false)}
                onOk={handleBindConnects}
            />
            {percent ? (
                <Progress
                    fieldid="UCG-FE-routes-list-components-InfoDrawer-index-4048957-Progress"
                    percent={percent}
                    style={{ position: "fixed", left: "1%", bottom: "10%", zIndex: "100" }}
                />
            ) : null}
        </Drawer>
    );
};

export default inject((rootStore) => {
    let ownerStore = rootStore[storeKey];
    return {
        ownerState: ownerStore.toJS(),
        ownerStore: ownerStore,
    };
})(observer(InfoDrawer));
