.gateway-drawer {
    .wui-radio .wui-radio-label {
        margin-right: 5px;
    }
    .wui-drawer-footer {
        z-index: 4; // 内部运行实例表格的z-index是1，table-header是3
    }
    .wui-drawer-header-title {
        height: 100%;
        line-height: normal;
    }
    .wui-drawer-close {
        line-height: 48px;
    }

    .drawer-header-title {
        display: flex;
        padding-right: 44px;
        justify-content: space-between;
        line-height: 46px;
    }
    .gateway-logo {
        width: 32px;
        height: 32px;
        border-radius: 50%;
    }
    .gateway-status {
        width: fit-content;
        height: 22px;
        border-radius: 3px;
        padding: 0 6px;
        font-size: 12px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        font-weight: 500;
        line-height: 22px;
        &-disable {
            color: #333333;
            background-color: #c2c4ca;
        }
        &-able {
            background: #ebf9f8;
            color: #00b39e;
        }
    }
    &-desc {
        overflow: hidden;
        margin: 5px 16px;
        padding-left: 12px;
        height: 32px;
        background: #fff7e7;
        border-radius: 4px;
        display: flex;
        align-items: center;

        &-icon {
            width: 17px;
            height: 17px;
        }
        &-text {
            margin: 0 14px 0 6px;
            font-size: 12px;
            font-family: PingFang-SC-Bold, PingFang-SC;
            font-weight: bold;
            color: #ee2223;
            line-height: 32px;
            max-width: 800px;
            .text-ellipsis();
        }
        &-help {
            cursor: pointer;
            height: 17px;
            font-size: 12px;
            font-family:
                PingFangSC-Regular,
                PingFang SC;
            font-weight: 400;
            color: #3a8df8;
            line-height: 17px;
        }
    }

    // 基本信息
    &-info {
        .commonPad();
        height: fit-content;
        // height: 194px;
        background: transparent;
    }
    .basic-info-edit {
        // background: #f7f9fd;
        .wui-form-item-has-success .wui-form-item-control-input-content > input {
            border-color: #d9d9d9;
        }
    }
    .header-common {
        margin-left: -10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        font-family: PingFang-SC-Heavy, PingFang-SC;
        font-weight: 800;
        color: #505766;
        line-height: 20px;
        &-line {
            display: block;
            width: 4px;
            height: 14px;
            background: #dd4048;
        }
        .uf-pencil-s {
            cursor: pointer;
            font-size: 11px;
            &:hover {
                border-radius: 2px;
                display: inline-block;
                background-color: #f2f3f3;
            }
        }
        .edit-btn {
            cursor: pointer;
            display: inline-block;
            font-size: 12px;
            font-family:
                PingFangSC-Regular,
                PingFang SC;
            font-weight: 400;
            color: #3a8df8;
            line-height: 20px;
            margin-right: 12px;
        }
    }
    .form-no-editing {
        display: flex;
        align-items: center;
        width: 100%;
        height: 17px;
        font-size: 12px;
        font-family:
            PingFangSC-Regular,
            PingFang SC;
        font-weight: 400;
        color: #333333;
    }
    .form-no-editing-text {
        max-width: 100%;
        .text-ellipsis();
    }
    .wui-form-item {
        margin-bottom: 9px;
    }
    &-connector {
        &-header {
            .commonPad();
        }
        &-btn-text {
            display: block;
            font-size: 12px;
            font-family:
                PingFangSC-Regular,
                PingFang SC;
            font-weight: 400;
            color: #4a8bf0;
            line-height: 35px;
            cursor: pointer;
        }
    }
    .upgrade-prompt {
        display: flex;
        align-items: center;
        > img {
            width: 12px;
            height: 12px;
            margin-right: 3px;
        }
        > span {
            font-size: 12px;
            font-family:
                PingFangSC-Regular,
                PingFang SC;
            font-weight: 400;
            color: #ff7301;
        }
    }
}
.gateway-drawer-tabs {
    height: calc(100% - 42px);
    .wui-tabs-content {
        flex: 1;
        overflow: hidden;
    }
    .wui-tabs-tabpane {
        height: 100%;
        display: flex;
        flex-direction: column;
    }
}
.commonPad {
    padding: 4px 25px 4px 35px;
}
.text-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

// 编辑表单组件
.edit-form-comp {
    &-item {
        .wui-form-item-control-input-content {
            display: flex;
            align-items: center;
            .wui-input {
                flex: 1;
            }
            .fill-btn {
                font-size: 12px;
                padding-left: 6px;
                color: #3a8df8;
            }
        }
        &-radio-desc {
            width: 249px;
            height: 17px;
            font-size: 12px;
            font-family:
                PingFangSC-Regular,
                PingFang SC;
            font-weight: 400;
            color: #999999;
            line-height: 17px;
            padding-left: 6px;
        }
    }

    .wui-input.rc-textarea {
        line-height: normal;
        padding: 4px 8px;
    }
    .error-input {
        textarea {
            border: 1px solid #e23;
        }
    }
}

// 连接器表格中的popover
.connect-grid-overlay-inner {
    display: flex;
    flex-direction: row;
    align-items: center;
}
.connect-grid-logo {
    width: 50px;
    height: 50px;
    border-radius: 50%;
}
.connect-content {
    max-width: 348px;
    overflow: hidden;
    margin-left: 14px;
}
.connect-logo {
    width: 72px;
    height: 72px;
    background: linear-gradient(180deg, #e53935 0%, #f46b65 100%);
    border-radius: 4px;
}
.connect-name {
    max-width: 348px;
    font-size: 16px;
    font-family: PingFang-SC-Heavy, PingFang-SC;
    font-weight: 800;
    color: #333333;
    line-height: 24px;
    margin-bottom: 5px;
    .text-ellipsis;
}
.connect-desc {
    font-size: 12px;
    font-family: PingFang-SC-Heavy, PingFang-SC;
    color: #333333;
    line-height: 24px;
}
//  集群模式--运行实例
.cluster-wrap {
    .commonPad();
    .wui-form-item {
        margin-bottom: 0;
    }
    .instance-header {
        margin-bottom: 6px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        > span {
            font-size: 12px;
            font-family: PingFang-SC-Bold, PingFang-SC;
            font-weight: bold;
            color: #333333;
            line-height: 17px;
        }
    }
    .instance-badge {
        width: 10px;
        height: 10px;
    }
}
.instance-state {
    width: min-content;
    background: #ebf9f8;
    border-radius: 3px;
    padding: 2px 6px;

    font-size: 12px;
    font-family: PingFang-SC-Medium, PingFang-SC;
    font-weight: 500;
    color: #00b39e;
    line-height: 17px;
}
.instance-state.online {
    background: #ebf9f8;
    color: #00b39e;
}
.instance-state.offline {
    background: #c2c4ca;

    color: #333333;
}
// }
.LightAppGatewayForm .groupadminBox {
    position: relative;
}
.LightAppGatewayForm .groupadminBox .groupadminBox-tip {
    position: absolute;
    top: 3px;
    left: 75%;
}
.LightAppGatewayForm .groupadminBox .groupadminBox-tip.radio {
    position: absolute;
    top: 4px;
    left: 38%;
}
.defaultGatewayLogo {
    background: #ebf9f8;
    color: #00b39e;
    padding: 2px 6px;
    border-radius: 3px;
    margin-left: 20px;
}
.InfoDrawer-gatewayName {
    max-width: 130px;
    /* 定义容器的宽度 */
    white-space: nowrap;
    /* 不换行 */
    overflow: hidden;
    /* 超出部分隐藏 */
    text-overflow: ellipsis;
    /* 显示省略号 */
}
