import React, { useCallback, useEffect, useState, useImperativeHandle } from "react";
import { Drawer, Space, Button, Icon, Form, Input, Row, Col } from "@tinper/next-ui";
import EditForm from "./EditForm";
import "./index.less";

const alignCenter = { display: "flex", alignItems: "center" };
const EditContent = (
    {
        title,
        data = {},
        fields = [],
        rowGutter = 16, // 行间距
        col = 2, // 每行显示几个
        formItemLayout,
        onSave,
        canEdit, // 当前组件是否单独编辑保存
        isEditing,
    },
    ref
) => {
    const [form] = Form.useForm();
    const [editingState, setEditingState] = useState(false); // 非受控的编辑和保存
    useEffect(() => {
        if (typeof isEditing === "boolean") {
            setEditingState(isEditing); // 受控编辑
        }
    }, [isEditing]);
    const handleSave = useCallback(async () => {
        try {
            const values = await form.validateFields();
            onSave && (await onSave(values));
            setEditingState(false);
        } catch (error) {
            console.log("验证失败");
        }
    }, [form, onSave]);
    useImperativeHandle(ref, () => {
        return {
            setFieldsValue: form.setFieldsValue,
            validateFields: form.validateFields,
        };
    });
    return (
        <div className={`gateway-drawer-info ${editingState ? "basic-info-edit" : ""}`}>
            <header className="header-common">
                <Space fieldid="ublinker-routes-list-components-InfoDrawer-EditContent-3584907-Space" align="center" style={alignCenter}>
                    <span className="header-common-line"></span>
                    <span>{title}</span>
                    {canEdit && (
                        <Icon
                            fieldid="ublinker-routes-list-components-InfoDrawer-EditContent-8457011-Icon"
                            type="uf-pencil-s"
                            onClick={() => {
                                setEditingState(true);
                                form.setFieldsValue(data);
                            }}
                        />
                    )}
                </Space>
                <div style={{ display: editingState && canEdit ? "block" : "none" }}>
                    <span className="edit-btn" onClick={() => setEditingState(false)}>
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041800E6", "取消") /* "取消" */}
                    </span>
                    <span className="edit-btn" onClick={handleSave}>
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041800E7", "保存") /* "保存" */}
                    </span>
                </div>
            </header>
            <EditForm data={data} formItemLayout={formItemLayout} fields={fields} formInstance={form} isEditing={editingState} gutter={rowGutter} />
        </div>
    );
};

export default React.forwardRef(EditContent);
