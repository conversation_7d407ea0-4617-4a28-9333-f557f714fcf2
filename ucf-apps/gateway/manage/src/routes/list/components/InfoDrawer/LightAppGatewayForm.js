import React, { useState, useEffect, useCallback, useRef, useMemo } from "react";
import { Button, FormControl, FormList, Radio, InputGroup, Menu, Select, Tooltip } from "components/TinperBee";
import { autoServiceMessage } from "utils/service";
import { ipReg, portReg, originReg, urlReg } from "utils/regExp";
// import {    getGwIpAddrService,    saveGwInfoService} from '../../../services';
import "./index.less";
const FormItem = FormList.Item;
const Option = Select.Option;
const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 12 },
};
const LightAppGatewayForm = (props) => {
    const [form] = FormList.useForm();
    const { ownerStore, ownerState, gatewayId } = props;
    const { getFieldProps, getFieldError, setFieldsValue, validateFields } = form;
    const [mobileLightAppInfo, setMobileLightAppInfo] = useState({});
    const [mobileLightAppList, setMobileLightAppList] = useState([]);
    const [connectId, setConnectId] = useState("");
    useEffect(() => {
        let init = async () => {
            let data = await ownerStore.getLightAppGatewayMsg(gatewayId);
            if (data) {
                setMobileLightAppInfo(data.mobileLightAppInfo);
                // let _cloudDocSelectData = data.yonbipMajorConnectInfo.map(item => {
                //     return {
                //       key: item.connectCode, value: item.alias,id:item.id
                //     }
                //   })
                // setMobileLightAppList(_cloudDocSelectData);
                setMobileLightAppList(data.yonbipMajorConnectInfo);
                let selectedConnect = data.yonbipMajorConnectInfo.find((item) => item.isSelected);
                setConnectId(selectedConnect && selectedConnect.id);
                setFieldsValue({
                    appAddress: data.mobileLightAppInfo.appAddress,
                    port: data.mobileLightAppInfo.port,
                    connectCode: selectedConnect && selectedConnect.connectCode,
                }); //,connectCode:data.mobileLightAppInfo.tenantConnect
            }
            // let list = await ownerStore.getLightAppGatewayList(gatewayId);
        };

        init();
    }, []);

    const handleVersionChange = useCallback((value, node, b) => {
        console.log(value, node, b);
        setConnectId(node.item.id);
        // setFieldsValue(node.item)
    }, []);

    const handleTest = useCallback(() => {
        validateFields().then((values) => {
            ownerStore.testLightAppGateway({ ...values, gatewayId });
        });
    }, []);
    const handleSave = useCallback(() => {
        // validateFields((error, values) => {
        //     if (!error) {
        //         saveGwConfig(values)
        //     }
        // })

        validateFields().then((values) => {
            ownerStore.saveLightAppGateway({ ...values, gatewayId, connectId });
        });
    });
    const gotoPage = (serviceCode) => {
        props.ownerStore.setGateway(undefined);
        jDiwork.ready(() => {
            jDiwork.openService(serviceCode);
        });
    };
    return (
        <FormList form={form} {...formItemLayout} className="LightAppGatewayForm">
            <FormItem label={lang.templateByUuid("UID:P_UBL-FE_1982A55A04D00031", "轻应用网关") /* "轻应用网关" */}>
                <Select
                    fieldid="31a8d6ae-7099-44a1-88d5-9af822709c99"
                    disabled
                    // onChange={(value, e) => { handleVersionChange(value) }}
                    defaultValue="major"
                >
                    <Option value={"major"}>{lang.templateByUuid("UID:P_UBL-FE_1982A55A04D0002C", "高级版") /* "高级版" */}</Option>
                </Select>
            </FormItem>
            <div className="groupadminBox">
                <FormItem
                    label={lang.templateByUuid("UID:P_UBL-FE_1982A55A04D0002E", "高级版信息") /* "高级版信息" */}
                    name="connectCode"
                    validateTrigger="onChange"
                >
                    <Select
                        fieldid="65e0f8ae-7ab5-4409-baf3-200305a7cc87"
                        onChange={(value, a, b) => {
                            handleVersionChange(value, a, b);
                        }}
                    >
                        {mobileLightAppList.map((item, index) => {
                            return (
                                <Option key={index} item={item} value={item.connectCode}>
                                    {item.alias}
                                </Option>
                            );
                        })}
                    </Select>
                </FormItem>
                <div className={`groupadminBox-tip`}>
                    <Button
                        fieldid="UCG-FE-application-components-Modal-ApplicationModal-index-73606171-Button"
                        style={{ textAlign: "left" }}
                        type="text"
                        bordered
                        onClick={() => {
                            gotoPage("kflj_ljpz");
                        }}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180543", "新增")}
                    </Button>
                </div>
            </div>
            <div className="groupadminBox">
                <FormItem
                    label={lang.templateByUuid("UID:P_UBL-FE_1982A55A04D00030", "轻应用网关外网地址") /* "轻应用网关外网地址" */}
                    name="appAddress"
                    validateTrigger="onChange"
                    rules={[
                        {
                            required: true,
                            message: lang.templateByUuid("UID:P_UBL-FE_1982A6AE04980011", "请输入轻应用网关外网地址") /* "请输入轻应用网关外网地址" */,
                        },
                    ]}
                >
                    <FormControl
                    // onChange={(value) => { handleConnectCodeChange(value) }}
                    />
                </FormItem>
                <div className={`groupadminBox-tip`}>
                    <Tooltip
                        fieldid="1e32addb-d7b2-48a8-b025-9f5fcf7eb092"
                        inverse
                        overlay={
                            lang.templateByUuid(
                                "UID:P_UBL-FE_1982A55A04D0002D",
                                "网关外网地址 输入格式应为http://网关服务器外网ip:轻应用网关端口 2.如网关服务器配置有nginx之类的服务，请填写nginx提供的外网地址 并映射到 1.中的地址 3.测试移动轻应用服务是否安装成功 可通过浏览器访问 http://网关服务器外网ip:轻应用网关端口/openapi/test 正确返回 mobile light app test connect success" //@notranslate
                            ) /* "网关外网地址 输入格式应为http://网关服务器外网ip:轻应用网关端口 2.如网关服务器配置有nginx之类的服务，请填写nginx提供的外网地址 并映射到 1.中的地址 3.测试移动轻应用服务是否安装成功 可通过浏览器访问 http://网关服务器外网ip:轻应用网关端口/openapi/test 正确返回 mobile light app test connect success" */
                        }
                    >
                        <i fieldid="be15fb57-ac38-4b1e-aad1-bc4c7fc6f292" className="cl cl-Q ucg-pad-l-5" style={{ color: "#505766" }} />
                    </Tooltip>
                </div>
            </div>
            <div className="groupadminBox">
                <FormItem
                    label={lang.templateByUuid("UID:P_UBL-FE_1982A55A04D00032", "轻应用网关端口") /* "轻应用网关端口" */}
                    name="port"
                    validateTrigger="onChange"
                    rules={[
                        {
                            required: true,
                            message: lang.templateByUuid("UID:P_UBL-FE_1982A6AE04980010", "请输入轻应用网关端口") /* "请输入轻应用网关端口" */,
                        },
                    ]}
                >
                    <FormControl
                    // onChange={(value) => { handleConnectCodeChange(value) }}
                    />
                </FormItem>
                <div className={`groupadminBox-tip`}>
                    <Tooltip
                        fieldid="e5898228-93c4-4316-bed5-f012ac07788e"
                        inverse
                        overlay={
                            lang.templateByUuid(
                                "UID:P_UBL-FE_1982A55A04D0002F",
                                "轻应用网关启动会开放此端口，用于轻应用访问，默认端口号是22290，如果此端口被占用，用户可修改端口号" //@notranslate
                            ) /* "轻应用网关启动会开放此端口，用于轻应用访问，默认端口号是22290，如果此端口被占用，用户可修改端口号" */
                        }
                    >
                        <i fieldid="e9f24dd5-759a-46a6-a12e-58ad7cf44969" className="cl cl-Q ucg-pad-l-5" style={{ color: "#505766" }} />
                    </Tooltip>
                </div>
            </div>
            <FormItem label=" ">
                <Button fieldid="ae50f17a-5e38-4a25-8980-121724c0ec35" colors="primary" className="ucg-mar-r-20 " onClick={handleSave}>
                    {lang.templateByUuid("UID:P_UBL-FE_1982A55A04D00034", "保存") /* "保存" */}
                </Button>
                <Button fieldid="7694488f-897f-4f18-9183-ecd6602f47da" className="ucg-mar-r-20 " onClick={handleTest}>
                    {lang.templateByUuid("UID:P_UBL-FE_1982A55A04D00033", "测试连接") /* "测试连接" */}
                </Button>
            </FormItem>
        </FormList>
    );
};

export default LightAppGatewayForm;
