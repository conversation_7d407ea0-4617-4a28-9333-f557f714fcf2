import React, { useEffect, useRef, useState } from "react";
import { Space, Button, Popover, Modal, Table } from "@tinper/next-ui";
import { Success, Error } from "utils/feedback";
import { autoServiceMessage } from "utils/service";
import * as ownerService from "../../service";
import Grid from "components/TinperBee/Grid";
import ExplainPop from "../IndexView/ExplainPop";
import ConnectorLogo from "components/ConnectorLogo";
import { SvgIconLib } from "iuap-ip-commonui-fe";
import "./index.less";
import ResizeObserver from "resize-observer-polyfill";
const { dragColumn } = Table;
const DragColumnTable = dragColumn(Table);

const alignCenter = { display: "flex", alignItems: "center" };
const ConnectorGrid = ({ data = [], onOperate, onAdd, ownerStore, ownerState }) => {
    const [isUpdating, setIsUpdating] = useState(false);
    const [size, setSize] = useState({ height: 0, width: 0 });
    const bodyRef = useRef();
    useEffect(() => {
        const resizeObserver = new ResizeObserver((entries) => {
            entries.forEach((entry) => {
                const { clientWidth, clientHeight } = entry.target;
                setSize({ width: clientWidth, height: clientHeight });
            });
        });
        if (bodyRef.current) {
            resizeObserver.observe(bodyRef.current);
        }
        return () => {
            resizeObserver.disconnect();
        };
    }, []);
    const connectPopContent = (record) => {
        const svgProps = {
            className: "connect-grid-logo",
            iconfontsize: { width: "50px", height: "50px" },
            logo: record.logo,
        };
        const YnfSvgIcon = SvgIconLib.getIconComponent(record?.logo);
        return (
            <div className="connect-grid-overlay-inner">
                {YnfSvgIcon ? <YnfSvgIcon width="50px" height="50px" className="connect-grid-logo" /> : <ConnectorLogo {...svgProps} />}
                <div className="connect-content">
                    <header className="connect-name">{record.name}</header>
                    <content className="connect-desc">{record.description}</content>
                </div>
            </div>
        );
    };
    const renderName = (value, record, index) => {
        return (
            <Popover
                fieldid="ublinker-routes-list-components-InfoDrawer-ConnectorGrid-7291324-Popover"
                placement="right"
                overlayInnerStyle={{ padding: "20px" }}
                content={connectPopContent(record)}
            >
                <span className="gateway-drawer-connector-btn-text">{value}</span>
            </Popover>
        );
    };
    const renderCount = (value, record, index) => {
        return <span /* className='gateway-drawer-connector-btn-text' */ style={{ textAlign: "center" }}>{value}</span>;
    };
    // 适配器表格列
    const columns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802BE", "连接器编码") /* "连接器编码" */,
            dataIndex: "code",
            width: 120,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802C2", "连接器名称") /* "连接器名称" */,
            dataIndex: "name",
            width: 120,
            render: renderName,
        },
        /* {
        title: '连接器版本',
        dataIndex: 'adapterVersion',
        width: 120,
    },  */
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802C6", "连接产品") /* "连接产品" */,
            dataIndex: "product",
            width: 100,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802C8", "产品归属厂商") /* "产品归属厂商" */,
            dataIndex: "isv",
            width: 100,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802B9", "连接配置数量") /* "连接配置数量" */,
            dataIndex: "itemSize",
            width: 120,
            render: renderCount,
        },
        {
            title: " ",
            dataIndex: "fa",
        },
    ];
    const columnsChild = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_1982A55A04D00037", "连接配置名称") /* "连接配置名称" */,
            dataIndex: "alias",
            // width: 50,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_1982A55A04D00038", "连接配置编码") /* "连接配置编码" */,
            dataIndex: "connectCode",
            width: 120,
            // render: function (value, record, index) {
            //     return (<div>
            //         {value}
            //         {
            //             record.isDefault && <span className="defaultGatewayLogo">{lang.templateByUuid("UID:P_UBL-FE_1982A55A04D00036", "默认") /* "默认" */}</span>
            //         }
            //     </div>)
            // }
        },
    ];
    const linkerDescription = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802BD", "连接器") /* "连接器" */,
            desc: lang.templateByUuid(
                "UID:P_UBL-FE_18D8CEF6041802BF",
                "高度封装的系统交互协议/业务系统；分为通用型、业务型。" //@notranslate
            ) /* "高度封装的系统交互协议/业务系统；分为通用型、业务型。" */,
            hasTag: true,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802C0", "如何使用") /* "如何使用" */,
            desc: lang.templateByUuid(
                "UID:P_UBL-FE_18D8CEF6041802C1",
                "连接器部署到对应的混合云网关上，即可连接到内网特定的业务系统和中间件。" //@notranslate
            ) /* "连接器部署到对应的混合云网关上，即可连接到内网特定的业务系统和中间件。" */,
        },
    ];

    const handleUndeploy = (record, type) => {
        console.log(record);
        Modal.confirm({
            fieldid: "************",
            title:
                lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802C4", "确定卸载", undefined, {
                    returnStr: true,
                }) +
                record.name +
                "?" +
                lang.templateByUuid("UID:P_UBL-FE_18E725900468004D", "卸载后内网将无法使用，请谨慎操作。", undefined, {
                    returnStr: true,
                }) /* "卸载后内网将无法使用，请谨慎操作。" */,
            okText: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802C5", "卸载") /* "卸载" */,
            onOk: () => {
                ownerStore.adapterOperation(record.adapterCode, "undeploy");
            },
        });
    };
    const handleUpgrade = async (record) => {
        setIsUpdating(true);
        let res = await autoServiceMessage({
            service: ownerService.adapterOperation(
                {
                    adapterId: record.adapterCode,
                    gatewayId: ownerState.selectedGateway.gatewayID,
                    action: "deploy",
                },
                { serviceCode: ownerState.serviceCodeDiwork }
            ),
            error: (err) => {
                Error(
                    err.msg || err.errmsg || err.errormsg || err.message || lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802BA", "未知错误") /* "未知错误" */
                );
                setIsUpdating(false);
            },
            success: () => {
                Success(
                    lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802BC", "操作成功", undefined, {
                        returnStr: true,
                    }) /* "操作成功" */
                );
                setIsUpdating(false);
            },
        });
        // const res = await ownerStore.adapterOperation(, '')
    };
    const hoverContent = (record) => {
        let node;
        if (isUpdating) {
            node = (
                <>
                    {/* <div className='upgrade-prompt'>
                    <img fieldid="ublinker-routes-list-components-InfoDrawer-ConnectorGrid-2042980-img" src={getLocalImg("gateway/prompt.png")} alt="" />
                    <span>{"正在升级，暂不支持操作"}</span>
                </div> */}
                </>
            );
        } else {
            if (record.isShowDeployAndUndeploy !== false) {
                node = (
                    <>
                        <Button
                            fieldid="ublinker-routes-list-components-InfoDrawer-ConnectorGrid-3154191-Button"
                            {...Grid.hoverButtonPorps}
                            onClick={() => handleUpgrade(record)}
                        >
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802C7", "部署") /* "部署" */}
                        </Button>
                        <Button
                            fieldid="ublinker-routes-list-components-InfoDrawer-ConnectorGrid-9970696-Button"
                            {...Grid.hoverButtonPorps}
                            onClick={() => handleUndeploy(record)}
                        >
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802C5", "卸载") /* "卸载" */}
                        </Button>
                    </>
                );
            } else {
                node = <></>;
            }
        }
        return node;
    };
    const handleSetDefault = (id) => {
        ownerStore.handleSetDefault(id);
    };
    const handleUpdateConnector = (record) => {
        ownerStore.handleUpdateConnector({ gatewayId: ownerState.selectedGateway.gatewayID, linkerCode: record.type });
    };
    const hoverContentChild = (record) => {
        let node;

        node = (
            <>
                {/* {record.isShowDefault && <Button fieldid="ublinker-routes-list-components-InfoDrawer-ConnectorGrid-3154191-Button"
                    {...Grid.hoverButtonPorps}
                    onClick={() => { console.log(record); handleSetDefault(record.id) }}>
                    {lang.templateByUuid("UID:P_UBL-FE_1984960605A80003", "设为默认") /* "设为默认" *}
                </Button>} */}
                {record.isShowUpdateGatewayConnector && (
                    <Button
                        fieldid="ublinker-routes-list-components-InfoDrawer-ConnectorGrid-31541911-Button"
                        {...Grid.hoverButtonPorps}
                        onClick={() => {
                            console.log(record);
                            handleUpdateConnector(record);
                        }}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_198A8F8204F80007", "更新连接器") /* "更新连接器" */}
                    </Button>
                )}
            </>
        );

        return node;
    };
    const expandedRowRender = (record, index, indent) => {
        return (
            <DragColumnTable
                fieldid="ublinker-routes-home-components-TablesList-index-44407711-Grid"
                rowKey={"id"}
                columns={columnsChild}
                data={record.items}
                hoverContent={hoverContentChild}
                dragborder
            />
        );
    };
    return (
        <>
            <header className="gateway-drawer-connector-header header-common">
                <Space fieldid="ublinker-routes-list-components-InfoDrawer-ConnectorGrid-2088306-Space" align="center" style={alignCenter}>
                    <span className="header-common-line"></span>
                    <span>
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802BD", "连接器") /* "连接器" */}
                        {`(${data.length})`}
                    </span>
                    <ExplainPop data={linkerDescription} />
                </Space>
            </header>
            <div ref={bodyRef} style={{ flex: 1 }}>
                <DragColumnTable
                    fieldid="ublinker-routes-list-components-InfoDrawer-ConnectorGrid-5346419-Grid"
                    rowKey={"id"}
                    columns={columns}
                    autoCheckedByClickRows={false}
                    data={data}
                    showRowNum={{ name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180075", "序号"), width: 65, base: 1 }}
                    hoverContent={hoverContent}
                    expandedRowRender={expandedRowRender}
                    dragborder
                    scroll={{ y: size.height - 52 }}
                    bodyStyle={{ minHeight: size.height - 52 }}
                />
            </div>
        </>
    );
};
export default ConnectorGrid;
