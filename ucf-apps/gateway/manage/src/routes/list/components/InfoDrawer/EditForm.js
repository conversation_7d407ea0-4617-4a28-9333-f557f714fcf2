import React, { useEffect, useState } from "react";
import { Select, Form, Input, Row, Col, AutoComplete, Table, Radio, Tooltip, Space } from "@tinper/next-ui";
import Modal from "components/TinperBee/Modal";
import "./index.less";
import { getLocalImg } from "utils/index";
import Grid from "components/TinperBee/Grid";
const { singleSelect } = Table;
let SingleSelectTable = singleSelect(Table, Radio);

const EditForm = ({ fields, formInstance, isEditing, formItemLayout, gutter, col, data }) => {
    const [options, setOptions] = useState([]);
    const [selectedRecord, setSelectedRecord] = useState({});
    const [modalId, setModalId] = useState(null);
    useEffect(() => {
        data && setOptions(data.ipList || []);
    }, [data]);
    const _formItemLayout = formItemLayout || {
        labelCol: {
            span: 6,
        },
        wrapperCol: {
            span: 16,
        },
    };
    const handleFillButtonClick = (field, otherElementProps) => {
        setModalId(field);
        otherElementProps.eventHandler();
    };
    const handleFillModalCancel = () => {
        setModalId(null);
        setSelectedRecord({});
    };
    const handleFillModalConfirm = (fieldName) => {
        formInstance.setFieldsValue({
            [fieldName]: selectedRecord[0][fieldName],
        });
        setModalId(null);
    };
    const getSelectedDataFunc = (record, index) => {
        setSelectedRecord(record);
    };
    const renderEditing = (filedItem) => {
        const { formItemProps: itemProps, formElementProps = {}, otherElementProps = {} } = filedItem;
        // debugger
        const defaultInput = <Input fieldid="ublinker-routes-list-components-InfoDrawer-EditForm-4486018-Input" {...formElementProps} />;
        let toolTip = null;
        if (filedItem.toolTip) {
            toolTip = (
                <Tooltip
                    fieldid="ublinker-routes-list-components-InfoDrawer-EditForm-5788861-Tooltip"
                    overlayStyle={{ maxWidth: "300px" }}
                    arrowPointAtCenter
                    overlay={<div>{filedItem.toolTip}</div>}
                >
                    <img
                        fieldid="ublinker-routes-list-components-InfoDrawer-EditForm-1234514-img"
                        style={{ cursor: "pointer", width: "14px", height: "14px", marginLeft: "5px", marginRight: "5px" }}
                        src={getLocalImg("gateway/help.png")}
                        alt=""
                    />
                </Tooltip>
            );
        }
        let children = null;
        switch (filedItem.type) {
            case "select":
                children = (
                    <Select
                        {...formElementProps}
                        fieldid="UCG-FE-routes-list-components-InfoDrawer-EditForm-5881758-Select"
                        allowClear
                        mode="combobox"
                    ></Select>
                );
                break;
            case "textarea":
                children = <Input.TextArea {...formElementProps} />;
                break;
            case "radioGroup":
                children = (
                    <Radio.Group {...formElementProps}>
                        <Space fieldid="ublinker-routes-list-components-InfoDrawer-EditForm-8906562-Space" direction={filedItem.direction} size={25}>
                            {formElementProps.options1.map((item) => (
                                <Space key={item.value} size={0} align="center">
                                    <Radio
                                        disabled={formElementProps.disabled}
                                        fieldid="ublinker-routes-list-components-InfoDrawer-EditForm-7135382-Radio"
                                        value={item.value}
                                    >
                                        {item.label}
                                    </Radio>
                                    {item.toolTip && (
                                        <Tooltip fieldid="ublinker-routes-list-components-InfoDrawer-EditForm-3882501-Tooltip" {...item.toolTip}>
                                            <img
                                                fieldid="ublinker-routes-list-components-InfoDrawer-EditForm-1298647-img"
                                                style={{
                                                    cursor: "pointer",
                                                    width: "14px",
                                                    height: "14px",
                                                }}
                                                src={getLocalImg("gateway/help.png")}
                                                alt=""
                                            />
                                        </Tooltip>
                                    )}
                                </Space>
                            ))}
                        </Space>
                    </Radio.Group>
                );
                break;
            case "modalTable": {
                return (
                    <>
                        <Modal
                            fieldid="ublinker-routes-list-components-InfoDrawer-EditForm-3858477-Modal"
                            show={modalId === itemProps.name}
                            title={otherElementProps.modalTitle}
                            onCancel={handleFillModalCancel}
                            onOk={() => handleFillModalConfirm(itemProps.name)}
                            width={800}
                            height={600}
                        >
                            <Grid
                                fieldid="ublinker-routes-ObjectSetting-components-IndexView-index-9043665-Grid"
                                columns={otherElementProps.modalTableColumns}
                                data={data.ipList}
                                radioSelect
                                selectedRowIndex={selectedRecord && selectedRecord.index}
                                getSelectedDataFunc={getSelectedDataFunc}
                            />
                            {/* <SingleSelectTable
                                    className="demo1302"
                                    bordered
                                    columns={otherElementProps.modalTableColumns}
                                    // autoCheckedByClickRows={false}
                                    // data={otherElementProps.data}
                                    data={data.ipList}
                                    selectedRowIndex={selectedRecord && selectedRecord.index}
                                    getSelectedDataFunc={getSelectedDataFunc} /> */}
                        </Modal>
                        <>
                            <Form.Item noStyle {...itemProps} label={null}>
                                {defaultInput}
                            </Form.Item>
                            {toolTip}
                            <a
                                fieldid="ublinker-routes-list-components-InfoDrawer-EditForm-2016552-a"
                                className="fill-btn"
                                onClick={() => handleFillButtonClick(itemProps.name, otherElementProps)}
                            >
                                {otherElementProps.buttonText} {/* 获取 */}
                            </a>
                        </>
                    </>
                );
            }
            default:
                children = defaultInput;
                break;
        }

        return (
            <>
                <Form.Item
                    noStyle
                    {...itemProps} // 设置name，不设置label
                    label={null}
                >
                    {children}
                </Form.Item>
                {toolTip}
            </>
        );
    };
    const translateText = (itemProps, value) => {
        switch (itemProps.name) {
            case "isCluster":
                return value
                    ? lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418021D", "集群部署") /* "集群部署" */
                    : lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418021C", "单一部署") /* "单一部署" */;
            case "channelProtocol":
                return value == "ws"
                    ? "WEBSOCKET" +
                          lang.templateByUuid("UID:P_UBL-FE_18E725900468004E", "长连接", undefined, {
                              returnStr: true,
                          }) /* "长连接" */
                    : lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418021B", "HTTP直连"); /* "HTTP直连" */
            case "enableLinkMonitor":
                return value
                    ? lang.templateByUuid("UID:P_UBL-FE_1B86E5BA04A00012", "开启") /* "开启" */
                    : lang.templateByUuid("UID:P_UBL-FE_1B86E5BA04A00010", "关闭") /* "关闭" */;
        }
    };
    const renderValue = (filedItem) => {
        const itemProps = filedItem.formItemProps;
        const noData = <span style={{ color: "#999999" }}>{lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418021E", "暂无") /* "暂无" */}</span>;
        // eslint-disable-next-line valid-typeof
        if (typeof data[itemProps.name] === undefined || typeof data[itemProps.name] === null) {
            return noData;
        } else if (filedItem?.formElementProps?.options1?.length) {
            const option = filedItem.formElementProps.options1.find((f) => f.value === data[itemProps.name]);
            return (
                <span className="form-no-editing-text" title={option?.label || ""}>
                    {option ? option.label : noData}
                </span>
            );
        }
        return (
            <span className="form-no-editing-text" title={data[itemProps.name]}>
                {data[itemProps.name]}
            </span>
        );
    };
    return (
        <Form
            fieldid="ublinker-routes-list-components-InfoDrawer-EditForm-263792-Form"
            noStyle
            {..._formItemLayout}
            // requiredMark={false}
            className="edit-form-comp"
            form={formInstance}
        >
            {
                <Row gutter={gutter}>
                    {fields.map((filedItem, index2) => {
                        if (!filedItem.hidden) {
                            const itemProps = filedItem.formItemProps;
                            return (
                                <Col span={filedItem.span} key={`${filedItem.name}-${index2}`}>
                                    <Form.Item className="edit-form-comp-item" colon {...itemProps} name={null}>
                                        {!isEditing ? (
                                            <span className="form-no-editing" title={data[itemProps.name]}>
                                                {renderValue(filedItem)}
                                                {filedItem.textExtraNodes && filedItem.textExtraNodes.map((item) => item)}
                                            </span>
                                        ) : (
                                            renderEditing(filedItem)
                                        )}
                                    </Form.Item>
                                </Col>
                            );
                        }
                    })}
                </Row>
            }
        </Form>
    );
};
export default EditForm;
