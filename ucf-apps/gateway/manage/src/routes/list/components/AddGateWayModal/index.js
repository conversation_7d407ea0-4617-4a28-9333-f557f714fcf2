import React, { useState, useEffect } from "react";
import Modal from "components/TinperBee/Modal";
import { FormControl, FormList, Radio } from "components/TinperBee";
import { Tooltip } from "@tinper/next-ui";
import { ipReg, portReg, codeReg, ipsEnterReg, ipWhiteReg } from "utils/regExp";
import { getLocalImg } from "utils/index";
const FormItem = FormList.Item;
import "./index.less";
// 添加网关弹窗
const AddGateWayModal = (props) => {
    const [form] = FormList.useForm();
    const { modalCommit, modalCancel, type, editGateway } = props;

    const isAdd = type === "add";
    //"新建网关"    // 编辑网关
    const modalTitle = isAdd
        ? lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180543", "新增") /* "新增" */
        : lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041800E2", "编辑"); /* "编辑" */

    const editId = isAdd ? "" : editGateway.id;

    const [gatewayCode, setGatewayCode] = useState(isAdd ? "" : editGateway.code);

    const [gatewayName, setGatewayName] = useState(isAdd ? "" : editGateway.name);
    const [deploymentModeValue, setDeploymentModeValue] = useState(false);

    // 表单字段验证方法
    const { validateFields } = form;

    // 取消
    const handleCancel = () => {
        modalCancel();
    };

    // 确认
    const handleCommit = () => {
        validateFields().then((values) => {
            if (isAdd) {
                // debugger
                // return
                modalCommit({
                    ...values,
                    name: gatewayName,
                    code: gatewayCode,
                });
            } else {
                modalCommit({
                    ...values,
                    id: editId,
                    name: gatewayName,
                    code: gatewayCode,
                });
            }
        });
    };
    const handleRadioChange = (value) => {
        setDeploymentModeValue(value);
    };
    const formItemLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 16 },
    };
    return (
        <>
            <Modal
                fieldid="ublinker-routes-list-components-AddGateWayModal-index-4344372-Modal"
                title={modalTitle}
                show={true}
                // width="620px"
                onCancel={handleCancel}
                onOk={handleCommit}
                className="gateway-create-modal"
            >
                <FormList
                    fieldid="ublinker-routes-list-components-AddGateWayModal-index-8115903-FormList"
                    size="sm"
                    // className="ucg-pad-20"
                    form={form}
                    name="form122"
                    labelAlign="right"
                    {...formItemLayout}
                >
                    <FormItem
                        fieldid="ublinker-routes-list-components-AddGateWayModal-index-5648840-FormItem"
                        label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803CF", "网关编码") /* "网关编码" */}
                        name="gatewayCode"
                        initialValue={gatewayCode}
                        validateTrigger="onChange"
                        rules={[
                            {
                                required: true,
                                message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803CB", "请输入") /* "请输入" */,
                            },
                            {
                                pattern: codeReg,
                                message: lang.templateByUuid(
                                    "UID:P_UBL-FE_18D8CEF6041803D4",
                                    "网关编码由英文、数字、下划线组成" //@notranslate
                                ) /* "网关编码由英文、数字、下划线组成" */,
                            },
                        ]}
                    >
                        <FormControl
                            fieldid="ublinker-routes-list-components-AddGateWayModal-index-6527617-FormControl"
                            size="sm"
                            placeholder={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803CB", "请输入", undefined, {
                                returnStr: true,
                            })}
                            maxLength="128"
                            onChange={setGatewayCode}
                        />
                    </FormItem>
                    <FormItem
                        fieldid="ublinker-routes-list-components-AddGateWayModal-index-2096704-FormItem"
                        label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803D8", "网关名称") /* "网关名称" */}
                        // "网关名称"
                        name="gatewayName"
                        initialValue={gatewayName}
                        validateTrigger="onChange"
                        rules={[
                            {
                                required: true,
                                message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803CB", "请输入") /* "请输入" */,
                            },
                        ]}
                    >
                        <FormControl
                            fieldid="ublinker-routes-list-components-AddGateWayModal-index-5594556-FormControl"
                            size="sm"
                            placeholder={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803CB", "请输入", undefined, {
                                returnStr: true,
                            })}
                            maxLength="128"
                            onChange={setGatewayName}
                        />
                    </FormItem>
                    <FormItem
                        fieldid="ublinker-routes-list-components-AddGateWayModal-index-6331746-FormItem"
                        label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803CE", "描述") /* "描述" */}
                        name="describe"
                    >
                        <FormControl.TextArea
                            row={5}
                            placeholder={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803CB", "请输入", undefined, {
                                returnStr: true,
                            })}
                            maxRows={8}
                        />
                    </FormItem>
                    <FormItem
                        fieldid="ublinker-routes-list-components-AddGateWayModal-index-3215737-FormItem"
                        label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803D3", "部署模式") /* "部署模式" */}
                        initialValue={deploymentModeValue}
                        name="isCluster"
                    >
                        <Radio.Group onChange={handleRadioChange}>
                            <Radio fieldid="ublinker-routes-list-components-AddGateWayModal-index-4562870-Radio" value={false} style={{ marginRight: 20 }}>
                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803D5", "单一部署") /* "单一部署" */}
                                <Tooltip
                                    fieldid="ublinker-routes-list-components-AddGateWayModal-index-6383011-Tooltip"
                                    arrowPointAtCenter
                                    overlay={
                                        <div>
                                            {
                                                lang.templateByUuid(
                                                    "UID:P_UBL-FE_1CC8ADDA0498000F",
                                                    "部署一个网关应用实例并启动，部署简单，推荐使用"//@notranslate
                                                ) /* "部署一个网关应用实例并启动，部署简单，推荐使用" */
                                            }
                                        </div>
                                    }
                                >
                                    <img
                                        fieldid="ublinker-routes-list-components-AddGateWayModal-index-4992793-img"
                                        style={{
                                            cursor: "pointer",
                                            width: "14px",
                                            height: "14px",
                                            marginLeft: "5px",
                                            marginRight: "5px",
                                            position: "absolute",
                                            zIndex: 999,
                                            top: 1,
                                            right: -23,
                                        }}
                                        src={getLocalImg("gateway/help.png")}
                                        alt=""
                                    />
                                </Tooltip>
                            </Radio>
                            <Radio fieldid="ublinker-routes-list-components-AddGateWayModal-index-6186852-Radio" value={true}>
                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803D7", "集群部署") /* "集群部署" */}
                                <Tooltip
                                    fieldid="ublinker-routes-list-components-AddGateWayModal-index-9942212-Tooltip"
                                    arrowPointAtCenter
                                    overlay={
                                        <div>
                                            {
                                                lang.templateByUuid(
                                                    "UID:P_UBL-FE_1CC8ADDA04980010",
                                                    "部署多个网关应用实例并启动，部署复杂度高但高可用，安装方式详见主页【安装说明】"//@notranslate
                                                ) /* "部署多个网关应用实例并启动，部署复杂度高但高可用，安装方式详见主页【安装说明】" */
                                            }
                                        </div>
                                    }
                                >
                                    <img
                                        fieldid="ublinker-routes-list-components-AddGateWayModal-index-9546734-img"
                                        style={{
                                            cursor: "pointer",
                                            width: "14px",
                                            height: "14px",
                                            marginLeft: "5px",
                                            marginRight: "5px",
                                            position: "absolute",
                                            zIndex: 999,
                                            top: 1,
                                            right: -23,
                                        }}
                                        src={getLocalImg("gateway/help.png")}
                                        alt=""
                                    />
                                </Tooltip>
                            </Radio>
                        </Radio.Group>
                    </FormItem>
                    {deploymentModeValue === true && (
                        <FormItem
                            fieldid="ublinker-routes-list-components-AddGateWayModal-index-7643561-FormItem"
                            label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803CC", "服务器IP") /* "服务器IP" */}
                            name="clusterIPs"
                            validateTrigger="onChange"
                            rules={[
                                { required: true, message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803CB", "请输入") /* "请输入" */ },
                                {
                                    pattern: ipsEnterReg,
                                    message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803CD", "请输入正确的ip") /* "请输入正确的ip" */,
                                },
                            ]}
                        >
                            <FormControl.TextArea
                                row={5}
                                placeholder={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803D0", "换行输入多个IP", undefined, {
                                    returnStr: true,
                                })}
                            />
                        </FormItem>
                    )}
                </FormList>
            </Modal>
        </>
    );
};

export default AddGateWayModal;
