import React, { useState, useEffect, useMemo, useCallback, forwardRef, useImperativeHandle, useRef } from "react";
import { Space, Button } from "@tinper/next-ui";
import Grid from "components/TinperBee/Grid";
import CustomTag from "components/CustomTag";
import { getAdapterListService, checkAdapterStatusService } from "./services";
import ConfigModal from "./ConfigModal";
import { Success, Error } from "utils/feedback";
import { formatTextArea } from "utils/index";
import ResizeObserver from "resize-observer-polyfill";
const useAdapters = (instanceInfo, _useGwConfigInfo, props) => {
    const { gatewayId, useMainTenantGateway } = instanceInfo;
    /** 适配器列表 */
    const [adapterList, setAdapterList] = useState([]);

    /** 待更新/部署 适配器 */
    const [updateAdapterInfo, setUpdateAdapter] = useState(null);

    /** 适配器操作action hide|deploy|undeploy|config */
    const [updateAction, setUpdateAction] = useState("hide");

    /** 适配器组件相关hooks 参数变化列表 */
    const useInputs = [gatewayId];

    const getAdapterList = useCallback((gwId) => {
        getAdapterListService(gwId, { serviceCode: props.ownerState.serviceCodeDiwork }).then((res) => {
            setAdapterList(res.data || []);
        });
    }, []);

    useEffect(() => {
        if (gatewayId) {
            getAdapterList(gatewayId);
        }
    }, useInputs);

    const handleCancel = useCallback((res) => {
        setUpdateAction("hide");
        setUpdateAdapter(null);
        if (res) {
            console.log(res);
            getAdapterList(gatewayId);
            getGatewayConfig();
        }
    }, useInputs);

    const handleUpdate = useCallback(async (adapter, action) => {
        setUpdateAdapter(adapter);
        setUpdateAction(action);
    }, useInputs);

    const checkAdapterStatus = useCallback(() => {
        checkAdapterStatusService({ gatewayId, useMainTenantGateway }, { serviceCode: props.ownerState.serviceCodeDiwork })
            .then(() => {
                Success(
                    lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802DC", "适配器运行正常", undefined, {
                        returnStr: true,
                    }) /* "适配器运行正常" */
                );
            })
            .catch((err) => {
                Error(formatTextArea(err.msg), 5);
            });
    }, useInputs);

    const adapterGridColumns = useMemo(() => {
        return [
            {
                title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802D2", "名称") /* "名称" */,
                dataIndex: "name",
                width: 300,
                render: (value, record) => {
                    return value + `(${record.appid})`;
                },
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802D6", "状态") /* "状态" */,
                dataIndex: "state",
                width: 100,
                render: (value) => {
                    return value === "ONLINE" ? (
                        <CustomTag status="success" tagName={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802D8", "已部署") /* "已部署" */} />
                    ) : (
                        "-"
                    );
                },
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802DA", "运行版本") /* "运行版本" */,
                dataIndex: "adapterversion",
                width: 110,
                render: Grid.renderText,
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802DB", "最新版本") /* "最新版本" */,
                dataIndex: "adapternewversion",
                width: 110,
                render: Grid.renderText,
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802DD", "部署时间") /* "部署时间" */,
                dataIndex: "starton",
                width: 180,
            },
        ];
    }, useInputs);

    const hoverContent = (record) => {
        return (
            <Space size={5}>
                <Button
                    {...Grid.hoverButtonPorps}
                    fieldid="UCG-FE-routes-list-components-Adapters-index-1380044-GridAction"
                    onClick={handleUpdate.bind(null, record, "deploy")}
                >
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802D3", "部署") /* "部署" */}
                </Button>
                <Button
                    {...Grid.hoverButtonPorps}
                    fieldid="UCG-FE-routes-list-components-Adapters-index-1973235-GridAction"
                    onClick={handleUpdate.bind(null, record, "config")}
                >
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802D4", "更新") /* "更新" */}
                </Button>
                <Button
                    {...Grid.hoverButtonPorps}
                    fieldid="UCG-FE-routes-list-components-Adapters-index-4247381-GridAction"
                    onClick={handleUpdate.bind(null, record, "undeploy")}
                >
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802D5", "卸载") /* "卸载" */}
                </Button>
            </Space>
        );
    };
    return {
        adapterGridColumns,
        adapterList,
        updateAdapterInfo,
        updateAction,
        handleCancel,
        checkAdapterStatus,
        handleUpdate,
        hoverContent,
    };
};

const AdaptersView = (props, ref) => {
    const { instanceInfo, getGatewayConfig } = props;
    const { adapterGridColumns, adapterList, updateAdapterInfo, updateAction, handleCancel, checkAdapterStatus, handleUpdate, hoverContent } = useAdapters(
        instanceInfo,
        getGatewayConfig,
        props
    );
    const [size, setSize] = useState({ height: 0, width: 0 });
    const bodyRef = useRef();
    useEffect(() => {
        const resizeObserver = new ResizeObserver((entries) => {
            entries.forEach((entry) => {
                const { clientWidth, clientHeight } = entry.target;
                setSize({ width: clientWidth, height: clientHeight });
            });
        });
        if (bodyRef.current) {
            resizeObserver.observe(bodyRef.current);
        }
        return () => {
            resizeObserver.disconnect();
        };
    }, []);
    useImperativeHandle(ref, () => {
        return {
            handleUpdateAdapter: handleUpdate,
        };
    });

    return (
        <>
            <div style={{ height: " 48px", paddingLeft: "8px", paddingRight: "8px", display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                <span
                    style={{
                        fontSize: "14px",
                        fontWeight: "600",
                        color: "#333333",
                    }}
                >
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802D7", "网关适配器管理")}
                </span>
                <Button fieldid="UCG-FE-routes-list-components-Adapters-index-3346074-GridAction" onClick={checkAdapterStatus}>
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802D9", "检查适配器状态") /* "检查适配器状态" */}
                </Button>
            </div>
            <div ref={bodyRef} style={{ flex: 1 }}>
                <Grid
                    scroll={{ y: size.height - 52 }}
                    bodyStyle={{ minHeight: size.height - 52 }}
                    fieldid="ublinker-routes-nc-components-Adapters-index-2168742-Grid"
                    columns={adapterGridColumns}
                    data={adapterList}
                    hoverContent={hoverContent}
                    showRowNum={{
                        name: lang.templateByUuid("UID:P_UBL-FE_1D9651E80450000A", "序号") /* "序号" */,
                        key: "_index",
                        width: 50,
                        base: 1,
                        type: "number",
                    }}
                />
            </div>

            <ConfigModal
                gatewayId={instanceInfo.gatewayId}
                adapterId={updateAdapterInfo ? updateAdapterInfo.appid : ""}
                adapterName={updateAdapterInfo ? updateAdapterInfo.name : ""}
                action={updateAction}
                onCancel={handleCancel}
                ownerState={props.ownerState}
            />
        </>
    );
};

export default forwardRef(AdaptersView);
