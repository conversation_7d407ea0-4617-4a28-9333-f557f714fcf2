.sys-connector-config-info-list {
  padding-bottom: 120px;
  background: #ffffff;
}
.sys-connector-config-info-item {
  padding: 20px 30px 30px;
  border-bottom: 1px dashed #E4E4E4;
}
.sys-connector-config-info-item:last-of-type {
  border-bottom: none;
}
.sys-connector-config-info-item .info-item-title {
  font-size: 16px;
  line-height: 18px;
  color: #333333;
  font-weight: bold;
  margin-bottom: 20px;
}
.sys-connector-config-info-item .info-item-title-expand {
  font-weight: normal;
  float: right;
  font-size: 12px;
}
.sys-connector-config-info-item .info-item-content {
  overflow: hidden;
  height: 0;
  transition: height 0.2s;
}
.sys-connector-config-info-item .info-item-content.expand {
  height: auto;
  min-height: 50px;
}
.sys-connector-config-info-item .config-action-content {
  padding-left: 140px;
}
.sys-connector-config-info-item .config-tip {
  font-size: 12px;
  color: #999999;
  line-height: 20px;
}
.sys-connector-config-info-item .config-text {
  color: #333333;
  font-size: 12px;
  line-height: 22px;
  vertical-align: middle;
}
.sys-connector-config-info-item .config-text > * {
  vertical-align: middle;
}
.sys-connector-config-info-item .config-text > i.cl {
  font-size: 18px;
}
.sys-connector-config-info-item .config-text-second {
  color: #333333;
  font-size: 12px;
  line-height: 22px;
  vertical-align: middle;
  color: #666;
}
.sys-connector-config-info-item .config-text-second > * {
  vertical-align: middle;
}
.sys-connector-config-info-item .config-text-second > i.cl {
  font-size: 18px;
}
.sys-connector-config-info-item .config-text-third {
  color: #333333;
  line-height: 22px;
  vertical-align: middle;
  padding-top: 5px;
  color: #999999;
  font-size: 12px;
  line-height: 18px;
}
.sys-connector-config-info-item .config-text-third > * {
  vertical-align: middle;
}
.sys-connector-config-info-item .config-text-third > i.cl {
  font-size: 18px;
}
.sys-connector-config-info-item .config-img {
  margin: 10px 0;
  width: 80%;
  height: auto;
}
.sys-connector-config-info-item .config-img > img {
  width: 100%;
  height: 100%;
}
.sys-connector-config-info-item .config-action-form {
  padding-left: 80px;
}
.sys-connector-config-info-item .config-action-form.md-action {
  padding-left: 80px;
}
.sys-connector-config-info-item .config-action-form.sm-action {
  padding-left: 60px;
}
.sys-connector-config-info-item .config-action-form.ucg-ma-form .u-form-item .u-select,
.sys-connector-config-info-item .config-action-form.ucg-ma-form .u-form-item .u-form-control {
  width: 320px;
}
.sys-connector-config-ques {
  width: 100%;
  padding: 15px 20px;
  background: #F9F9F9;
  border: 1px solid #E4E4E4;
  margin-bottom: 10px;
  font-size: 12px;
  line-height: 22px;
  color: #333333;
  position: relative;
  height: 52px;
  overflow: hidden;
}
.sys-connector-config-ques > *:first-of-type {
  padding-bottom: 15px;
}
.sys-connector-config-ques .expand-icon {
  position: absolute;
  right: 20px;
  top: 15px;
  color: #505F79;
  cursor: pointer;
  transform: scale(0.8);
}
.sys-connector-config-ques:last-of-type {
  margin-bottom: 0;
}
.sys-connector-config-ques.expand {
  height: auto;
}
.sys-connector-config-ques .ques-img {
  padding: 0 15px 15px;
}
.sys-connector-config-ques .ques-img > img {
  max-width: 70%;
  height: auto;
}
/*# sourceMappingURL=index.css.map */