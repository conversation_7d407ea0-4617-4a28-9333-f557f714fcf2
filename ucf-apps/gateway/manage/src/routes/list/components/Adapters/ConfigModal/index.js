import React, { useState, useEffect, useMemo, useCallback } from "react";
import { FormControl, InputNumber, Radio, FormList } from "components/TinperBee";
import { Modal } from "@tinper/next-ui";
// import FormList from "components/TinperBee/Form";
import commonText from "constants/commonText";
import { autoServiceMessage } from "utils/service";
import { codeReg, codeMessage, ipReg, portReg, ipWhiteReg, ipsEnterReg, integerReg } from "utils/regExp";
import { getAdapterConfigByTemplateService, updateAdapterByTemplateService } from "../services";
import { getLocalImg } from "utils/index";

const FormItem = FormList.Item;
const labelCol = 100;
/**
 * 适配器配置弹窗
 * @param {Object} props
 * @param {String} props.gatewayId -网关Id
 * @param {String} props.adapterId -适配列表中的appId
 * @param {String} [props.action=deploy部署|config更新|undeploy卸载|hide隐藏]
 * @returns {*}
 * @constructor
 */
const ConfigModal = (props) => {
    const [form] = FormList.useForm();
    const { getFieldProps, getFieldError, validateFields, setFieldsValue } = form;
    const formItemLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 16 },
    };
    const { gatewayId, adapterId, adapterName, action } = props;
    const [show, setShow] = useState(false);
    const [templates, setTemplates] = useState([]);

    const getTemplates = useCallback(async () => {
        let res = await autoServiceMessage({
            service: getAdapterConfigByTemplateService(
                {
                    adapterId,
                    gatewayId,
                },
                { serviceCode: props.ownerState.serviceCodeDiwork }
            ),
        });
        if (res) {
            setTemplates(res.data);
        }
    }, [gatewayId, adapterId]);

    const updateAdapterInputs = [action, gatewayId, adapterId, templates];

    const updateAdapter = useCallback(async (values) => {
        const _values = Object.keys(values).map((item) => {
            const isNumber = templates.find((tem) => tem.code === item).type === "number";
            return {
                code: item,
                value: isNumber ? values[item] : values[item],
            };
        });
        let res = await autoServiceMessage({
            service: updateAdapterByTemplateService(
                {
                    gatewayId,
                    action,
                    adapterId,
                    values: _values,
                },
                { serviceCode: props.ownerState.serviceCodeDiwork }
            ),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804F6", "操作成功！", undefined, {
                returnStr: true,
            }) /* "操作成功！" */,
        });

        if (res) {
            props.onCancel(res);
        }
    }, updateAdapterInputs);

    useEffect(() => {
        if (action === "hide") {
            setShow(false);
            setTemplates([]);
        } else {
            if ((action === "deploy" || action === "config") && adapterId) {
                getTemplates();
            }
            setShow(true);
        }
    }, [action, adapterId, gatewayId]);

    const modalTitle = useMemo(() => {
        let title = "";
        switch (action) {
            case "deploy":
                title =
                    lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804F9", "部署：", undefined, {
                        returnStr: true,
                    }) + adapterName;
                break;
            case "config":
                title =
                    lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804FA", "更新：", undefined, {
                        returnStr: true,
                    }) + adapterName;
                break;
            case "undeploy":
                title =
                    lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804FC", "卸载：", undefined, {
                        returnStr: true,
                    }) + adapterName;
                break;
            case "hide":
            default:
                title = "-";
                break;
        }
        return title;
    }, [action, adapterName]);

    const getRules = (templateItem) => {
        const { type, code, defaultValue, required, name, note } = templateItem;
        let rules = [
            {
                required: required,
                message:
                    lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804F7", "请输入", undefined, {
                        returnStr: true,
                    }) + name,
            },
        ];
        const isNumber = type === "number";
        if (isNumber) {
            let { max, min } = templateItem;
            rules = rules.concat([
                {
                    type: "number",
                    min,
                    max,
                    message:
                        lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804F7", "请输入", undefined, {
                            returnStr: true,
                        }) +
                        min +
                        "~" +
                        max +
                        lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804F8", "范围内的数字", undefined, {
                            returnStr: true,
                        }) /* "范围内的数字" */,
                    transform: (value) => {
                        let _value = Number(value);
                        return isNaN(_value) ? value : _value;
                    },
                },
                { pattern: integerReg, message: lang.templateByUuid("UID:P_UBL-FE_1A6A374205E80017", "禁止输入小数") /* "禁止输入小数" */ },
            ]);
        }
        const isText = type === "text"; //IP  whitelist
        if (isText) {
            rules = [{ required: false, message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418030E", "请输入") /* "请输入" */ }];
            if (code == "whitelist") {
                rules = [
                    ...rules,
                    {
                        pattern: ipWhiteReg,
                        message: lang.templateByUuid(
                            "UID:P_UBL-FE_1A6A374205E80016",
                            "格式为X.X.X.X，X为0-255之间,多个IP用逗号分隔"//@notranslate
                        ) /* "格式为X.X.X.X，X为0-255之间,多个IP用逗号分隔" */,
                    },
                ];
            }
        }
        return rules;
    };
    const renderFormItem = useCallback((templateItem) => {
        let InputNode = null;
        const { type, code, defaultValue, required, name, note } = templateItem;
        // let rules = [{
        // 	required: required,
        // 	message: "请输入" + name
        // }]
        // const isNumber = type === 'number';
        // if (isNumber) {
        // 	let { max, min } = templateItem;
        // 	rules = rules.concat([{
        // 		type: 'number', min, max,
        // 		message: "请输入 <%= min %>~<%= num %> 范围内的数字",
        // 		transform: (value) => {
        // 			let _value = Number(value);
        // 			return isNaN(_value) ? value : _value
        // 		}
        // 	}])
        // }

        // const fieldProps = getFieldProps(code, {
        // 	initialValue: defaultValue,
        // 	rules: rules,
        // })
        switch (type) {
            case "number":
            case "text":
            case "password":
                InputNode = (
                    <FormControl
                        fieldid="ublinker-nc-components-Adapters-ConfigModal-index-8001096-FormControl"
                        // {...fieldProps}
                        type={type}
                        placeholder={note}
                        precision={0}
                    />
                );
                break;
            case "radio":
                const { list } = templateItem;
                InputNode = (
                    <Radio.Group
                    // {...fieldProps}
                    >
                        {list.map((item) => {
                            const { value, name } = item;
                            return (
                                <Radio fieldid="ublinker-nc-components-Adapters-ConfigModal-index-7909045-Radio" value={value} key={value}>
                                    {name}
                                </Radio>
                            );
                        })}
                    </Radio.Group>
                );
                break;
        }

        return InputNode;
    }, []);

    const renderTemplates = (templates) => {
        let templateItems = templates.map((templateItem) => {
            const { tenantShow } = templateItem;
            if (tenantShow) {
                const { name, required, code, defaultValue } = templateItem;
                return (
                    <FormItem
                        fieldid="ublinker-nc-components-Adapters-ConfigModal-index-7902370-FormItem"
                        key={code}
                        label={name}
                        // required={required}
                        // labelCol={labelCol}
                        // error={getFieldError(code)}
                        name={code}
                        initialValue={defaultValue}
                        rules={getRules(templateItem)}
                        // // rules={[{
                        // // 	required:required,
                        // // 	message: "请输入" + name
                        // // }]
                        // }
                    >
                        {renderFormItem(templateItem)}
                    </FormItem>
                );
            } else {
                return null;
            }
        });

        if (templateItems.length > 0) {
            return (
                <FormList
                    fieldid="ublinker-nc-components-Adapters-ConfigModal-index-1394534-FormList"
                    form={form}
                    name="form122"
                    labelAlign="right"
                    {...formItemLayout}
                    className="ucg-pad-20-30"
                >
                    {templateItems}
                </FormList>
            );
        } else {
            return (
                <div style={{ textAlign: "center" }} className="ucg-pad-20-30">
                    <div className="ucg-pad-b-10">
                        <img
                            fieldid="ublinker-nc-components-Adapters-ConfigModal-index-5109664-img"
                            style={{ width: "64px", height: "auto" }}
                            src={getLocalImg("empty.png")}
                            alt=""
                        />
                    </div>
                    <p style={{ fontSize: "14px" }}>
                        {
                            lang.templateByUuid(
                                "UID:P_UBL-FE_18D8CEF6041804FD",
                                "此适配器无需配置配置参数，请点击确认直接保存"//@notranslate
                            ) /* "此适配器无需配置配置参数，请点击确认直接保存" */
                        }
                    </p>
                </div>
            );
        }
    };

    const getContent = () => {
        if (action === "hide") {
            return null;
        } else if (action === "undeploy") {
            return <p className="ucg-pad-20-30">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804FB", "确认卸载此适配器？") /* "确认卸载此适配器？" */}</p>;
        } else {
            return renderTemplates(templates);
        }
    };

    const handleOk = useCallback(() => {
        // validateFields((error, values) => {
        // 	if (!error) {
        // 		updateAdapter(values)
        // 	}
        // })
        validateFields().then((values) => {
            updateAdapter(values);
        });
    }, updateAdapterInputs);

    return (
        <Modal
            fieldid="ublinker-nc-components-Adapters-ConfigModal-index-7311880-Modal"
            show={show}
            title={modalTitle}
            onCancel={props.onCancel}
            onOk={handleOk}
            width={700}
            size="md"
        >
            {getContent()}
        </Modal>
    );
};

export default ConfigModal;
