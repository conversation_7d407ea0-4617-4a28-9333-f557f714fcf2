import React, { Fragment, useState } from "react";
import { Dropdown, Button, Icon, Progress } from "components/TinperBee";
import { Modal } from "components/TinperBee";
import Menu from "components/TinperBee/Menu";
import Logo from "components/Card/Logo";
import { PRIVATE } from "utils/util";
console.log("gateway", PRIVATE);
import { autoServiceMessage } from "utils/service";
import { downloadOss } from "utils";
import { getGateWayDownloadUrl, GateWayDownload } from "../../service";
import "./index.less";
import styles from "./index.modules.css";
import { gateWayOssVersion } from "../IndexView";
import CreateCard from "components/CreateCard";
import onlineSvg from "static/images/gateway/online.svg";
import outlineSvg from "static/images/gateway/outline.svg";
import classNames from "classnames";
import refreshSvg from "static/images/gateway/refresh.svg";
const GateWayCard = (props) => {
    const {
        item = {},
        item: { fromMainTenant } = {},
        index,
        title = "",
        status,
        code,
        gatewayID,
        isDefault,
        deleteCallback,
        editCallback,
        setDefaultCallback,
        resetGateWayCallback,
        downLoadSecretKeyCallback,
        createGateway,
    } = props;

    const [clickedDownloadClient, setClickedDownloadClient] = useState(false);
    const [percent, setPercent] = useState(0);
    const handleDelete = () => {
        Modal.confirm({
            fieldid: "************",
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418014A", "确定删除此网关？") /* "确定删除此网关？" */,
            onOk: () => {
                deleteCallback();
            },
        });
    };

    const getMenu = () => {
        return (
            <div className="gate-way-bottom-list-button-container">
                {/* <div className="gate-way-bottom-list-button" onClick={setDefaultCallback}>
                    {设为默认 }
                </div> */}
                <div
                    className={`gate-way-bottom-list-button`}
                    onClick={editCallback}
                    fieldid="ublinker-routes-list-components-GateWayCard-index-43820051-Dropdown-div"
                >
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802F4", "详情", undefined, {
                        returnStr: true,
                    })}
                </div>
                {!item?.num && item.status !== 1 ? (
                    <div
                        className={`gate-way-bottom-list-button`}
                        onClick={handleDelete}
                        fieldid="ublinker-routes-list-components-GateWayCard-index-43820052-Dropdown-div"
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418013F", "删除") /* "删除" */}
                    </div>
                ) : null}
            </div>
        );
    };
    const handleDownload = async ({ key }) => {
        setClickedDownloadClient(true);
        try {
            let res = await autoServiceMessage({
                service: getGateWayDownloadUrl({
                    sysType: gateWayOssVersion[key].sysType,
                    bit: gateWayOssVersion[key].bit,
                }),
                error: (err) => {
                    setClickedDownloadClient(false);
                    return Modal.info({
                        backdropClosable: false,
                        width: "800",
                        content: <div dangerouslySetInnerHTML={{ __html: err.message }}></div>,
                        cancelButtonProps: {
                            style: {
                                display: "none",
                            },
                        },
                    });
                },
            });
            downloadOss(res);
        } catch (error) {
            console.log("error", error);
        }
        setClickedDownloadClient(false);
    };

    const menuOnCard = (
        <Menu fieldid="ublinker-routes-list-components-GateWayCard-index-2577861-Menu" onClick={handleDownload}>
            <Menu.Item fieldid="UCG-FE-routes-list-components-GateWayCard-index-2936552-Menu.Item" key={0}>
                windows x64
            </Menu.Item>
            {/* <Menu.Item fieldid="UCG-FE-routes-list-components-GateWayCard-index-6278175-Menu.Item" key={1}>windows x86</Menu.Item> */}
            <Menu.Item fieldid="UCG-FE-routes-list-components-GateWayCard-index-1906118-Menu.Item" key={2}>
                linux x64
            </Menu.Item>
            {/* <Menu.Item fieldid="UCG-FE-routes-list-components-GateWayCard-index-5444877-Menu.Item" key={3}>linux x86</Menu.Item> */}
            <Menu.Item key={4}>arrch64</Menu.Item>
        </Menu>
    );
    const responseCardClass = classNames(styles["response-card"], "gate-way-card", {
        "gateway-online": status === 1,
        "gateway-outline": status === 2,
    });
    const statusTagClass = classNames(styles["gateway-status"], { [styles.enable]: status === 1, [styles.disable]: status === 2 });
    return (
        <>
            {item?.type === "add" ? (
                <CreateCard
                    fieldid="670be3f0-6f6f-4b69-8ec6-61099830b1d3"
                    text={lang.templateByUuid("UID:P_UBL-FE_1C007BD405D80008", "新增") /* "新增" */}
                    classProp={responseCardClass}
                    onClick={createGateway}
                />
            ) : (
                <div fieldid="6cc92cce-53cb-4b6e-82c3-4a54d6cd020e" className={responseCardClass}>
                    <div className="gate-way-card-header" onClick={!fromMainTenant && editCallback}>
                        {/* <Logo className="gate-way-logo" logo={null} title={title || "".substr(0, 2)} /> */}
                        <div className={styles["gateway-logo"]}>
                            <img src={status === 1 ? onlineSvg : outlineSvg} alt="logo" />
                        </div>
                        <div className="gate-way-card-header-right">
                            <div className="gate-way-card-header-right-title" title={title}>
                                {fromMainTenant
                                    ? lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180135", "主", undefined, {
                                          returnStr: true,
                                      }) + title
                                    : title}
                            </div>
                            <div style={{ display: "flex", alignItems: "center" }}>
                                <div className={statusTagClass}>
                                    <span className={`gate-way-card-header-right-status`}>
                                        {
                                            status === 1
                                                ? lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418013A", "在线") /* "在线" */
                                                : lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418013B", "离线") /* "离线" */
                                        }
                                    </span>
                                </div>
                                <img
                                    fieldid="6f6b8870-661a-408c-a7ad-90794e43e167"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        resetGateWayCallback();
                                    }}
                                    className={styles["refresh-svg"]}
                                    src={refreshSvg}
                                    alt="refresh"
                                />
                            </div>
                            {/* <div title={"重置网关状态"} className="gate-way-card-header-right-button-reflash" onClick={resetGateWayCallback}>
                                    </div> */}
                            <div></div>
                        </div>
                    </div>
                    <div className="gate-way-card-content" onClick={!fromMainTenant && editCallback}>
                        <aside className="gate-way-card-content-left">
                            <div
                                className="gate-way-code"
                                title={`${lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180142", "网关ID：", undefined, {
                                    returnStr: true,
                                })}${gatewayID}`}
                            >
                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180143", "网关ID") /* "网关ID" */}：
                                {gatewayID || (
                                    <span style={{ color: "#999999" }}>{lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180138", "暂无") /* "暂无" */}</span>
                                )}
                                {/* 网关ID */}
                            </div>
                            <div
                                className="gate-way-code"
                                title={`${lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180146", "网关编码：", undefined, {
                                    returnStr: true,
                                })}${code}`}
                            >
                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180147", "网关编码") /* "网关编码" */}：
                                {code || <span style={{ color: "#999999" }}>{lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180138", "暂无") /* "暂无" */}</span>}
                                {/* 网关编码 */}
                            </div>
                            {item?.isCluster ? (
                                <>
                                    <div
                                        className="gate-way-code"
                                        title={`${lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180148", "Nginx访问地址：", undefined, {
                                            returnStr: true,
                                        })}${item.clusterNgAddress}`}
                                    >
                                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180149", "Nginx访问地址") /* "Nginx访问地址" */}：
                                        {item.clusterNgAddress || (
                                            <span style={{ color: "#999999" }}>
                                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180138", "暂无") /* "暂无" */}
                                            </span>
                                        )}
                                        {/* Nginx访问地址 */}
                                    </div>
                                </>
                            ) : (
                                <>
                                    <div
                                        className="gate-way-code"
                                        title={`${lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418014B", "内网访问IP：", undefined, {
                                            returnStr: true,
                                        })}${item?.realIp || lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180138", "暂无")}`} //@notranslate
                                    >
                                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418014C", "内网访问IP") /* "内网访问IP" */}：
                                        {item.realIp || (
                                            <span style={{ color: "#999999" }}>
                                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180138", "暂无") /* "暂无" */}
                                            </span>
                                        )}
                                        {/* 内网访问IP */}
                                    </div>
                                    <div
                                        className="gate-way-code"
                                        title={`${lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180136", "内网访问端口：", undefined, {
                                            returnStr: true,
                                        })}${item.port}`}
                                    >
                                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180137", "内网访问端口") /* "内网访问端口" */}：
                                        {item.port == null ? (
                                            <span style={{ color: "#999999" }}>
                                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180138", "暂无") /* "暂无" */}
                                            </span>
                                        ) : (
                                            item.port
                                        )}
                                        {/* 内网访问端口 */}
                                    </div>
                                </>
                            )}
                        </aside>
                        <div className="gate-way-card-content-divide"></div>
                        <aside className={`gate-way-card-content-right ${status === 1 ? "online-style" : ""}`}>
                            <span className="linker-number">{item?.num || 0}</span>
                            <span>{lang.templateByUuid("UID:P_UBL-FE_1C39D49404C80006", "连接配置") /* "连接配置" */}</span>
                        </aside>
                    </div>
                    <div className="gate-way-card-divide"></div>
                    <footer className="gateway-operations">
                        <Dropdown fieldid="ublinker-routes-list-components-GateWayCard-index-1521848-Dropdown" overlay={menuOnCard} placement="bottom">
                            <Button
                                fieldid="ublinker-routes-list-components-GateWayCard-index-3281611-Button"
                                type="link"
                                icon="uf-cloud-o-down"
                                title={lang.templateByUuid("UID:P_UBL-FE_1C3CF25005200013", "下载客户端") /* "下载客户端" */}
                                disabled={clickedDownloadClient}
                            >
                                {lang.templateByUuid("UID:P_UBL-FE_1C3CF25005200014", "1.下载客户端") /* "1.下载客户端" */}
                            </Button>
                        </Dropdown>
                        <Button
                            fieldid="ublinker-routes-list-components-GateWayCard-index-4675171-Button"
                            type="link"
                            icon="uf-weidongjie"
                            onClick={downLoadSecretKeyCallback}
                            title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180144", "下载密钥", undefined, {
                                returnStr: true,
                            })}
                        >
                            2.{lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180144", "下载密钥") /* "下载密钥" */}
                        </Button>
                        <Button
                            fieldid="ublinker-routes-list-components-GateWayCard-index-6315184-Button"
                            type="link"
                            icon="uf-zoom-out"
                            onClick={resetGateWayCallback}
                            title={lang.templateByUuid("UID:P_UBL-FE_1C3CF25005200015", "测试连接") /* "测试连接" */}
                        >
                            {lang.templateByUuid("UID:P_UBL-FE_1C3CF25005200016", "3.测试连接") /* "3.测试连接" */}
                        </Button>
                        <Dropdown
                            fieldid="ublinker-routes-list-components-GateWayCard-index-4382005-Dropdown"
                            disabled={fromMainTenant}
                            trigger={["hover"]}
                            overlay={getMenu()}
                            overlayClassName="gateway-3dot-overlay"
                            animation="slide-up"
                        >
                            <Icon
                                fieldid="ublinker-routes-list-components-GateWayCard-index-5872572-Icon"
                                type="uf-3dot-v"
                                style={{ cursor: "pointer", color: `${fromMainTenant ? "#00000040" : "#4A8BF0"}` }}
                            />
                        </Dropdown>
                    </footer>
                    {/* {status === 1 && (
                        <div class={styles.watermark}>
                            <img src={watermarkSvg} alt="Watermark" />
                        </div>
                    )} */}
                    {percent ? (
                        <Progress
                            fieldid="UCG-FE-routes-list-components-GateWayCard-index-6705223-Progress"
                            percent={percent}
                            style={{ position: "fixed", left: "1%", bottom: "10%", zIndex: "100" }}
                        />
                    ) : null}

                    {/* 右上角默认按钮 */}
                    {isDefault ? <div className="default-gate-way">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180139", "默认") /* "默认" */}</div> : null}
                    {item.isCluster ? <div className="jiqun-gate-way">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418013D", "集群") /* "集群" */}</div> : null}
                </div>
            )}
        </>
    );
};

export default GateWayCard;
