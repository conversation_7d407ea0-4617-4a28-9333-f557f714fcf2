.iuapIpaasDataintegrationFe-gateway {
    .gate-way-card {
        height: 234px;
        background: #ffffff;
        box-shadow: 0px 0px 16px 0px rgba(173, 180, 188, 0.2);
        display: flex;
        flex-direction: column;
        position: relative;
        padding: 20px 0 0;
        overflow: hidden;

        &-header {
            height: 55px;
            display: flex;
            padding: 0 13px;
            cursor: pointer;
            .gate-way-logo {
                width: 55px;
                height: 55px;
                text-align: center;
                font-size: 28px;
                font-family:
                    PingFangSC-Regular,
                    PingFang SC;
                font-weight: 400;
                color: #ffffff;
                line-height: 40px;
                flex: 0 0 55px;
            }
            &-right {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                // flex: 1;
                overflow: hidden;
                padding-left: 10px;

                &-title {
                    width: 100%;
                    font-size: 16px;
                    font-family: PingFang-SC-Heavy, PingFang-SC;
                    font-weight: 800;
                    color: #000000;
                    line-height: 22px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
                &-status {
                    display: inline-block;
                    padding: 0 6px;
                    font-size: 12px;
                    font-family: PingFang-SC-Medium, PingFang-SC;
                    font-weight: 500;
                    line-height: 22px;
                    // width: fit-content;
                    height: 22px;
                    text-align: center;
                    margin-right: 4px;
                }

                &-button {
                    &-reflash {
                        width: 16px;
                        height: 16px;
                        margin-left: 6px;
                        border-radius: 4px;
                        border: 1px solid #d0d0d0;
                        cursor: pointer;
                        background-image: url(~static/images/gateway/reflash.svg);
                        background-size: 100% 100%;
                    }
                }
            }
        }
        &-content {
            display: flex;
            height: 111px;
            width: 100%;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            &-left {
                overflow: hidden;
                flex: 1;
                padding: 9px 20px 13px 23px;
            }
            &-divide {
                width: 1px;
                height: 62px;
                border: 1px solid #e8e9eb;
            }
            &-right {
                text-align: center;
                width: 119px;
                height: 100%;
                padding-top: 9px;
                font-size: 12px;
                font-family:
                    PingFangSC-Regular,
                    PingFang SC;
                font-weight: 400;
                color: #333333;
                line-height: 22px;
                display: flex;
                flex-direction: column;
                align-items: center;
            }
            .linker-number {
                font-size: 45px;
                line-height: 45px;
                height: 45px;
            }
            .gate-way-code {
                font-size: 12px;
                font-family:
                    PingFangSC-Regular,
                    PingFang SC;
                font-weight: 400;
                color: #333333;
                line-height: 22px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }

        &-divide {
            width: 100%;
            height: 1px;
            border: 1px solid #e8e9eb;
        }
        .gateway-operations {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 12px;
            font-family:
                PingFangSC-Regular,
                PingFang SC;
            font-weight: 400;
            color: #4a8bf0;
            line-height: 22px;
            padding-right: 15px;
            overflow: hidden;
            .wui-button {
                flex: 1;
                padding: 0;
                overflow: hidden; //超出的文本隐藏
                text-overflow: ellipsis; //溢出用省略号显示
                white-space: nowrap;
            }
        }
        .gate-way-bottom {
            flex: 1;
            display: flex;
            align-items: flex-end;
            justify-content: space-between;
            font-size: 12px;
            font-family:
                PingFangSC-Regular,
                PingFang SC;
            font-weight: 400;
            color: #4a8bf0;
            line-height: 22px;
            padding: 0 20px;

            &-button {
                cursor: pointer;
            }
            &-button.point {
                padding-bottom: 5px;
                .point-item {
                    display: inline-block;
                    width: 3px;
                    height: 3px;
                    background: #999999;
                    border-radius: 50%;
                    margin-right: 2px;
                }
            }
        }
        .btndisabled {
            color: #b9bcc2;
            .gate-way-bottom-button {
                cursor: auto;
            }
        }
        .default-gate-way {
            position: absolute;
            right: 0;
            top: 0;
            width: 38px;
            height: 16px;
            background: #ecf3fe;
            border-radius: 0px 6px 0px 8px;
            font-weight: 400;
            color: #588ce9;
            line-height: 16px;
            text-align: center;
            font-size: 10px;
        }
        .jiqun-gate-way {
            background: #505766;
            border-radius: 0px 0px 0px 8px;
            position: absolute;
            right: 0;
            top: 0;
            padding: 2px 8px;
            font-size: 12px;
            font-family:
                PingFangSC-Regular,
                PingFang SC;
            font-weight: 400;
            color: #ffffff;
        }
    }
    .gateway-online {
        background: linear-gradient(180deg, #e2f0ff 0px, #ffffff 94px, #ffffff 100%);
        border-top: 4px;
        border-top-style: solid;
        border-image: linear-gradient(270deg, #4689ff 0%, #74a6ff 100%) 1 stretch;
    }
    .gateway-outline {
        background: linear-gradient(180deg, rgba(226, 240, 255, 0.5) 0px, rgba(255, 255, 255, 0.5) 100px, #ffffff 100%);
        border-top: 4px;
        border-top-style: solid;
        border-image: linear-gradient(270deg, rgba(70, 137, 255, 0.3) 0%, rgba(116, 166, 255, 0.3) 100%) 1 stretch;
    }
    .gate-way-bottom-list-button-container {
        border: 1px solid #dfe4e8;
        border-radius: 5px;
        box-shadow: 0px 2px 13px 0px rgba(0, 0, 0, 0.1);

        .gate-way-bottom-list-button {
            cursor: pointer;
            // width: 80px;
            padding: 0 10px;
            height: 46px;
            background-color: #fff;
            line-height: 46px;
            text-align: center;
        }

        .gate-way-bottom-list-button:hover {
            background-color: #ebecf0;
        }

        .disabled {
            color: #c4c4c4;
            cursor: not-allowed;
        }
    }

    .gateway-3dot-overlay {
        .gate-way-bottom-list-button {
            padding: 0 15px;
            height: 35px;
            line-height: 35px;
        }
    }
    .aa .wui-modal-dialog .wui-modal-content.react-draggable {
        width: 700px !important;
    }
}
