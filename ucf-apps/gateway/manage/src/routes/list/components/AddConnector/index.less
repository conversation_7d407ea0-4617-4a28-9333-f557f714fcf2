.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.add-connector {
  .wui-modal-body {
    height: 505px !important;
    background: #f7f9fd;
    overflow: auto;
    flex: 0 0 auto;
    padding: 0 0 16px;
    .wui-checkbox-group {
      display: flex;
      flex-wrap: wrap;
    //   justify-content: space-between;
    }
    .wui-checkbox {
        margin-top: -6px;
    }
  }
}
.connector-box {
 margin-left: 16px;
  margin-top: 16px;
  padding: 15px 10px 0 17px;
  width: 250px;
  height: 123px;
  background: #ffffff;
  box-shadow: 0px 0px 16px 0px rgba(173, 180, 188, 0.2);
  border: 1px solid #e8e9eb;
  display: flex;
  align-items: flex-start;
  border-radius: 5px;
  &:hover{
    border: 1px solid #505766;
  }
  .connector-logo {
    width: 40px;
    height: 40px;
    background: linear-gradient(180deg, #e53935 0%, #f46b65 100%);
    border-radius: 4px;
  }
  .connector-content {
    flex: 1;
    overflow: hidden;
    padding: 0 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    .wui-select {
      width: 100% !important;
      margin-top: 9px;
    }
    &-title {
      font-size: 14px;
      color: #000000;
      line-height: 20px;
      .text-ellipsis();
    }
    &-desc {
      font-size: 12px;
      color: #666666;
      line-height: 22px;
      .text-ellipsis();
    }
  }
  .connector-check {
    width: 14px;
  }
}
