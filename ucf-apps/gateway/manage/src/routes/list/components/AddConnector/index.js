import React, { useState, useEffect } from "react";
import { Checkbox, Modal } from "@tinper/next-ui";
import "./index.less";
import { getCompleteImg } from "utils";
import { GatewayBindStatus } from "../InfoDrawer";

const AddConnector = (props) => {
    const { data = [], show, onCancel, ownerStore, ownerState, onOk } = props;
    const [checkedValues, setCheckedValues] = useState([]);
    useEffect(() => {
        if (show) {
            const checkedIds = data.filter((item = {}) => item.bind === GatewayBindStatus.bind).reduce((checkedIds, item) => checkedIds.concat(item.id), []);
            setCheckedValues(checkedIds);
        }
    }, [show]);
    const onChange = (values) => {
        setCheckedValues(values);
    };
    const onConfirm = () => {
        if (checkedValues.length) {
            const res = ownerStore.bindConnectors(ownerState.selectedGateway.gatewayID, checkedValues);
            if (res) {
                onOk();
            }
        } else {
            onCancel();
        }
    };
    return (
        <Modal
            fieldid="ublinker-routes-list-components-AddConnector-index-9806553-Modal"
            title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418050B", "添加连接器", undefined, {
                returnStr: true,
            })}
            show={show}
            width="1080px"
            onCancel={onCancel}
            onOk={onConfirm}
            className="add-connector"
        >
            <Checkbox.Group
                fieldid="UCG-FE-routes-list-components-AddConnector-index-3447665-Checkbox.Group"
                style={{ width: "100%" }}
                onChange={onChange}
                value={checkedValues}
            >
                {data.map((item) => {
                    return (
                        <div key={item.id} className={"connector-box"}>
                            <img
                                fieldid="ublinker-routes-list-components-AddConnector-index-9841096-img"
                                src={getCompleteImg(props.linkerLogo)}
                                className="connector-logo"
                            ></img>
                            <div className="connector-content">
                                <div className="connector-content-title">{item.adapterName || ""}</div>
                                <div className="connector-content-desc">{item.connectorName || ""}</div>
                                <div>{item.adapterVersion || ""}</div>
                                {/* <Select fieldid="ublinker-routes-list-components-AddConnector-index-9821480-Select" 
                                        defaultValue="all"
                                        style={{ width: 200, marginRight: 6 }}
                                        onChange={handleChange}
                                        onFocus={handFocus}
                                        onBlur={onBlur}
                                        autoFocus>
                                        <Option fieldid="UCG-FE-routes-list-components-AddConnector-index-2679590-Option" value="all">全部</Option>
                                        <Option fieldid="UCG-FE-routes-list-components-AddConnector-index-8838118-Option" value="confirming">待确认</Option>
                                    </Select> */}
                            </div>
                            <Checkbox fieldid="ublinker-routes-list-components-AddConnector-index-3275188-Checkbox" value={item.id}></Checkbox>
                        </div>
                    );
                })}
            </Checkbox.Group>
        </Modal>
    );
};
export default AddConnector;
