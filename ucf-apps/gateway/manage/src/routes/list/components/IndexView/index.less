.iuapIpaasDataintegrationFe-gateway {
    .hybrid-cloud-gateway-header {
        display: flex;
        background-color: #f7f9fd;
        border: none;
        .page-header-content {
            flex: 1;
            padding: 0 !important;
            display: flex;
            justify-content: space-between;
            align-items: center;
            > * {
                vertical-align: middle;
            }
        }
        .ucg-float-r {
            float: none;
        }
        &-left {
            font-size: 16px;
            font-family: PingFang-SC-Heavy, PingFang-SC;
            font-weight: 800;
            color: #333333;
        }
        &-right {
            display: flex !important;
            align-items: center;
        }
    }
    .hybrid-cloud-gateway-content {
        background: #f7f9fd;
        padding: 0 20px 30px 20px;
        overflow: auto;

        .create-gate-way {
            width: 358px;
            height: 234px;
            background: #fcfcfc;
            border-radius: 5px;
            border: 1px dashed rgba(80, 87, 102, 0.5);
            margin-top: 20px;
            margin-left: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            font-size: 14px;
            font-family:
                PingFangSC-Regular,
                PingFang SC;
            font-weight: 400;
            color: #333333;
            line-height: 20px;
            &:hover {
                border: 1px dashed #505766;
            }
            .add-icon {
                width: 32px;
                height: 32px;
                background-size: 100% 100%;
                background-image: url(~static/images/gateway/plus.png);
            }
        }

        .create-gate-way:hover {
            cursor: pointer;
            // border: 1px solid #505766;
        }
    }

    .u-dropdown-menu {
        padding-top: 4px;
        padding-bottom: 4px;

        .download-container {
            width: 157px;
            height: 40px;
            display: flex;
            justify-content: center;
            align-items: center;

            .download-button {
                width: 141px;
                height: 32px;
                border-radius: 5px;
                border: 1px solid #eaeaea;
                margin: 4px 8px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                font-weight: 400;
                color: rgba(0, 0, 0, 0.8);

                .download-text {
                    width: 85px;
                    text-align: center;
                }

                .download-icon {
                    height: 14px;
                    width: 14px;
                    background-size: 100% 100%;
                }

                .download-right {
                    background-image: url(~static/images/gateway/download.svg);
                }
            }

            .download-button:hover {
                border: 1px solid #505765;

                .download-right {
                    background-size: 100% 100%;
                    background-image: url(~static/images/gateway/downloadhover.svg);
                }
            }
        }
    }
    body .u-button i.uf {
        padding-left: 5px;
        padding-right: 0px;
    }
    .mix-ma-page-header .page-header-content {
        float: right;
        padding: 24px 10px 16px 0;
    }
}
// 网关帮助文档
.gateway-help {
    max-width: 150px;
    overflow: hidden;
    margin-right: 19px;
    font-size: 12px;
    font-family:
        PingFangSC-Regular,
        PingFang SC;
    font-weight: 400;
    color: #03c;
    line-height: 17px;
    cursor: pointer;
}
.gateway-help-overlay {
    // left: 75px !important;
    .wui-popover-content {
        width: 640px;
        background: #ffffff;
        box-shadow: 0px 2px 3px 0px rgba(0, 0, 0, 0.1);
        border: 1px solid #d9d9d9;
        & > .wui-popover-arrow {
            // top: 70px;
        }
    }
    .wui-popover-inner {
        padding: 0 16px 20px 20px;
    }
    &-title {
        width: 600px;
        height: 50px;
        position: absolute;
        font-size: 14px;
        font-family: Helvetica;
        color: #505766;
        line-height: 17px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        background-color: white;
        z-index: 100;
    }
    &-display {
        height: 50px;
    }
    &-close {
        width: 26px;
        height: 26px;
        cursor: pointer;
    }
    &-header {
        width: 605px;
        background: #fff7e7;
        border-radius: 4px;
        display: flex;
        flex-direction: row;
        padding: 3px 10px;
        line-height: 22px;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        img {
            width: 17px;
            height: 17px;
            margin-right: 6px;
        }
        span {
            flex: 1;
            font-size: 12px;
            font-family:
                PingFangSC-Regular,
                PingFang SC;
            font-weight: 400;
            color: #ee2223;
            white-space: wrap;
        }
    }
    // 网关帮助步骤条自定义图标
    .wui-steps-icon {
        img {
            width: 22px;
            height: 22px;
        }
    }
    .wui-steps-item-content {
        overflow: hidden;
        margin-left: 36px;
    }
    .wui-steps-item-title {
        font-size: 12px;
        font-family:
            PingFangSC-Regular,
            PingFang SC;
        font-weight: 400;
        color: #111111;
        line-height: 17px;
    }
    .wui-steps-item-description {
        font-size: 12px;
        font-family:
            PingFangSC-Regular,
            PingFang SC;
        font-weight: 400;
        color: #666666;
        line-height: 22px;
    }
    .help-example1 {
        width: 231px;
        height: 147px;
    }
    .help-example3 {
        width: 81px;
        height: 107px;
        margin-left: 26px;
    }
}
// 混合云网关说明
.hybrid-cloud-gateway-explanation-target {
    width: 14px;
    height: 14px;
    cursor: pointer;
}
.hybrid-cloud-gateway-explanation {
    width: 490px;
    height: fit-content;
    background: #ffffff;
    .explain {
        padding-left: 14px;
        position: relative;
        color: #333333;
        &-header {
            margin-bottom: 16px;
            position: relative;
        }
        &-title {
            font-size: 16px;
            font-family: PingFang-SC-Heavy, PingFang-SC;
            font-weight: 800;
            line-height: 24px;
        }
        &-title-tag {
            &::before {
                position: absolute;
                left: -12px;
                top: 4px;
                display: inline-block;
                content: "";
                width: 4px;
                height: 15px;
                background: #dd4048;
            }
        }
        &-desc {
            margin-top: 5px;
            font-size: 12px;
            font-weight: 400;
            font-family:
                PingFangSC-Regular,
                PingFang SC;
            line-height: 17px;
        }
    }
}
.gateway-empty-img {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
