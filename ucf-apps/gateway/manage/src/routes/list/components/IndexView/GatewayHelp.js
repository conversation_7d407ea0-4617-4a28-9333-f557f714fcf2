import React, { useState } from "react";
import { Popover, Steps, Icon, Button } from "components/TinperBee";
import { getLocalImg } from "utils/index";
const { Step } = Steps;

const GatewayHelpModal = (props) => {
    const Desc1 = (locale) => {
        return (
            <div>
                <div>
                    {
                        lang.templateByUuid(
                            "UID:P_UBL-FE_18D8CEF6041802AA",
                            "1. 找一台7x24小时都能访问您企业内部系统，且能访问互联网的机器，安装网关客户端。" //@notranslate
                        ) /* "1. 找一台7x24小时都能访问您企业内部系统，且能访问互联网的机器，安装网关客户端。" */
                    }
                </div>
                <div>
                    {
                        lang.templateByUuid(
                            "UID:P_UBL-FE_18D8CEF6041802AC",
                            "2.下载完成后解压，需保证解压后的文件目录不存在中文字符。" //@notranslate
                        ) /* "2.下载完成后解压，需保证解压后的文件目录不存在中文字符。" */
                    }
                </div>

                <div>
                    {
                        lang.templateByUuid(
                            "UID:P_UBL-FE_1DC2B8EA0580000B",
                            "3.提供3种主流操作系统版本，即：Windows x64、Linux x64、arrch64"//@notranslate
                        ) /* "3.提供3种主流操作系统版本，即：Windows x64、Linux x64、arrch64" */
                    }
                </div>

                <div>
                    {
                        lang.templateByUuid(
                            "UID:P_UBL-FE_1C2801860438003E",
                            "4.集群部署：支持下载多个客户端安装至同一/多个服务器" //@notranslate
                        ) /* "4.集群部署：支持下载多个客户端安装至同一/多个服务器" */
                    }
                </div>
            </div>
        );
    };
    const Desc2 = (locale) => {
        return (
            <div>
                <div>
                    {
                        lang.templateByUuid(
                            "UID:P_UBL-FE_1C28018604380038",
                            "1.请将下载的密钥解压至网关客户端解压目录的config文件夹下" //@notranslate
                        ) /* "1.请将下载的密钥解压至网关客户端解压目录的config文件夹下" */
                    }
                </div>
                <div>
                    {
                        lang.templateByUuid(
                            "UID:P_UBL-FE_1C28018604380039",
                            "2.集群部署：请使用同一网关内的密钥文件" //@notranslate
                        ) /* "2.集群部署：请使用同一网关内的密钥文件" */
                    }
                </div>
            </div>
        );
    };
    const Desc3 = (
        <div>
            <div>
                {
                    lang.templateByUuid(
                        "UID:P_UBL-FE_1C2801860438003C",
                        "1.在网关客户端解压目录的bin文件夹，运行startup.bat" //@notranslate
                    ) /* "1.在网关客户端解压目录的bin文件夹，运行startup.bat" */
                }
            </div>
            <div>
                {
                    lang.templateByUuid(
                        "UID:P_UBL-FE_1C2801860438003D",
                        "2.Linux环境：在网关客户端解压目录的bin文件夹，运行startup.sh" //@notranslate
                    ) /* "2.Linux环境：在网关客户端解压目录的bin文件夹，运行startup.sh" */
                }
            </div>
            <div>
                {
                    lang.templateByUuid(
                        "UID:P_UBL-FE_1C2801860438003F",
                        "3.集群部署：请先添加客户端所在服务器IP，再启动网关客户端" //@notranslate
                    ) /* "3.集群部署：请先添加客户端所在服务器IP，再启动网关客户端" */
                }
            </div>
        </div>
    );
    const Desc4 = (locale) => (
        <div>
            {lang.templateByUuid("UID:P_UBL-FE_1CE33C4405580006", "1.点击【测试连接】，连接成功即网关启动") /* "1.点击【测试连接】，连接成功即网关启动" */}
        </div>
    );

    const { placement, overlayMaxHeight = false, autoAdjustOverflow = true, locale } = props;
    console.log(locale);
    const [visible, setVisible] = useState(false);
    const popContent = (setVisible, locale) => (
        <>
            <div className="gateway-help-overlay-title">
                <span>{lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802AB", "安装说明") /* "安装说明" */}</span>
                <img
                    fieldid="ublinker-routes-list-components-IndexView-GatewayHelp-7048375-img"
                    src={getLocalImg("assetPack/pop-close.png")}
                    className="gateway-help-overlay-close"
                    onClick={() => setVisible(false)}
                />
            </div>
            <div className="gateway-help-overlay-display"></div>
            <header className="gateway-help-overlay-header">
                <img fieldid="ublinker-routes-list-components-IndexView-GatewayHelp-3190920-img" src={getLocalImg("assetPack/help-hint.png")} />
                <span>
                    {
                        lang.templateByUuid(
                            "UID:P_UBL-FE_18D8CEF6041802AF",
                            "按照下面步骤完成网关创建、客户端下载及安装" //@notranslate
                        ) /* "按照下面步骤完成网关创建、客户端下载及安装" */
                    }
                </span>
            </header>
            <Steps fieldid="ublinker-routes-list-components-IndexView-GatewayHelp-5559338-Steps" direction="vertical" size="small" type="default" current={4}>
                <Step
                    title={lang.templateByUuid("UID:P_UBL-FE_1C2801860438003A", "第一步：下载客户端") /* "第一步：下载客户端" */}
                    icon={<img fieldid="ublinker-routes-list-components-IndexView-GatewayHelp-9445417-img" src={getLocalImg("assetPack/help1.svg")} />}
                    description={Desc1(locale)}
                />
                <Step
                    title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802B1", "第二步：下载密钥", undefined, {
                        returnStr: true,
                    })}
                    icon={<img fieldid="ublinker-routes-list-components-IndexView-GatewayHelp-1557362-img" src={getLocalImg("assetPack/help2.svg")} />}
                    description={Desc2(locale)}
                />
                <Step
                    title={lang.templateByUuid("UID:P_UBL-FE_1C28018604380037", "第三步：启动客户端") /* "第三步：启动客户端" */}
                    icon={<img fieldid="ublinker-routes-list-components-IndexView-GatewayHelp-2744758-img" src={getLocalImg("assetPack/help3.svg")} />}
                    description={Desc3}
                />
                <Step
                    title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802B7", "第四步：测试连接", undefined, {
                        returnStr: true,
                    })}
                    icon={<img fieldid="ublinker-routes-list-components-IndexView-GatewayHelp-9250206-img" src={getLocalImg("assetPack/help4.svg")} />}
                    description={Desc4(locale)}
                />
                {/* <Step title={第五步：下载和启动nginx（集群部署时需要） *} icon={<><div style={{width:'17px',height:'17px',lineHeight:'17px',background: '#505766',borderRadius:'50%',textAlign:'center',
     color: '#FFFFFF',marginLeft:'4px',fontSize:'10px'}}>5</div></>} description={Desc5} /> */}
            </Steps>
        </>
    );
    return (
        <Popover
            fieldid="ublinker-routes-list-components-IndexView-GatewayHelp-9269004-Popover"
            overlayMaxHeight={overlayMaxHeight}
            autoAdjustOverflow={autoAdjustOverflow}
            arrowPointAtCenter={true}
            placement={placement || "rightTop"}
            overlayClassName="gateway-help-overlay"
            className="gateway-help"
            trigger="hover"
            show={visible} //
            content={() => popContent(setVisible, locale)}
            onHide={() => setVisible(false)}
        >
            <div
                onMouseEnter={() => setVisible(true)}
                title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802AB", "安装说明", undefined, {
                    returnStr: true,
                })}
            >
                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802AB", "安装说明") /* "安装说明" */}
            </div>
        </Popover>
    );
};

export default GatewayHelpModal;
