import React, { useState } from "react";
import { Popover, Icon } from "components/TinperBee";
import { getLocalImg } from "utils/index";
import "./index.less";

const explainContent = (data) =>
    data.map((item) => (
        <div key={item.title} className="explain">
            <header className="explain-header">
                <div className={`explain-title ${item.hasTag ? "explain-title-tag" : ""}`}>{item.title}</div>
                <div className="explain-desc">{Array.isArray(item.desc) ? item.desc.map((item, index) => <div key={index}>{item}</div>) : item.desc}</div>
            </header>
        </div>
    ));
const ExplainPop = (props) => {
    const { data } = props;
    return (
        <Popover
            fieldid="ublinker-routes-list-components-IndexView-ExplainPop-8967076-Popover"
            overlayClassName="hybrid-cloud-gateway-explanation"
            className="hybrid-cloud-gateway-explanation-target"
            // show={true}
            placement="rightBottom"
            trigger="hover"
            content={explainContent(data)}
        >
            <img fieldid="ublinker-routes-list-components-IndexView-ExplainPop-5211107-img" src={getLocalImg("gateway/help.png")} alt="" />
        </Popover>
    );
};

export default ExplainPop;
