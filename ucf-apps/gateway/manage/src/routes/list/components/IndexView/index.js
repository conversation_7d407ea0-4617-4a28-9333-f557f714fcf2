import React, { Component, Fragment } from "react";
import { Input } from "@tinper/next-ui";
import { Button, Menu, Empty, Tabs } from "components/TinperBee";
import withRouter from "decorator/withRouter";
import GateWayCard from "../GateWayCard";
import { gatewayOS } from "constants/gatewayOS";
import AddGateWayModal from "../AddGateWayModal";
import InfoDrawer from "../InfoDrawer";
import GatewayHelp from "./GatewayHelp";
import PageIntroduce from "components/PageIntroduce";
import responseStyles from "styles/response.modules.css";
import "./index.less";
import styles from "./index.modules.css";
import { Space } from "@tinper/next-ui";
import classNames from "classnames";
const { TabPane } = Tabs;
export const gateWayOssVersion = {
    0: { sysType: "windows", bit: 64 },
    1: { sysType: "windows", bit: 32 },
    2: { sysType: "linux", bit: 64 },
    3: { sysType: "linux", bit: 32 },
    4: { sysType: "linux", bit: "arrch64" },
};
@withRouter
class GatewayList extends Component {
    constructor(props) {
        super(props);

        this.state = {
            // 是否显示添加网关弹窗
            isShowAddModal: false,
            // 网关弹窗的类型 add 新增 edit 编辑
            modalType: "add",
            // 编辑的网关对象
            editGateway: null,
            selectedGateway: null,
            isOpenDrawer: false,
            locale: "zh_CN",

            allGateways: [], // 存储所有网关数据
            filteredGateways: [], // 存储过滤后的网关数据
            statusFilter: 0, // 状态筛选条件
            searchText: "", // 搜索文本
        };
    }

    componentDidMount() {
        this.props.ownerStore.resetState();
        this._getData();
        window.jDiwork.getContext((data) => {
            console.log(data);
            this.setState({
                locale: data.locale == "en_US" ? data.locale : "zh_CN",
            });
        });
    }

    static getDerivedStateFromProps(nextProps, prevState) {
        return;
        const dataSource = nextProps.ownerState.dataSource;

        if (dataSource !== prevState.allGateways) {
            return {
                allGateways: dataSource,
                filteredGateways: GatewayList.filterGateways(dataSource, prevState.statusFilter, prevState.searchText),
            };
        }
    }
    handleStatusFilterChange = (state) => {
        this.props.ownerStore.setSearchInfo({ state });
        this._getData();
        // status = parseInt(status);
        // this.setState({ statusFilter: status, filteredGateways: GatewayList.filterGateways(this.state.allGateways, status, this.state.searchText) });
    };

    handleSearchTextChange = (searchKey) => {
        this.props.ownerStore.setSearchInfo({ searchKey });
        this._getData();
        // this.setState({ searchText: value, filteredGateways: GatewayList.filterGateways(this.state.allGateways, this.state.statusFilter, value) });
    };

    static filterGateways(gateways, statusFilter, searchText) {
        let filteredGateways = gateways;
        // 根据状态筛选
        if (statusFilter) {
            filteredGateways = filteredGateways.filter((gateway) => gateway.status === statusFilter);
        }

        // 根据搜索文本筛选
        if (searchText.trim() !== "") {
            filteredGateways = filteredGateways.filter(
                (gateway) => gateway.name.toLowerCase().includes(searchText.toLowerCase()) || gateway.code.toLowerCase().includes(searchText.toLowerCase())
            );
        }

        return filteredGateways;
    }
    _getData = async () => {
        const { ownerStore } = this.props;
        await ownerStore.getDataSource();
        // await ownerStore.getGatewayConnectors();
    };

    // 创建网关
    createGateway = () => {
        // 显示创建网关弹窗
        this.setState({
            modalType: "add",
            editGateway: null,
            isShowAddModal: true,
        });
    };

    // 删除网关
    handleDelete = (editData) => {
        const { ownerStore } = this.props;
        ownerStore.deleteGateWay(editData.gatewayID);
    };

    // 下载密钥
    handleDownloadSecretKey = (e, editData) => {
        e.stopPropagation();
        if (editData.fromMainTenant) {
            return;
        }
        const { ownerStore } = this.props;
        ownerStore.downloadSecretKey(editData.gatewayID);
    };

    // 设为默认回调
    handleSetDefaultCallback = (editData) => {
        const { ownerStore } = this.props;
        ownerStore.setDefaultGateWay({
            gatewayId: editData.id,
        });
    };

    // 获取下载网关列表
    getGateWayDrop = () => {
        return (
            <Menu fieldid="ublinker-routes-list-components-IndexView-index-5595728-Menu" onSelect={this.handleDownloadGateWay}>
                {gatewayOS.map((item) => {
                    let { key, value } = item;
                    return (
                        <Menu.Item fieldid="UCG-FE-routes-list-components-IndexView-index-4797571-Menu.Item" key={value}>
                            <div className="download-container">
                                <div className="download-button">
                                    <div
                                        className="download-icon"
                                        style={{
                                            "background-image":
                                                item.key.indexOf("linux") === -1
                                                    ? "url(/ublinker-fe/static/images/gateway/windows.svg)"
                                                    : "url(/ublinker-fe/static/images/gateway/linux.svg)",
                                        }}
                                    ></div>
                                    <div className="download-text">{key}</div>
                                    <div className="download-icon download-right"></div>
                                </div>
                            </div>
                        </Menu.Item>
                    );
                })}
            </Menu>
        );
    };

    handleAddGateWayCommit = (data) => {
        const { ownerStore } = this.props;
        // 判断弹窗是新增还是编辑
        if (this.state.modalType === "add") {
            ownerStore.addGateWay(data);
        } else {
            ownerStore.editGateWay(data);
        }
        this.setState({
            isShowAddModal: false,
        });
    };

    handleAddGateWayCancel = () => {
        this.setState({
            isShowAddModal: false,
        });
    };

    // 网关点击刷新按钮-重置网关
    handleResetGateWayCallback = async (e, editData) => {
        e.stopPropagation();
        const { ownerStore } = this.props;
        const res = await ownerStore.resetGateWay(editData.id);
        ownerStore.getDataSource();
    };
    // 网关健康检查
    handleHealthCheck = async (e, item) => {
        const { ownerStore } = this.props;
        let res = await ownerStore.healthCheck(item.gatewayID, item.isCluster);
        // if (res && res.status == 1) {
        ownerStore.getDataSource();
        // }
    };
    handleOpenDrawer = (item) => {
        this.props.ownerStore.setGateway(item);
    };
    handleCloseDrawer = () => {
        this.props.ownerStore.setGateway(undefined);
        this._getData();
    };
    gatewayDescription = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418035E", "混合云网关") /* "混合云网关" */,
            desc: lang.templateByUuid(
                "UID:P_UBL-FE_18D8CEF60418035F",
                "通过YonLinker实现与内网服务集成的代理系统。" //@notranslate
            ) /* "通过YonLinker实现与内网服务集成的代理系统。" */,
            hasTag: true,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180360", "如何使用") /* "如何使用" */,
            desc: [
                lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418035A", "混合云网关由 Server 和 Client 构成：") /* "混合云网关由 Server 和 Client 构成：" */,
                lang.templateByUuid(
                    "UID:P_UBL-FE_18F6645805080007",
                    "1、服务器: 支持网关管理、客户端配置、连接器部署等功能操作。" //@notranslate
                ) /* "1、服务器: 支持网关管理、客户端配置、连接器部署等功能操作。" */,
                lang.templateByUuid(
                    "UID:P_UBL-FE_18D8CEF60418035C",
                    "2、Client: 实现YonLinker与用户内网服务的交互。" //@notranslate
                ) /* "2、Client: 实现YonLinker与用户内网服务的交互。" */,
            ],
        },
    ];
    handleSearch = (searchValue) => {
        this.props.ownerStore.getDataSource(searchValue);
    };

    render() {
        let { ownerStore, ownerState } = this.props;
        let { dataSource = [], selectedGateway } = ownerState;
        let { isShowAddModal, modalType, editGateway, locale, filteredGateways, allGateways } = this.state;
        const cardWrapClass = classNames(responseStyles.columns, `hybrid-cloud-gateway-content`);
        return (
            <div style={{ display: "flex", flexDirection: "column", height: "100%", backgroundColor: "#f7f9fd" }}>
                <PageIntroduce
                    code="gatewayManage"
                    title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418035E", "混合云网关") /* "混合云网关" */}
                    descList={[
                        lang.templateByUuid(
                            "UID:P_UBL-FE_1C28024E04380011",
                            "混合云网关是实现内网穿透的安全通道，当集成系统间跨网络访问时，则需要安装。通过【新建网关→下载客户端→下载密钥→启动客户端→测试连接】即可完成网关安装"//@notranslate
                        ) /* "混合云网关是实现内网穿透的安全通道，当集成系统间跨网络访问时，则需要安装。通过【新建网关→下载客户端→下载密钥→启动客户端→测试连接】即可完成网关安装" */,
                    ]}
                    linkList={[
                        {
                            text: <GatewayHelp locale={locale}></GatewayHelp>,
                        },
                    ]}
                />
                <div className={styles["gateway-header"]}>
                    <Tabs fieldid="deef683c-4554-4027-b603-9d0e12c66aed" defaultActiveKey="all" onChange={this.handleStatusFilterChange}>
                        <TabPane
                            fieldid="d483fc42-aca8-4c5a-ae86-77aa554f671e"
                            tab={
                                <span className={styles["gateway-header-tab"]}>
                                    {lang.templateByUuid("UID:P_UBL-FE_1C28024E0438000E", "全部") /* "全部" */}
                                </span>
                            }
                            key="all"
                        />
                        <TabPane
                            fieldid="c4821f1d-53a6-405a-89db-d3661bbb00cc"
                            tab={
                                <span className={styles["gateway-header-tab"]}>
                                    {lang.templateByUuid("UID:P_UBL-FE_1C28024E0438000F", "在线") /* "在线" */}
                                </span>
                            }
                            key="ONLINE"
                        />
                        <TabPane
                            fieldid="f946b378-f14d-4637-a9c7-1e62e7549c9a"
                            tab={
                                <span className={styles["gateway-header-tab"]}>
                                    {lang.templateByUuid("UID:P_UBL-FE_1C28024E04380010", "离线") /* "离线" */}
                                </span>
                            }
                            key="OFFLINE"
                        />
                    </Tabs>
                    <Space size={8} className={styles["gateway-header-oper"]}>
                        <Input
                            fieldid="158a9c22-33b0-47fd-a178-91bcd74b515d"
                            placeholder={lang.templateByUuid("UID:P_UBL-FE_1C27C2C80438000D", "请输入编码/名称", undefined, {
                                returnStr: true,
                            })}
                            allowClear
                            type="search"
                            onSearch={this.handleSearchTextChange}
                            style={{ width: "300px" }}
                        />
                        <Button fieldid="93ea5214-f411-4fc3-8254-37f97a9e4ac0" type="primary" onClick={this.createGateway}>
                            {lang.templateByUuid("UID:P_UBL-FE_1C007BD405D80008", "新增") /* "新增" */}
                        </Button>
                    </Space>
                </div>
                {dataSource?.length === 0 ? (
                    <div className="gateway-empty-img">
                        <Empty fieldid="616bd90b-b70f-4f52-bbd9-ff477efbb38f" />
                    </div>
                ) : (
                    <div className={cardWrapClass}>
                        {[{ type: "add", id: "add" }, ...dataSource].map((item = {}, index) => {
                            return (
                                <GateWayCard
                                    key={item.id}
                                    ownerState={ownerState}
                                    ownerStore={ownerStore}
                                    // isDefault={item.isdefault}
                                    title={item.name}
                                    status={item.status}
                                    code={item.code}
                                    gatewayID={item.gatewayID}
                                    item={item}
                                    index={index}
                                    resetGateWayCallback={(e) => {
                                        this.handleHealthCheck(e, item);
                                    }}
                                    // connectConfigCallback={() => { this.handleConnectConfigClick(item); }}
                                    deleteCallback={() => {
                                        this.handleDelete(item);
                                    }}
                                    editCallback={() => this.handleOpenDrawer(item)}
                                    // setDefaultCallback={() => { this.handleSetDefaultCallback(item); }}
                                    downLoadSecretKeyCallback={(e) => {
                                        this.handleDownloadSecretKey(e, item);
                                    }}
                                    createGateway={this.createGateway}
                                />
                            );
                        })}
                    </div>
                )}
                {isShowAddModal ? (
                    <AddGateWayModal
                        type={modalType}
                        editGateway={editGateway}
                        modalCommit={this.handleAddGateWayCommit}
                        modalCancel={this.handleAddGateWayCancel}
                    />
                ) : null}
                {selectedGateway && <InfoDrawer locale={locale} onClose={this.handleCloseDrawer} onDelete={this.handleDelete} ownerState={ownerState} />}
            </div>
        );
    }
}

export default GatewayList;
