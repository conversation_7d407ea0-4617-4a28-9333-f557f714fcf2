import { getInvokeService, getServicePath } from "utils/service";
import { a_download } from "utils/index";
import _template from "lodash/template";
import { Success, Error } from "utils/feedback";
import commonText from "constants/commonText";
import { PRIVATE } from "utils/util";
//#region 网关接口
/**
 * 获取网关列表
 * @param {{order, isAll}} param
 * @returns
 */
export const listService = function (param, header = {}) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwmanage/gwportal/mygwapp/gateway/list",
            header,
            showLoading: false,
        },
        param
    );
};
export const connectCountService = function (param, header = {}) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwmanage/gwportal/mygwapp/gateway/connect/count",
            header,
            showLoading: false,
        },
        param
    );
};
/***
 *
 * @param data data.name: 网关名称 data.code: 网关编码
 * @return {Promise | Promise<unknown>}
 */
/**
 * 新建网关
 * @param {{name, code}} data
 * @returns
 */
export const addGateWay = function (data, header = {}) {
    return getInvokeService(
        {
            method: "POST",
            path: "/gwmanage/gwportal/mygwapp/ncconnect/gateway/create",
            header,
        },
        data
    );
};

/***
 * 编辑网关
 * @param data data.gatewayId: 网关ID data.name: 网关名称 data.code: 网关编码
 * @return {Promise | Promise<unknown>}
 */
export const editGateWay = function ({ id, ...data }, header) {
    return getInvokeService(
        {
            method: "PUT",
            path: `/gwmanage/gwportal/mygwapp/ncconnect/gateway/${id}/update`,
            header,
        },
        data
    );
};
/***
 * 保存网关
 * @param data data.gatewayId: 网关ID data.name: 网关名称 data.code: 网关编码
 * @return {Promise | Promise<unknown>}
 */
export const saveEditGateWay = function ({ gatewayId, ...data }, header) {
    return getInvokeService(
        {
            method: "PUT",
            path: `/gwmanage/gwportal/mygwapp/ncconnect/gateway/cluster/${gatewayId}/update`,
            header,
        },
        data
    );
};

/**
 * 删除网关
 * @param {*} gatewayId
 * @returns  {Promise | Promise<unknown>}
 */
export const deleteGateWay = function (gatewayId, header) {
    return getInvokeService({
        method: "PUT",
        path: `/gwmanage/gwportal/mygwapp/ncconnect/gateway/${gatewayId}/delete`,
        header,
    });
};

/***
 * 设置默认
 * @param data data.gatewayId: 网关ID
 * @return {Promise | Promise<unknown>}
 */
export const setDefaultGateWay = function (data, header) {
    return getInvokeService(
        {
            method: "POST",
            path: "/gwmanage/gwportal/diwork/ncconnect/setDefaultNCConnector",
            header,
        },
        data
    );
};

/***
 * 重置网关
 * @param data data.gatewayId: 网关ID
 * @return {Promise | Promise<unknown>}
 */
export const resetGateWay = function (gatewayId, header) {
    return getInvokeService({
        method: "PUT",
        path: `/gwmanage/gwportal/mygwapp/gateway/${gatewayId}/state`,
        header,
    });
};

/***
 * 获取下载网关url
 * @param data
 * @return {Promise | Promise<unknown>}
 */
export const getGateWayDownloadUrl = function (data, header) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwmanage/gwportal/diwork/ncconnect/gateway/download/url",
            // path: "/gwmanage/gateway/download/gatewayClientDownloadV1",
            showLoading: () => {
                Success(
                    lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418053D", "下载中,请等待", undefined, {
                        returnStr: true,
                    }) /* "下载中,请等待" */
                );
            },
            header,
        },
        data
    );
};
export const getGateWayDownloadOssUrl = function (path) {
    return getInvokeService({
        method: "GET",
        path,
    });
};

export const GateWayDownload = function ({ sysType, bit }) {
    let url =
        window.location.origin +
        "/iuap-ipaas-dataintegration" +
        getServicePath("/diwork/ncconnect/gateway/download/url?sysType=<%= sysType %>&bit=<%= bit %>&serviceCode=kflj_wggl");
    a_download(_template(url)({ sysType, bit }), true);
};

/***
 * 下载密钥
 * @param gatewayId
 * @param {Boolean} isPrivate
 */
export const downloadGatewayKey = function (gatewayId) {
    let url;
    if (PRIVATE) {
        //  url = window.location.origin + '/iuap-ipaas-dataintegration' + getServicePath('/diwork/gateway/secretkey/download/<%= gatewayId %>');
        url =
            window.location.origin +
            "/iuap-ipaas-dataintegration" +
            getServicePath("/diwork/ncconnect/gateway/download/SecretKey/<%= gatewayId %>?serviceCode=kflj_wggl");
    } else {
        url =
            window.location.origin +
            "/iuap-ipaas-dataintegration" +
            getServicePath("/diwork/ncconnect/gateway/download/SecretKey/<%= gatewayId %>?serviceCode=kflj_wggl");
    }
    Success(
        lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418034E", "下载中,请等待", undefined, {
            returnStr: true,
        })
    );
    a_download(_template(url)({ gatewayId }), true);
};

/**
 * 根据分类获取连接信息
 * @param {{gatewayId}} param
 * @returns
 */
export const getConnectsByType = function (param, header) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwportal/diwork/ncconnect/listMyConnects/classifyByType",
            header,
        },
        param
    );
};

/***
 * 获取网关配置
 * @param data data.gatewayId: 网关ID data.pageNo  data.pageSize
 * @return {Promise | Promise<unknown>}
 */
export const getGateWayConfig = function (gatewayId, header) {
    return getInvokeService({
        method: "GET",
        path: `/gwmanage/gwportal/mygwapp/ncconnect/gateway/${gatewayId}/config`,
        header,
    });
};
/**
 * 获取IP地址列表接口
 * @param {*} gatewayId
 * @returns
 */
export const getClientIpList = function (gatewayId, header) {
    return getInvokeService(
        {
            method: "GET",
            path: `/gwmanage/gwportal/mygwapp/ncconnect/getClientIpList`,
            header,
        },
        null,
        { gatewayId: gatewayId }
    );
};

/***
 * 保存网关配置
 * @param data data.gatewayId: 网关ID data.inPort 入网端口 data.ip 网关IP data.whiteList 白名单 data.outPort 出网端口
 * @return {Promise | Promise<unknown>}
 */
export const saveGateWayConfig = function ({ gatewayId, ...data }, header) {
    return getInvokeService(
        {
            method: "PUT",
            path: `/gwmanage/gwportal/mygwapp/ncconnect/gateway/${gatewayId}/config`,
            header,
        },
        data
    );
};

/***
 * 绑定连接
 * @param data
 * @return {Promise | Promise<unknown>}
 */
export const bindConnect = function (gatewayId, data, header) {
    return getInvokeService(
        {
            method: "POST",
            path: "/gwmanage/gwportal/diwork/connect/bindConnectsToGateway" + "?gatewayId=" + gatewayId,
            header,
        },
        data
    );
};

/***
 * 适配器操作
 * @param param {{adapterId, gatewayId, action: 'config'|'deploy'|'undeploy'}}
 * @return {Promise | Promise<unknown>}
 */
export const adapterOperation = function (param, header) {
    return getInvokeService(
        {
            method: "POST",
            path: `/gwmanage/gwportal/mygwapp/gateway/adapter/action`,
            showLoading: param.action === "deploy" ? false : true, //升级操作去除loading，显示’正在升级，暂不支持操作‘
            header,
        },
        [],
        param
    );
};

/***
 * 网关健康检查
 * @param param param.gatewayId 网关ID
 * @return {Promise | Promise<unknown>}
 */
export const healthCheck = function (param, header) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwmanage/gwportal/mygwapp/gateway/state",
            header,
        },
        param
    );
};
/***
 * 网关健康检查  集群
 * @param param param.gatewayId 网关ID
 * @return {Promise | Promise<unknown>}
 */
export const ClusterhealthCheck = function (param, header) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwportal/mygwapp/ncconnect/cluster/gateway/state",
            header,
        },
        param
    );
};
/**
 * 刷新集群模式连接实例
 * @param {} param
 * @returns
 */
export const getInstanceList = function (param, header) {
    return getInvokeService(
        {
            method: "GET",
            path: "",
            header,
        },
        param
    );
};

//下载ng配置
export const downloadNG = function (param, header) {
    return getInvokeService(
        {
            method: "POST",
            path: "/gwportal/mygwapp/ncconnect/down/nginx",
            responseType: "blob", //下载流文件需要在这声明
            timeout: 30000,
            header,
            // header: {

            //     // 'Accept':'text/html;charset=utf-8',
            //      'Content-Type':'application/xml'

            //     }
            // ,
        },
        param
    );
};
//单个实例刷新
export const refreshInstance = function (param, header) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwmanage/gwportal/diwork/ncconnect/refreshState",
            header,
        },
        param
    );
};
//单个实例删除
export const deleteInstance = function (param, header) {
    return getInvokeService({
        method: "POST",
        path: `/gwportal/mygwapp/ncconnect/deleteClient/${param.serverId}/${param.gatewayId}`,
        header,
    });
};
export const getLightAppGatewayMsgService = function (param, header) {
    return getInvokeService(
        {
            method: "GET",
            path: `/gwmanage/gwportal/diwork/mobileLightApp/find/mobileLightAppInfo`, //回显
            header,
        },
        param
    );
};
export const getLightAppGatewayListService = function (param, header) {
    return getInvokeService(
        {
            method: "GET",
            path: `/gwmanage/gwportal/diwork/mobileLightApp/find/connect/byGatewayId`, //select内容
            header,
        },
        param
    );
};

export const TestLightAppGatewayService = function (param, header) {
    return getInvokeService(
        {
            method: "POST",
            path: `/gwmanage//gwportal/diwork/mobileLightApp/test/connect`,
            timeout: 60000,
            header,
        },
        param
    );
};
export const SaveLightAppGatewayService = function (param, header) {
    return getInvokeService(
        {
            method: "POST",
            path: `/gwmanage/gwportal/diwork/mobileLightApp/save/mobileLightAppInfo`,
            timeout: 60000,
            header,
        },
        param
    );
};
export const setDefaultService = function (param, header) {
    return getInvokeService(
        {
            method: "POST",
            path: `/gwmanage/gwportal/diwork/ncconnect/connect/setDefault`,
            header,
        },
        param
    );
};

export const updateConnectorService = function (param, header) {
    return getInvokeService(
        {
            method: "POST",
            path: `/gwmanage/gwportal/diwork/ncconnect/update/gateway/connector`,
            header,
        },
        param
    );
};
// 网关部署路径刷新
export const reloadGatewayAddressService = function (gatewayId) {
    return getInvokeService(
        {
            method: "GET",
            path: `/gwmanage/gwportal/mygwapp/gateway/address/reload`,
        },
        {},
        { gatewayId }
    );
};
// 获取网关地址信息
export const getGatewayAddressService = function (gatewayId) {
    return getInvokeService(
        {
            method: "GET",
            path: `/gwmanage/gwportal/mygwapp/gateway/address`,
        },
        null,
        { gatewayId }
    );
};
