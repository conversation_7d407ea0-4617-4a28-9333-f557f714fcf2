import React from "react";
import { render, Provider } from "core";
import { MemoryRouter as Router } from "react-router";
import Routes from "./routes";
import lang from "tne-core-fe/i18n";
import withMultiLang from "components/AsyncComponent/withMultiLang";
const MultiLangRouter = withMultiLang(Routes, ["YS_IPAAS_UBL-FE"], { serviceCode: "kflj_wggl" });
lang.init({ zhcn: {}, zhtw: {}, enus: {} }, null);
import "./app.less";
import "static/cl/iconfont.css";
import "static/ipaas/iconfont.css";
import "static/icon";
import { configure } from "mobx";
configure({ isolatedGlobalState: true });
const { SmallApplication } = window.tnsSdk;
export default function SmallAppBootstrap() {
    return (
        <SmallApplication class="iuapIpaasDataintegrationFe-gateway" style={{ transform: "translate(0)" }}>
            {(appContext) => {
                return (
                    <Provider serviceCodeDiwork="kflj_wggl">
                        <Router initialEntries={[appContext.currRoutePath || "/"]}>
                            <MultiLangRouter />
                        </Router>
                    </Provider>
                );
            }}
        </SmallApplication>
    );
}
