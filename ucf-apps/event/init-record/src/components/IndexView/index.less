.grid-header {
    &-btn {
        float: right;
        margin-right: 20px;
    }

    &::after {
        content: '.';
        display: block;
        visibility: hidden;
        clear: both;
        zoom: 1;
    }
}

.ucg-ma-modal {

    .u-modal-footer,
    .u-modal-header {
        border: 1px solid #E4E4E4 !important;
    }

    .u-label {
        font-size: 14px !important;
        line-height: 1.2;
    }

    p {
        display: inline-block;
        min-height: 28px;
        line-height: 28px;
        font-size: 14px;
        // line-height: 1.2;
    }

    &-code{
        display: inline-block;
        vertical-align: top;
        width: 80% !important;
    }
}

.ucg-ma-grid-header {
    overflow: hidden;
}

.u-form-control {
    width: 400px !important;
}

.u-form-item {
    padding: 0 10px;
    min-height: 48px;

    &:first-of-type {
        padding-top: 16px;
        height: 64px;
    }

    &:last-of-type {
        padding-bottom: 16px;
    }

    .error {
        display: block;
        margin-left: 85px;

        i {
            display: none;
        }
    }
}