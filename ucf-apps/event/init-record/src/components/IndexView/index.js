import React, { Component, Fragment } from "react";
import { Header } from "components/PageView/Header";
import { Content } from "components/PageView";
import { <PERSON>dal, Button, FormList, FormControl, Icon, Radio } from "components/TinperBee";
import SearchInput from "components/TinperBee/SearchInput";
import CodeEditor from "components/CodeEditor";
import Grid from "components/TinperBee/Grid";
import { Error } from "utils/feedback";
import "./index.less";
const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
};
const FormItem = FormList.FormItem;
class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            showModal: false,
            saveData: {},
            tenantId: "",
        };
        this.form = React.createRef();
        this.columns = [
            {
                title: lang.templateByUuid("UID:P_UBL-FE_200960400428038F", "租户ID") /* "租户ID" */,
                dataIndex: "tenantId",
                key: "tenantId",
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_2009604004280391", "ERP类型") /* "ERP类型" */,
                dataIndex: "erpType",
                key: "erpType",
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_2009604004280395", "详细版本") /* "详细版本" */,
                dataIndex: "detailType",
                key: "detailType",
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_2009604004280396", "是否HR") /* "是否HR" */,
                dataIndex: "isHr",
                key: "isHr",
                render: (value) => {
                    return (
                        <p>
                            {
                                value
                                    ? lang.templateByUuid("UID:P_UBL-FE_2009604004280399", "是") /* "是" */
                                    : lang.templateByUuid("UID:P_UBL-FE_2009604004280398", "否") /* "否" */
                            }
                        </p>
                    );
                },
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_200960400428038B", "职级体系类型") /* "职级体系类型" */,
                dataIndex: "rankTypeName",
                key: "rankTypeName",
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_200960400428039B", "操作") /* "操作" */,
                dataIndex: "action",
                key: "action",
                render: (value, record) => {
                    return (
                        <Fragment>
                            <a fieldid="ublinker-init-record-src-components-IndexView-index-7391725-a" onClick={this.handleDetail.bind(null, record)}>
                                {lang.templateByUuid("UID:P_UBL-FE_200960400428039C", "查看") /* "查看" */}
                            </a>
                            &nbsp;&nbsp;&nbsp;
                            <a fieldid="ublinker-init-record-src-components-IndexView-index-6805531-a" onClick={this.handleEdit.bind(null, record)}>
                                {lang.templateByUuid("UID:P_UBL-FE_200960400428038C", "编辑") /* "编辑" */}
                            </a>
                        </Fragment>
                    );
                },
            },
        ];
    }

    save = () => {
        let { ownerStore } = this.props;
        let { saveData, tenantId } = this.state;
        let { update } = ownerStore;
        this.form.current.validateFields().then(async (values) => {
            let param = {
                tenantId: saveData.tenantId,
                erpType: values.erpType,
                detailType: values.detailType,
                isHr: values.isHr,
            };
            let res = await update(param, tenantId);
            if (res) {
                this.setState({ showModal: false, saveData: {} });
            }
        });
    };

    close = () => {
        this.setState({ showModal: false, saveData: {} });
    };

    handleEdit = (record) => {
        this.setState({ showModal: "edit", saveData: record }, () => {
            this.form.current.setFieldsValue(record);
        });
    };

    handleDetail = (record) => {
        this.setState({ showModal: "view", saveData: record }, () => {
            this.form.current.setFieldsValue(record);
        });
    };

    handleSearch = (value) => {
        if (!value) {
            Error(lang.templateByUuid("UID:P_UBL-FE_200960400428039A", "请输入查询条件") /* "请输入查询条件" */);
            return;
        }
        this.setState({
            tenantId: value,
        });
        this.props.ownerStore.getDataSource({ tenantId: value });
    };

    render() {
        let { ownerState } = this.props;
        let { showModal, saveData } = this.state;
        let { dataSource } = ownerState;
        let isDetail = showModal == "view";
        let isEdit = showModal == "edit";
        let { tenantId, erpType, detailType, isHr, rankTypeName, initClasses } = saveData;
        return (
            <Fragment>
                <Header title={lang.templateByUuid("UID:P_UBL-FE_200960400428038A", "初始化记录") /* "初始化记录" */} />
                <Content>
                    <Grid
                        fieldid="ublinker-init-record-src-components-IndexView-index-4989449-Grid"
                        header={
                            <SearchInput
                                className="ucg-mar-r-20 ucg-float-r"
                                size="lg"
                                ref={(searchNode) => (this.searchNode = searchNode)}
                                showClear
                                onSearch={this.handleSearch}
                            />
                        }
                        data={dataSource}
                        columns={this.columns}
                    />

                    <Modal
                        fieldid="ublinker-init-record-src-components-IndexView-index-8351433-Modal"
                        className="ucg-ma-modal"
                        show={showModal}
                        onCancel={this.close}
                    >
                        <Modal.Header closeButton>
                            <Modal.Title>{lang.templateByUuid("UID:P_UBL-FE_200960400428038E", "初始化记录详情") /* "初始化记录详情" */}</Modal.Title>
                        </Modal.Header>

                        <Modal.Body>
                            <FormList
                                fieldid="ublinker-init-record-src-components-IndexView-index-3163147-FormList"
                                size="sm"
                                ref={this.form}
                                {...formItemLayout}
                                className="form"
                            >
                                <FormItem
                                    fieldid="ublinker-init-record-src-components-IndexView-index-7871486-FormItem"
                                    label={lang.templateByUuid("UID:P_UBL-FE_200960400428038F", "租户ID") /* "租户ID" */}
                                    name="tenantId"
                                >
                                    <p>{tenantId}</p>
                                </FormItem>
                                <FormItem
                                    fieldid="ublinker-init-record-src-components-IndexView-index-7651978-FormItem"
                                    label={lang.templateByUuid("UID:P_UBL-FE_2009604004280391", "ERP类型") /* "ERP类型" */}
                                    name="erpType"
                                    rules={[
                                        {
                                            required: true,
                                            message: (
                                                <span>
                                                    <Icon fieldid="ublinker-init-record-src-components-IndexView-index-9185439-Icon" type="uf-exc-t"></Icon>
                                                    <span>{lang.templateByUuid("UID:P_UBL-FE_2009604004280394", "请输入ERP版本") /* "请输入ERP版本" */}</span>
                                                </span>
                                            ),
                                        },
                                    ]}
                                >
                                    <FormControl fieldid="ublinker-init-record-src-components-IndexView-index-9950839-FormControl" disabled={isDetail} />
                                </FormItem>

                                <FormItem
                                    fieldid="ublinker-init-record-src-components-IndexView-index-1639157-FormItem"
                                    label={lang.templateByUuid("UID:P_UBL-FE_2009604004280395", "详细版本") /* "详细版本" */}
                                    name="detailType"
                                    rules={[
                                        {
                                            required: true,
                                            message: (
                                                <span>
                                                    <Icon fieldid="ublinker-init-record-src-components-IndexView-index-6913863-Icon" type="uf-exc-t"></Icon>
                                                    <span>{lang.templateByUuid("UID:P_UBL-FE_2009604004280397", "请输入详细版本") /* "请输入详细版本" */}</span>
                                                </span>
                                            ),
                                        },
                                    ]}
                                >
                                    <FormControl fieldid="ublinker-init-record-src-components-IndexView-index-8231456-FormControl" disabled={isDetail} />
                                </FormItem>

                                <FormItem
                                    fieldid="ublinker-init-record-src-components-IndexView-index-6370504-FormItem"
                                    label={lang.templateByUuid("UID:P_UBL-FE_2009604004280396", "是否HR") /* "是否HR" */}
                                    name="isHr"
                                >
                                    {/* <p>{isHr ? '是' : '否'}</p> */}
                                    <Radio.Group>
                                        <Radio fieldid="ublinker-init-record-src-components-IndexView-index-1524965-Radio" value={true}>
                                            {lang.templateByUuid("UID:P_UBL-FE_2009604004280399", "是") /* "是" */}
                                        </Radio>
                                        <Radio fieldid="ublinker-init-record-src-components-IndexView-index-1882603-Radio" value={false}>
                                            {lang.templateByUuid("UID:P_UBL-FE_2009604004280398", "否") /* "否" */}
                                        </Radio>
                                    </Radio.Group>
                                </FormItem>

                                <FormItem
                                    fieldid="ublinker-init-record-src-components-IndexView-index-8522730-FormItem"
                                    label={lang.templateByUuid("UID:P_UBL-FE_200960400428038B", "职级体系类型") /* "职级体系类型" */}
                                    name="rankTypeName"
                                >
                                    <p>{rankTypeName}</p>
                                </FormItem>

                                <FormItem
                                    fieldid="ublinker-init-record-src-components-IndexView-index-9173358-FormItem"
                                    label={lang.templateByUuid("UID:P_UBL-FE_200960400428038D", "已初始化分类") /* "已初始化分类" */}
                                >
                                    <CodeEditor className="ucg-ma-modal-code" value={JSON.stringify(initClasses, null, "\t")} readOnly={true} />
                                </FormItem>
                            </FormList>
                        </Modal.Body>

                        <Modal.Footer>
                            {isDetail ? (
                                <Button fieldid="ublinker-init-record-src-components-IndexView-index-1405422-Button" onClick={this.close} colors="primary">
                                    {lang.templateByUuid("UID:P_UBL-FE_2009604004280390", "关闭") /* "关闭" */}
                                </Button>
                            ) : null}
                            {isEdit ? (
                                <Fragment>
                                    <Button
                                        fieldid="ublinker-init-record-src-components-IndexView-index-7898142-Button"
                                        className="ucg-mr-10"
                                        onClick={this.close}
                                        colors="secondary"
                                    >
                                        {lang.templateByUuid("UID:P_UBL-FE_2009604004280392", "取消") /* "取消" */}
                                    </Button>
                                    <Button fieldid="ublinker-init-record-src-components-IndexView-index-9005394-Button" onClick={this.save} colors="primary">
                                        {lang.templateByUuid("UID:P_UBL-FE_2009604004280393", "确定") /* "确定" */}
                                    </Button>
                                </Fragment>
                            ) : null}
                        </Modal.Footer>
                    </Modal>
                </Content>
            </Fragment>
        );
    }
}

export default IndexView;
