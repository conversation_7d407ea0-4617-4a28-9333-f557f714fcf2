import { observable, action, toJS, reaction, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { defaultListMap } from "utils/pageListUtils";
import { autoServiceMessage } from "utils/service";
import * as ownerService from "./service";
const initState = {
    dataSource: [],
    serviceCodeDiwork: "kflj_ssgj",
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    getDataSource = async (param) => {
        let res = await autoServiceMessage({
            service: ownerService.getByTenantIdService(param, { serviceCode: this.state.serviceCodeDiwork }),
        });

        if (res.data) {
            this.changeState({
                dataSource: [res.data],
            });
        }
    };

    update = async (param, tenantId) => {
        let res = await autoServiceMessage({
            service: ownerService.updateErpMessageService(param, { serviceCode: this.state.serviceCodeDiwork }),
        });

        if (res) {
            this.getDataSource({ tenantId });
            return true;
        }
    };
}

export const storeKey = "eventInitRecordStore";

export default Store;
