import { defineService } from "utils/service";

/**
 * 初始化记录-查询
 * @param {*} data
 * @param {String} data.tenantId 租户id
 */
export const getByTenantIdService = function (data, header) {
    let service = defineService({
        method: "GET",
        path: "/gwmanage/initRecord/getByTenantId",
        header,
    });

    return service.invoke(data);
};

/**
 * 初始化记录-修改ERP版本信息
 * @param {*} data
 * @param {String} data.tenantId 租户id
 * @param {String} data.erpType erp版本
 * @param {String} data.detailType 详细版本
 */
export const updateErpMessageService = function (data, header) {
    let service = defineService({
        method: "POST",
        path: "/gwmanage/initRecord/updateErpMessage",
        header,
    });

    return service.invoke(data);
};
