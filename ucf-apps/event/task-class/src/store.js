import { observable, action, toJS, reaction, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { defaultListMap } from "utils/pageListUtils";
import { autoServiceMessage } from "utils/service";
import * as ownerService from "./service";
const initState = {
    dataSource: [],
    serviceCodeDiwork: "kflj_ssgj",
    ymsMicro: {},
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    getDataSource = async () => {
        let res = await autoServiceMessage({
            service: ownerService.getAllClassesListService({}, { serviceCode: this.state.serviceCodeDiwork }),
        });

        if (res) {
            this.changeState({
                dataSource: res.data,
            });
        }
    };

    save = async (param) => {
        let res = await autoServiceMessage({
            service: ownerService.upsertService(param, { serviceCode: this.state.serviceCodeDiwork }),
        });

        if (res) {
            this.getDataSource();
            return true;
        }
    };

    delete = async (param) => {
        let res = await autoServiceMessage({
            service: ownerService.deleteService(param, { serviceCode: this.state.serviceCodeDiwork }),
        });

        if (res) {
            this.getDataSource();
        }
    };
    download = async (param) => {
        let res = await autoServiceMessage({
            service: ownerService.downloadService(param, { serviceCode: this.state.serviceCodeDiwork }),
        });
        if (res) {
            const blob = res.data;
            const reader = new FileReader();
            reader.readAsDataURL(blob);
            reader.onload = (e) => {
                const a = document.createElement("a");
                a.download = res.headers["content-disposition"].split("=")[1];
                // a.download = `文件名称.zip`;
                // 后端设置的文件名称在res.headers的 "content-disposition": "form-data; name=\"attachment\"; filename=\"20181211191944.zip\"",
                a.href = e.target.result;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
            };
        }
    };
    // 获取YMS微服务列表
    getYmsMicro = async () => {
        let res = await autoServiceMessage({
            service: ownerService.getYmsMicroService(),
        });
        this.changeState({
            ymsMicro: res.data,
        });
    };
}

export const storeKey = "eventTaskClassStore";

export default Store;
