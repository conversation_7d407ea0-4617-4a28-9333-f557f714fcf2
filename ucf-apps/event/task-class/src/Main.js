import config from "./config";
import AsyncComponent from "components/AsyncComponent";
import Store, { storeKey } from "./store";

import mixCore from "core";
mixCore.addStore({
    storeKey,
    store: new Store(),
});

const Com = AsyncComponent((cb) => {
    require.ensure(
        [],
        (require) => {
            let com = require("./container");
            cb && cb(com.default);
        },
        "operate/api-logger/module"
    );
});

export default {
    ...config,
    component: Com,
};
