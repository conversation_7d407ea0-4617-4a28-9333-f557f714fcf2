import { defineService } from "utils/service";

/**
 * 任务分类-查询所有
 */
export const getAllClassesListService = function (data, header) {
    let service = defineService({
        method: "GET",
        path: "/gwmanage/gwportal/diwork/classes/getAll",
        header,
    });

    return service.invoke();
};

/**
 * 任务分类-新增/修改
 * @param {*} data
 * @param {String} data.code 编码
 * @param {String} data.name 名称
 * @param {Number} data.order
 */
export const upsertService = function (data, header) {
    let service = defineService({
        method: "POST",
        path: "/gwmanage/gwportal/diwork/classes/upsert",
        header,
    });

    return service.invoke(data);
};

/**
 * 任务分类-删除
 * @param {*} data
 * @param {String} data.code 编码
 */
export const deleteService = function (data, header) {
    let service = defineService({
        method: "GET",
        path: "/gwmanage/gwportal/diwork/classes/delete",
        header,
    });

    return service.invoke(data);
};

export const downloadService = function (data, header) {
    let service = defineService({
        method: "POST",
        path: "/gwmanage/export/mongoData",
        responseType: "blob", //下载流文件需要在这声明
        header,
    });

    return service.invoke(data);
};
// 获取yms微服务
export const getYmsMicroService = function (header) {
    let service = defineService({
        method: "GET",
        path: "/gwmanage/gwportal/diwork/msCode/listWithNoDomainPrefix",
        header,
    });
    return service.invoke();
};
