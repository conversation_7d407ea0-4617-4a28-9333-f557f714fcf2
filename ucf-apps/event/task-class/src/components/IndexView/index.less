.grid-header {
    &-btn {
        float: right;
        margin-right: 20px;
    }

    &::after {
        content: '.';
        display: block;
        visibility: hidden;
        clear: both;
        zoom: 1;
    }
}

.ucg-ma-modal {

    .u-modal-footer,
    .u-modal-header {
        border: 1px solid #E4E4E4 !important;
    }
}

.ucg-ma-grid-header {
    padding: 0;
}

.task-class {
    .u-form-control {
        width: 400px !important;
    }

    .u-form-item {
        padding: 0 10px;
        min-height: 48px;

        &:first-of-type {
            padding-top: 16px;
            height: 64px;
        }

        &:last-of-type {
            padding-bottom: 16px;
        }

        .error {
            display: block;
            margin-left: 85px;

            i {
                display: none;
            }
        }
    }
}