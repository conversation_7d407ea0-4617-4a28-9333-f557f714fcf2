/* eslint-disable react/no-unknown-property */
import React, { Component, Fragment } from "react";
import { Header } from "components/PageView/Header";
import { Content } from "components/PageView";
import { Modal, FormList, FormControl } from "components/TinperBee";
import Grid from "components/TinperBee/Grid";
import { Space, Button, Select } from "@tinper/next-ui";
import "./index.less";
const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
};
const FormItem = FormList.FormItem;
class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            showModal: false,
            selectedData: {},
        };
        this.form = React.createRef();
        this.columns = [
            {
                title: lang.templateByUuid("UID:P_UBL-FE_20096040042803A8", "序号") /* "序号" */,
                dataIndex: "order",
                key: "order",
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_20096040042803A4", "编码") /* "编码" */,
                dataIndex: "code",
                key: "code",
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_20096040042803A9", "名称") /* "名称" */,
                dataIndex: "name",
                key: "name",
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_20096040042803B0", "繁体名称") /* "繁体名称" */,
                dataIndex: "nameTw",
                key: "nameTw",
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_20096040042803B1", "英文名称") /* "英文名称" */,
                dataIndex: "nameEn",
                key: "nameEn",
            },
        ];
    }

    delete = (record) => {
        let { ownerStore } = this.props;
        Modal.confirm({
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042803B3", "确定要删除这条数据吗？") /* "确定要删除这条数据吗？" */,
            content: lang.templateByUuid("UID:P_UBL-FE_20096040042803B4", "数据删除后将不能恢复！") /* "数据删除后将不能恢复！" */,
            onOk() {
                ownerStore.delete({ code: record.code });
            },
        });
    };

    save = () => {
        let { ownerStore } = this.props;
        let { save } = ownerStore;
        this.form.current.validateFields().then(async (values) => {
            let res = await save(values);
            if (res) {
                this.setState({ showModal: false, selectedData: {} });
            }
        });
    };

    close = () => {
        this.setState({ showModal: false, selectedData: {} });
    };

    handleEdit = (record) => {
        if ("code" in record) {
            this.setState({ selectedData: record }, () => {
                this.form.current.setFieldsValue(record);
            });
        }
        this.setState({ showModal: true });
    };
    download = (record) => {
        // if ('code' in record) {
        //   this.setState({ selectedData: record });
        // }
        // this.setState({ showModal: true });
        this.props.ownerStore.download({ code: record.code });
    };
    hoverContent = (record, index) => {
        return (
            <Space fieldid="eafdac93-0b36-4954-91cc-da6c6fd9b6af">
                <Button fieldid="d6e9d3c5-3cf4-4cb3-b945-45d997aba3d8" {...Grid.hoverButtonPorps} onClick={this.handleEdit.bind(null, record)}>
                    {lang.templateByUuid("UID:P_UBL-FE_20096040042803AA", "编辑") /* "编辑" */}
                </Button>
                <Button fieldid="c959d543-2375-49e4-9b19-4eb2d9befe11" {...Grid.hoverButtonPorps} onClick={this.delete.bind(null, record)}>
                    {lang.templateByUuid("UID:P_UBL-FE_20096040042803AC", "删除") /* "删除" */}
                </Button>
                <Button fieldid="64ed0e15-0b8f-40fe-913c-2c30e0a023d8" {...Grid.hoverButtonPorps} onClick={this.download.bind(null, record)}>
                    {lang.templateByUuid("UID:P_UBL-FE_20096040042803AF", "下载预置数据") /* "下载预置数据" */}
                </Button>
            </Space>
        );
    };
    async componentDidUpdate(prevProps, prevState) {
        if (prevState.showModal !== this.state.showModal) {
            // 这里写您想要执行的代码
            const { ownerStore } = this.props;
            if (this.state.showModal) {
                await ownerStore.getYmsMicro();
            }
        }
    }
    render() {
        let { ownerState } = this.props;
        let { showModal } = this.state;
        let { dataSource, ymsMicro } = ownerState;
        return (
            <div className="task-class">
                <Header title={lang.templateByUuid("UID:P_UBL-FE_20096040042803B5", "任务分类") /* "任务分类" */} />
                <Content>
                    <Grid
                        fieldid="ublinker-task-class-src-components-IndexView-index-8160770-Grid"
                        header={
                            <div className="grid-header">
                                <Button fieldid="369d6cf9-dcd4-4114-8f29-1a7fd7cbaeb1" className="grid-header-btn" colors="primary" onClick={this.handleEdit}>
                                    {lang.templateByUuid("UID:P_UBL-FE_20096040042803A0", "新增") /* "新增" */}
                                </Button>
                            </div>
                        }
                        data={dataSource}
                        columns={this.columns}
                        hoverContent={this.hoverContent}
                    />

                    <Modal fieldid="2cb9f7f6-2027-4150-8c9e-8695ab0e6c94" className="ucg-ma-modal" show={showModal} onHide={this.close}>
                        <Modal.Header closeButton>
                            <Modal.Title>{lang.templateByUuid("UID:P_UBL-FE_20096040042803A3", "新增任务分类") /* "新增任务分类" */}</Modal.Title>
                        </Modal.Header>

                        <Modal.Body>
                            <FormList
                                fieldid="ublinker-task-class-src-components-IndexView-index-6060389-FormList"
                                size="sm"
                                ref={this.form}
                                {...formItemLayout}
                                className="form"
                            >
                                <FormItem
                                    label={lang.templateByUuid("UID:P_UBL-FE_20096040042803A4", "编码") /* "编码" */}
                                    name="code"
                                    rules={[
                                        {
                                            required: true,
                                            message: lang.templateByUuid("UID:P_UBL-FE_20096040042803A7", "请输入任务分类编码") /* "请输入任务分类编码" */,
                                        },
                                    ]}
                                >
                                    <FormControl fieldid="ublinker-task-class-src-components-IndexView-index-7717917-FormControl" />
                                </FormItem>

                                <FormItem
                                    label={lang.templateByUuid("UID:P_UBL-FE_20096040042803A9", "名称") /* "名称" */}
                                    name="name"
                                    rules={[
                                        {
                                            required: true,
                                            message: lang.templateByUuid("UID:P_UBL-FE_20096040042803AD", "请输入任务分类名称") /* "请输入任务分类名称" */,
                                        },
                                    ]}
                                >
                                    <FormControl fieldid="ublinker-task-class-src-components-IndexView-index-2995721-FormControl" />
                                </FormItem>

                                <FormItem
                                    label={lang.templateByUuid("UID:P_UBL-FE_20096040042803B0", "繁体名称") /* "繁体名称" */}
                                    name="nameTw"
                                    rules={[
                                        {
                                            required: true,
                                            message: lang.templateByUuid(
                                                "UID:P_UBL-FE_20096040042803B2",
                                                "请输入任务分类繁体名称" //@notranslate
                                            ) /* "请输入任务分类繁体名称" */,
                                        },
                                    ]}
                                >
                                    <FormControl fieldid="ublinker-task-class-src-components-IndexView-index-4686102-FormControl" />
                                </FormItem>

                                <FormItem
                                    label={lang.templateByUuid("UID:P_UBL-FE_20096040042803B1", "英文名称") /* "英文名称" */}
                                    name="nameEn"
                                    rules={[
                                        {
                                            required: true,
                                            message: lang.templateByUuid(
                                                "UID:P_UBL-FE_20096040042803B6",
                                                "请输入任务分类英文名称" //@notranslate
                                            ) /* "请输入任务分类英文名称" */,
                                        },
                                    ]}
                                >
                                    <FormControl fieldid="ublinker-task-class-src-components-IndexView-index-2058531-FormControl" />
                                </FormItem>

                                <FormItem
                                    label={lang.templateByUuid("UID:P_UBL-FE_20096040042803A1", "排序") /* "排序" */}
                                    name="order"
                                    rules={[
                                        {
                                            required: true,
                                            message: lang.templateByUuid(
                                                "UID:P_UBL-FE_20096040042803A2",
                                                "请输入任务分类排序序号" //@notranslate
                                            ) /* "请输入任务分类排序序号" */,
                                        },
                                    ]}
                                >
                                    <FormControl fieldid="ublinker-task-class-src-components-IndexView-index-7965317-FormControl" />
                                </FormItem>
                                {JSON.parse(localStorage.getItem("ublinkerEnv") || "{}").env === "test" ? (
                                    <FormItem
                                        label={lang.templateByUuid("UID:P_UBL-FE_20096040042803A5", "YMS微服务") /* "YMS微服务" */}
                                        name="microServiceCode"
                                        validateTrigger="onChange"
                                    >
                                        <Select
                                            showSearch
                                            allowClear
                                            optionFilterProp="children"
                                            placeholder={lang.templateByUuid("UID:P_UBL-FE_20096040042803A6", "请选择") /* "请选择" */}
                                        >
                                            {Object.entries(ymsMicro || {}).map(([key, value]) => (
                                                <option key={value} value={value}>
                                                    {key}
                                                </option>
                                            ))}
                                        </Select>
                                    </FormItem>
                                ) : null}
                            </FormList>
                        </Modal.Body>

                        <Modal.Footer>
                            <Button fieldid="cdba1b40-f660-48aa-825a-c3abbbaa571e" className="ucg-mr-10" onClick={this.close} colors="secondary">
                                {lang.templateByUuid("UID:P_UBL-FE_20096040042803AB", "取消") /* "取消" */}
                            </Button>
                            <Button fieldid="4574743d-0e55-4884-8823-ea2914fd34e1" onClick={this.save} colors="primary">
                                {lang.templateByUuid("UID:P_UBL-FE_20096040042803AE", "确定") /* "确定" */}
                            </Button>
                        </Modal.Footer>
                    </Modal>
                </Content>
            </div>
        );
    }
}

export default IndexView;
