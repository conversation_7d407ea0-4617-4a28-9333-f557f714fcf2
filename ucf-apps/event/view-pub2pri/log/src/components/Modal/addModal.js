import React, { Component } from "react";
import { <PERSON><PERSON>, Modal, Message } from "components/TinperBee";
import Grid from "components/TinperBee/Grid";
class AddModal extends Component {
    constructor(props) {
        super(props);
        this.state = {
            selectedList: [],
        };
    }

    columns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_200960400428026B", "档案名称") /* "档案名称" */,
            dataIndex: "defdecname",
            key: "defdecname",
            width: "45%",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280267", "事件类型") /* "事件类型" */,
            dataIndex: "eventtype",
            key: "eventtype",
            width: "45%",
        },
    ];

    gridSelectFunc = (selectedList) => {
        this.setState({
            selectedList,
        });
    };

    onConfirm = async () => {
        let { selectedList } = this.state;
        let { save, close } = this.props;
        if (selectedList.length == 0) {
            Message.create({ content: "", color: "danger" });
        } else {
            let res = await save(selectedList);
            if (res) {
                close();
            }
        }
    };

    render() {
        let { showModal, close, dataSource } = this.props;
        let { selectedList } = this.state;
        let pagination = {
            total: dataSource.total,
            items: dataSource.totalPages,
            pageSize: dataSource.pageSize,
            activePage: dataSource.pageNo,
            onSelect: this.onPagiSelect,
            onDataNumSelect: this.onDataNumSelect,
        };
        return (
            <Modal fieldid="ublinker-log-src-components-Modal-addModal-9178664-Modal" show={showModal} onHide={close}>
                <Modal.Header closeButton>
                    <Modal.Title>{lang.templateByUuid("UID:P_UBL-FE_200960400428026A", "新增订阅") /* "新增订阅" */}</Modal.Title>
                </Modal.Header>

                <Modal.Body>
                    <Grid
                        fieldid="ublinker-log-src-components-Modal-addModal-6673392-Grid"
                        data={dataSource.list}
                        columns={this.columns}
                        multiSelect={true}
                        selectedList={selectedList}
                        getSelectedDataFunc={this.gridSelectFunc}
                        pagination={dataSource.total > 10 ? pagination : null}
                    />
                </Modal.Body>

                <Modal.Footer>
                    <Button fieldid="ublinker-log-src-components-Modal-addModal-4240659-Button" onClick={close} colors="secondary" style={{ marginRight: 16 }}>
                        {lang.templateByUuid("UID:P_UBL-FE_2009604004280268", "取消") /* "取消" */}
                    </Button>
                    <Button fieldid="ublinker-log-src-components-Modal-addModal-4469032-Button" onClick={this.onConfirm} colors="primary">
                        {lang.templateByUuid("UID:P_UBL-FE_2009604004280269", "确定") /* "确定" */}
                    </Button>
                </Modal.Footer>
            </Modal>
        );
    }
}

export default AddModal;
