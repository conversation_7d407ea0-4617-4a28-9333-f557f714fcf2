import React, { Component, Fragment } from "react";
import { Header } from "components/PageView/Header";
import { Content } from "components/PageView";
import { But<PERSON>, Select, DatePicker } from "components/TinperBee";
import SearchPanel from "components/SearchPanel";
import FormList from "components/TinperBee/Form";
import Grid from "components/TinperBee/Grid";
import "./index.less";

const { RangePicker } = DatePicker;
const Option = Select.Option;
class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            showModal: false,
            selectedList: [],
        };
    }

    columns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042803F1", "事件ID") /* "事件ID" */,
            dataIndex: "defdecname",
            key: "defdecname",
            width: "300px",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042803F6", "事件源") /* "事件源" */,
            dataIndex: "eventtype",
            key: "eventtype",
            width: "150px",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042803F7", "事件类型") /* "事件类型" */,
            dataIndex: "defdecname",
            key: "defdecname",
            width: "150px",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042803F8", "节点编码") /* "节点编码" */,
            dataIndex: "eventtype",
            key: "eventtype",
            width: "150px",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042803F9", "租户编码") /* "租户编码" */,
            dataIndex: "defdecname",
            key: "defdecname",
            width: "100px",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042803FC", "发送地址") /* "发送地址" */,
            dataIndex: "eventtype",
            key: "eventtype",
            width: "300px",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042803FD", "开始时间") /* "开始时间" */,
            dataIndex: "defdecname",
            key: "defdecname",
            width: "100px",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042803E9", "结束时间") /* "结束时间" */,
            dataIndex: "eventtype",
            key: "eventtype",
            width: "100px",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042803EA", "事件状态") /* "事件状态" */,
            dataIndex: "state",
            key: "state",
            width: "80px",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042803EC", "操作") /* "操作" */,
            dataIndex: "action",
            key: "action",
            width: "240px",
            render: (value, record) => {
                return (
                    <Fragment>
                        <a fieldid="ublinker-log-src-components-IndexView-index-5128104-a" onClick={this.handleDelete.bind(null, record)}>
                            {lang.templateByUuid("UID:P_UBL-FE_20096040042803EF", "事件内容") /* "事件内容" */}
                        </a>
                        <a fieldid="ublinker-log-src-components-IndexView-index-395322-a" onClick={this.handleDelete.bind(null, record)}>
                            {lang.templateByUuid("UID:P_UBL-FE_20096040042803F2", "返回信息") /* "返回信息" */}
                        </a>
                        <a fieldid="ublinker-log-src-components-IndexView-index-2202748-a" onClick={this.handleDelete.bind(null, record)}>
                            {lang.templateByUuid("UID:P_UBL-FE_20096040042803F4", "错误信息") /* "错误信息" */}
                        </a>
                    </Fragment>
                );
            },
        },
    ];

    gridSelectFunc = (selectedList) => {
        this.setState({
            selectedList,
        });
    };

    onPagiSelect = (active) => {
        this.props.ownerStore.getDataSource({
            pageNo: active,
        });
    };

    onDataNumSelect = (index, value) => {
        this.props.ownerStore.getDataSource({
            pageSize: value,
            pageNo: 1,
        });
    };

    handleChange = (type, value) => {
        let { handleChange } = this.props.ownerStore;
        handleChange(type, value);
    };

    render() {
        let { ownerState, ownerStore } = this.props;
        let { showModal, selectedList } = this.state;
        let { dataSource, query } = ownerState;
        let { getDataSource } = ownerStore;
        let pagination = {
            total: dataSource.total,
            items: dataSource.totalPages,
            pageSize: dataSource.pageSize,
            activePage: dataSource.pageNo,
            onSelect: this.onPagiSelect,
            onDataNumSelect: this.onDataNumSelect,
        };
        return (
            <Fragment>
                <Header title={lang.templateByUuid("UID:P_UBL-FE_20096040042803EB", "日志查看") /* "日志查看" */}></Header>
                <Content
                    contentTop={
                        <SearchPanel search={getDataSource} reset={getDataSource}>
                            <FormList fieldid="ublinker-log-src-components-IndexView-index-9664314-FormList" layoutOpt={{ md: 4, xs: 4 }}>
                                <FormList.Item label={lang.templateByUuid("UID:P_UBL-FE_20096040042803ED", "档案类型") /* "档案类型" */} labelCol={100}>
                                    <Select
                                        fieldid="ublinker-log-src-components-IndexView-index-3141225-Select"
                                        value={query.defdecname}
                                        onChange={this.handleChange.bind(null, "defdecname")}
                                    >
                                        <Option fieldid="UCG-FE-log-src-components-IndexView-index-8308417-Option" value="">
                                            {lang.templateByUuid("UID:P_UBL-FE_20096040042803EE", "全部") /* "全部" */}
                                        </Option>
                                        <Option fieldid="UCG-FE-log-src-components-IndexView-index-7650026-Option" value="confirming">
                                            {lang.templateByUuid("UID:P_UBL-FE_20096040042803F0", "待确认") /* "待确认" */}
                                        </Option>
                                        <Option fieldid="UCG-FE-log-src-components-IndexView-index-692451-Option" value="executing">
                                            {lang.templateByUuid("UID:P_UBL-FE_20096040042803F3", "执行中") /* "执行中" */}
                                        </Option>
                                        <Option fieldid="UCG-FE-log-src-components-IndexView-index-9154563-Option" value="termination">
                                            {lang.templateByUuid("UID:P_UBL-FE_20096040042803F5", "终止") /* "终止" */}
                                        </Option>
                                    </Select>
                                </FormList.Item>

                                <FormList.Item label={lang.templateByUuid("UID:P_UBL-FE_20096040042803F7", "事件类型") /* "事件类型" */} labelCol={100}>
                                    <Select
                                        fieldid="ublinker-log-src-components-IndexView-index-1644891-Select"
                                        value={query.defdecname}
                                        onChange={this.handleChange.bind(null, "defdecname")}
                                    >
                                        <Option fieldid="UCG-FE-log-src-components-IndexView-index-2283225-Option" value="">
                                            {lang.templateByUuid("UID:P_UBL-FE_20096040042803EE", "全部") /* "全部" */}
                                        </Option>
                                        <Option fieldid="UCG-FE-log-src-components-IndexView-index-9856950-Option" value="confirming">
                                            {lang.templateByUuid("UID:P_UBL-FE_20096040042803F0", "待确认") /* "待确认" */}
                                        </Option>
                                        <Option fieldid="UCG-FE-log-src-components-IndexView-index-5948991-Option" value="executing">
                                            {lang.templateByUuid("UID:P_UBL-FE_20096040042803F3", "执行中") /* "执行中" */}
                                        </Option>
                                        <Option fieldid="UCG-FE-log-src-components-IndexView-index-5544315-Option" value="termination">
                                            {lang.templateByUuid("UID:P_UBL-FE_20096040042803F5", "终止") /* "终止" */}
                                        </Option>
                                    </Select>
                                </FormList.Item>

                                <FormList.Item label={lang.templateByUuid("UID:P_UBL-FE_20096040042803FA", "同步时间") /* "同步时间" */} labelCol={100}>
                                    <RangePicker
                                        fieldid="UCG-FE-log-src-components-IndexView-index-8367253-RangePicker"
                                        placeholder={lang.templateByUuid("UID:P_UBL-FE_20096040042803FB", "开始 ~ 结束") /* "开始 ~ 结束" */}
                                        format="YYYY-MM-DD"
                                    />
                                </FormList.Item>
                            </FormList>
                        </SearchPanel>
                    }
                >
                    <Grid
                        fieldid="ublinker-log-src-components-IndexView-index-2658049-Grid"
                        header={
                            <div className="grid-header btn">
                                <Button
                                    fieldid="ublinker-log-src-components-IndexView-index-9250249-Button"
                                    colors="primary"
                                    onClick={this.open}
                                    className="grid-header-right"
                                >
                                    {lang.templateByUuid("UID:P_UBL-FE_20096040042803FE", "重试") /* "重试" */}
                                </Button>
                            </div>
                        }
                        data={dataSource.list}
                        columns={this.columns}
                        multiSelect={true}
                        selectedList={selectedList}
                        getSelectedDataFunc={this.gridSelectFunc}
                        pagination={dataSource.total > 10 ? pagination : null}
                    />
                </Content>
            </Fragment>
        );
    }
}

export default IndexView;
