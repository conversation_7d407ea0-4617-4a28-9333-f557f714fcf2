import { defineService } from "utils/service";

export const getMyIsvList = function (data) {
    let service = defineService({
        method: "GET",
        path: "/openConsole/isv/myIsvList",
    });

    return service.invoke(data);
};

export const setDefaultService = function (data) {
    let service = defineService({
        method: "GET",
        path: `/openConsole/isv/setDefault/${data}`,
    });

    return service.invoke();
};

export const getVerifyUrlService = function (data) {
    let service = defineService({
        method: "GET",
        path: "/openConsole/isv/getVerifyUrl",
    });

    return service.invoke(data);
};
