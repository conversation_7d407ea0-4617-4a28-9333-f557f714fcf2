import { observable, action, toJS, reaction, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { defaultListMap } from "utils/pageListUtils";
import { autoServiceMessage } from "utils/service";
import * as ownerService from "./service";
const initState = {
    dataSource: {
        ...defaultListMap,
    },
    query: {
        defdecname: "",
        callbackurl: "",
        erptype: "",
        viewcode: "",
    },
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    getDataSource = async () => {
        let res = await autoServiceMessage({
            service: ownerService.getMyIsvList(),
        });

        if (res) {
            this.changeState({ dataSource: res.data });
        }
    };

    handleChange = (type, value) => {
        let { query } = this.state;
        query[type] = value;
        this.changeState({ query });
    };
}
/**
 * 根据具体模块修改 storeKey 为保证唯一性
 * storeKey 由 模块文件夹名称 + 页面名称 + 'Store' 小驼峰
 *
 * storeKey是作为当前 Store 的唯一键， storeKey与Store的实例会在 rootStore或者StoreMap中以 键值对的 形式存在
 * 1. 用于在模块的 storeMap（模块routes/store.js）中注册并初始化当前Store
 * 2. 用于在container的 inject 中关联当前Store的实例时，从模块的storeMap或者rootStore中获取对应store实例
 * */
export const storeKey = "systemDateStore";

export default Store;
