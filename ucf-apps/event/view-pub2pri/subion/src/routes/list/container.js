import React, { Component } from "react";
import IndexView from "./components/IndexView";
import { inject, observer } from "mobx-react";
import { storeKey } from "./store";
@inject((rootStore) => {
    let ownerStore = rootStore[storeKey];
    return {
        ownerState: ownerStore.toJS(),
        ownerStore: ownerStore,
    };
})
@observer
class Container extends Component {
    constructor(props) {
        super(props);
    }

    componentDidMount() {
        this.props.ownerStore.getDataSource();
    }

    render() {
        return <IndexView {...this.props} />;
    }
}
export default Container;
