import { observable, action, toJS, reaction, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { defaultListMap } from "utils/pageListUtils";
import { autoServiceMessage } from "utils/service";
import * as ownerService from "./service";
const initState = {
    dataSource: {
        ...defaultListMap,
    },
    pagination: null,
    addDataSource: {
        ...defaultListMap,
    },
    addPagination: null,
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    getDataSource = async (param) => {
        this.getPagesListFunc({
            service: ownerService.getPageList,
            requestData: param,
            dataKey: "dataSource",
        });
    };

    getAddDataSource = async (param) => {
        let res = await this.getPagesListFunc({
            service: ownerService.getDataTypeList,
            requestData: param,
            dataKey: "addDataSource",
            paginationKey: "addPagination",
            onPageChange: this.getAddDataSource,
        });

        return res;
    };

    enable = async (id) => {
        let res = await autoServiceMessage({
            service: ownerService.enableEventType(id),
            success: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050089") /* "操作成功！" */,
        });

        if (res) {
            return true;
        }
    };

    disable = async (id) => {
        let res = await autoServiceMessage({
            service: ownerService.disableEventType(id),
            success: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050089") /* "操作成功！" */,
        });

        if (res) {
            return true;
        }
    };

    del = async (id) => {
        let res = await autoServiceMessage({
            service: ownerService.delForTenant(id),
            success: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050081") /* "删除成功！" */,
        });

        if (res) {
            return true;
        }
    };

    save = async (param) => {
        let res = await autoServiceMessage({
            service: ownerService.addForTenant(param),
            success: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050853") /* "新增订阅成功！" */,
        });

        if (res) {
            return true;
        }
    };
}
/**
 * 根据具体模块修改 storeKey 为保证唯一性
 * storeKey 由 模块文件夹名称 + 页面名称 + 'Store' 小驼峰
 *
 * storeKey是作为当前 Store 的唯一键， storeKey与Store的实例会在 rootStore或者StoreMap中以 键值对的 形式存在
 * 1. 用于在模块的 storeMap（模块routes/store.js）中注册并初始化当前Store
 * 2. 用于在container的 inject 中关联当前Store的实例时，从模块的storeMap或者rootStore中获取对应store实例
 * */
export const storeKey = "systemSubscriptListStore";

export default Store;
