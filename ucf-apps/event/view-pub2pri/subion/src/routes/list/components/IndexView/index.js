import React, { Component, Fragment } from "react";
import { Button, Space } from "@tinper/next-ui";
import { Content } from "components/PageView";
import { Switch, Modal } from "components/TinperBee";
import Grid from "components/TinperBee/Grid";
import AddModal from "../Modal/addModal.js";
import "./index.less";
import withRouter from "decorator/withRouter";

@withRouter
class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            showModal: false,
        };
    }

    columns = [
        {
            title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050830") /* "档案名称" */,
            dataIndex: "defdocname",
            key: "defdocname",
            width: "20%",
        },
        {
            title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050832") /* "事件类型" */,
            dataIndex: "eventtypename",
            key: "eventtypename",
            width: "20%",
        },
        {
            title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050833") /* "启用" */,
            dataIndex: "enable",
            key: "enable",
            width: "20%",
            render: (value, record, index) => {
                return (
                    <Switch
                        fieldid="ublinker-routes-list-components-IndexView-index-6182382-Switch"
                        size="sm"
                        checked={value}
                        onChange={this.handleSwitch.bind(null, record, index)}
                    ></Switch>
                );
            },
        },
        {
            title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050028") /* "操作" */,
            dataIndex: "action",
            key: "action",
            width: "20%",
            render: (value, record) => {
                return <Fragment></Fragment>;
            },
        },
    ];
    hoverContent = (record, index) => {
        return (
            <Space fieldid="0e753e30-b155-4aff-90f2-ea502c180391">
                <Button fieldid="982aa7a9-f871-47e6-b09e-6fa3d067266e" {...Grid.hoverButtonPorps} onClick={this.goDataDetail.bind(null, record)}>
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050839") /* "数据查看" */}
                </Button>
                <Button fieldid="e7836b22-3b87-4cad-987b-f88530e1b2b2" {...Grid.hoverButtonPorps} onClick={this.goLogDetail.bind(null, record)}>
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050842") /* "日志查看" */}
                </Button>
                <Button fieldid="b7562e8a-f916-490b-8498-01cd52476f32" {...Grid.hoverButtonPorps} onClick={this.handleDelete.bind(null, record)}>
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050046") /* "删除" */}
                </Button>
            </Space>
        );
    };
    goDataDetail = (record) => {
        this.props.navigate(`/dataDetail/${record.typetag}`);
    };

    goLogDetail = (record) => {
        this.props.navigate(`/logDetail?eventtype=${record.eventtypecode}&typetag=${record.typetag}`);
    };

    onPagiSelect = (active) => {
        this.props.ownerStore.getDataSource({
            pageNo: active,
        });
    };

    onDataNumSelect = (index, value) => {
        this.props.ownerStore.getDataSource({
            pageSize: value,
            pageNo: 1,
        });
    };

    handleSwitch = async (record, index) => {
        let { ownerStore, ownerState } = this.props;
        let { enable, disable, changeState } = ownerStore;
        let { dataSource } = ownerState;
        let res = null;
        if (record.enable) {
            res = disable(record.pk_id);
        } else {
            res = enable(record.pk_id);
        }
        if (res) {
            dataSource.list[index].enable = !dataSource.list[index].enable;
            changeState({ dataSource });
        }
    };

    handleDelete = (record) => {
        Modal.confirm({
            title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050844") /* "确定要删除这条数据吗？" */,
            content: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050838") /* "删除后将不能恢复。" */,
            onOk: async () => {
                let { del, getDataSource } = this.props.ownerStore;
                let res = await del(record.pk_id);
                if (res) {
                    getDataSource();
                }
            },
        });
    };

    close = () => {
        this.setState({ showModal: false });
    };

    open = async () => {
        let res = await this.props.ownerStore.getAddDataSource();
        if (res) {
            this.setState({ showModal: true });
        }
    };

    save = async (param) => {
        let { getDataSource, save } = this.props.ownerStore;
        let res = await save(param);
        if (res) {
            getDataSource();
            this.close();
        }
    };

    render() {
        let { ownerState } = this.props;
        let { showModal } = this.state;
        let { dataSource, pagination, addDataSource, addPagination } = ownerState;
        return (
            <div className="my-subion">
                <Content>
                    <Grid
                        fieldid="ublinker-routes-list-components-IndexView-index-9587826-Grid"
                        header={
                            <div className="grid-header">
                                <div className="grid-header-title">{/*{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050836") /* "我的订阅" *!/*/}</div>
                                <Button
                                    fieldid="ublinker-routes-list-components-IndexView-index-8718452-Button"
                                    colors="primary"
                                    onClick={this.open}
                                    className="grid-header-right"
                                >
                                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050845") /* "新增" */}
                                </Button>
                            </div>
                        }
                        data={dataSource.list}
                        columns={this.columns}
                        pagination={pagination}
                        hoverContent={this.hoverContent}
                    />

                    {showModal ? (
                        <AddModal showModal={showModal} close={this.close} save={this.save} dataSource={addDataSource} pagination={addPagination} />
                    ) : null}
                </Content>
            </div>
        );
    }
}

export default IndexView;
