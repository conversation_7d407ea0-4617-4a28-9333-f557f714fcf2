import React, { Component } from "react";
import { <PERSON><PERSON>, Modal, Message } from "components/TinperBee";
import Grid from "components/TinperBee/Grid";
import "./index.less";
class AddModal extends Component {
    constructor(props) {
        super(props);
        this.state = {
            selectedList: [],
        };
    }

    columns = [
        {
            title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050830") /* "档案名称" */,
            dataIndex: "defdocname",
            key: "defdocname",
            width: "45%",
        },
        {
            title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050832") /* "事件类型" */,
            dataIndex: "eventtypename",
            key: "eventtypename",
            width: "45%",
        },
    ];

    gridSelectFunc = (selectedList) => {
        this.setState({
            selectedList,
        });
    };

    onConfirm = async () => {
        let { selectedList } = this.state;
        let { save, close } = this.props;
        if (selectedList.length == 0) {
            Message.create({ content: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050852") /* "请选择需要新增的订阅事件！" */, color: "danger" });
        } else {
            let res = await save(selectedList[0]);
            if (res) {
                close();
            }
        }
    };

    render() {
        let { showModal, close, dataSource, pagination } = this.props;
        let { selectedList } = this.state;
        return (
            <Modal fieldid="ublinker-routes-list-components-Modal-addModal-5254330-Modal" show={showModal} onHide={close} className="modal-add ucg-ma-modal">
                <Modal.Header closeButton>
                    <Modal.Title>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050826") /* "新增订阅" */}</Modal.Title>
                </Modal.Header>

                <Modal.Body>
                    <Grid
                        fieldid="ublinker-routes-list-components-Modal-addModal-9080061-Grid"
                        className="modal-grid"
                        dataKey="pk_id"
                        tableType="modal"
                        data={dataSource.list}
                        columns={this.columns}
                        radioSelect={true}
                        selectedList={selectedList}
                        getSelectedDataFunc={this.gridSelectFunc}
                        pagination={pagination}
                    />
                </Modal.Body>

                <Modal.Footer>
                    <Button
                        fieldid="ublinker-routes-list-components-Modal-addModal-7935671-Button"
                        onClick={close}
                        colors="secondary"
                        style={{ marginRight: 16 }}
                    >
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050063") /* "取消" */}
                    </Button>
                    <Button fieldid="ublinker-routes-list-components-Modal-addModal-6626002-Button" onClick={this.onConfirm} colors="primary">
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050099") /* "确定" */}
                    </Button>
                </Modal.Footer>
            </Modal>
        );
    }
}

export default AddModal;
