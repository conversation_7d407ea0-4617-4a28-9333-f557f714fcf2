import { defineService } from "utils/service";

/**
 * 启用
 * @param string id 事件id
 */
export const enableEventType = function (data) {
    let service = defineService({
        method: "POST",
        path: `/gwmanage/gwportal/diwork/erpdata/datatypeeventtypemap/enable/${data}`,
    });

    return service.invoke();
};

/**
 * 停用
 * @param string id 事件id
 */
export const disableEventType = function (data) {
    let service = defineService({
        method: "POST",
        path: `/gwmanage/gwportal/diwork/erpdata/datatypeeventtypemap/disable/${data}`,
    });

    return service.invoke();
};

/**
 * 我的订阅-系统-分页查询
 */
export const getDataTypeList = function (data) {
    let service = defineService({
        method: "GET",
        path: "/gwmanage/gwportal/diwork/erpdata/datatypeeventtypemap/pageNeedAddForTenant",
    });

    return service.invoke(data);
};

/**
 * 我的订阅分页查询
 */
export const getPageList = function (data) {
    let service = defineService({
        method: "GET",
        path: "/gwmanage/gwportal/diwork/erpdata/datatypeeventtypemap/pageForTenant",
    });

    return service.invoke(data);
};

/**
 * 新增订阅
 * @param string eventtypecode
 * @param string typetag
 */
export const addForTenant = function (data) {
    let service = defineService({
        method: "POST",
        path: "/gwmanage/gwportal/diwork/erpdata/datatypeeventtypemap/addForTenant",
    });

    return service.invoke(data);
};

/**
 * 删除订阅
 */
export const delForTenant = function (data) {
    let service = defineService({
        method: "POST",
        path: `/gwmanage/gwportal/diwork/erpdata/datatypeeventtypemap/delForTenant/${data}`,
    });

    return service.invoke();
};
