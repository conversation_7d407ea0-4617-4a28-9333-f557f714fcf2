import { defineService } from "utils/service";

/**
 * 删除档案事件类型那个绑定数据
 * @param string id 事件id
 */
export const delDataTypeEventType = function (data) {
    let service = defineService({
        method: "POST",
        path: `/gwmanage/erpdata/datatypeeventtypemap/del/${data}`,
    });

    return service.invoke();
};

/**
 * 新建档案事件类型那个绑定数据
 * @param string eventtypeid
 * @param string eventtypecode
 * @param string eventtypename
 * @param string typetag
 * @param string defdocid
 * @param string defdocname
 */
export const addDataTypeEventType = function (data) {
    let service = defineService({
        method: "POST",
        path: "/gwmanage/erpdata/datatypeeventtypemap/add",
    });

    return service.invoke(data);
};

/**
 * 根据id查询档案事件类型那个绑定数据
 * @param string id 事件id
 */
export const getDataTypeEventType = function (data) {
    let service = defineService({
        method: "GET",
        path: `/gwmanage/erpdata/datatypeeventtypemap/get/${data}`,
    });

    return service.invoke();
};

/**
 * 编辑档案事件类型那个绑定数据
 * @param string id 事件id
 * @param string eventtypeid
 * @param string eventtypecode
 * @param string eventtypename
 * @param string typetag
 * @param string defdocid
 * @param string defdocname
 */
export const editDataTypeEventType = function (data) {
    let service = defineService({
        method: "POST",
        path: `/gwmanage/erpdata/datatypeeventtypemap/edit/${data.id}`,
    });

    return service.invoke(data);
};

/**
 * 我的订阅-系统-分页查询
 */
export const getDataTypeList = function (data) {
    let service = defineService({
        method: "GET",
        path: "/gwmanage/erpdata/datatypeeventtypemap/page",
    });

    return service.invoke(data);
};

/**
 * 获取全部事件类型数据
 */
export const getAllEventType = function (data) {
    let service = defineService({
        method: "GET",
        path: "/gwmanage/erpdata/eventtype/listAll",
    });

    return service.invoke(data);
};

/**
 * 分页获取档案信息
 */
export const getIndexDataWithEvent = function (data) {
    let service = defineService({
        method: "GET",
        path: "/gwmanage/erpdata/datatype/getIndexDataWithEvent",
    });

    return service.invoke(data);
};
