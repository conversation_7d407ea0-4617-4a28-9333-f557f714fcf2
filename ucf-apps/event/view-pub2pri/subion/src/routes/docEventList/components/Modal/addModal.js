import React, { Component } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, FormList, Icon, Select, Message } from "components/TinperBee";
import { UcfReferInput } from "../Refer/index";
import "./index.less";
const Option = Select.Option;
const FormItem = FormList.FormItem;
const AddModal = FormList.createForm()(
    class AddModal extends Component {
        constructor(props) {
            super(props);
        }

        componentDidMount() {
            let {
                dataSource,
                form: { setFieldsValue },
            } = this.props;
            if ("pk_id" in dataSource) {
                setFieldsValue({
                    eventtypecode: dataSource.eventtypecode,
                });
            }
        }

        handleDocChange = (v) => {
            let { dataSource, handleChange } = this.props;
            dataSource.defdocname = v.typename;
            dataSource.defdocid = v.id;
            dataSource.typetag = v.typetag;
            handleChange(dataSource);
        };

        onConfirm = async () => {
            let { save, dataSource, handleChange, eventList } = this.props;
            this.props.form.validateFields((err, values) => {
                if (!err) {
                    if (dataSource.defdocname.length == 0) {
                        Message.create({ content: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050841") /* "请选择档案！" */, color: "danger" });
                        return null;
                    }
                    dataSource.eventtypecode = values.eventtypecode;
                    eventList.find((item) => {
                        if (item.code == values.eventtypecode) {
                            dataSource.eventtypeid = item.id;
                            dataSource.eventtypename = item.name;
                        }
                    });
                    handleChange(dataSource);
                    save();
                }
            });
        };

        render() {
            let { getFieldProps, getFieldError } = this.props.form;
            let { showModal, close, eventList, dataSource } = this.props;
            let { defdocname, defdocid } = dataSource;

            return (
                <Modal
                    fieldid="ublinker-routes-docEventList-components-Modal-addModal-7222619-Modal"
                    className="ucg-ma-modal modal-add"
                    show={showModal}
                    onHide={close}
                >
                    <Modal.Header closeButton>
                        <Modal.Title>
                            {
                                "pk_id" in dataSource
                                    ? window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050827") /* "编辑档案事件类型" */
                                    : window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050851") /* "新增档案事件类型" */
                            }
                        </Modal.Title>
                    </Modal.Header>

                    <Modal.Body>
                        <FormItem fieldid="UCG-FE-routes-docEventList-components-Modal-addModal-1366317-FormItem">
                            <span>
                                <Icon fieldid="ublinker-routes-docEventList-components-Modal-addModal-6355122-Icon" type="uf-mi" className="mast"></Icon>
                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050828") /* "档案" */}
                            </span>
                            <UcfReferInput
                                buttonType={"a"}
                                searchField={"key"}
                                selectedList={[{ id: defdocid, name: defdocname }]}
                                onOk={(v) => this.handleDocChange(v[0])}
                            ></UcfReferInput>
                        </FormItem>

                        <FormItem fieldid="UCG-FE-routes-docEventList-components-Modal-addModal-9355060-FormItem">
                            <span>
                                <Icon fieldid="ublinker-routes-docEventList-components-Modal-addModal-3056716-Icon" type="uf-mi" className="mast"></Icon>
                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050832") /* "事件类型" */}
                            </span>
                            <Select
                                fieldid="ublinker-routes-docEventList-components-Modal-addModal-643954-Select"
                                className={getFieldError("eventtypecode") ? "form-error" : ""}
                                {...getFieldProps("eventtypecode", {
                                    validateTrigger: "onBlur",
                                    rules: [
                                        {
                                            required: true,
                                            message: (
                                                <span>
                                                    <Icon fieldid="ublinker-routes-docEventList-components-Modal-addModal-892157-Icon" type="uf-exc-t"></Icon>
                                                    <span>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050850") /* "请选择事件类型" */}</span>
                                                </span>
                                            ),
                                        },
                                    ],
                                })}
                            >
                                {eventList.map((item) => {
                                    return (
                                        <Option fieldid="UCG-FE-routes-docEventList-components-Modal-addModal-5342687-Option" key={item.code} value={item.code}>
                                            {item.name}
                                        </Option>
                                    );
                                })}
                            </Select>
                            <span className="error">{getFieldError("eventtypecode")}</span>
                        </FormItem>
                    </Modal.Body>

                    <Modal.Footer>
                        <Button
                            fieldid="ublinker-routes-docEventList-components-Modal-addModal-4378613-Button"
                            onClick={close}
                            colors="secondary"
                            style={{ marginRight: 16 }}
                        >
                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050063") /* "取消" */}
                        </Button>
                        <Button fieldid="ublinker-routes-docEventList-components-Modal-addModal-2341278-Button" onClick={this.onConfirm} colors="primary">
                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050099") /* "确定" */}
                        </Button>
                    </Modal.Footer>
                </Modal>
            );
        }
    }
);

export default AddModal;
