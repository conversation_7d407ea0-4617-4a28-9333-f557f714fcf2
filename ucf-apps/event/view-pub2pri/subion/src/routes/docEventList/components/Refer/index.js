import React from "react";
import { WithInput as WithInputBase } from "components/Refer";
import * as ownerService from "../../service";
import Highlight from "components/Highlight";

export const UcfReferInput = (props) => {
    let renderHighlight = (content) => {
        let { requestParam } = props;
        return <Highlight content={content} keyword={requestParam} />;
    };

    const columns = [
        {
            title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050830") /* "档案名称" */,
            dataIndex: "typename",
            key: "typename",
            render: renderHighlight,
        },
        {
            title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050855") /* "档案编码" */,
            dataIndex: "typetag",
            key: "typetag",
        },
    ];

    let referProps = {
        columns: columns,
        hasPage: true,
        title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050828") /* "档案" */,
        service: ownerService.getIndexDataWithEvent,
        pkKey: "id",
    };

    return <WithInputBase {...props} {...referProps} />;
};
