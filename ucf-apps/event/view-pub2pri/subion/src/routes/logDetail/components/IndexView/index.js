import React, { Component, Fragment } from "react";
import { Header, Content } from "components/PageView";
import { Tag, Icon } from "components/TinperBee";
import SearchInput from "components/TinperBee/SearchInput";
import Highlight from "components/Highlight";
import Grid from "components/TinperBee/Grid";
class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            content: "",
        };
        const iconStyle = {
            verticalAlign: "text-top",
            marginRight: "8px",
        };
        this.CollapsedIcon = <i fieldid="ublinker-routes-logDetail-components-IndexView-index-4364186-i" className="cl cl-add" style={iconStyle} />;
        this.ExpandedIcon = <i fieldid="ublinker-routes-logDetail-components-IndexView-index-1756373-i" className="cl cl-Minus_sign" style={iconStyle} />;
    }

    columns = () => {
        let { cloudPk } = this.props.ownerState;
        return [
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050835") /* "云主键" */,
                dataIndex: "cloudpk",
                key: "cloudpk",
                width: "20%",
                render: (content) => {
                    return <Highlight content={content} keyword={cloudPk} />;
                },
            },
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050127") /* "ERP主键" */,
                dataIndex: "erppk",
                key: "erppk",
                width: "20%",
            },
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050834") /* "操作类型" */,
                dataIndex: "operationType",
                key: "operationType",
                width: "20%",
                render: (value) => {
                    let text = "";
                    switch (value) {
                        case 0:
                            text = window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050825") /* "事件" */;
                            break;
                        case 1:
                            text = window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050856") /* "重试" */;
                            break;
                    }
                    return <p>{text}</p>;
                },
            },
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050846") /* "执行结果" */,
                dataIndex: "status",
                key: "status",
                width: "20%",
                render: (value) => {
                    let color = "",
                        text = "";
                    switch (value) {
                        case 0:
                            color = "danger";
                            text = window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050123") /* "失败" */;
                            break;
                        case 1:
                            color = "success";
                            text = window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050166") /* "成功" */;
                            break;
                        case 2:
                            color = "info";
                            text = window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050848") /* "已重试" */;
                            break;
                    }
                    return (
                        <Tag fieldid="ublinker-routes-logDetail-components-IndexView-index-2663918-Tag" colors={color}>
                            {text}
                        </Tag>
                    );
                },
            },
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050028") /* "操作" */,
                dataIndex: "action",
                key: "action",
                width: "20%",
                render: (value, record) => {
                    return (
                        <Fragment>
                            {record.status == 0 ? (
                                <a fieldid="ublinker-routes-logDetail-components-IndexView-index-9271884-a" onClick={this.handleRetry.bind(null, record)}>
                                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050856") /* "重试" */}
                                </a>
                            ) : null}
                        </Fragment>
                    );
                },
            },
        ];
    };

    handleChange = (value) => {
        this.props.ownerStore.handleChange("cloudPk", value);
    };

    handleSearch = () => {
        let { ownerStore } = this.props;
        ownerStore.getDataSource({
            pageNo: 1,
        });
    };

    onPagiSelect = (active) => {
        this.props.ownerStore.getDataSource({
            pageNo: active,
        });
    };

    onDataNumSelect = (index, value) => {
        this.props.ownerStore.getDataSource({
            pageSize: value,
            pageNo: 1,
        });
    };

    handleRetry = (record) => {
        this.props.ownerStore.failrepay(record.pk_id);
    };

    expandedRowRender = (record) => {
        return <p style={{ whiteSpace: "normal" }} dangerouslySetInnerHTML={{ __html: record.logMessage }}></p>;
    };

    render() {
        let { ownerState } = this.props;
        let { content } = this.state;
        let { dataSource, cloudPk, pagination } = ownerState;

        return (
            <div className="data-view">
                <Header back></Header>
                <Content>
                    <Grid
                        fieldid="ublinker-routes-logDetail-components-IndexView-index-1950699-Grid"
                        header={
                            <div className="grid-header">
                                <SearchInput
                                    className="grid-header-input"
                                    value={cloudPk}
                                    onChange={this.handleChange}
                                    onSearch={this.handleSearch}
                                    showClear
                                />
                            </div>
                        }
                        rowKey={"pk_id"}
                        data={dataSource.list}
                        columns={this.columns()}
                        pagination={pagination}
                        expandedRowRender={this.expandedRowRender}
                        collapsedIcon={this.CollapsedIcon}
                        expandedIcon={this.ExpandedIcon}
                    />
                </Content>
            </div>
        );
    }
}

export default IndexView;
