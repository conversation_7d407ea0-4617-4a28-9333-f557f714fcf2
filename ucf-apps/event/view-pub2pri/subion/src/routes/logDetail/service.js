import { defineService } from "utils/service";

/**
 * 重试
 * @param string id 事件id
 */
export const failrepay = function (data) {
    let service = defineService({
        method: "POST",
        path: `/gwmanage/gwportal/diwork/erpdata/failrepay/repay/${data}`,
    });

    return service.invoke();
};

/**
 * 查询日志
 */
export const getPageList = function (data) {
    let service = defineService({
        method: "GET",
        path: "/gwmanage/gwportal/diwork/erpdata/failrepay/page",
    });

    return service.invoke(data);
};
