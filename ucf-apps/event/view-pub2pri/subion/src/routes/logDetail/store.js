import { observable, action, toJS, reaction, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { defaultListMap } from "utils/pageListUtils";
import { autoServiceMessage } from "utils/service";
import * as ownerService from "./service";
const initState = {
    dataSource: {
        ...defaultListMap,
    },
    cloudPk: "",
    typeTag: "",
    eventType: "",
    pagination: null,
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    getDataSource = async (param) => {
        let { typeTag, eventType, cloudPk } = this.state;
        let _param = {
            typeTag,
            eventType,
            cloudPk,
            ...param,
        };

        this.getPagesListFunc({
            service: ownerService.getPageList,
            requestData: _param,
            dataKey: "dataSource",
        });
    };

    failrepay = async (id) => {
        let res = await autoServiceMessage({
            service: ownerService.failrepay(id),
            success: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050831") /* "重试成功！" */,
        });

        if (res) {
            this.getDataSource();
            return true;
        }
    };

    handleChange = (type, value) => {
        this.changeState({ [type]: value });
    };
}
/**
 * 根据具体模块修改 storeKey 为保证唯一性
 * storeKey 由 模块文件夹名称 + 页面名称 + 'Store' 小驼峰
 *
 * storeKey是作为当前 Store 的唯一键， storeKey与Store的实例会在 rootStore或者StoreMap中以 键值对的 形式存在
 * 1. 用于在模块的 storeMap（模块routes/store.js）中注册并初始化当前Store
 * 2. 用于在container的 inject 中关联当前Store的实例时，从模块的storeMap或者rootStore中获取对应store实例
 * */
export const storeKey = "subionLogDetailStore";

export default Store;
