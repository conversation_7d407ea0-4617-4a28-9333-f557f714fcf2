/**
 * 汇总模块中的各个业务 store，形成一个总的 storeMap
 * 1. 方便模块中各个业务页面之间数据共享和交互
 * 2. 方便将业务模块store合并的 rootStore 中（待规划）
 * */

import ListStore, { storeKey as listStoreKey } from "./list/store";
import LogDetailStore, { storeKey as logDetailStoreKey } from "./logDetail/store";
import DataDetailStore, { storeKey as dataDetailStoreKey } from "./dataDetail/store";
import EventListStore, { storeKey as eventListStoreKey } from "./eventList/store";
import DocEventListStore, { storeKey as docEventListStoreKey } from "./docEventList/store";
import mixCore from "core";

mixCore.addStore([
    { storeKey: listStoreKey, store: new ListStore() },
    { storeKey: logDetailStoreKey, store: new LogDetailStore() },
    { storeKey: dataDetailStoreKey, store: new DataDetailStore() },
    { storeKey: eventListStoreKey, store: new EventListStore() },
    { storeKey: docEventListStoreKey, store: new DocEventListStore() },
]);
