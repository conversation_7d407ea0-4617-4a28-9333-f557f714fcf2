import React, { Component, Fragment } from "react";
import { Header, Content } from "components/PageView";
import SearchInput from "components/TinperBee/SearchInput";
import Highlight from "components/Highlight";
import Grid from "components/TinperBee/Grid";
import AddModal from "../Modal/addModal.js";
import "./index.less";
import commonText from "constants/commonText";

class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            showModal: false,
            content: "",
        };
    }

    columns = () => {
        let { key } = this.props.ownerState;
        return [
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050835") /* "云主键" */,
                dataIndex: "cloudpk",
                key: "cloudpk",
                width: "30%",
                render: (content) => {
                    return <Highlight content={content} keyword={key} />;
                },
            },
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050127") /* "ERP主键" */,
                dataIndex: "erpid",
                key: "erpid",
                width: "30%",
            },
            {
                title: window.lang.template(commonText.actions),
                dataIndex: "action",
                key: "action",
                width: "30%",
                render: (value, record) => {
                    return (
                        <Fragment>
                            <a fieldid="ublinker-routes-dataDetail-components-IndexView-index-1259083-a" onClick={this.handleDetail.bind(null, record)}>
                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050189") /* "数据详情" */}
                            </a>
                        </Fragment>
                    );
                },
            },
        ];
    };

    handleDetail = (record) => {
        this.setState({ showModal: true, content: JSON.stringify(record.content, null, "\t") });
    };

    handleChange = (value) => {
        this.props.ownerStore.handleChange("key", value);
    };

    handleSearch = () => {
        let { ownerState, ownerStore } = this.props;
        let { key } = ownerState;
        ownerStore.getDataSource({
            key: key,
        });
    };

    onPagiSelect = (active) => {
        this.props.ownerStore.getDataSource({
            pageNo: active,
        });
    };

    onDataNumSelect = (index, value) => {
        this.props.ownerStore.getDataSource({
            pageSize: value,
            pageNo: 1,
        });
    };

    close = () => {
        this.setState({ showModal: false });
    };

    render() {
        let { ownerState } = this.props;
        let { showModal, content } = this.state;
        let { dataSource, key, pagination } = ownerState;

        return (
            <div className="data-view">
                <Header back></Header>
                <Content>
                    <Grid
                        fieldid="ublinker-routes-dataDetail-components-IndexView-index-3294421-Grid"
                        header={
                            <div className="grid-header">
                                <SearchInput className="grid-header-input" value={key} onChange={this.handleChange} onSearch={this.handleSearch} showClear />
                            </div>
                        }
                        data={dataSource.list}
                        columns={this.columns()}
                        pagination={pagination}
                    />

                    {showModal ? <AddModal showModal={showModal} close={this.close} content={content} /> : null}
                </Content>
            </div>
        );
    }
}

export default IndexView;
