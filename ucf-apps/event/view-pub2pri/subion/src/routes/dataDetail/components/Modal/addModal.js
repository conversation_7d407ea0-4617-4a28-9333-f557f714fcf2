import React, { Component } from "react";
import Modal from "components/TinperBee/Modal";
import CodeEditor from "components/CodeEditor";
class AddModal extends Component {
    constructor(props) {
        super(props);
    }

    render() {
        let { showModal, close, content } = this.props;
        return (
            <Modal
                fieldid="ublinker-routes-dataDetail-components-Modal-addModal-6096010-Modal"
                show={showModal}
                onCancel={close}
                cancelText={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050197") /* "关闭" */}
                title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050189") /* "数据详情" */}
                okHide
            >
                <CodeEditor value={content} readOnly={true} />
            </Modal>
        );
    }
}

export default AddModal;
