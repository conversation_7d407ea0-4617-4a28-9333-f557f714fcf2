import { observable, action, toJS, reaction, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { defaultListMap } from "utils/pageListUtils";
import { autoServiceMessage } from "utils/service";
import * as ownerService from "./service";
const initState = {
    dataSource: {
        ...defaultListMap,
    },
    modalDataSource: {
        code: "",
        name: "",
        description: "",
    },
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    getDataSource = async (param) => {
        let res = await autoServiceMessage({
            service: ownerService.getEventTypeList(param),
        });

        if (res) {
            this.changeState({
                dataSource: {
                    list: res.data.items,
                    pageNo: res.data.pageIndex,
                    pageSize: res.data.pageSize,
                    total: res.data.itemCount,
                    totalPages: res.data.pageCount,
                },
            });
        }
    };

    getDetail = async (id) => {
        let res = await autoServiceMessage({
            service: ownerService.getEventType(id),
        });

        if (res) {
            this.changeState({
                modalDataSource: res.data,
            });
            return true;
        }
    };

    del = async (id) => {
        let res = await autoServiceMessage({
            service: ownerService.delEventType(id),
            success: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050081") /* "删除成功！" */,
        });

        if (res) {
            return true;
        }
    };

    save = async () => {
        let { modalDataSource } = this.state;
        let res = await autoServiceMessage({
            service: "id" in modalDataSource ? ownerService.editEventType(modalDataSource) : ownerService.addEventType(modalDataSource),
            success: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050089") /* "操作成功！" */,
        });

        if (res) {
            return true;
        }
    };

    handleChange = (value) => {
        this.changeState({ modalDataSource: value });
    };
}
/**
 * 根据具体模块修改 storeKey 为保证唯一性
 * storeKey 由 模块文件夹名称 + 页面名称 + 'Store' 小驼峰
 *
 * storeKey是作为当前 Store 的唯一键， storeKey与Store的实例会在 rootStore或者StoreMap中以 键值对的 形式存在
 * 1. 用于在模块的 storeMap（模块routes/store.js）中注册并初始化当前Store
 * 2. 用于在container的 inject 中关联当前Store的实例时，从模块的storeMap或者rootStore中获取对应store实例
 * */
export const storeKey = "systemSubscriptEventListStore";

export default Store;
