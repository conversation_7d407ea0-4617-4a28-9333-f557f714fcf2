import { defineService } from "utils/service";

/**
 * 分页获取事件类型信息
 */
export const getEventTypeList = function (data) {
    let service = defineService({
        method: "GET",
        path: "/gwmanage/erpdata/eventtype/page",
    });

    return service.invoke(data);
};

/**
 * 删除事件类型
 * @param string id 事件id
 */
export const delEventType = function (data) {
    let service = defineService({
        method: "POST",
        path: `/gwmanage/erpdata/eventtype/del/${data}`,
    });

    return service.invoke();
};

/**
 * 新增事件类型
 * @param string code
 * @param string name
 * @param string description
 */
export const addEventType = function (data) {
    let service = defineService({
        method: "POST",
        path: "/gwmanage/erpdata/eventtype/add",
    });

    return service.invoke(data);
};

/**
 * 根据id获取事件类型详情
 * @param string id 事件id
 */
export const getEventType = function (data) {
    let service = defineService({
        method: "GET",
        path: `/gwmanage/erpdata/eventtype/get/${data}`,
    });

    return service.invoke();
};

/**
 * 编辑事件类型
 * @param string id 事件id
 * @param string code
 * @param string name
 * @param string description
 */
export const editEventType = function (data) {
    let service = defineService({
        method: "POST",
        path: `/gwmanage/erpdata/eventtype/edit/${data.id}`,
    });

    return service.invoke(data);
};
