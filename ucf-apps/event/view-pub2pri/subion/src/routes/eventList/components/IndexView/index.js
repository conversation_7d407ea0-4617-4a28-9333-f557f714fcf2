import React, { Component, Fragment } from "react";
import { Content } from "components/PageView";
import { <PERSON>ton, Modal } from "components/TinperBee";
import Grid from "components/TinperBee/Grid";
import AddModal from "../Modal/addModal.js";
import "./index.less";

class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            showModal: false,
        };
    }

    initData = {
        code: "",
        name: "",
        description: "",
    };

    columns = [
        {
            title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050840") /* "事件编码" */,
            dataIndex: "code",
            key: "code",
        },
        {
            title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050849") /* "事件名称" */,
            dataIndex: "name",
            key: "name",
        },
        {
            title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050847") /* "事件描述" */,
            dataIndex: "description",
            key: "description",
        },
        {
            title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050028") /* "操作" */,
            dataIndex: "action",
            key: "action",
            render: (value, record) => {
                return (
                    <Fragment>
                        <a fieldid="ublinker-routes-eventList-components-IndexView-index-4856609-a" onClick={this.open.bind(null, record)}>
                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050036") /* "编辑" */}
                        </a>
                        <a fieldid="ublinker-routes-eventList-components-IndexView-index-4145148-a" onClick={this.handleDelete.bind(null, record)}>
                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050046") /* "删除" */}
                        </a>
                    </Fragment>
                );
            },
        },
    ];

    onPagiSelect = (active) => {
        this.props.ownerStore.getDataSource({
            pageNo: active,
        });
    };

    onDataNumSelect = (index, value) => {
        this.props.ownerStore.getDataSource({
            pageSize: value,
            pageNo: 1,
        });
    };

    handleDelete = (record) => {
        Modal.confirm({
            title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050844") /* "确定要删除这条数据吗？" */,
            content: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050838") /* "删除后将不能恢复。" */,
            onOk: async () => {
                let { del, getDataSource } = this.props.ownerStore;
                let res = await del(record.id);
                if (res) {
                    getDataSource();
                }
            },
        });
    };

    close = () => {
        this.props.ownerStore.handleChange(this.initData);
        this.setState({ showModal: false });
    };

    open = async (record) => {
        if ("id" in record) {
            await this.props.ownerStore.getDetail(record.id);
        }
        this.setState({ showModal: true });
    };

    save = async () => {
        let { getDataSource, save } = this.props.ownerStore;
        let res = await save();
        if (res) {
            getDataSource();
            this.close();
        }
    };

    render() {
        let { ownerState, ownerStore } = this.props;
        let { showModal } = this.state;
        let { dataSource, modalDataSource } = ownerState;
        let { handleChange } = ownerStore;
        let pagination = {
            total: dataSource.itemCount,
            items: dataSource.pageCount,
            pageSize: dataSource.pageSize,
            activePage: dataSource.pageNo,
            onSelect: this.onPagiSelect,
            onDataNumSelect: this.onDataNumSelect,
        };
        return (
            <Fragment>
                <Content>
                    <Grid
                        fieldid="ublinker-routes-eventList-components-IndexView-index-2139027-Grid"
                        header={
                            <div className="grid-header">
                                <div className="grid-header-title">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050832") /* "事件类型" */}</div>
                                <Button
                                    fieldid="ublinker-routes-eventList-components-IndexView-index-8284203-Button"
                                    colors="primary"
                                    onClick={this.open}
                                    className="grid-header-right"
                                >
                                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050845") /* "新增" */}
                                </Button>
                            </div>
                        }
                        data={dataSource.list}
                        columns={this.columns}
                        pagination={dataSource.total > 10 ? pagination : null}
                    />

                    {showModal ? (
                        <AddModal
                            showModal={showModal}
                            close={this.close}
                            save={this.save}
                            dataSource={modalDataSource}
                            handleChange={(v) => handleChange(v)}
                        />
                    ) : null}
                </Content>
            </Fragment>
        );
    }
}

export default IndexView;
