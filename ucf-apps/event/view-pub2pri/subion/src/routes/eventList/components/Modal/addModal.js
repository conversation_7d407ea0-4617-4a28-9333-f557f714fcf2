import React, { Component } from "react";
import { Button, FormControl, Modal, FormList, Icon } from "components/TinperBee";
import "./index.less";
const FormItem = FormList.FormItem;
const AddModal = FormList.createForm()(
    class AddModal extends Component {
        constructor(props) {
            super(props);
        }

        componentDidMount() {
            let {
                dataSource,
                form: { setFieldsValue },
            } = this.props;
            if ("id" in dataSource) {
                setFieldsValue({
                    name: dataSource.name,
                    code: dataSource.code,
                    description: dataSource.description,
                });
            }
        }

        onConfirm = async () => {
            let { save, dataSource, handleChange } = this.props;
            this.props.form.validateFields((err, values) => {
                if (!err) {
                    dataSource.name = values.name;
                    dataSource.code = values.code;
                    dataSource.description = values.description;
                    handleChange(dataSource);
                    save();
                }
            });
        };

        render() {
            let { getFieldProps, getFieldError } = this.props.form;
            let { showModal, close, dataSource } = this.props;

            return (
                <Modal
                    fieldid="ublinker-routes-eventList-components-Modal-addModal-7112422-Modal"
                    className="ucg-ma-modal modal-add"
                    show={showModal}
                    onHide={close}
                >
                    <Modal.Header closeButton>
                        <Modal.Title>
                            {
                                "id" in dataSource
                                    ? window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050857") /* "编辑事件类型" */
                                    : window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050837") /* "新增事件类型" */
                            }
                        </Modal.Title>
                    </Modal.Header>

                    <Modal.Body>
                        <FormItem fieldid="UCG-FE-routes-eventList-components-Modal-addModal-3886593-FormItem">
                            <span>
                                <Icon fieldid="ublinker-routes-eventList-components-Modal-addModal-7755010-Icon" type="uf-mi" className="mast"></Icon>
                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050840") /* "事件编码" */}
                            </span>
                            <FormControl
                                fieldid="ublinker-routes-eventList-components-Modal-addModal-1604676-FormControl"
                                className={getFieldError("code") ? "form-error" : ""}
                                {...getFieldProps("code", {
                                    validateTrigger: "onBlur",
                                    rules: [
                                        {
                                            required: true,
                                            message: (
                                                <span>
                                                    <Icon fieldid="ublinker-routes-eventList-components-Modal-addModal-2972275-Icon" type="uf-exc-t"></Icon>
                                                    <span>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050829") /* "请输入事件编码" */}</span>
                                                </span>
                                            ),
                                        },
                                    ],
                                })}
                            />
                            <span className="error">{getFieldError("code")}</span>
                        </FormItem>

                        <FormItem fieldid="UCG-FE-routes-eventList-components-Modal-addModal-7913559-FormItem">
                            <span>
                                <Icon fieldid="ublinker-routes-eventList-components-Modal-addModal-3879264-Icon" type="uf-mi" className="mast"></Icon>
                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050849") /* "事件名称" */}
                            </span>
                            <FormControl
                                fieldid="ublinker-routes-eventList-components-Modal-addModal-7065040-FormControl"
                                className={getFieldError("name") ? "form-error" : ""}
                                {...getFieldProps("name", {
                                    validateTrigger: "onBlur",
                                    rules: [
                                        {
                                            required: true,
                                            message: (
                                                <span>
                                                    <Icon fieldid="ublinker-routes-eventList-components-Modal-addModal-6331791-Icon" type="uf-exc-t"></Icon>
                                                    <span>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050854") /* "请输入事件名称" */}</span>
                                                </span>
                                            ),
                                        },
                                    ],
                                })}
                            />
                            <span className="error">{getFieldError("name")}</span>
                        </FormItem>

                        <FormItem fieldid="UCG-FE-routes-eventList-components-Modal-addModal-7916453-FormItem">
                            <span>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050847") /* "事件描述" */}</span>
                            <FormControl
                                fieldid="ublinker-routes-eventList-components-Modal-addModal-2426864-FormControl"
                                {...getFieldProps("description", {})}
                            />
                        </FormItem>
                    </Modal.Body>

                    <Modal.Footer>
                        <Button
                            fieldid="ublinker-routes-eventList-components-Modal-addModal-9138096-Button"
                            onClick={close}
                            colors="secondary"
                            style={{ marginRight: 16 }}
                        >
                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050063") /* "取消" */}
                        </Button>
                        <Button fieldid="ublinker-routes-eventList-components-Modal-addModal-7072613-Button" onClick={this.onConfirm} colors="primary">
                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050099") /* "确定" */}
                        </Button>
                    </Modal.Footer>
                </Modal>
            );
        }
    }
);

export default AddModal;
