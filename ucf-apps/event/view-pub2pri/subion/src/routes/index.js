import React from "react";
import { RoutesRender } from "core";

import ListContainer from "./list/container";
import EventListContainer from "./eventList/container";
import DocEventListContainer from "./docEventList/container";
import DataDetailContainer from "./dataDetail/container";
import LogDetailContainer from "./logDetail/container";
import getConfigProvider from "components/ConfigProvider";
import config from "../config";
const ConfigProvider = getConfigProvider(config);

const routes = [
    {
        path: "/",
        exact: true,
        component: ListContainer,
    },
    {
        path: "/list",
        component: ListContainer,
    },
    {
        path: "/dataDetail/:id",
        component: DataDetailContainer,
    },
    {
        path: "/logDetail",
        component: LogDetailContainer,
    },
    {
        path: "/eventList",
        component: EventListContainer,
    },
    {
        path: "/docEventList",
        component: DocEventListContainer,
    },
];

const Routes = () => {
    return (
        <ConfigProvider fieldid="UCG-FE-view-pub2pri-subion-src-routes-index-5066220-ConfigProvider">
            <RoutesRender routes={routes} />
        </ConfigProvider>
    );
};
export default Routes;
