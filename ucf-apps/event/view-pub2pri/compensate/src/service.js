import { defineService } from "utils/service";

/**
 * 分页获取失败补偿数据
 */
export const getFailRepayList = function (data) {
    let service = defineService({
        method: "GET",
        path: "/gwmanage/gwportal/diwork/erpdata/failrepay/page",
    });

    return service.invoke(data);
};

/**
 * 根据id查询失败补偿数据详情
 */
export const getFailRepayDetail = function (data) {
    let service = defineService({
        method: "GET",
        path: `/gwmanage/gwportal/diwork/erpdata/failrepay/get/${data}`,
    });

    return service.invoke();
};

/**
 * 重试
 */
export const repayData = function (data) {
    let service = defineService({
        method: "POST",
        path: `/gwmanage/gwportal/diwork/erpdata/failrepay/repay/${data}`,
    });

    return service.invoke();
};
