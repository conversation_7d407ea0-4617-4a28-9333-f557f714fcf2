import React, { Component, Fragment } from "react";
import { Button, Space } from "@tinper/next-ui";
import { Modal, FormList } from "components/TinperBee";
// import FormList from "components/TinperBee/Form";
import { Content } from "components/PageView";
import Grid from "components/TinperBee/Grid";
import "./index.less";

const FormItem = FormList.Item;
class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            showModal: false,
        };
    }

    hoverContent = (record, index) => {
        return (
            <Space fieldid="5468811c-1204-4b5d-b63c-787f564929bf">
                <Button fieldid="f674f4b0-a06b-4b1c-87f0-a50e37381945" {...Grid.hoverButtonPorps} onClick={this.open.bind(null, record)}>
                    {lang.templateByUuid("UID:P_UBL-FE_2009604004280206", "查看") /* "查看" */}
                </Button>
                <Button fieldid="08473b53-1492-4f5c-bdd5-e210b2893ed7" {...Grid.hoverButtonPorps} s onClick={this.repay.bind(null, record)}>
                    {lang.templateByUuid("UID:P_UBL-FE_2009604004280207", "重试") /* "重试" */}
                </Button>
            </Space>
        );
    };

    columns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042801FF", "档案名称") /* "档案名称" */,
            dataIndex: "defdocname",
            key: "defdocname",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280200", "云端主键") /* "云端主键" */,
            dataIndex: "cloudpk",
            key: "cloudpk",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280201", "云端编码") /* "云端编码" */,
            dataIndex: "cloudcode",
            key: "cloudcode",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280202", "ERP主键") /* "ERP主键" */,
            dataIndex: "erppk",
            key: "erppk",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280204", "ERP编码") /* "ERP编码" */,
            dataIndex: "erpcode",
            key: "erpcode",
        },
    ];

    onPagiSelect = (active) => {
        this.props.ownerStore.getDataSource({
            pageNo: active,
        });
    };

    onDataNumSelect = (index, value) => {
        this.props.ownerStore.getDataSource({
            pageSize: value,
            pageNo: 1,
        });
    };

    close = () => {
        this.setState({ showModal: false });
    };

    open = async (record) => {
        if ("pk_id" in record) {
            await this.props.ownerStore.getDetail(record.pk_id);
        }
        this.setState({ showModal: true });
    };

    repay = async (record) => {
        if ("pk_id" in record) {
            await this.props.ownerStore.repayService(record.pk_id);
        }
    };

    labelCol = 120;

    render() {
        let { ownerState } = this.props;
        let { showModal } = this.state;
        let { dataSource, detail } = ownerState;
        let { defdocname, cloudpk, cloudcode, erppk, erpcode, erpdata } = detail;
        let pagination = {
            total: dataSource.itemCount,
            items: dataSource.pageCount,
            pageSize: dataSource.pageSize,
            activePage: dataSource.pageNo,
            onSelect: this.onPagiSelect,
            onDataNumSelect: this.onDataNumSelect,
        };
        return (
            <Fragment>
                <Content>
                    <Grid
                        fieldid="ublinker-compensate-src-components-IndexView-index-7038297-Grid"
                        header={
                            <div className="grid-header">
                                <div className="grid-header-title">{lang.templateByUuid("UID:P_UBL-FE_2009604004280203", "失败补偿") /* "失败补偿" */}</div>
                            </div>
                        }
                        data={dataSource.list}
                        columns={this.columns}
                        pagination={dataSource.total > 10 ? pagination : null}
                        hoverContent={this.hoverContent}
                    />

                    <Modal
                        fieldid="ublinker-compensate-src-components-IndexView-index-1087987-Modal"
                        className="ucg-ma-modal"
                        show={showModal}
                        onHide={this.close}
                    >
                        <Modal.Header closeButton>
                            <Modal.Title>{lang.templateByUuid("UID:P_UBL-FE_2009604004280208", "详情") /* "详情" */}</Modal.Title>
                        </Modal.Header>

                        <Modal.Body>
                            <Fragment>
                                <FormList fieldid="ublinker-compensate-src-components-IndexView-index-842438-FormList" layoutOpt={{ md: 12 }}>
                                    <FormItem
                                        fieldid="ublinker-compensate-src-components-IndexView-index-4838210-FormItem"
                                        label={lang.templateByUuid("UID:P_UBL-FE_20096040042801FF", "档案名称") /* "档案名称" */}
                                        labelCol={this.labelCol}
                                    >
                                        <p>{defdocname}</p>
                                    </FormItem>

                                    <FormItem
                                        fieldid="ublinker-compensate-src-components-IndexView-index-9540170-FormItem"
                                        label={lang.templateByUuid("UID:P_UBL-FE_2009604004280200", "云端主键") /* "云端主键" */}
                                        labelCol={this.labelCol}
                                    >
                                        <p>{cloudpk}</p>
                                    </FormItem>

                                    <FormItem
                                        fieldid="ublinker-compensate-src-components-IndexView-index-9197217-FormItem"
                                        label={lang.templateByUuid("UID:P_UBL-FE_2009604004280201", "云端编码") /* "云端编码" */}
                                        labelCol={this.labelCol}
                                    >
                                        <p>{cloudcode}</p>
                                    </FormItem>

                                    <FormItem
                                        fieldid="ublinker-compensate-src-components-IndexView-index-1612034-FormItem"
                                        label={lang.templateByUuid("UID:P_UBL-FE_2009604004280202", "ERP主键") /* "ERP主键" */}
                                        labelCol={this.labelCol}
                                    >
                                        <p>{erppk}</p>
                                    </FormItem>

                                    <FormItem
                                        fieldid="ublinker-compensate-src-components-IndexView-index-7028275-FormItem"
                                        label={lang.templateByUuid("UID:P_UBL-FE_2009604004280204", "ERP编码") /* "ERP编码" */}
                                        labelCol={this.labelCol}
                                    >
                                        <p>{erpcode}</p>
                                    </FormItem>

                                    <FormItem
                                        fieldid="ublinker-compensate-src-components-IndexView-index-6741309-FormItem"
                                        label={lang.templateByUuid("UID:P_UBL-FE_2009604004280205", "ERP数据信息") /* "ERP数据信息" */}
                                        labelCol={this.labelCol}
                                    >
                                        <p>{erpdata}</p>
                                    </FormItem>
                                </FormList>
                            </Fragment>
                        </Modal.Body>

                        <Modal.Footer>
                            <Button fieldid="ublinker-compensate-src-components-IndexView-index-5878628-Button" onClick={this.close} colors="primary">
                                {lang.templateByUuid("UID:P_UBL-FE_2009604004280209", "关闭") /* "关闭" */}
                            </Button>
                        </Modal.Footer>
                    </Modal>
                </Content>
            </Fragment>
        );
    }
}

export default IndexView;
