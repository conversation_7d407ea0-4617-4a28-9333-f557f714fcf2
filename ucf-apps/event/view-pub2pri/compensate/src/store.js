import { observable, action, toJS, reaction, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { defaultListMap } from "utils/pageListUtils";
import { autoServiceMessage } from "utils/service";
import * as ownerService from "./service";
const initState = {
    dataSource: {
        ...defaultListMap,
    },
    detail: {
        pk_id: "",
        defdocid: "",
        typetag: "",
        defdocname: "",
        cloudpk: "",
        cloudcode: "",
        erppk: "",
        erpcode: "",
        erpdata: "",
        status: 0,
        erptype: "",
    },
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    getDataSource = async (param) => {
        let res = await autoServiceMessage({
            service: ownerService.getFailRepayList(param),
        });

        if (res) {
            this.changeState({
                dataSource: {
                    list: res.data.items,
                    pageNo: res.data.pageIndex,
                    pageSize: res.data.pageSize,
                    total: res.data.itemCount,
                    totalPages: res.data.pageCount,
                },
            });
        }
    };

    getDetail = async (param) => {
        let res = await autoServiceMessage({
            service: ownerService.getFailRepayDetail(param),
        });

        if (res) {
            this.changeState({
                detail: res.data,
            });
            return true;
        }
    };

    repayService = async (id) => {
        let res = await autoServiceMessage({
            service: ownerService.repayData(id),
            success: lang.templateByUuid("UID:P_UBL-FE_200960400428037C", "操作成功") /* "操作成功" */,
        });

        if (res) {
            this.getDataSource();
            return true;
        }
    };
}

export const storeKey = "eventCompensateStore";

export default Store;
