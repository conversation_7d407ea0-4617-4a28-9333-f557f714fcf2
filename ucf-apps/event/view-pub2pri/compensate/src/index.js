/**
 * 入口、导入组件样式、渲染
 */

import React from "react";
import { HashRouter } from "react-router";
import { render, Provider } from "core";
import Container from "./container";
import "./store";
import "./app.less";
import Store, { storeKey } from "./store";

import mixCore from "core";
mixCore.addStore({
    storeKey,
    store: new Store(),
});

const rootStore = {
    [storeKey]: new Store(),
};

const App = () => {
    return (
        <Provider store={rootStore} router={HashRouter}>
            <Container />
        </Provider>
    );
};

render(App, undefined, "ManagementSide");
