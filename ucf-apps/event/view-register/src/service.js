import { defineService } from "utils/service";

/**
 * 视图注册-查询所有
 * @param {*} data
 * @param {String} data.key 人员
 * @param {String} data.erpType erp类型
 * @param {String} data.taskClass 根据任务分类-查询所有获取列表
 */
export const getAllListService = function (data, header) {
    let service = defineService({
        method: "GET",
        path: "/gwmanage/gwportal/diwork/viewReg/getAll",
        header,
    });

    return service.invoke(data);
};

/**
 * 视图注册-新增/修改
 * @param {*} data
 * @param {String} data.datatypeName 视图名称
 * @param {String} data.tableview 视图标签
 * @param {String} data.erpversion ERP版本
 * @param {Boolean} data.simple
 * @param {Boolean} data.hr
 * @param {Enum} data.job  0通用 1职务类别 2岗位序列
 * @param {String} data.replaceView 为空时，该字段不传
 */
export const upsertService = function (data, header) {
    let service = defineService({
        method: "POST",
        path: "/gwmanage/gwportal/diwork/viewReg/upsert",
        header,
    });

    return service.invoke(data);
};

/**
 * 视图注册-删除
 * @param {*} data
 * @param {String} data.id
 */
export const deleteService = function (data, header) {
    let service = defineService({
        method: "GET",
        path: "/gwmanage/gwportal/diwork/viewReg/delete",
        header,
    });

    return service.invoke(data);
};

/**
 * 任务分类-查询所有
 */
export const getAllClassesListService = function (data, header) {
    let service = defineService({
        method: "GET",
        path: "/gwmanage/gwportal/diwork/classes/getAll",
        header,
    });

    return service.invoke();
};
