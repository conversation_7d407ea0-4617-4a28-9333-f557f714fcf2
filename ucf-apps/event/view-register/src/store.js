import { observable, action, toJS, reaction, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { defaultListMap } from "utils/pageListUtils";
import { autoServiceMessage } from "utils/service";
import * as ownerService from "./service";
import { act } from "react-dom/test-utils";
const initState = {
    dataSource: { ...defaultListMap },
    info: {
        datatypeName: "", //视图名称
        tableview: "", //视图标签
        doctype: 0,
        erpversion: "", //ERP版本
        simple: true,
        hr: true,
        job: 0,
        taskClass: "",
    },
    classList: [],
    serviceCodeDiwork: "kflj_ssgj",
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;
    @observable searchInfo = {
        key: "",
        erpType: "",
        taskClass: "",
    };

    @action
    setSearchInfo = (data) => {
        this.searchInfo = Object.values(data).length ? { ...this.searchInfo, ...data } : {};
    };

    getDataSource = async (reqData) => {
        let requestData = {
            ...reqData,
            ...this.searchInfo,
        };
        this.getPagesListFunc({
            service: ownerService.getAllListService,
            requestData,
            dataKey: "dataSource",
            header: { serviceCode: this.state.serviceCodeDiwork },
        });
    };
    clear = () => {
        let info = {
            datatypeName: "", //视图名称
            tableview: "", //视图标签
            erpversion: "", //ERP版本
            simple: true,
            hr: true,
            job: 0,
            taskClass: "",
        };
        this.changeState({ info });
    };

    reset = () => {
        let searchInfo = {
            key: "",
            erpType: "",
            taskClass: "",
        };
        this.changeState({ searchInfo });
        this.getDataSource();
    };

    handleSearchInfoChange = (type, value) => {
        let { searchInfo } = this.state;
        searchInfo[type] = value;
        this.changeState({ searchInfo });
    };

    handleInfoChange = (type, value) => {
        let { info } = this.state;
        info[type] = value;
        this.changeState({ info });
    };

    getAllClassesList = async () => {
        let res = await autoServiceMessage({
            service: ownerService.getAllClassesListService({}, { serviceCode: this.state.serviceCodeDiwork }),
        });

        if (res) {
            this.changeState({
                classList: res.data,
            });
        }
    };

    save = async () => {
        let { info } = this.state;
        let res = await autoServiceMessage({
            service: ownerService.upsertService(info, { serviceCode: this.state.serviceCodeDiwork }),
        });

        if (res) {
            this.getDataSource();
            return true;
        }
    };

    delete = async (param) => {
        let res = await autoServiceMessage({
            service: ownerService.deleteService(param, { serviceCode: this.state.serviceCodeDiwork }),
        });

        if (res) {
            this.getDataSource();
        }
    };
}

export const storeKey = "eventViewRegisterStore";

export default Store;
