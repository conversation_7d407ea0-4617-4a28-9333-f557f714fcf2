import React, { Component, Fragment } from "react";
import { Space } from "@tinper/next-ui";
import { Header } from "components/PageView/Header";
import { Content } from "components/PageView";
import { Modal, Button, Select, FormControl, FormList, Row, Col } from "components/TinperBee";
import { SearchForm } from "tne-tinpernextpro-fe";
import Grid from "components/TinperBee/Grid";
import "./index.less";

const SearchFormItem = SearchForm.Item;

const formItemLayout = {
    labelCol: { span: 8 },
    wrapperCol: { span: 14 },
};
const searchFormLayout = {
    labelCol: { span: 10 },
    wrapperCol: { span: 14 },
};
const Option = Select.Option;
const FormItem = FormList.Item;

class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            showModal: false,
        };
        this.form = React.createRef();
    }

    columns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_200960400428032E", "序号") /* "序号" */,
            dataIndex: "$$index",
            key: "$$index",
            width: "50px",
            render: (text, record, index) => {
                return index + 1;
            },
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280323", "视图名称") /* "视图名称" */,
            dataIndex: "datatypeName",
            key: "datatypeName",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280325", "视图标签") /* "视图标签" */,
            dataIndex: "tableview",
            key: "tableview",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280329", "视图类型") /* "视图类型" */,
            dataIndex: "doctype",
            key: "doctype",
            width: "80px",
            render: (text, record, index) => {
                switch (text) {
                    case 0:
                        return "0";
                    case 1:
                        return lang.templateByUuid("UID:P_UBL-FE_200960400428032B", "系统级数据") /* "系统级数据" */;
                    case 2:
                        return "2";
                    case 3:
                        return lang.templateByUuid("UID:P_UBL-FE_200960400428032C", "组织级数据") /* "组织级数据" */;
                }
            },
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280331", "ERP版本") /* "ERP版本" */,
            dataIndex: "erpversion",
            key: "erpversion",
            width: "120px",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280330", "任务分类") /* "任务分类" */,
            dataIndex: "taskClass",
            key: "taskClass",
            width: "250px",
            render: (text, record, index) => {
                return this.props.ownerState.classList.filter((item) => item.code == text).map((item) => item.name);
            },
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280335", "是否简版") /* "是否简版" */,
            dataIndex: "simple",
            key: "simple",
            width: "80px",
            render: (text) => {
                return (
                    <p>
                        {
                            text
                                ? lang.templateByUuid("UID:P_UBL-FE_2009604004280333", "是") /* "是" */
                                : lang.templateByUuid("UID:P_UBL-FE_2009604004280334", "否") /* "否" */
                        }
                    </p>
                );
            },
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280337", "是否hr版") /* "是否hr版" */,
            dataIndex: "hr",
            key: "hr",
            width: "80px",
            render: (text) => {
                return (
                    <p>
                        {
                            text
                                ? lang.templateByUuid("UID:P_UBL-FE_2009604004280333", "是") /* "是" */
                                : lang.templateByUuid("UID:P_UBL-FE_2009604004280334", "否") /* "否" */
                        }
                    </p>
                );
            },
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_200960400428033A", "旗舰版是否HR") /* "旗舰版是否HR" */,
            dataIndex: "bipIsHr",
            key: "bipIsHr",
            render: (text) => {
                const bipIsHrEnum = [
                    lang.templateByUuid("UID:P_UBL-FE_2009604004280320", "通用") /* "通用" */,
                    lang.templateByUuid("UID:P_UBL-FE_2009604004280333", "是") /* "是" */,
                    lang.templateByUuid("UID:P_UBL-FE_2009604004280334", "否") /* "否" */,
                ];
                return <p>{bipIsHrEnum[text]}</p>;
            },
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280336", "是否只支持升迁") /* "是否只支持升迁" */,
            dataIndex: "promotion",
            key: "promotion",
            width: "120px",
            render: (text) => {
                return (
                    <p>
                        {
                            text
                                ? lang.templateByUuid("UID:P_UBL-FE_2009604004280333", "是") /* "是" */
                                : lang.templateByUuid("UID:P_UBL-FE_2009604004280320", "通用") /* "通用" */
                        }
                    </p>
                );
            },
        },

        {
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280341", "是否只支持新架构") /* "是否只支持新架构" */,
            dataIndex: "supportNewArch",
            key: "supportNewArch",
            width: "120px",
            render: (text) => {
                return (
                    <p>
                        {
                            text == 0
                                ? lang.templateByUuid("UID:P_UBL-FE_2009604004280320", "通用") /* "通用" */
                                : text == 1
                                  ? lang.templateByUuid("UID:P_UBL-FE_200960400428031E", "支持") /* "支持" */
                                  : lang.templateByUuid("UID:P_UBL-FE_200960400428031F", "不支持") /* "不支持" */
                        }
                    </p>
                );
            },
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280322", "替换视图(实际显示视图)") /* "替换视图(实际显示视图)" */,
            dataIndex: "replaceView",
            key: "replaceView",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280324", "YonSuite视图") /* "YonSuite视图" */,
            dataIndex: "yousuiteview",
            key: "yousuiteview",
        },
    ];
    hoverContent = (record) => {
        return (
            <Space fieldid="d066cb66-f913-463b-be8e-3f7bf19cfdb1" size={8}>
                <Button fieldid="f2156cd4-1caa-47ac-9a3b-a6824c676789" {...Grid.hoverButtonPorps} onClick={this.handleEdit.bind(null, record)}>
                    {lang.templateByUuid("UID:P_UBL-FE_2009604004280327", "编辑") /* "编辑" */}
                </Button>
                <Button fieldid="a757cd73-e79f-4c28-b43c-d2ed7474fd42" {...Grid.hoverButtonPorps} onClick={this.delete.bind(null, record)}>
                    {lang.templateByUuid("UID:P_UBL-FE_200960400428032A", "删除") /* "删除" */}
                </Button>
            </Space>
        );
    };
    componentDidMount() {
        this.props.ownerStore.getAllClassesList();
    }

    delete = (record) => {
        let { ownerStore } = this.props;
        Modal.confirm({
            title: lang.templateByUuid("UID:P_UBL-FE_200960400428032D", "确定要删除这条数据吗？") /* "确定要删除这条数据吗？" */,
            content: lang.templateByUuid("UID:P_UBL-FE_200960400428032F", "数据删除后将不能恢复！") /* "数据删除后将不能恢复！" */,
            onOk() {
                ownerStore.delete({ id: record.pkId });
            },
        });
    };

    save = async () => {
        let res = await this.props.ownerStore.save();
        if (res) {
            this.props.ownerStore.clear();
            this.setState({ showModal: false });
        }
    };

    close = () => {
        this.props.ownerStore.clear();
        this.setState({ showModal: false });
    };

    handleSearchInfoChange = (type, value) => {
        this.props.ownerStore.handleSearchInfoChange(type, value);
    };

    handleInfoChange = (type, value) => {
        this.props.ownerStore.handleInfoChange(type, value);
    };

    handleEdit = async (record) => {
        if ("pkId" in record) {
            this.props.ownerStore.changeState({ info: record });
        }
        this.setState({ showModal: true }, () => {
            this.form.current.setFieldsValue(record);
        });
    };

    labelCol = 120;
    reset = () => {
        this.props.ownerStore.reset();
        this.form.current.setFieldsValue({
            key: "",
            erpType: "",
            taskClass: "",
        });
    };
    handleKeyDown = (e) => {
        if (e.keyCode == 13) {
            this.props.ownerStore.getDataSource();
        }
    };
    handleSearch = (values) => {
        const { setSearchInfo, getDataSource } = this.props.ownerStore;
        setSearchInfo(values);
        getDataSource({ pageNo: 1 });
    };

    render() {
        let { ownerState, ownerStore } = this.props;
        let { showModal } = this.state;
        let { reset, getDataSource } = ownerStore;
        let { dataSource, info, classList, pagination } = ownerState;
        let { datatypeName, tableview, erpversion, simple, hr, job, taskClass: _taskClass } = info;

        return (
            <div className="view-register">
                <Header title={lang.templateByUuid("UID:P_UBL-FE_2009604004280339", "视图注册") /* "视图注册" */} />
                <Content
                    contentTop={
                        <>
                            <SearchForm
                                fieldid="e6a08424-ee14-4f08-9fb4-0ef398e18bf6"
                                {...searchFormLayout}
                                formLayout={3}
                                onSearch={this.handleSearch}
                                onReset={() => this.handleSearch({})}
                                key="submitter-demo-search-form1"
                                locale={window.lang.lang || "zh_cn"}
                                submitter={{ searchConfig: { resetText: lang.templateByUuid("UID:P_UBL-FE_18D7622804180027", "重置") } }}
                                showSelected={false}
                            >
                                <SearchFormItem
                                    fieldid="4d697a9f-8c5c-4726-bef0-f1e7c599867c"
                                    formLayout
                                    allowClear
                                    inputType="input"
                                    labelCol={{ span: 12 }}
                                    placeholder={lang.templateByUuid("UID:P_UBL-FE_200960400428033C", "视图名称或标签") /* "视图名称或标签" */}
                                    label={lang.templateByUuid("UID:P_UBL-FE_200960400428033C", "视图名称或标签") /* "视图名称或标签" */}
                                    name="key"
                                />
                                <SearchFormItem
                                    fieldid="*************-475b-b020-67e145a1ec15"
                                    allowClear
                                    inputType="input"
                                    placeholder={lang.templateByUuid("UID:P_UBL-FE_200960400428033D", "请输入ERP版本") /* "请输入ERP版本" */}
                                    label={lang.templateByUuid("UID:P_UBL-FE_2009604004280331", "ERP版本") /* "ERP版本" */}
                                    name="erpType"
                                />
                                <SearchFormItem
                                    fieldid="8d5ff517-fa2a-443b-b268-9a55f0d9d54e"
                                    allowClear
                                    inputType="select"
                                    placeholder={lang.templateByUuid("UID:P_UBL-FE_200960400428033E", "请选择任务分类") /* "请选择任务分类" */}
                                    label={lang.templateByUuid("UID:P_UBL-FE_2009604004280330", "任务分类") /* "任务分类" */}
                                    name="taskClass"
                                    options={classList.map((item) => ({ value: item.code, label: item.name }))}
                                    initialValue={""}
                                />
                            </SearchForm>
                        </>
                    }
                >
                    <Grid
                        fieldid="ublinker-view-register-src-components-IndexView-index-3759242-Grid"
                        header={
                            <div className="grid-header">
                                <Button
                                    fieldid="ublinker-view-register-src-components-IndexView-index-7103315-Button"
                                    className="grid-header-btn"
                                    colors="primary"
                                    onClick={this.handleEdit}
                                >
                                    {lang.templateByUuid("UID:P_UBL-FE_2009604004280340", "新增") /* "新增" */}
                                </Button>
                            </div>
                        }
                        rowKey={"pk_id"}
                        data={dataSource.list}
                        columns={this.columns}
                        pagination={pagination}
                        hoverContent={this.hoverContent}
                    />

                    <Modal
                        fieldid="ublinker-view-register-src-components-IndexView-index-1405305-Modal"
                        className="ucg-ma-modal"
                        show={showModal}
                        onCancel={this.close}
                    >
                        <Modal.Header closeButton>
                            <Modal.Title>{lang.templateByUuid("UID:P_UBL-FE_2009604004280321", "新增注册") /* "新增注册" */}</Modal.Title>
                        </Modal.Header>

                        <Modal.Body>
                            <Fragment>
                                <FormList fieldid="ublinker-view-register-src-components-IndexView-index-6455863-FormList" ref={this.form} {...formItemLayout}>
                                    <FormItem
                                        fieldid="ublinker-view-register-src-components-IndexView-index-1356534-FormItem"
                                        label={lang.templateByUuid("UID:P_UBL-FE_2009604004280323", "视图名称") /* "视图名称" */}
                                        name="datatypeName"
                                    >
                                        <FormControl
                                            fieldid="ublinker-view-register-src-components-IndexView-index-1656585-FormControl"
                                            onChange={this.handleInfoChange.bind(null, "datatypeName")}
                                        />
                                    </FormItem>

                                    <FormItem
                                        fieldid="ublinker-view-register-src-components-IndexView-index-6415868-FormItem" //好像不能修改，修改提交之后，依然是原来值
                                        label={lang.templateByUuid("UID:P_UBL-FE_2009604004280325", "视图标签") /* "视图标签" */}
                                        name="tableview"
                                    >
                                        <FormControl
                                            fieldid="ublinker-view-register-src-components-IndexView-index-324263-FormControl"
                                            onChange={this.handleInfoChange.bind(null, "tableview")}
                                        />
                                    </FormItem>

                                    <FormItem
                                        fieldid="ublinker-view-register-src-components-IndexView-index-8142763-FormItem"
                                        label={lang.templateByUuid("UID:P_UBL-FE_2009604004280329", "视图类型") /* "视图类型" */}
                                        name="doctype"
                                    >
                                        <Select
                                            fieldid="ublinker-view-register-src-components-IndexView-index-2209070-Select"
                                            onChange={this.handleInfoChange.bind(null, "doctype")}
                                        >
                                            <Option fieldid="UCG-FE-view-register-src-components-IndexView-index-3763158-Option" key="0" value={0}>
                                                0
                                            </Option>
                                            <Option fieldid="UCG-FE-view-register-src-components-IndexView-index-4907478-Option" key="1" value={1}>
                                                {lang.templateByUuid("UID:P_UBL-FE_200960400428032B", "系统级数据") /* "系统级数据" */}
                                            </Option>
                                            <Option fieldid="UCG-FE-view-register-src-components-IndexView-index-5043762-Option" key="2" value={2}>
                                                2
                                            </Option>
                                            <Option fieldid="UCG-FE-view-register-src-components-IndexView-index-5778674-Option" key="3" value={3}>
                                                {lang.templateByUuid("UID:P_UBL-FE_200960400428032C", "组织级数据") /* "组织级数据" */}
                                            </Option>
                                            {/* {
                        classList.map(item => {
                          return <Option fieldid="UCG-FE-view-register-src-components-IndexView-index-217477-Option" key={item.code} value={item.code}>{item.name}</Option>
                        })
                      } */}
                                        </Select>
                                    </FormItem>
                                    <FormItem
                                        fieldid="ublinker-view-register-src-components-IndexView-index-6641431-FormItem"
                                        label={lang.templateByUuid("UID:P_UBL-FE_2009604004280330", "任务分类") /* "任务分类" */}
                                        name="taskClass"
                                    >
                                        <Select
                                            fieldid="ublinker-view-register-src-components-IndexView-index-8906639-Select"
                                            onChange={this.handleInfoChange.bind(null, "taskClass")}
                                        >
                                            {classList.map((item) => {
                                                return (
                                                    <Option
                                                        fieldid="UCG-FE-view-register-src-components-IndexView-index-59031-Option"
                                                        key={item.code}
                                                        value={item.code}
                                                    >
                                                        {item.name}
                                                    </Option>
                                                );
                                            })}
                                        </Select>
                                    </FormItem>

                                    <FormItem
                                        fieldid="ublinker-view-register-src-components-IndexView-index-7349382-FormItem"
                                        label={lang.templateByUuid("UID:P_UBL-FE_2009604004280331", "ERP版本") /* "ERP版本" */}
                                        name="erpversion"
                                    >
                                        <FormControl
                                            fieldid="ublinker-view-register-src-components-IndexView-index-7177100-FormControl"
                                            onChange={this.handleInfoChange.bind(null, "erpversion")}
                                        />
                                    </FormItem>

                                    <FormItem
                                        fieldid="ublinker-view-register-src-components-IndexView-index-73493821-FormItem"
                                        label={lang.templateByUuid("UID:P_UBL-FE_2009604004280322", "替换视图(实际显示视图)") /* "替换视图(实际显示视图)" */}
                                        name="replaceView"
                                    >
                                        <FormControl
                                            fieldid="ublinker-view-register-src-components-IndexView-index-71771001-FormControl"
                                            onChange={this.handleInfoChange.bind(null, "replaceView")}
                                        />
                                    </FormItem>

                                    <FormItem
                                        fieldid="ublinker-view-register-src-components-IndexView-index-734938211-FormItem"
                                        label={lang.templateByUuid("UID:P_UBL-FE_2009604004280324", "YonSuite视图") /* "YonSuite视图" */}
                                        name="yousuiteview"
                                    >
                                        <FormControl
                                            fieldid="ublinker-view-register-src-components-IndexView-index-717710011-FormControl"
                                            onChange={this.handleInfoChange.bind(null, "yousuiteview")}
                                        />
                                    </FormItem>

                                    <FormItem
                                        fieldid="ublinker-view-register-src-components-IndexView-index-2669803-FormItem"
                                        label={lang.templateByUuid("UID:P_UBL-FE_2009604004280332", "是否HR") /* "是否HR" */}
                                        name="hr"
                                    >
                                        <Select
                                            fieldid="ublinker-view-register-src-components-IndexView-index-5538473-Select"
                                            onChange={this.handleInfoChange.bind(null, "hr")}
                                        >
                                            <Option fieldid="UCG-FE-view-register-src-components-IndexView-index-3534635-Option" key={true} value={true}>
                                                {lang.templateByUuid("UID:P_UBL-FE_2009604004280333", "是") /* "是" */}
                                            </Option>
                                            <Option fieldid="UCG-FE-view-register-src-components-IndexView-index-9647661-Option" key={false} value={false}>
                                                {lang.templateByUuid("UID:P_UBL-FE_2009604004280334", "否") /* "否" */}
                                            </Option>
                                        </Select>
                                    </FormItem>

                                    <FormItem
                                        fieldid="ublinker-view-register-src-components-IndexView-index-6814166-FormItem"
                                        label={lang.templateByUuid("UID:P_UBL-FE_2009604004280335", "是否简版") /* "是否简版" */}
                                        name="simple"
                                    >
                                        <Select
                                            fieldid="ublinker-view-register-src-components-IndexView-index-2558894-Select"
                                            onChange={this.handleInfoChange.bind(null, "simple")}
                                        >
                                            <Option fieldid="UCG-FE-view-register-src-components-IndexView-index-6907171-Option" key={true} value={true}>
                                                {lang.templateByUuid("UID:P_UBL-FE_2009604004280333", "是") /* "是" */}
                                            </Option>
                                            <Option fieldid="UCG-FE-view-register-src-components-IndexView-index-1075994-Option" key={false} value={false}>
                                                {lang.templateByUuid("UID:P_UBL-FE_2009604004280334", "否") /* "否" */}
                                            </Option>
                                        </Select>
                                    </FormItem>

                                    <FormItem
                                        fieldid="ublinker-view-register-src-components-IndexView-index-68141661-FormItem"
                                        label={lang.templateByUuid("UID:P_UBL-FE_2009604004280336", "是否只支持升迁") /* "是否只支持升迁" */}
                                        name="promotion"
                                    >
                                        <Select
                                            fieldid="ublinker-view-register-src-components-IndexView-index-25588941-Select"
                                            onChange={this.handleInfoChange.bind(null, "promotion")}
                                        >
                                            <Option fieldid="UCG-FE-view-register-src-components-IndexView-index-69071711-Option" key={1} value={1}>
                                                {lang.templateByUuid("UID:P_UBL-FE_2009604004280333", "是") /* "是" */}
                                            </Option>
                                            <Option fieldid="UCG-FE-view-register-src-components-IndexView-index-10759941-Option" key={0} value={0}>
                                                {lang.templateByUuid("UID:P_UBL-FE_2009604004280320", "通用") /* "通用" */}
                                            </Option>
                                        </Select>
                                    </FormItem>

                                    <FormItem
                                        fieldid="ublinker-view-register-src-components-IndexView-index-6673926-FormItem"
                                        label={lang.templateByUuid("UID:P_UBL-FE_2009604004280338", "职级体系") /* "职级体系" */}
                                        name="job"
                                    >
                                        <Select
                                            fieldid="ublinker-view-register-src-components-IndexView-index-3641643-Select"
                                            onChange={this.handleInfoChange.bind(null, "job")}
                                        >
                                            <Option fieldid="UCG-FE-view-register-src-components-IndexView-index-5282000-Option" key={0} value={0}>
                                                {lang.templateByUuid("UID:P_UBL-FE_2009604004280320", "通用") /* "通用" */}
                                            </Option>
                                            <Option fieldid="UCG-FE-view-register-src-components-IndexView-index-5426954-Option" key={1} value={1}>
                                                {lang.templateByUuid("UID:P_UBL-FE_200960400428033B", "职务类别") /* "职务类别" */}
                                            </Option>
                                            <Option fieldid="UCG-FE-view-register-src-components-IndexView-index-4159121-Option" key={2} value={2}>
                                                {lang.templateByUuid("UID:P_UBL-FE_200960400428033F", "岗位序列") /* "岗位序列" */}
                                            </Option>
                                            <Option fieldid="UCG-FE-view-register-src-components-IndexView-index-4159121-Option" key={3} value={3}>
                                                {lang.templateByUuid("UID:P_UBL-FE_200960400428031F", "不支持") /* "不支持" */}
                                            </Option>
                                        </Select>
                                    </FormItem>
                                    <FormItem
                                        fieldid="ublinker-view-register-src-components-IndexView-index-6673926-FormItem"
                                        label={lang.templateByUuid("UID:P_UBL-FE_200960400428033A", "旗舰版是否HR") /* "旗舰版是否HR" */}
                                        name="bipIsHr"
                                    >
                                        <Select fieldid="4e4cd921-1b59-4590-ae80-42da5f820c76" onChange={this.handleInfoChange.bind(null, "bipIsHr")}>
                                            <Option key={0} value={0}>
                                                {lang.templateByUuid("UID:P_UBL-FE_2009604004280320", "通用") /* "通用" */}
                                            </Option>
                                            <Option key={1} value={1}>
                                                {lang.templateByUuid("UID:P_UBL-FE_2009604004280333", "是") /* "是" */}
                                            </Option>
                                            <Option key={2} value={2}>
                                                {lang.templateByUuid("UID:P_UBL-FE_2009604004280334", "否") /* "否" */}
                                            </Option>
                                        </Select>
                                    </FormItem>
                                    <FormItem
                                        fieldid="ublinker-view-register-src-components-IndexView-index-5811750-FormItem"
                                        label={lang.templateByUuid("UID:P_UBL-FE_2009604004280341", "是否只支持新架构") /* "是否只支持新架构" */}
                                        name="supportNewArch"
                                    >
                                        <Select
                                            fieldid="ublinker-view-register-src-components-IndexView-index-845582-Select"
                                            onChange={this.handleInfoChange.bind(null, "supportNewArch")}
                                        >
                                            <Option fieldid="UCG-FE-view-register-src-components-IndexView-index-521206-Option" key={0} value={0}>
                                                {lang.templateByUuid("UID:P_UBL-FE_2009604004280320", "通用") /* "通用" */}
                                            </Option>
                                            <Option fieldid="UCG-FE-view-register-src-components-IndexView-index-7914158-Option" key={1} value={1}>
                                                {lang.templateByUuid("UID:P_UBL-FE_200960400428031E", "支持") /* "支持" */}
                                            </Option>
                                            <Option fieldid="UCG-FE-view-register-src-components-IndexView-index-680639-Option" key={2} value={2}>
                                                {lang.templateByUuid("UID:P_UBL-FE_200960400428031F", "不支持") /* "不支持" */}
                                            </Option>
                                        </Select>
                                    </FormItem>
                                </FormList>
                            </Fragment>
                        </Modal.Body>

                        <Modal.Footer>
                            <Button
                                fieldid="ublinker-view-register-src-components-IndexView-index-6232106-Button"
                                className="ucg-mr-10"
                                onClick={this.save}
                                colors="primary"
                            >
                                {lang.templateByUuid("UID:P_UBL-FE_2009604004280326", "确定") /* "确定" */}
                            </Button>
                            <Button fieldid="ublinker-view-register-src-components-IndexView-index-2452666-Button" onClick={this.close} colors="secondary">
                                {lang.templateByUuid("UID:P_UBL-FE_2009604004280328", "取消") /* "取消" */}
                            </Button>
                        </Modal.Footer>
                    </Modal>
                </Content>
            </div>
        );
    }
}

export default IndexView;
