import React, { Component, Fragment } from "react";
import query from "query-string";
import withRouter from "decorator/withRouter";
import { Tabs } from "components/TinperBee";
import Affix from "components/TinperBee/Affix";
import { Content } from "components/PageView";
import { getScrollContainer, appContainer } from "utils/containerEle";
import { getTenantInfoService } from "services/common";
import SearchInput from "components/TinperBee/SearchInput";
import CardList from "components/Card";
import "./index.less";
import { getLogo } from "connector/ec/routes/utils";

const defaultLogo = getLogo("connector/defaultLogo.png");

@withRouter
class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            hoverData: null,
        };
    }

    componentDidMount() {
        this.props.ownerStore.getDataSource(false);
        getTenantInfoService();
    }

    componentWillUnmount() {
        this.props.ownerStore.initTab();
    }

    getTabs = (tabs) => {
        return tabs.map((tab) => {
            if (tab.hide) {
                return null;
            } else {
                let { type, key, name } = tab;
                return <Tabs.TabPane fieldid="UCG-FE-routes-home-components-IndexView-index-8764405-Tabs.TabPane" tab={name} key={key} />;
            }
        });
    };

    handleSearch = (searchValue) => {
        this.props.ownerStore.getDataSource(false, searchValue);
    };

    getActiveTab = (tabs, activeTabKey) => {
        return tabs.find((tab) => tab.key === activeTabKey);
    };

    handleConfig = async (data) => {
        this.props.navigate({
            pathname: "/config",
            search:
                "?" +
                query.stringify({
                    appCode: data.appcode,
                }),
        });
        // const { ownerStore: { configAuthStatus } } = this.props;
        // let authRes = await configAuthStatus(data);
        // if (authRes) {
        //   if (authRes.data.state) {
        //     this.props.navigate({
        //       pathname: '/config',
        //       search: '?' + query.stringify({
        //         appCode: data.appcode
        //       })
        //     })
        //   }else {
        //     let isDiwork= QUERY_PARAMS.from === 'diwork';
        //     Modal.confirm({
        //       title:
        //       content:
        //       // okText: isDiwork ? '前往我的应用' : '前往云市场',
        //       icon: <Icon fieldid="ublinker-routes-home-components-IndexView-index-6576016-Icon" type="uf-i-c-2"/>,
        //       confirmType: 'one',
        //       onOk: () => {
        //         if (QUERY_PARAMS.from === 'diwork') {
        //           window.jDiwork.openService('GZTSYS010', 2)
        //         }else {
        //           //f8986ec6-34ef-409a-a45f-08191c2ebe0e
        //         }
        //       },
        //     })
        //   }
        // }
    };

    handleTabChange = (tabKey) => {
        let { ownerState, ownerStore } = this.props;
        let nextTab = this.getActiveTab(ownerState.tabs, tabKey);
        this.searchNode.setValue(nextTab.searchKey);
        ownerStore.changeActiveTab(tabKey);
    };

    getFooterDisabled = (data) => {
        return !data.enable;
    };

    footerOptions = [
        {
            key: "config",
            icon: "cl cl-peizhi1",
            name: lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F8005D", "配置") /* "配置" */,
            onClick: this.handleConfig,
        },
        {
            key: "init-set",
            icon: "cl cl-chushihuayuchuli",
            name: lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F80062", "初始化预处理") /* "初始化预处理" */,
            disabled: this.getFooterDisabled,
            onClick: this.props.ownerStore.initData,
        },
        {
            key: "init",
            icon: "cl cl-chushihua",
            name: lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F8005B", "初始化") /* "初始化" */,
            disabled: this.getFooterDisabled,
            onClick: this.props.ownerStore.pushData,
        },
    ];

    columns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F8005F", "应用名称") /* "应用名称" */,
            dataIndex: "appname",
            $$type: "title",
        },
        {
            title: "logo",
            dataIndex: "logopath",
            $$type: "logo",
            render: (value, record) => {
                const { logopath } = record;
                return logopath ? getLogo("eclogo/" + logopath) : defaultLogo;
            },
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F8005C", "公司名称") /* "公司名称" */,
            dataIndex: "corpname",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F8005E", "网址") /* "网址" */,
            dataIndex: "httpurl",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F80061", "公司地址") /* "公司地址" */,
            dataIndex: "corpaddress",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F80063", "企业热线") /* "企业热线" */,
            dataIndex: "telephone",
        },
    ];

    render() {
        let { ownerState, ownerStore } = this.props;
        let { tabs, activeTabKey } = ownerState;
        tabs[0].name = lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F80067", "全部电商"); /* "全部电商" */
        tabs[1].name = lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F80064", "已开通"); /* "已开通" */
        tabs[2].name = lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F80066", "未开通"); /* "未开通" */
        let activeTabData = this.getActiveTab(tabs, activeTabKey);
        return (
            <Content className="sys-connector-content">
                <Affix
                    fieldid="ublinker-routes-home-components-IndexView-index-5514009-Affix"
                    zIndex={1600}
                    getPopupContainer={appContainer}
                    // container={appContainer}
                    target={getScrollContainer}
                >
                    <Tabs
                        fieldid="ublinker-routes-home-components-IndexView-index-9512467-Tabs"
                        className="sys-connector-tab"
                        activeKey={activeTabKey}
                        onChange={this.handleTabChange}
                        extraContent={
                            <Fragment>
                                <SearchInput
                                    className="ucg-mar-r-20 ucg-float-r"
                                    size="lg"
                                    ref={(searchNode) => (this.searchNode = searchNode)}
                                    placeholder={lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F80060", "按名称搜索", undefined, {
                                        returnStr: true,
                                    })}
                                    // value={activeTabData.searchKey}
                                    // onChange={ownerStore.changeSearchKey}
                                    showClear
                                    onSearch={this.handleSearch}
                                />
                            </Fragment>
                        }
                    >
                        {this.getTabs(tabs)}
                    </Tabs>
                </Affix>

                <div className="sys-connector-view-wrap">
                    <CardList
                        fieldid="UCG-FE-routes-home-components-IndexView-index-4188730-CardList"
                        labelAlign="right"
                        dataSource={activeTabData.dataSource}
                        actions={this.footerOptions}
                        columns={this.columns}
                        isEmpty={activeTabData.loaded && activeTabData.dataSource.length <= 0}
                    />
                </div>
            </Content>
        );
    }
}

export default IndexView;
