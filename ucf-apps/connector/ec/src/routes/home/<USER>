import { observable, action, toJS, reaction, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { defaultListMap } from "utils/pageListUtils";
import { autoServiceMessage } from "utils/service";
import * as ownerService from "./service";
import commonText from "constants/commonText";
const initState = {
    tabs: [
        {
            key: "all",
            name: lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F80067", "全部电商") /* "全部电商" */,
            dataSource: [],
            loaded: false,
            searchKey: "",
        },
        {
            key: "opened",
            name: lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F80064", "已开通") /* "已开通" */,
            dataSource: [],
            loaded: false,
            searchKey: "",
        },
        {
            key: "notOpened",
            name: lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F80066", "未开通") /* "未开通" */,
            dataSource: [],
            loaded: false,
            searchKey: "",
        },
    ],
    activeTabKey: "all",
    searchKey: "",
    serviceCodeDiwork: "kfljdslj",
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = {
        tabs: [
            {
                key: "all",
                name: lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F80067", "全部电商") /* "全部电商" */,
                dataSource: [],
                loaded: false,
                searchKey: "",
            },
            {
                key: "opened",
                name: lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F80064", "已开通") /* "已开通" */,
                dataSource: [],
                loaded: false,
                searchKey: "",
            },
            {
                key: "notOpened",
                name: lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F80066", "未开通") /* "未开通" */,
                dataSource: [],
                loaded: false,
                searchKey: "",
            },
        ],
        activeTabKey: "all",
        searchKey: "",
        serviceCodeDiwork: "kfljdslj",
    };

    initTab = () => {
        this.state.tabs = initState.tabs;
    };

    changeActiveTab = (tabKey) => {
        this.state.activeTabKey = tabKey;
        this.getDataSource(true);
    };

    // changeSearchKey = (_searchKey) => {
    //   console.log(_searchKey)
    //   let { activeTabKey, tabs } = this.state;
    //   let tab = tabs.find(tab => tab.key === activeTabKey);
    //   tab.searchKey = _searchKey;
    //   this.state.tabs = tabs;
    // }

    getDataSource = async (isChangeTab = false, searchValue) => {
        let { activeTabKey, tabs } = this.state;
        let tab = tabs.find((tab) => tab.key === activeTabKey);
        if (isChangeTab && tab.loaded) {
            this.state.activeDataSource = tab.dataSource;
            return false;
        }
        let enable = "";
        switch (activeTabKey) {
            case "opened":
                enable = "1";
                break;
            case "notOpened":
                enable = "0";
                break;
            case "all":
            default:
                enable = "";
                break;
        }
        let { searchKey } = tab;
        let _searchKey = typeof searchValue === "undefined" ? searchKey : searchValue;
        let res = await autoServiceMessage({
            service: ownerService.getConnectorEcListService({ enable: enable, key: _searchKey }, { serviceCode: this.state.serviceCodeDiwork }),
        });
        if (res) {
            let resData = res.data || [];
            tab.dataSource = resData;
            tab.loaded = true;
            tab.searchKey = _searchKey;
            this.state.tabs = tabs;
        }
    };

    initData = async (data) => {
        await autoServiceMessage({
            service: ownerService.initDataService(data.appcode, { serviceCode: this.state.serviceCodeDiwork }),
            success: lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F80065", "操作成功") /* "操作成功" */,
        });
    };

    pushData = async (data) => {
        await autoServiceMessage({
            service: ownerService.pushDataService(data.appcode, { serviceCode: this.state.serviceCodeDiwork }),
            success: lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F80065", "操作成功") /* "操作成功" */,
        });
    };

    configAuthStatus = async (data) => {
        return await autoServiceMessage({
            service: ownerService.configAuthStatusService(data.appcode, { serviceCode: this.state.serviceCodeDiwork }),
        });
    };
}
/**
 * 根据具体模块修改 storeKey 为保证唯一性
 * storeKey 由 模块文件夹名称 + 页面名称 + 'Store' 小驼峰
 *
 * storeKey是作为当前 Store 的唯一键， storeKey与Store的实例会在 rootStore或者StoreMap中以 键值对的 形式存在
 * 1. 用于在模块的 storeMap（模块routes/store.js）中注册并初始化当前Store
 * 2. 用于在container的 inject 中关联当前Store的实例时，从模块的storeMap或者rootStore中获取对应store实例
 * */
export const storeKey = "connectorEcHomeStore";

export default Store;
