import { getInvokeService } from "utils/service";

/**
 * 获取电商应用
 * @param {Object} data
 * @param {String|Number} data.enable=[''|0|1] -空：获取全部；1：获取已开通；0：获取未开通
 * @returns {Promise<unknown>}
 */
export const getConnectorEcListService = function (data, header) {
    return getInvokeService(
        {
            method: "GET",
            path: "/mygwapp/sysauth/list",
            header,
        },
        data
    );
};

/**
 * 初始化预处理
 * @param appCode
 * @returns {Promise<unknown>}
 */
export const initDataService = function (appCode, header) {
    let url = "",
        method = "";
    if (appCode === "ebnew") {
        method = "GET";
        url = "/mygwapp/erpdata/task/nc6/ebnew/init";
    } else {
        method = "POST";
        url = "/mygwapp/sysauth/initdata/" + appCode;
    }
    return getInvokeService({
        method: method,
        path: url,
        header,
    });
};

/**
 * 初始化数据
 * @param appCode
 * @returns {Promise<unknown>}
 */
export const pushDataService = function (appCode, header) {
    return getInvokeService({
        method: "POST",
        path: "/mygwapp/sysauth/pushdata/" + appCode,
        header,
    });
};

/**
 * 判断应用是否可配置，点击配置先调用此接口，如果不可配置，提示去购买开通
 * @param appCode
 * @returns {Promise<unknown>}
 */
export const configAuthStatusService = function (appCode, header) {
    return getInvokeService({
        method: "GET",
        path: "/mygwapp/sysauth/auth/" + appCode,
        header,
    });
};
