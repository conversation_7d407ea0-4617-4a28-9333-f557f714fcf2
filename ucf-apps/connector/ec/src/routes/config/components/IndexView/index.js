import React, { Fragment, useState, useCallback, useMemo } from "react";
import { Header, Content } from "components/PageView";
// import FormList from "components/TinperBee/Form";
import { FormControl, Checkbox, Button, FormList, Icon } from "components/TinperBee";
import Modal from "components/TinperBee/Modal";
import commonText from "constants/commonText";
import emptyImg from "./empty.png";
import "./index.less";
const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 12 },
};
const FormItem = FormList.Item;
const ConfigForm = (props) => {
    const { fields, data, form, children } = props;
    form.setFieldsValue(data);
    if (fields.length > 0) {
        return (
            <FormList
                fieldid="ublinker-routes-config-components-IndexView-index-9386676-FormList"
                layoutOpt={{ md: 12 }}
                form={form}
                name="form122"
                labelAlign="right"
                {...formItemLayout}
            >
                {fields.map((item) => {
                    const { id, name, label, placeholder, disabled } = item;
                    return (
                        <FormItem fieldid="ublinker-routes-config-components-IndexView-index-4800432-FormItem" label={label} key={id} name={name}>
                            <FormControl
                                fieldid="ublinker-routes-config-components-IndexView-index-6932326-FormControl"
                                placeholder={placeholder}
                                disabled={disabled}
                            />
                        </FormItem>
                    );
                })}
                <FormItem fieldid="ublinker-routes-config-components-IndexView-index-5377429-FormItem" name="aaf" label=" ">
                    {children}
                </FormItem>
            </FormList>
        );
    } else {
        return null;
    }
};

const CustomConfigModal = (props) => {
    const { customFields, show, form, saveCustom, deleteCustomItem, changeCustomConfigModalShow, changeAddCustomItemModalShow } = props;

    const handleOk = useCallback(() => {
        saveCustom();
        changeCustomConfigModalShow();
    }, [show]);

    const notFields = customFields.length <= 0;

    return (
        <Modal
            fieldid="ublinker-routes-config-components-IndexView-index-6291760-Modal"
            show={show}
            title={lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F8004E", "自定义授权模板", undefined, {
                returnStr: true,
            })}
            okText={lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F80050", "保存模板") /* "保存模板" */}
            width={"700px"}
            okDisabled={notFields}
            onOk={handleOk}
            onCancel={changeCustomConfigModalShow}
            maxHeight={0.8}
        >
            <div className="ucg-pad-20-30">
                {notFields ? null : (
                    <div className="custom-list">
                        {customFields.map((item, index) => {
                            const { id, name, label, placeholder } = item;
                            return (
                                <div className="custom-item" key={id}>
                                    <span className="custom-item-label">{label}</span>
                                    <div className="custom-item-input">
                                        <FormControl
                                            fieldid="ublinker-routes-config-components-IndexView-index-5874655-FormControl"
                                            placeholder={placeholder}
                                            readOnly
                                        />
                                    </div>
                                    <span className="custom-item-clear" onClick={deleteCustomItem.bind(null, index)}>
                                        <i fieldid="ublinker-routes-config-components-IndexView-index-1579630-i" className="cl cl-cancel" />
                                    </span>
                                </div>
                            );
                        })}
                    </div>
                )}
                <a
                    fieldid="ublinker-routes-config-components-IndexView-index-7447307-a"
                    className="con-ec-config-add-custom-btn"
                    role="button"
                    onClick={changeAddCustomItemModalShow}
                >
                    <i fieldid="ublinker-routes-config-components-IndexView-index-3386619-i" className="cl cl-plus" />
                    {lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F8004F", "添加文本") /* "添加文本" */}
                </a>

                {notFields ? (
                    <div className="custom-empty">
                        <img fieldid="ublinker-routes-config-components-IndexView-index-4483336-img" src={emptyImg} alt="" />
                        <p>{lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F80054", "暂无内容") /* "暂无内容" */}</p>
                    </div>
                ) : null}
            </div>
        </Modal>
    );
};

const AddCustomItemModal = (props) => {
    const {
        show,
        changeAddCustomItemModalShow,
        addCustomItem,
        form,
        form: { validateFields },
    } = props;

    const handleOk = useCallback(() => {
        validateFields().then((values) => {
            addCustomItem(values);
            changeAddCustomItemModalShow();
        });
    }, [show]);

    return (
        <Modal
            fieldid="ublinker-routes-config-components-IndexView-index-6366301-Modal"
            show={show}
            backdrop={false}
            title={lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F8004F", "添加文本", undefined, {
                returnStr: true,
            })}
            size="md"
            onCancel={changeAddCustomItemModalShow}
            onOk={handleOk}
        >
            <FormList
                fieldid="ublinker-routes-config-components-IndexView-index-4755572-FormList"
                className="ucg-pad-20-30"
                form={form}
                name="form1223"
                labelAlign="right"
                {...formItemLayout}
                // layoutOpt={{ md: 12 }}
            >
                <FormItem
                    fieldid="ublinker-routes-config-components-IndexView-index-8455995-FormItem"
                    label={lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F80057", "字段编码") /* "字段编码" */}
                    name="name"
                    rules={[
                        {
                            required: true,
                            message: lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F80058", "请输入字段编码") /* "请输入字段编码" */,
                        },
                    ]}
                >
                    <FormControl
                        fieldid="ublinker-routes-config-components-IndexView-index-7812098-FormControl"
                        placeholder={lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F80058", "请输入字段编码", undefined, {
                            returnStr: true,
                        })}
                    />
                </FormItem>
                <FormItem
                    fieldid="ublinker-routes-config-components-IndexView-index-6774088-FormItem"
                    label={lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F8004C", "字段名称") /* "字段名称" */}
                    name="label"
                    rules={[
                        {
                            required: true,
                            message: lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F8004D", "请输入字段名称") /* "请输入字段名称" */,
                        },
                    ]}
                >
                    <FormControl
                        fieldid="ublinker-routes-config-components-IndexView-index-5415228-FormControl"
                        placeholder={lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F8004D", "请输入字段名称", undefined, {
                            returnStr: true,
                        })}
                    />
                </FormItem>
                <FormItem
                    fieldid="ublinker-routes-config-components-IndexView-index-4677518-FormItem"
                    label={lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F80051", "字段提示") /* "字段提示" */}
                    name="placeholder"
                >
                    <FormControl
                        fieldid="ublinker-routes-config-components-IndexView-index-2008081-FormControl"
                        placeholder={lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F80053", "请输入字段提示", undefined, {
                            returnStr: true,
                        })}
                    />
                </FormItem>
                <FormItem
                    fieldid="ublinker-routes-config-components-IndexView-index-5879421-FormItem"
                    label={lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F80055", "帮助信息") /* "帮助信息" */}
                    name="helper"
                >
                    <FormControl
                        fieldid="ublinker-routes-config-components-IndexView-index-6117576-FormControl"
                        placeholder={lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F80056", "请输入帮助信息", undefined, {
                            returnStr: true,
                        })}
                    />
                </FormItem>
                <FormItem
                    fieldid="ublinker-routes-config-components-IndexView-index-6435720-FormItem"
                    label=" "
                    name="disabled"
                    initialValue={false}
                    valuePropName={"checked"}
                >
                    <Checkbox fieldid="UCG-FE-routes-config-components-IndexView-index-3773251-Checkbox">
                        {lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F80059", "是否可编辑") /* "是否可编辑" */}
                    </Checkbox>
                </FormItem>
            </FormList>
        </Modal>
    );
};

const Index = (props) => {
    const [form] = FormList.useForm();
    const { ownerState, ownerStore } = props;
    const {
        fieldList,
        customFields,
        config: { data = {} },
        appInfo: { appname = "" },
        custom,
    } = ownerState;

    const [customConfigModalShow, setCustomConfigModalShow] = useState(false);

    const [addCustomItemModalShow, setAddCustomItemModalShow] = useState(false);

    const handleSave = useCallback(() => {
        let { validateFields } = form;
        validateFields().then((values) => {
            props.ownerStore.save(values);
        });
    }, []);

    const changeCustomConfigModalShow = useCallback(() => {
        let preStatus = !customConfigModalShow;
        ownerStore.initCustomFields(preStatus);
        setCustomConfigModalShow(preStatus);
    }, [customConfigModalShow]);

    const changeAddCustomItemModalShow = useCallback(() => {
        setAddCustomItemModalShow(!addCustomItemModalShow);
    }, [addCustomItemModalShow]);

    const canCustom = useMemo(() => {
        return custom || !data.id;
    }, [custom, data.id]);

    return (
        <Fragment>
            <Header
                back
                bordered
                title={
                    appname +
                    lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F80052", "授权信息", undefined, {
                        returnStr: true,
                    }) /* "授权信息" */
                }
            >
                {canCustom ? (
                    <Button fieldid="ublinker-routes-config-components-IndexView-index-3479191-Button" bordered onClick={changeCustomConfigModalShow}>
                        {lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F8004E", "自定义授权模板") /* "自定义授权模板" */}
                    </Button>
                ) : null}
            </Header>

            <Content>
                <div className="con-ec-config-form">
                    <ConfigForm fields={fieldList} data={data} form={form} labelCol={266}>
                        <Button fieldid="ublinker-routes-config-components-IndexView-index-8570517-Button" onClick={handleSave} colors="primary">
                            {lang.templateByUuid("UID:P_UBL-FE_18D7622804180242", "保存") /* "保存" */}
                        </Button>
                    </ConfigForm>
                </div>
            </Content>

            <CustomConfigModal
                show={customConfigModalShow}
                customFields={customFields}
                changeCustomConfigModalShow={changeCustomConfigModalShow}
                changeAddCustomItemModalShow={changeAddCustomItemModalShow}
                deleteCustomItem={ownerStore.deleteCustomItem}
                saveCustom={ownerStore.saveCustom}
                form={form}
            />

            <AddCustomItemModal
                show={addCustomItemModalShow}
                addCustomItem={ownerStore.addCustomItem}
                changeAddCustomItemModalShow={changeAddCustomItemModalShow}
                form={form}
            />
        </Fragment>
    );
};

export default Index;
