.con-ec-config-form {
  width: 50%;
  max-width: 660px;
  padding-top: 20px;
}
.con-ec-config-add-custom-btn {
  display: block;
  width: 160px;
  height: 40px;
  margin: 16px auto;
  border: 1px dashed #DBE0E5;
  font-size: 12px;
  line-height: 40px;
  color: #588CE9;
  text-align: center;
}
.con-ec-config-add-custom-btn .cl {
  font-size: 12px;
}
.custom-empty {
  padding: 120px 0 185px;
  text-align: center;
}
.custom-empty > img {
  height: 80px;
  width: auto;
}
.custom-empty > p {
  font-size: 12px;
  color: #999;
  line-height: 18px;
}
.custom-item {
  padding: 8px 64px 8px 8px;
  border: 1px dashed transparent;
  position: relative;
}
.custom-item:hover {
  border-color: #DBE0E5;
  background: #F7F9FD;
}
.custom-item:hover .custom-item-clear {
  display: block;
}
.custom-item .custom-item-label {
  width: 148px;
  float: left;
  text-align: right;
  line-height: 32px;
  font-size: 12px;
  color: #333333;
  margin-right: 10px;
}
.custom-item .custom-item-input {
  overflow: hidden;
}
.custom-item .custom-item-clear {
  position: absolute;
  top: 12px;
  right: 20px;
  width: 24px;
  height: 24px;
  border: 1px solid #E4E4E4;
  border-radius: 50%;
  background: #FFFFFF;
  line-height: 24px;
  text-align: center;
  cursor: pointer;
  display: none;
}
.custom-item .custom-item-clear i.cl {
  font-size: 12px;
  font-weight: bold;
  transform: scale(0.8);
  display: block;
}
/*# sourceMappingURL=index.css.map */