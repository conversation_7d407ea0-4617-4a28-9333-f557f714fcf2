import { observable, makeObservable } from "mobx";
import DefaultS<PERSON> from "utils/defaultStore";
import * as ownerService from "./services";
import { autoServiceMessage } from "utils/service";
import commonText from "constants/commonText";
const defaultSchemaProperty = {
    type: "string",
    title: "",
    required: false,
};

const defaultField = {
    id: "",
    name: "",
    label: "",
    placeholder: "",
    helper: "",
    disabled: false,
    type: "text",
};

// const defaultConfig = {
// 	"schema": {
//
// 	},
// 	"data": {},
// 	"options": {
// 		"validate": true
// 	}
// }

const initState = {
    appInfo: {},
    config: {},
    fieldList: [],
    queryParams: {
        appCode: "",
    },
    custom: false, //是否为自定义配置,
    customFields: [],
    serviceCodeDiwork: "kfljdslj",
};

class Store extends DefaultStore {
    constructor() {
        super();
        makeObservable(this);
    }
    @observable state = initState;

    setPageParams = (paramMap) => {
        this.changeState(paramMap);
    };

    getConfig = async () => {
        const { queryParams } = this.state;
        let res = await autoServiceMessage({
            service: ownerService.getEcConfigInfoService(queryParams.appCode, { serviceCode: this.state.serviceCodeDiwork }),
        });

        if (res) {
            let { app, contentModel } = res.data;
            let { config } = contentModel || {};
            let custom = false;
            if (config) {
                custom = true;
            } else {
                config = observable(defaultConfig);
                config.data = contentModel || {};
            }
            const {
                options: { fields },
            } = config;
            const fieldKeys = Object.keys(fields);
            const fieldList = fieldKeys.map((fieldKey) => {
                return fields[fieldKey];
            });
            console.log(app, config, fieldList, custom);
            this.changeState({
                appInfo: app,
                config: config,
                fieldList: fieldList,
                custom: custom,
            });
        }
    };

    save = async (data) => {
        let { custom, config, appInfo } = this.state;
        let service = null;
        if (custom) {
            data = {
                appcode: appInfo.appcode,
                config: {
                    data: data,
                },
            };
            service = ownerService.saveCustomConfigService;
        } else {
            if (config.data && config.data.id) {
                data.id = config.data.id;
            }
            data.appcode = appInfo.appcode;
            data.dataversion = appInfo.dataversion;
            service = ownerService.saveDefaultConfigService;
        }

        return await autoServiceMessage({
            service: service(data, { serviceCode: this.state.serviceCodeDiwork }),
            success: lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F8005A", "保存成功") /* "保存成功" */,
        });
    };

    initCustomFields = (status) => {
        let customFields = [];
        if (status) {
            const { custom } = this.state;
            if (custom) {
                customFields = this.toJS(this.state.fieldList);
            }
        }

        this.state.customFields = customFields;
    };

    addCustomItem = (data) => {
        data.id = data.name;
        this.state.customFields.push(data);
    };

    deleteCustomItem = (index) => {
        this.state.customFields.splice(index, 1);
    };

    saveCustom = async () => {
        let { config, custom, customFields, queryParams } = this.toJS();
        let options_fields = {};
        let schema_properties = {};
        customFields.forEach((item) => {
            let { label, name } = item;
            options_fields[name] = { ...item };
            schema_properties[name] = {
                type: "string",
                title: label,
            };
        });
        config.schema.properties = schema_properties;
        config.options.fields = options_fields;
        const res = await autoServiceMessage({
            service: ownerService.saveCustomService(
                {
                    appcode: queryParams.appCode,
                    config: JSON.stringify(config),
                    custom,
                },
                { serviceCode: this.state.serviceCodeDiwork }
            ),
            success: lang.templateByUuid("UID:P_UBL-FE_18EE9DCE04F8005A", "保存成功") /* "保存成功" */,
        });
        if (res) {
            this.getConfig();
        }
    };
}

export const storeKey = "connectorEcConfigStore";

export default Store;
