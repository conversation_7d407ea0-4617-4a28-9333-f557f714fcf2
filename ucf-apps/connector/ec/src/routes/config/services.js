import { getInvokeService } from "utils/service";

/**
 * 获取配置信息
 * @param appCode
 * @returns {Promise<unknown>}
 */
export const getEcConfigInfoService = function (appCode, header) {
    return getInvokeService({
        method: "GET",
        path: "/mygwapp/sysauth/info/" + appCode,
        header,
    });
};

/**
 * 保存默认配置
 * @param data
 * @returns {Promise<unknown>}
 */
export const saveDefaultConfigService = function (data, header) {
    let path = "/mygwapp/sysauth/";
    if (data.id) {
        path += "edit/" + data.appcode;
    }

    let formData = new FormData();

    Object.keys(data).forEach((dataKey) => {
        let dataValue = data[dataKey];
        formData.append(dataKey, dataValue || "");
    });

    return getInvokeService(
        {
            method: "POST",
            path: path,
            header,
        },
        formData
    );
};

/**
 * 保存自定义配置
 * @param data
 * @returns {Promise<unknown>}
 */
export const saveCustomConfigService = function (data, header) {
    return getInvokeService(
        {
            method: "POST",
            path: "/mygwapp/sysauth/custom",
            header,
        },
        data
    );
};

/**
 * 保存自定义模板
 * @param {Object} data
 * @param {String} data.appcode
 * @param {Object} data.config
 * @param {Boolean} data.custom
 * @returns {Promise<unknown>}
 */
export const saveCustomService = function (data, header) {
    const { custom, ...otherData } = data;
    let path = "/mygwapp/sysauth";
    if (custom) {
        path += "/form";
    } else {
        path += "/design";
    }
    return getInvokeService(
        {
            method: "POST",
            path: path,
            header,
        },
        otherData
    );
};
