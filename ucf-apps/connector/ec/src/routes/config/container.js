import React, { Component } from "react";
import IndexView from "./components/IndexView";
import { inject, observer } from "mobx-react";
import Store, { storeKey } from "./store";
import core from "core";
import { getPageParams } from "decorator/index";
core.addStore({
    storeKey: storeKey,
    store: new Store(),
});

@inject((rootStore) => {
    let ownerStore = rootStore[storeKey];
    return {
        ownerState: ownerStore.toJS(),
        ownerStore: ownerStore,
    };
})
@getPageParams
class Container extends Component {
    constructor(props) {
        super(props);
        let pageParam = this.props.getPageParams(props);
        props.ownerStore.setPageParams(pageParam);
    }

    componentDidMount() {
        this.props.ownerStore.getConfig();
    }

    render() {
        return <IndexView {...this.props} />;
    }
}
export default Container;
