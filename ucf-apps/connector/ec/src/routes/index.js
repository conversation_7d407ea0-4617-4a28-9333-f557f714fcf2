import React from "react";
import { RoutesRender } from "core";

import HomeContainer from "./home/<USER>";
import ConfigContainer from "./config/container";
import getConfigProvider from "components/ConfigProvider";
import config from "../config";
const ConfigProvider = getConfigProvider(config);

const routes = [
    {
        path: "/",
        exact: true,
        component: HomeContainer,
    },
    {
        path: "/home",
        component: HomeContainer,
    },
    {
        path: "/config",
        component: ConfigContainer,
    },
];

const Routes = () => {
    return (
        <ConfigProvider fieldid="UCG-FE-connector-ec-src-routes-index-18839-ConfigProvider">
            <RoutesRender routes={routes} />
        </ConfigProvider>
    );
};
export default Routes;
