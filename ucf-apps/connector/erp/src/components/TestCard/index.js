import React, { Fragment, useMemo, useEffect, useState, forwardRef, useImperativeHandle, useCallback } from "react";
import { formatTextArea } from "utils";
import DotLoading from "./DotLoading";
import "./index.less";

const TestCard = (props, ref) => {
    const { show = false, className = "", targetNode, cardType = "card", style = {}, steps, stepExpresses, onClose } = props;
    /** 下次要执行的express的索引 */
    const [nextExpressIndex, setNextExpressIndex] = useState(0);
    /** 已执行的express的状态 */
    const [expressStatus, setExpressStatus] = useState([]);
    const [error, setError] = useState(null);
    const [canClose, setCanClose] = useState(false);
    const [cardStyle, cardArrowStyle] = useMemo(() => {
        const node = typeof targetNode.current === "undefined" ? targetNode : targetNode.current;
        let _cardStyle = { ...style },
            arrowStyle = null;
        if (node) {
            try {
                const targetEle = node;
                const left = targetEle.offsetLeft,
                    width = targetEle.offsetWidth,
                    height = targetEle.offsetHeight;
                arrowStyle = {
                    left: left + width / 2 - 6,
                };
                if (cardType === "tip") {
                    _cardStyle = {
                        ..._cardStyle,
                        position: "absolute",
                        zIndex: 99,
                        top: height,
                    };
                }
            } catch (e) {
                arrowStyle = {};
            }
        } else {
            arrowStyle = {};
        }
        return [_cardStyle, arrowStyle];
    }, [targetNode, targetNode.current]);

    const init = useCallback(() => {
        if (canClose) {
            setNextExpressIndex(0);
            setExpressStatus([]);
            setCanClose(false);
            setError(null);
        }
    }, [canClose]);

    useEffect(() => {
        if (!show) {
            init();
        }
    }, [show]);

    const cls = useMemo(() => {
        return className ? "test-card-wrap " + className : "test-card-wrap";
    }, [className]);

    useEffect(() => {
        if (show) {
            let stepExpress = stepExpresses[nextExpressIndex];
            if (stepExpress && stepExpress.promise) {
                let status = expressStatus[nextExpressIndex] || "init";
                if (status === "init") {
                    let promise = stepExpress.promise;
                    expressStatus[nextExpressIndex] = "pending";
                    setExpressStatus([...expressStatus]);
                    setCanClose(false);
                    let _promise = typeof promise === "function" ? promise() : promise;
                    _promise
                        .then((res = {}) => {
                            let status = res.status || "success";
                            expressStatus[nextExpressIndex] = status;
                            setExpressStatus([...expressStatus]);
                            if (status === "success") {
                                setNextExpressIndex(nextExpressIndex + 1);
                            } else {
                                if (res.message) {
                                    setError({
                                        message: res.message,
                                        color: stepExpress[status + "Color"],
                                    });
                                }
                            }

                            setCanClose(true);
                        })
                        .catch((error) => {
                            setError({
                                message: error.msg || "",
                            });
                            expressStatus[nextExpressIndex] = "fail";
                            setExpressStatus([...expressStatus]);
                            setCanClose(true);
                        });
                }
            }
        }
    }, [nextExpressIndex, expressStatus, show]);

    useImperativeHandle(ref, () => {
        return {
            refresh: init,
        };
    }, [canClose]);

    const closeCls = useMemo(() => {
        return "uf uf-close card-close" + (canClose ? "" : " mix-disabled");
    }, [canClose]);

    if (show) {
        return (
            <div className={cls} style={cardStyle} ref={ref}>
                <span className="card-arrow" style={cardArrowStyle} />
                <i fieldid="ublinker-erp-src-components-TestCard-index-287709-i" className={closeCls} onClick={onClose} />
                <ul className="test-card-step-list">
                    {steps.map((item, index) => {
                        const { icon, name } = item;
                        const stepExpress = stepExpresses[index - 1] || {};
                        const status = expressStatus[index - 1] || "init";
                        return (
                            <Fragment key={index}>
                                {index > 0 ? (
                                    <li className="test-card-step-between">
                                        <span className={"text " + status} style={{ color: stepExpress[status + "Color"] }}>
                                            {stepExpress[status + "Text"]}
                                            {status === "pending" ? <DotLoading /> : null}
                                        </span>
                                        <span className="arrow" />
                                    </li>
                                ) : null}
                                <li className="test-card-step-item">
                                    <div className="step-icon">{icon}</div>
                                    <p className="step-name">
                                        <span>{name}</span>
                                    </p>
                                </li>
                            </Fragment>
                        );
                    })}
                </ul>
                {error && error.message ? (
                    <div className="test-card-error" style={{ color: error.color }}>
                        <i fieldid="ublinker-erp-src-components-TestCard-index-1167133-i" className="cl cl-notice-l" />
                        <p>{formatTextArea(error.message)}</p>
                    </div>
                ) : null}
            </div>
        );
    } else {
        return null;
    }
};

export default forwardRef(TestCard);
