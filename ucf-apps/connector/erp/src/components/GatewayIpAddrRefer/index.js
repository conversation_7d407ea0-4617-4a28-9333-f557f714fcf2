import React from "react";
import { getGwIpAddrListService } from "./service";
import { WithButton as WithButtonBase, WithInput as WithInputBase } from "components/Refer";

const columns = [
    {
        title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050665") /* "编号" */,
        dataIndex: "index",
        render: (value, record, index) => {
            return index + 1;
        },
        width: 120,
    },
    {
        title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050381") /* "IP地址" */,
        dataIndex: "ipAddr",
    },
];

let referProps = {
    columns: columns,
    hasPage: false,
    title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050662") /* "请选择IP地址" */,
    service: getGwIpAddrListService,
    pkKey: "ipAddr",
    modalWidth: "520px",
    transformResData: (resData = {}) => {
        const { localAddress = [] } = resData || {};
        return localAddress.map((item) => {
            return { ipAddr: item };
        });
    },
};

export const GatewayIpaddrReferButton = (props) => {
    return <WithButtonBase {...props} {...referProps} />;
};

export const GatewayIpaddrReferInput = (props) => {
    return <WithInputBase {...props} {...referProps} />;
};
