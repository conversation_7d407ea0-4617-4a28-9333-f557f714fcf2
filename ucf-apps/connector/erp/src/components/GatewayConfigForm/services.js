import { getInvokeService } from "utils/service";

/**
 * 获取已设置网关IP
 * @param {Object} data
 * @param {String} data.gatewayId
 * @returns {Promise | Promise<unknown>}
 */
export const getGwIpAddrService = function (data) {
    return getInvokeService(
        {
            method: "GET",
            path: "/mygwapp/ncconnect/getClientRealIp",
            showLoading: false,
        },
        data
    );
};

/**
 * 保存网关IP设置
 * @param {Object} data
 * @param {String} data.gatewayId
 * @param {String} data.realIp
 * @returns {Promise | Promise<unknown>}
 */
export const saveGwIpAddrService = function (data) {
    let { gatewayId, ..._data } = data;
    return getInvokeService(
        {
            method: "PUT",
            path: `/mygwapp/gateway/${gatewayId}/ip/config`,
        },
        _data
    );
};

/**
 * 保存网关配置
 * @param {Object} data
 * @param {String} data.gatewayId
 * @param {String} data.port
 * @param {String} data.whiteList
 * @param {String} data.gatewayAddress
 * @return {Promise<unknown>}
 */
export const saveGwInfoService = function (data) {
    const { gatewayId, ...other } = data;
    return getInvokeService(
        {
            method: "PUT",
            path: `/mygwapp/gateway/${gatewayId}/config`,
        },
        other
    );
};
