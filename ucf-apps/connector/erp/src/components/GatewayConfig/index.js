import React, { useState, useEffect, useCallback, useRef, useMemo } from "react";
import { Button, FormControl, FormList, Radio, InputGroup, Menu, Select, Tooltip } from "components/TinperBee";
import { Form } from "@tinper/next-ui";
import { PRIVATE } from "utils/util";
import { autoServiceMessage } from "utils/service";
import commonText from "constants/commonText";
// import FormList from "components/TinperBee/Form";
import { ipReg, portReg, originReg, urlReg } from "utils/regExp";
import { testErpToGwService } from "services/gwServices";
import TestCard from "../TestCard";
import { getGwIpAddrService, saveGwInfoService } from "./services";
import { GatewayIpaddrReferButton } from "../GatewayIpAddrRefer";
import erpIcon from "../../images/erp_icon.png";
import gwIcon from "../../images/gw_icon.png";
import "./index.less";

const useSetGwConfig = function (gatewayId, useMainTenantGateway) {
    /** 网关IP */
    const [gwIpAddr, setGwIpAddr] = useState("");
    const [clientData, setClientData] = useState(undefined);

    const [shouldSave, setShouldSave] = useState(!gatewayId);

    /** 获取网关Ip */
    const getGwIpAddr = useCallback(async () => {
        let res = await autoServiceMessage({
            service: getGwIpAddrService({ gatewayId }),
            error: () => {},
        });
        if (res) {
            setClientData(res.data);
            let { realIp } = res.data || {};
            let _realIp = realIp || "";
            setGwIpAddr(_realIp);
            if (!_realIp) {
                setShouldSave(true);
            }
        }
    }, [gatewayId]);

    /** 自动获取网关IP */
    useEffect(() => {
        !!gatewayId && getGwIpAddr();
    }, [gatewayId]);

    /** 保存网关配置 */
    const saveGwConfigInputs = [gatewayId];
    const saveGwConfig = useCallback(async (data) => {
        data.gatewayId = gatewayId;
        const serviceFunc = saveGwInfoService;
        let res = await autoServiceMessage({
            service: serviceFunc(data),
            success: window.lang.template(commonText.saveSuccess),
        });
        if (res) {
            setShouldSave(false);
        }
    }, saveGwConfigInputs);

    /** 测试连通性 */
    const testConnectInputs = [gatewayId];
    const testConnect = useCallback(() => {
        return new Promise((resolve, reject) => {
            testErpToGwService(gatewayId, false, useMainTenantGateway)
                .then((res) => {
                    let type = res.status == 1 ? "success" : "warning";
                    resolve({
                        status: type,
                        message: res.msg,
                    });
                })
                .catch((error) => {
                    reject(error);
                });
        });
    }, [gatewayId]);

    return {
        gwIpAddr,
        setGwIpAddr,
        saveGwConfig,
        saveGwConfigInputs,
        testConnect,
        testConnectInputs,
        shouldSave,
        setShouldSave,
        clientData,
    };
};

const FormItem = FormList.Item;

const steps = [
    {
        name: "ERP",
        icon: <img fieldid="ublinker-erp-src-components-GatewayConfig-index-7430999-img" src={erpIcon} />,
    },
    {
        name: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050144") /* "网关" */,
        icon: <img fieldid="ublinker-erp-src-components-GatewayConfig-index-4594593-img" src={gwIcon} />,
    },
];
const GatewayConfigForm = (props) => {
    const formItemLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 12 },
    };
    const [form] = FormList.useForm();
    const {
        gatewayId,
        useMainTenantGateway,
        gatewayConfig,
        layoutOpt,
        className = "",
        setErpConfigDisabled,
        title,
        size,
        labelCol = 120,
        actionLabelCol,
        saveColors = "primary",
        testCardType = "card",
    } = props;
    const initialValues = {
        channelProtocol: "ws",
        mobileLightPort: "33377",
        // 'protocolType': 'https'
    };
    const { getFieldProps, getFieldError, setFieldsValue, validateFields } = form;
    const [testCardShow, setTestCardShow] = useState(false);
    const [channelProtocol, setChannelProtocol] = useState(initialValues.channelProtocol);
    const { gwIpAddr, setGwIpAddr, saveGwConfig, saveGwConfigInputs, testConnect, testConnectInputs, shouldSave, setShouldSave, clientData } = useSetGwConfig(
        gatewayId,
        useMainTenantGateway
    );

    useEffect(() => {
        setErpConfigDisabled && setErpConfigDisabled(shouldSave);
    }, [shouldSave]);

    useEffect(() => {
        const { port } = gatewayConfig;
        setFieldsValue({
            port: port,
        });
    }, [gatewayConfig]);

    useEffect(() => {
        if (clientData) {
            setFieldsValue({
                gatewayAddress: clientData.realIp || "",
                extranetGatewayAddress: clientData.extranetGatewayAddress || "",
                // protocolType: clientData.protocolType || initialValues.protocolType,
                channelProtocol: clientData.channelProtocol || initialValues.channelProtocol,
                mobileLightPort: clientData.mobileLightPort || initialValues.mobileLightPort,
            });
            clientData.channelProtocol && setChannelProtocol(clientData.channelProtocol);
        }
    }, [clientData]);
    const handleChannelProtocolChange = useCallback((value) => {
        setChannelProtocol(value);
    }, []);
    const handleIpAddrReferConfirm = useCallback((selectedIpAddrList) => {
        const [selectedGwIpAddr] = selectedIpAddrList;
        if (selectedGwIpAddr) {
            setGwIpAddr(selectedGwIpAddr.ipAddr);
            setFieldsValue({
                gatewayAddress: selectedGwIpAddr.ipAddr,
            });
        }
    }, []);

    const handleChange = useCallback(() => {
        setShouldSave(true);
    }, []);

    const testCardNode = useRef(null);
    const handleTestShowChange = useCallback((showState) => {
        setTestCardShow(showState);
    }, []);
    const handleTest = useCallback(() => {
        if (testCardShow) {
            testCardNode.current.refresh();
        } else {
            handleTestShowChange(true);
        }
    }, [testCardShow]);

    const testCardTargetNode = useRef(null);

    const stepExpresses = useMemo(() => {
        return [
            {
                successText: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050779") /* "网络连接成功" */,
                failText: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050777") /* "网络连接失败" */,
                warningText: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050783") /* "该功能无法使用" */,
                warningColor: "#FFA600",
                pendingText: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050765") /* "连接中" */,
                promise: testConnect,
            },
        ];
    }, testConnectInputs);

    const handleSave = useCallback(() => {
        // validateFields((error, values) => {
        //     if (!error) {
        //         saveGwConfig(values)
        //     }
        // })

        validateFields().then((values) => {
            if (PRIVATE) {
                values = { ...values, channelProtocol: "ws" };
            }
            saveGwConfig(values);
        });
    }, saveGwConfigInputs);

    const notGateway = !gatewayId;
    return (
        <FormList
            fieldid="ublinker-erp-src-components-GatewayConfig-index-9955786-FormList"
            // layoutOpt={layoutOpt}
            className={className}
            title={title}
            size={size}
            form={form}
            name="form122"
            labelAlign="right"
            {...formItemLayout}
            initialValues={initialValues}
        >
            {!PRIVATE ? (
                <FormItem fieldid="ublinker-erp-src-components-GatewayConfig-index-1856102-FormItem" label=" " name="channelProtocol">
                    <Radio.Group name="channel-protocol" onChange={handleChannelProtocolChange}>
                        <Radio fieldid="ublinker-erp-src-components-GatewayConfig-index-19675-Radio" value={"ws"}>
                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202206271904")}
                            {/* websocket长连接 */}
                        </Radio>
                        <Radio fieldid="ublinker-erp-src-components-GatewayConfig-index-9274330-Radio" value={"http"}>
                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202206271905")}
                            {/* http直连 */}
                        </Radio>
                    </Radio.Group>
                </FormItem>
            ) : null}

            <div className="gatewayAddressBox">
                <FormItem
                    fieldid="ublinker-erp-src-components-GatewayConfig-index-7708646-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050671") /* "网关服务器IP" */}
                    // error={getFieldError('gatewayAddress')}
                    // labelCol={labelCol}
                    // required
                    name="gatewayAddress"
                    rules={[
                        {
                            required: true,
                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202111301358") /* "请输入" */,
                        },
                        {
                            pattern: ipReg,
                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050789") /* "请输入正确的网关服务器IP" */,
                        },
                    ]}
                >
                    <FormControl
                        fieldid="ublinker-erp-src-components-GatewayConfig-index-4623666-FormControl"
                        className="ucg-mar-r-5"
                        // size={size}
                        // style={{ width: 320 }}
                        placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202111301358") /* "请输入" */}
                        // {...getFieldProps('gatewayAddress', {
                        //     initialValue: '',
                        //     rules: [{
                        //         required: true, message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050789") /* "请输入正确的网关服务器IP" */
                        //     }, {
                        //         pattern: ipReg, message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050789") /* "请输入正确的网关服务器IP" */
                        //     }],
                        //     normalize: value => value.trim(),
                        //     onChange: handleChange
                        // })}
                        normalize={(value) => value.trim()}
                        onChange={handleChange}
                    />
                </FormItem>

                <div className="gatewayAddressBox-btn">
                    {useMainTenantGateway !== "true" ? (
                        <GatewayIpaddrReferButton
                            btnProps={{
                                bordered: true,
                                disabled: notGateway,
                                size: size,
                            }}
                            requestParams={{ gatewayId: gatewayId }}
                            onOk={handleIpAddrReferConfirm}
                            selectedList={[{ ipAddr: gwIpAddr }]}
                        >
                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050659") /* "获取IP地址" */}
                        </GatewayIpaddrReferButton>
                    ) : null}
                </div>
            </div>

            <FormItem
                fieldid="ublinker-erp-src-components-GatewayConfig-index-7625746-FormItem"
                label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050342") /* "网关端口号" */}
                // labelCol={labelCol}
                // required
                // error={getFieldError('port')}
                name="port"
                rules={[
                    {
                        required: true,
                        message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202111301358") /* "请输入" */,
                    },
                    {
                        pattern: portReg,
                        message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050527") /* "请输入正确的端口号" */,
                    },
                ]}
            >
                <FormControl
                    fieldid="ublinker-erp-src-components-GatewayConfig-index-5425669-FormControl"
                    // size={size}
                    // style={{ width: 320 }}
                    // placeholder={window.lang.template('MIX_UBL_ALL_UBL_FE_LOC_00050614') /* "系统调用网关使用端口号" */}
                    placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202111301358") /* "系统调用网关使用端口号" */}
                    // {...getFieldProps('port', {
                    //     rules: [{
                    //         required: true, message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050419") /* "请输入端口号" */
                    //     }, {
                    //         pattern: portReg, message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050527") /* "请输入正确的端口号" */
                    //     }],
                    //     onChange: handleChange
                    // })}
                    onChange={handleChange}
                />
            </FormItem>

            {!PRIVATE && channelProtocol !== initialValues.channelProtocol && (
                <>
                    <FormItem
                        fieldid="ublinker-erp-src-components-GatewayConfig-index-4926032-FormItem"
                        label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202206271906")} //'网关入口端口号'
                        name="mobileLightPort"
                        rules={[
                            {
                                required: true,
                                message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202111301358") /* "请输入" */,
                            },
                            {
                                pattern: portReg,
                                message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050527") /* "请输入正确的端口号" */,
                            },
                        ]}
                    >
                        <FormControl
                            fieldid="ublinker-erp-src-components-GatewayConfig-index-3843659-FormControl"
                            placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202111301358") /* "请输入" */}
                            onChange={handleChange}
                        />
                    </FormItem>
                    <FormItem
                        fieldid="ublinker-erp-src-components-GatewayConfig-index-8204762-FormItem"
                        label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202206271907")} // 网关外网地址
                        required
                    >
                        <FormItem
                            fieldid="ublinker-erp-src-components-GatewayConfig-index-3232158-FormItem"
                            className="gateway-address-form-item"
                            style={{ flex: 1 }}
                            name="extranetGatewayAddress"
                            rules={[
                                {
                                    required: true,
                                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202111301358") /* "请输入" */,
                                },
                                {
                                    pattern: originReg,
                                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202206271908") /* "请输入正确的网关外网地址" */,
                                },
                            ]}
                        >
                            <FormControl
                                fieldid="ublinker-erp-src-components-GatewayConfig-index-7084296-FormControl"
                                style={{ width: "100%" }}
                                placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202111301358") /* "请输入" */}
                                onChange={handleChange}
                            />
                        </FormItem>
                    </FormItem>
                </>
            )}

            <FormItem
                fieldid="ublinker-erp-src-components-GatewayConfig-index-3452564-FormItem"
                // labelCol={actionLabelCol || labelCol}
                label=" "
            >
                {useMainTenantGateway !== "true" ? (
                    <Button
                        fieldid="ublinker-erp-src-components-GatewayConfig-index-601122-Button"
                        colors={saveColors}
                        className="ucg-mar-r-10"
                        disabled={notGateway}
                        onClick={handleSave}
                        size={size}
                    >
                        {window.lang.template(commonText.save)}
                    </Button>
                ) : null}
                <Button
                    fieldid="ublinker-erp-src-components-GatewayConfig-index-2555217-Button"
                    colors="secondary"
                    disabled={shouldSave}
                    ref={testCardTargetNode}
                    onClick={handleTest}
                    size={size}
                >
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050788") /* "测试网关连接" */}
                </Button>

                <TestCard
                    ref={testCardNode}
                    cardType={testCardType}
                    targetNode={testCardTargetNode}
                    style={{ width: 400 }}
                    show={testCardShow}
                    steps={steps}
                    stepExpresses={stepExpresses}
                    onClose={handleTestShowChange.bind(null, false)}
                />
            </FormItem>
        </FormList>
    );
};

export default GatewayConfigForm;
