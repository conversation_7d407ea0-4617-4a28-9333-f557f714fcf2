import React from "react";
import { RoutesRender } from "core";

import getConfigProvider from "components/ConfigProvider";
import config from "../config";
const ConfigProvider = getConfigProvider(config);

import AsyncComponent from "components/AsyncComponent";

const HomeComponent = AsyncComponent((cb) => {
    require.ensure(
        [],
        (require) => {
            let com = require("./home/<USER>");
            cb && cb(com.default);
        },
        "connector/erp/home/<USER>"
    );
});

const ConfigComponent = AsyncComponent((cb) => {
    require.ensure(
        [],
        (require) => {
            let com = require("./config/container");
            cb && cb(com.default);
        },
        "connector/erp/config/module"
    );
});

const LogComponent = AsyncComponent((cb) => {
    require.ensure(
        [],
        (require) => {
            let com = require("./log/container");
            cb && cb(com.default);
        },
        "connector/erp/log/module"
    );
});

const routes = [
    {
        path: "/",
        exact: true,
        component: HomeComponent,
    },
    {
        path: "/home",
        component: HomeComponent,
    },
    {
        path: "/config",
        component: ConfigComponent,
    },
    {
        path: "/log",
        component: LogComponent,
    },
];

const Routes = () => {
    return (
        <ConfigProvider fieldid="UCG-FE-connector-erp-src-routes-index-3027267-ConfigProvider">
            <RoutesRender routes={routes} isRoot />
        </ConfigProvider>
    );
};
export default Routes;
