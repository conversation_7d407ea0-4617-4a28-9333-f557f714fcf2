import { observable, makeObservable } from "mobx";
import DefaultS<PERSON> from "utils/defaultStore";
import { defaultListMap } from "utils/pageListUtils";
import * as ownerService from "./service";
const initState = {
    tabs: [
        {
            key: "send",
            name: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050759") /* "发送日志" */,
            dataSource: defaultListMap,
            loaded: false,
            searchKey: "",
        },
        {
            key: "receive",
            name: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050796") /* "接受日志" */,
            dataSource: defaultListMap,
            loaded: false,
            searchKey: "",
        },
    ],
    activeTabKey: "send",
    queryParams: {
        gatewayId: "",
    },
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    setPageParams = (pageInfo) => {
        this.changeState(pageInfo);
    };

    initTab = (tabKey = "send") => {
        let { tabs } = this.state;
        let tab = tabs.find((tab) => tab.key === tabKey);
        tab.dataSource = [];
        tab.loaded = false;
        tab.searchKey = "";
        this.state.tabs = tabs;
    };

    changeActiveTab = (tabKey) => {
        this.state.activeTabKey = tabKey;
        this.getDataSource();
    };

    getDataSource = async (params) => {
        let { activeTabKey, tabs, queryParams } = this.state;
        let tab = tabs.find((tab) => tab.key === activeTabKey);
        if (tab.loaded && !params) {
            return false;
        }
        let res = await this.getPagesListFunc({
            service: ownerService.getLogListService,
            requestData: {
                type: activeTabKey,
                gatewayId: queryParams.gatewayId,
                ...params,
            },
            dataSource: tab.dataSource,
        });
        if (res) {
            const { dataSource, pagination } = res;
            console.log(pagination);
            tab.dataSource = dataSource;
            tab.pagination = pagination;
            tab.loaded = true;
            this.state.tabs = this.toJS(tabs);
        }
    };
}
/**
 * 根据具体模块修改 storeKey 为保证唯一性
 * storeKey 由 模块文件夹名称 + 页面名称 + 'Store' 小驼峰
 *
 * storeKey是作为当前 Store 的唯一键， storeKey与Store的实例会在 rootStore或者StoreMap中以 键值对的 形式存在
 * 1. 用于在模块的 storeMap（模块routes/store.js）中注册并初始化当前Store
 * 2. 用于在container的 inject 中关联当前Store的实例时，从模块的storeMap或者rootStore中获取对应store实例
 * */
export const storeKey = "connectorErpGwLogStore";

export default Store;
