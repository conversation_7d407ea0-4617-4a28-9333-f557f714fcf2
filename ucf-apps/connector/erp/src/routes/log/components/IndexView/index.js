import React, { useEffect, useMemo, useCallback, Fragment } from "react";
import { Tabs } from "components/TinperBee";
import { Content, Header } from "components/PageView";
import Grid from "components/TinperBee/Grid";

import "./index.less";

const iconStyle = {
    verticalAlign: "text-top",
    marginRight: "8px",
};
const CollapsedIcon = <i fieldid="ublinker-routes-log-components-IndexView-index-4701321-i" className="cl cl-add" style={iconStyle} />;

const ExpandedIcon = <i fieldid="ublinker-routes-log-components-IndexView-index-5777402-i" className="cl cl-Minus_sign" style={iconStyle} />;

const IndexView = (props) => {
    const { ownerStore, ownerState } = props;
    const { activeTabKey, tabs } = ownerState;

    useEffect(() => {
        ownerStore.getDataSource();
    }, []);

    const TabNodes = useMemo(() => {
        return tabs.map((tab) => {
            if (tab.hide) {
                return null;
            } else {
                let { type, key, name } = tab;
                return <Tabs.TabPane fieldid="UCG-FE-routes-log-components-IndexView-index-3496881-Tabs.TabPane" tab={name} key={key} />;
            }
        });
    }, []);

    const activeTab = useMemo(() => {
        return tabs.find((tab) => tab.key === activeTabKey);
    }, [tabs, activeTabKey]);

    const handleTabChange = useCallback((tabKey) => {
        ownerStore.changeActiveTab(tabKey);
    }, []);

    const expandRowRender = useCallback((record) => {
        const { postdata, responsedata, nccontext } = record;
        return (
            <div className="connector-erp-log-expand">
                <p>
                    <span>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050742") /* "发送数据" */}</span>
                    <span>{postdata}</span>
                </p>
                <p>
                    <span>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050778") /* "接受数据" */}</span>
                    <span>{responsedata}</span>
                </p>
                <p>
                    <span>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050733") /* "NC配置" */}</span>
                    <span>{nccontext}</span>
                </p>
            </div>
        );
    }, []);

    const columns = useMemo(() => {
        return [
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050773") /* "行号" */,
                dataIndex: "$$index",
                width: 100,
                render: Grid.renderIndex,
            },
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050110") /* "创建时间" */,
                dataIndex: "ccreationtime",
            },
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050110") /* "创建时间" */,
                dataIndex: "finishtime",
            },
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050742") /* "发送数据" */,
                dataIndex: "postdata",
                width: 350,
            },
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050772") /* "接收数据" */,
                dataIndex: "responsedata",
                width: 350,
            },
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050733") /* "NC配置" */,
                dataIndex: "nccontext",
                width: 350,
            },
        ];
    }, []);

    const { pagination, dataSource } = activeTab;

    return (
        <Fragment>
            <Header title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050757") /* "日志" */} back bordered />
            <Content>
                <Tabs
                    fieldid="ublinker-routes-log-components-IndexView-index-3913374-Tabs"
                    className="sys-connector-tab"
                    activeKey={activeTabKey}
                    onChange={handleTabChange}
                >
                    {TabNodes}
                </Tabs>
                <Grid
                    fieldid="ublinker-routes-log-components-IndexView-index-8649981-Grid"
                    className="connector-erp-log-grid"
                    columns={columns}
                    data={dataSource.list}
                    pagination={pagination}
                    rowKey={"mssageid"}
                    expandedRowRender={expandRowRender}
                    // expandIconAsCell={true}
                    collapsedIcon={CollapsedIcon}
                    expandedIcon={ExpandedIcon}
                />
            </Content>
        </Fragment>
    );
};

export default IndexView;
