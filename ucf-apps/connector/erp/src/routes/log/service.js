import { getInvokeService, getServicePath } from "utils/service";

/***
 * 获取日志列表
 * @param {Object} data
 * @param {String} data.gatewayId
 * @param {String} data.type=send|receive
 * @returns {*}
 */
export const getLogListService = function (data) {
    const { gatewayId, type, ...other } = data;
    return getInvokeService(
        {
            method: "GET",
            path: `/mygwapp/erpdata/eventbus/log/${type}/${gatewayId}`,
        },
        other
    );
};
