/**
 * location.search gatewayId
 * */

import React, { Component } from "react";
import IndexView from "./components/IndexView";
import { inject, observer } from "mobx-react";
import Store, { storeKey } from "./store";
import { getPageParams } from "decorator";
import core from "core";
core.addStore({
    storeKey: storeKey,
    store: new Store(),
});

@inject((rootStore) => {
    let ownerStore = rootStore[storeKey];
    return {
        ownerState: ownerStore.toJS(),
        ownerStore: ownerStore,
    };
})
@observer
@getPageParams
class Container extends Component {
    constructor(props) {
        super(props);
        const { ownerStore } = props;
        ownerStore.setPageParams(this.props.getPageParams());
    }

    render() {
        return <IndexView {...this.props} />;
    }
}
export default Container;
