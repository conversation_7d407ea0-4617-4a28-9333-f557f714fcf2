import React, { useEffect, useMemo, useState, useCallback, memo, useContext } from "react";
import { MobXProviderContext } from "mobx-react";
import classnames from "classnames";
import _template from "lodash/template";
import { Icon } from "components/TinperBee";
import { storeKey } from "connector/erp/routes/config/store";

import "./index.less";

function getExpandText(expanded) {
    return expanded
        ? window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050010") /* "收起" */
        : window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050044"); /* "展开" */
}

function useExpand({ defaultExpanded, expanded, parentExpanded }) {
    const [selfExpanded, setSelfExpanded] = useState(defaultExpanded);
    const [childrenExpandedNum, setChildrenExpandedNum] = useState(0);
    const [childrenExpandTotal, setChildrenExpandTotal] = useState(0);

    const handleExpand = useCallback(
        (expand) => {
            if (childrenExpandTotal > 0) {
                if (childrenExpandedNum === 0) {
                    setSelfExpanded(true);
                    setChildrenExpandedNum(childrenExpandTotal);
                } else {
                    setSelfExpanded(false);
                    setChildrenExpandedNum(0);
                }
            } else {
                let _expanded = typeof expand === "boolean" ? expand : !selfExpanded;
                setSelfExpanded(_expanded);
            }
        },
        [selfExpanded, childrenExpandedNum, childrenExpandTotal]
    );

    useEffect(() => {
        if (childrenExpandTotal > 0) {
            setChildrenExpandedNum(childrenExpandTotal);
        }
    }, [defaultExpanded, childrenExpandTotal]);

    useEffect(() => {
        if (typeof expanded !== "undefined") {
            setSelfExpanded(!!expanded);
        }
    }, [expanded]);

    useEffect(() => {
        if (typeof parentExpanded === "boolean") {
            setSelfExpanded(parentExpanded);
        }
    }, [parentExpanded]);

    const childrenExpandChange = useCallback(
        (childrenExpanded) => {
            let _childrenExpandedNum = childrenExpandedNum;
            if (childrenExpanded) {
                _childrenExpandedNum += 1;
            } else {
                _childrenExpandedNum -= 1;
            }
            if (_childrenExpandedNum === 0) {
                setSelfExpanded(false);
            } else if (_childrenExpandedNum === childrenExpandTotal) {
                setSelfExpanded(true);
            }
            setChildrenExpandedNum(_childrenExpandedNum);
        },
        [selfExpanded, childrenExpandedNum, childrenExpandTotal]
    );

    return {
        selfExpanded,
        handleExpand,
        childrenExpandChange,
        childrenExpandedNum,
        childrenExpandTotal,
        setChildrenExpandTotal,
    };
}

export const ConfigInfoItem = (props, ref) => {
    const storeContext = useContext(MobXProviderContext);
    console.log(storeContext);
    console.log(storeKey);
    console.log(configCommonStore);
    const configCommonStore = storeContext[storeKey];
    const { title, subTitle, children, action = null, step, connectorInfo = {}, expandType = "none", expanded, expandChange } = props;

    const _title = useMemo(() => {
        return _template(title)(connectorInfo);
    }, [title]);

    const hasExpand = useMemo(() => expandType !== "none", [expandType]);

    const isMultipleExpand = useMemo(() => expandType === "multiple", [expandType]);

    const isQuestion = useMemo(() => step === "question", [step]);

    const _useExpand = hasExpand
        ? useExpand({
              defaultExpanded: true,
              expanded,
              expandChange,
          })
        : {};

    const { selfExpanded, handleExpand, childrenExpandChange, childrenExpandedNum, setChildrenExpandTotal } = _useExpand;

    useEffect(() => {
        if (isQuestion) {
            configCommonStore.setQuestionShow();
        } else {
            configCommonStore.setNavLinks(_title);
        }
    }, [title, step]);

    let total = 0;

    const _children = useMemo(() => {
        if (isMultipleExpand) {
            total = children ? children.length || 0 : 0;
            setChildrenExpandTotal(total);
            return React.Children.map(children, (child) => {
                if (child && child.type === QuestionItem) {
                    return React.cloneElement(child, {
                        parentExpanded: selfExpanded,
                        defaultExpanded: selfExpanded,
                        childrenExpandedNum: childrenExpandedNum,
                        expandChange: childrenExpandChange,
                    });
                } else {
                    return child;
                }
            });
        } else {
            return children;
        }
    }, [isMultipleExpand, selfExpanded, childrenExpandedNum, children]);

    const expandBtn = useMemo(() => {
        return hasExpand ? (
            <a fieldid="ublinker-config-routes-components-ConfigInfo-index-242288-a" className="info-item-title-expand" onClick={handleExpand}>
                {isMultipleExpand
                    ? window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050739") /* "全部" */ + getExpandText(childrenExpandedNum > 0)
                    : getExpandText(selfExpanded)}
            </a>
        ) : null;
    }, [isMultipleExpand, selfExpanded, childrenExpandedNum]);

    const cls = useMemo(() => {
        return classnames("sys-connector-config-info-item", {
            "config-item": !isQuestion,
        });
    }, [step]);

    const contentCls = useMemo(() => {
        return classnames("info-item-content", {
            expand: !hasExpand || isMultipleExpand || selfExpanded,
        });
    }, [hasExpand, isMultipleExpand, selfExpanded]);

    return (
        <div className={cls}>
            <div className="info-item-title">
                <span className="title-text">{_title}</span>
                <span>{subTitle}</span>
                {action}
                {expandBtn}
            </div>
            <div className={contentCls}>{_children}</div>
        </div>
    );
};

export const ConfigInfoList = (props) => {
    return <div className="sys-connector-config-info-list">{props.children}</div>;
};

export const QuestionItem = memo((props) => {
    const { children, defaultExpanded = true, parentExpanded, expandChange, childrenExpandedNum } = props;
    const { handleExpand, selfExpanded } = useExpand({
        defaultExpanded,
        parentExpanded,
    });

    const _handleExpand = useCallback(() => {
        expandChange(!selfExpanded);
        handleExpand();
    }, [selfExpanded, childrenExpandedNum]);

    const cls = useMemo(() => {
        return classnames("sys-connector-config-ques", {
            expand: selfExpanded,
        });
    }, [selfExpanded]);
    return (
        <div className={cls}>
            {children}
            <Icon
                fieldid="ublinker-config-routes-components-ConfigInfo-index-4715649-Icon"
                className="expand-icon"
                type={selfExpanded ? "uf-arrow-up" : "uf-arrow-down"}
                onClick={_handleExpand}
            />
        </div>
    );
});
