.sys-connector {
    &-config-info-list {
        padding-bottom: 120px;
        background: #ffffff;
    }

    &-config-info-item {
        padding: 20px 30px 30px;
        border-bottom: 1px dashed #E4E4E4;
        &:last-of-type {
            border-bottom: none;
        }
        .info-item-title {
            font-size: 16px;
            line-height: 18px;
            color: #333333;
            margin-bottom: 20px;
            .title-text {
                font-weight: bold;
            }
        }
        .info-item-title-expand {
            font-weight: normal;
            float: right;
            font-size: 12px;
        }
        .info-item-content {
            overflow: hidden;
            height: 0;
            transition: height 0.2s;
            &.expand {
                height: auto;
                min-height: 50px;
            }
        }
        .config-action-content {
            padding-left: 140px;
        }
        .config-tip {
            font-size: 12px;
            color: #999999;
            line-height: 20px;
        }
        .config-text {
            color: #333333;
            font-size: 12px;
            line-height: 22px;
            vertical-align: middle;
            &>* {
                vertical-align: middle;
            }
            &>i.cl {
                font-size: 18px;
            }
        }
        .config-text-second {
            .config-text;
            color: #666;
        }
        .config-text-third {
            .config-text;
            padding-top: 5px;
            color: #999999;
            font-size: 12px;
            line-height: 18px;
        }

        .config-img {
            margin: 10px 0;
            width: 80%;
            height: auto;
            &>img {
                width: 100%;
                height: 100%;
            }
        }
        .config-action-form {
            padding: 0 100px 0 80px;
            &.md-action {
                padding-left: 80px;
            }
            &.sm-action {
                padding-left: 60px;
            }
            &.ucg-ma-form {
                .u-form-item {
                    .u-select, .u-form-control {
                        width: 320px;
                    }
                }
            }
        }

    }
    &-config-ques {
        width: 100%;
        padding: 15px 20px;
        background: #F9F9F9;
        border: 1px solid #E4E4E4;
        margin-bottom: 10px;
        font-size: 12px;
        line-height: 22px;
        color: #333333;
        position: relative;
        height: 52px;
        overflow: hidden;
        &>*:first-of-type {
            padding-bottom: 15px;
        }
        .expand-icon {
            position: absolute;
            right: 20px;
            top: 15px;
            color: #505F79;
            cursor: pointer;
            transform: scale(0.8);
        }

        &:last-of-type {
            margin-bottom: 0;
        }
        .ques-title {

        }
        &.expand {
            height: auto;
        }
        .ques-img {
            padding:0 15px 15px;
            &>img {
                max-width: 70%;
                height: auto;
            }
        }
    }
}

