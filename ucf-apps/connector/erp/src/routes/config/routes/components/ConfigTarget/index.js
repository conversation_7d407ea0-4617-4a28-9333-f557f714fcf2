import React, { useEffect, useContext } from "react";
import { MobXProviderContext } from "mobx-react";
import { storeKey as configCommonStoreKey } from "../../../store";
import "./index.less";
import { getLocalImg, getCompleteImg } from "utils/index";

const ConfigTarget = (props) => {
    let { logo, title, intro } = props;
    const rootStore = useContext(MobXProviderContext);
    const configCommonStore = rootStore[configCommonStoreKey];
    useEffect(() => {
        configCommonStore.setConfigName(title);
    }, []);

    return (
        <div className="sys-connector-config-info-target">
            <div className="target-logo">
                <img fieldid="ublinker-config-routes-components-ConfigTarget-index-4297002-img" src={getCompleteImg(logo)} alt="" />
            </div>
            <div className="info-target-con">
                <p className="target-title">{title}</p>
                <p className="target-intro">{intro}</p>
            </div>
        </div>
    );
};

export default ConfigTarget;
