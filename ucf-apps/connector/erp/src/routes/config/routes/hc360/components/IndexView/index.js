import React, { Fragment, useMemo, useEffect, useCallback } from "react";
import { getLocalImg } from "utils/index";
import FormList from "components/TinperBee/Form";
import ConfigTarget from "../../../components/ConfigTarget";
import { ConfigInfoList, ConfigInfoItem } from "../../../components/ConfigInfo";
import { useLocationParamsMemo } from "../../../common/utilHooks";
import { connectorInfoMap } from "../../../common/constants";
import { useEcConfigHook } from "../../../zkh360/hooks";
import { Button, FormControl } from "components/TinperBee";

const FormItem = FormList.Item;
const labelCol = 120;
const IndexView = (props) => {
    const {
        location,
        form: { setFieldsValue, getFieldProps, validateFields, getFieldError },
    } = props;
    const { queryParams, connectorType } = useLocationParamsMemo();
    const connectorInfo = useMemo(() => connectorInfoMap[connectorType], [connectorType]);
    const useEcConfig = useEcConfigHook(connectorType);

    const { ecConfigInfo, saveEcConfig } = useEcConfig;

    const isEdit = useMemo(() => !!queryParams.id, [queryParams]);

    const hasOpen = useMemo(() => ecConfigInfo && !!ecConfigInfo.customerCode, [ecConfigInfo]);

    useEffect(() => {
        if (ecConfigInfo) {
            let { customerCode = "" } = ecConfigInfo;
            setFieldsValue({ customerCode });
        }
    }, [ecConfigInfo]);

    const handleConfig = useCallback(() => {
        validateFields(["customerCode"], (error, values) => {
            if (!error) {
                saveEcConfig(values);
            }
        });
    });

    return (
        <Fragment>
            <ConfigTarget logo={connectorInfo.logo} title={connectorInfo.name} intro={connectorInfo.intro} />

            <ConfigInfoList>
                {isEdit ? null : (
                    <ConfigInfoItem title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050282") /* "注册慧聪网会员" */}>
                        <p className="config-text">
                            <span>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050037") /* "登录" */}</span>
                            {/* <a fieldid="ublinker-routes-hc360-components-IndexView-index-4501646-a" href="http://www.hc360.com/" target="_blank">http://www.hc360.com </a> */}
                            <span>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050625") /* "点击右连的“免费注册”。" */}</span>
                        </p>
                        <div className="config-img">
                            <img fieldid="ublinker-routes-hc360-components-IndexView-index-3425141-img" src={getLocalImg("connector/hc/reginfo1.png")} alt="" />
                        </div>
                        <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050640") /* "输入管理员信息，进行注册。" */}</p>
                        <div className="config-img">
                            <img fieldid="ublinker-routes-hc360-components-IndexView-index-6700886-img" src={getLocalImg("connector/hc/reginfo2.png")} alt="" />
                        </div>
                        <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050374") /* "输入企业基本信息，进行注册。" */}</p>
                        <div className="config-img">
                            <img fieldid="ublinker-routes-hc360-components-IndexView-index-4163294-img" src={getLocalImg("connector/hc/reginfo3.png")} alt="" />
                        </div>
                        <p className="config-text">
                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050407") /* "注意：目前慧聪提供的询报价接口，使用“会员用户名”来标识采购方。" */}
                        </p>
                        <p className="config-text">
                            {
                                window.lang.template(
                                    "MIX_UBL_ALL_UBL_FE_LOC_00050563"
                                ) /* "对于友云采上的一个企业租户，需要先在慧聪网上注册采购用户，将注册的用户名绑定到友云采。" */
                            }
                        </p>
                        <p className="config-text">
                            {
                                window.lang.template(
                                    "MIX_UBL_ALL_UBL_FE_LOC_00050401"
                                ) /* "在慧聪系统，多个用户注册相同的”公司名称“，会被当作多个企业来处理。也就是说，新建一个用户，就相当于新建了一个采购企业，即使逻辑上多个用户属于同一个企业，但在慧聪会把它们当作多个企业来处理。" */
                            }
                        </p>
                    </ConfigInfoItem>
                )}

                <ConfigInfoItem title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050599") /* "配置友企连慧聪网账户信息" */}>
                    <FormList fieldid="ublinker-routes-hc360-components-IndexView-index-7614995-FormList" className="config-action-form" layoutOpt={{ md: 12 }}>
                        <FormItem
                            fieldid="ublinker-routes-hc360-components-IndexView-index-6977274-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050351") /* "慧聪网会员账号" */}
                            required
                            labelCol={labelCol}
                            error={getFieldError("customerCode")}
                        >
                            <FormControl
                                fieldid="ublinker-routes-hc360-components-IndexView-index-9708307-FormControl"
                                placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050351") /* "慧聪网会员账号" */}
                                {...getFieldProps("customerCode", {
                                    initialValue: "",
                                    rules: [
                                        {
                                            required: true,
                                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050619") /* "请输入慧聪网会员账号" */,
                                        },
                                    ],
                                })}
                            />
                        </FormItem>

                        <FormItem fieldid="ublinker-routes-hc360-components-IndexView-index-3643478-FormItem" labelCol={labelCol}>
                            <Button fieldid="ublinker-routes-hc360-components-IndexView-index-2887450-Button" colors="primary" onClick={handleConfig}>
                                {
                                    hasOpen
                                        ? window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050446") /* "修改配置" */
                                        : window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050579") /* "绑定账号" */
                                }
                            </Button>
                        </FormItem>
                    </FormList>
                </ConfigInfoItem>
            </ConfigInfoList>
        </Fragment>
    );
};

export default FormList.createForm()(IndexView);
