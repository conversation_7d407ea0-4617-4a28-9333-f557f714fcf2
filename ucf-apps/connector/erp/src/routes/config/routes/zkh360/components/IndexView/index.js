import React, { Fragment, useMemo, useEffect, useCallback } from "react";
import FormList from "components/TinperBee/Form";
import ConfigTarget from "../../../components/ConfigTarget";
import { ConfigInfoList, ConfigInfoItem } from "../../../components/ConfigInfo";
import { useLocationParamsMemo } from "../../../common/utilHooks";
import { connectorInfoMap } from "../../../common/constants";
import { useEcConfigHook } from "../../hooks";
import ProtocolModal from "../ProtocolModal";
import { Button, FormControl, Modal } from "components/TinperBee";
import confirmInfo from "../../confirmInfo";
import { phoneReg } from "utils/regExp";

const FormItem = FormList.Item;
const labelCol = 120;
const IndexView = (props) => {
    const {
        form: { setFieldsValue, getFieldProps, validateFields, getFieldError, getFieldValue },
    } = props;
    const { queryParams, connectorType } = useLocationParamsMemo();
    const connectorInfo = useMemo(() => connectorInfoMap[connectorType], [connectorType]);

    const useEcConfig = useEcConfigHook(connectorType);

    const { ecConfigInfo, protocolStatus, setProtocolStatus, openEc } = useEcConfig;

    useEffect(() => {
        if (ecConfigInfo) {
            let { companyName, adminPhone, custUserCode = "" } = ecConfigInfo;
            setFieldsValue({ companyName, adminPhone, custUserCode });
        }
    }, [ecConfigInfo]);

    const hasOpen = useMemo(() => ecConfigInfo && !!ecConfigInfo.custUserCode, [ecConfigInfo]);

    const handleOpen = useCallback(() => {
        validateFields((error, values) => {
            confirmInfo(values, ["companyName", "adminPhone"], async () => {
                if (!error) {
                    let requestData = {
                        UserName: values.adminPhone,
                        UserType: 1,
                        InvoiceTitle: values.companyName,
                        InvoiceType: 2,
                    };
                    let res = await openEc(requestData);
                }
            });
        });
    });

    return (
        <Fragment>
            <ConfigTarget logo={connectorInfo.logo} title={connectorInfo.name} intro={connectorInfo.intro} />

            <ConfigInfoList>
                <ConfigInfoItem title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050629") /* "填写基本信息，开通震坤行会员。" */}>
                    <FormList
                        fieldid="ublinker-routes-zkh360-components-IndexView-index-6794081-FormList"
                        className="config-action-form"
                        layoutOpt={{ md: 12 }}
                    >
                        <FormItem
                            fieldid="ublinker-routes-zkh360-components-IndexView-index-8546207-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050389") /* "企业名称" */}
                            required
                            labelCol={labelCol}
                            error={getFieldError("companyName")}
                        >
                            <FormControl
                                fieldid="ublinker-routes-zkh360-components-IndexView-index-8773368-FormControl"
                                placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050417") /* "企业发票抬头" */}
                                disabled={hasOpen}
                                {...getFieldProps("companyName", {
                                    initialValue: "",
                                    rules: [
                                        {
                                            required: true,
                                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050470") /* "请输入企业名称" */,
                                        },
                                    ],
                                })}
                            />
                        </FormItem>

                        <FormItem
                            fieldid="ublinker-routes-zkh360-components-IndexView-index-8447267-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050258") /* "联系人手机号" */}
                            required
                            labelCol={labelCol}
                            error={getFieldError("adminPhone")}
                        >
                            <FormControl
                                fieldid="ublinker-routes-zkh360-components-IndexView-index-2925159-FormControl"
                                placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050362") /* "请输入您的手机号码，用于开通震坤行账号" */}
                                disabled={hasOpen}
                                {...getFieldProps("adminPhone", {
                                    initialValue: "",
                                    rules: [
                                        {
                                            required: true,
                                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050635") /* "请输入联系人手机号" */,
                                        },
                                        {
                                            pattern: phoneReg,
                                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050441") /* "请输入正确的手机号码" */,
                                        },
                                    ],
                                })}
                            />
                        </FormItem>

                        <FormItem
                            fieldid="ublinker-routes-zkh360-components-IndexView-index-9718229-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050632") /* "开通的账号" */}
                            required
                            labelCol={labelCol}
                            error={getFieldError("custUserCode")}
                        >
                            <FormControl
                                fieldid="ublinker-routes-zkh360-components-IndexView-index-6493151-FormControl"
                                placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050632") /* "开通的账号" */}
                                disabled
                                {...getFieldProps("custUserCode")}
                            />
                        </FormItem>

                        {hasOpen ? null : (
                            <FormItem fieldid="ublinker-routes-zkh360-components-IndexView-index-1692010-FormItem" labelCol={labelCol}>
                                {hasOpen ? null : (
                                    <Button fieldid="ublinker-routes-zkh360-components-IndexView-index-4558249-Button" colors="primary" onClick={handleOpen}>
                                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050366") /* "账号开通" */}
                                    </Button>
                                )}
                            </FormItem>
                        )}

                        <p className="config-text">
                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050256") /* "注意：完成这一步，您就可以去工业品超市进行下单了。" */}
                        </p>
                    </FormList>
                </ConfigInfoItem>
            </ConfigInfoList>

            <ProtocolModal connectorType={connectorType} name={connectorInfo.name} protocolStatus={protocolStatus} setProtocolStatus={setProtocolStatus} />
        </Fragment>
    );
};

export default FormList.createForm()(IndexView);
