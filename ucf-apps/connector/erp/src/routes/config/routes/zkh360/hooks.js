import { useState, useMemo, useCallback, useEffect } from "react";
import { autoServiceMessage } from "utils/service";
import { getEcConfigInfoService, saveEcConfigService, openEcService, sendEmailService } from "./services";
import commonText from "constants/commonText";

/**
 * 慧聪hc360 西域ehcy 米思米misumi 史泰博staples 震坤行zkh360 等电商配置相关hook
 * @param connectorType
 * @return {{setProtocolStatus: *, saveEcConfig: *, emailStatus: *, setEmailStatus: *, sendEcEmail: *, protocolStatus: *, getEcConfig: *, ecConfigInfo: *, openEc: *, setEcConfigInfo: *}}
 */
export function useEcConfigHook(connectorType) {
    const [ecConfigInfo, setEcConfigInfo] = useState(null);
    const [emailStatus, setEmailStatus] = useState(false); //是否发送过邮件
    const [protocolStatus, setProtocolStatus] = useState(true); //是否同意了协议

    const getEcConfig = useCallback(async (connectorType) => {
        let res = await autoServiceMessage({
            service: getEcConfigInfoService(connectorType),
        });
        if (res) {
            let { config, emailstatus, protocalstatus } = res.data;
            setEcConfigInfo(config);
            setEmailStatus(emailstatus);
            setProtocolStatus(protocalstatus);
        }
    }, []);
    useEffect(() => {
        getEcConfig(connectorType);
    }, [connectorType]);

    const saveEcConfig = useCallback(
        async (data) => {
            let requestData = {
                connectorType,
                ...data,
            };
            let res = await autoServiceMessage({
                service: saveEcConfigService(requestData),
                success: window.lang.template(commonText.saveSuccess),
            });
            if (res) {
            }
            return res;
        },
        [connectorType, ecConfigInfo]
    );

    const sendEcEmail = useCallback(
        async (data) => {
            let requestData = {
                connectorType,
                ...data,
            };
            let res = await autoServiceMessage({
                service: sendEmailService(requestData),
                success: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050378") /* "邮件发送成功！" */,
            });
            if (res) {
                setEmailStatus(true);
            }
            return res;
        },
        [connectorType, ecConfigInfo, emailStatus]
    );

    const openEc = useCallback(
        async (data) => {
            let requestData = {
                connectorType,
                ...data,
            };
            let res = await autoServiceMessage({
                service: openEcService(requestData),
                success: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050578") /* "电商开通成功！" */,
                error: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050422") /* "电商开通失败！" */,
            });
            if (res) {
                let { config, emailstatus, protocalstatus } = res.data;
                setEcConfigInfo(config);
            }
            return res;
        },
        [connectorType, ecConfigInfo, emailStatus]
    );

    return {
        ecConfigInfo,
        emailStatus,
        protocolStatus,
        setEcConfigInfo,
        setEmailStatus,
        setProtocolStatus,
        getEcConfig,
        saveEcConfig,
        sendEcEmail,
        openEc,
    };
}
