import { getInvokeService, getServicePath } from "utils/service";

/**
 * 获取电商配置接口
 * @param connectorType
 * @return {Promise<unknown>}
 */
export const getEcConfigInfoService = function (connectorType) {
    return getInvokeService({
        method: "GET",
        path: "/mygwapp/ecconnect/config/get/" + connectorType,
    });
};

/**
 * 保存电商配置接口
 * @param {Object} data
 * @param {String} data.connectorType
 * @return {Promise<unknown>}
 */
export const saveEcConfigService = function (data) {
    let { connectorType, ..._data } = data;
    return getInvokeService(
        {
            method: "POST",
            path: "/mygwapp/ecconnect/config/save/" + connectorType,
        },
        _data
    );
};

/**
 * 发送邮件
 * @param {Object} data
 * @param {String} data.connectorType
 * @return {Promise<unknown>}
 */
export const sendEmailService = function (data) {
    let { connectorType, ..._data } = data;
    return getInvokeService(
        {
            method: "POST",
            path: "/mygwapp/ecconnect/contactEC/" + connectorType,
        },
        _data
    );
};

/**
 * 开通电商服务
 * @param {Object} data
 * @param {String} data.connectorType
 * @return {Promise<unknown>}
 */
export const openEcService = function (data) {
    let { connectorType, ..._data } = data;
    return getInvokeService(
        {
            method: "POST",
            path: "/mygwapp/ecconnect/openservice/" + connectorType,
        },
        _data
    );
};
