import React, { useEffect, useState, useCallback, useMemo } from "react";
import withRouter from "decorator/withRouter";
import Modal from "components/TinperBee/Modal";
import { autoServiceMessage } from "utils/service";
import { agreeProtocolService } from "./services";
import _template from "lodash/template";
import "./index.less";

const useProtocolHook = (connectorType, protocolStatus) => {
    const [modalShow, setModalShow] = useState(false);

    useEffect(() => {
        if (!protocolStatus) {
            setModalShow(true);
        }
    }, [protocolStatus]);

    const agreeProtocol = useCallback(async () => {
        let res = await autoServiceMessage({
            service: agreeProtocolService(connectorType),
        });
        if (res) {
            setModalShow(false);
        }
        return res;
    }, [connectorType]);
    return { modalShow, agreeProtocol };
};

const ProtocolModal = (props) => {
    const { navigate, connectorType, protocolStatus, setProtocolStatus, name } = props;
    const { modalShow, agreeProtocol } = useProtocolHook(connectorType, protocolStatus);
    const handleOk = useCallback(async () => {
        let res = agreeProtocol();
        if (res) {
            setProtocolStatus(true);
        }
    });

    const protocolLink = useMemo(() => {
        return `http://ncc-yc-cpumall-ec.oss-cn-beijing.aliyuncs.com/ec/protocal/${connectorType}.html`;
    }, [connectorType]);

    const protocolName = useMemo(() => {
        return window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050484", { name });
    }, [name]);

    return (
        <Modal
            fieldid="ublinker-routes-zkh360-components-ProtocolModal-index-973990-Modal"
            show={modalShow}
            title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050405") /* "开通协议" */}
            cancelHide
            onCancel={() => navigate(-1)}
            okText={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050628") /* "同意协议" */}
            onOk={handleOk}
            size="md"
        >
            <div className="ec-proto-info-wrap">
                <p>
                    <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050313") /* "【审慎阅读】" */}</strong>
                    {
                        window.lang.template(
                            "MIX_UBL_ALL_UBL_FE_LOC_00050460"
                        ) /* "您在申请开通流程中点击“同意”前，应当认真阅读以下协议。请您务必审慎阅读、充分理解协议中相关条款内容，其中包括：" */
                    }
                </p>
                <ul className="proto-info-list">
                    <li>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050476") /* "1、与您约定免除或限制责任的条款；" */}</li>
                    <li>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050250") /* "2、与您约定法律适用和管辖的条款；" */}</li>
                    <li>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050445") /* "3、其他以粗体下划线标识的重要条款。" */}</li>
                </ul>
                <p>
                    {
                        window.lang.template(
                            "MIX_UBL_ALL_UBL_FE_LOC_00050271"
                        ) /* "阅读协议的过程中，如果您不同意相关协议或其中任何条款约定，您应立即停止注册程序。" */
                    }
                </p>
                <ul>
                    <li>
                        <a fieldid="ublinker-routes-zkh360-components-ProtocolModal-index-8694252-a" href={protocolLink} target="_blank">
                            {protocolName}
                        </a>
                    </li>
                </ul>
            </div>
        </Modal>
    );
};

export default withRouter(ProtocolModal);
