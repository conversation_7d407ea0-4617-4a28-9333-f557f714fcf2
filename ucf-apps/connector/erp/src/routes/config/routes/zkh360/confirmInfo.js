import React from "react";

import { Modal } from "components/TinperBee";
import FormList from "components/TinperBee/Form";
import commonText from "constants/commonText";

const fieldNames = {
    companyName: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050389") /* "企业名称" */,
    adminPhone: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050258") /* "联系人手机号" */,
    ecUserName: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050462") /* "用户名" */,
    adminName: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050473") /* "联系人姓名" */,
    adminEmail: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050466") /* "联系人邮箱" */,
    isMerchant: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050428") /* "是否贸易商" */,
    companyAddress: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050586") /* "联系地址" */,
    companyTelNo: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050517") /* "办公电话" */,
    companyFaxNo: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050648") /* "传真" */,
    companyBankAccount: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050437") /* "银行账号" */,
    adminTelNo: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050435") /* "联系人办公电话" */,
};

const getFieldValue = (data, field) => {
    let value = data[field];
    if (field === "isMerchant") {
        return value == "0" ? window.lang.template(commonText.not) : window.lang.template(commonText.yes);
    } else {
        return value;
    }
};

const labelCol = 150;
const FormItem = FormList.Item;
const getContent = (data, fields) => {
    return (
        <FormList fieldid="ublinker-routes-config-routes-zkh360-confirmInfo-8143230-FormList" layoutOpt={{ md: 12 }}>
            {fields.map((field) => {
                return (
                    <FormItem
                        fieldid="ublinker-routes-config-routes-zkh360-confirmInfo-5765108-FormItem"
                        label={fieldNames[field]}
                        key={field}
                        labelCol={labelCol}
                    >
                        {getFieldValue(data, field)}
                    </FormItem>
                );
            })}
        </FormList>
    );
};

export default function (data, fields, onOk) {
    Modal.confirm({
        title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050310") /* "请确认您的输入内容" */,
        content: getContent(data, fields),
        onOk: onOk,
        width: 500,
    });
}
