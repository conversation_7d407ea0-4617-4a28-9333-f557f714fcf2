import React, { Fragment, useMemo, useEffect, useCallback } from "react";
import { getLocalImg } from "utils/index";
import FormList from "components/TinperBee/Form";
import ConfigTarget from "../../../components/ConfigTarget";
import { ConfigInfoList, ConfigInfoItem } from "../../../components/ConfigInfo";
import { useLocationParamsMemo } from "../../../common/utilHooks";
import { connectorInfoMap } from "../../../common/constants";
import { useEcConfigHook } from "../../../zkh360/hooks";
import ProtocolModal from "../../../zkh360/components/ProtocolModal";
import { Button, FormControl } from "components/TinperBee";
import { emailReg, phoneReg } from "utils/regExp";
import confirmInfo from "../../../zkh360/confirmInfo";

const FormItem = FormList.Item;
const labelCol = 180;
const IndexView = (props) => {
    const {
        location,
        form: { setFieldsValue, getFieldProps, validateFields, getFieldError, getFieldValue },
    } = props;
    const { queryParams, connectorType } = useLocationParamsMemo();
    const connectorInfo = useMemo(() => connectorInfoMap[connectorType], [connectorType]);
    const useEcConfig = useEcConfigHook(connectorType);

    const { ecConfigInfo, protocolStatus, setProtocolStatus, emailStatus, sendEcEmail, saveEcConfig } = useEcConfig;

    const isEdit = useMemo(() => !!queryParams.id, [queryParams]);

    const hasOpen = useMemo(() => ecConfigInfo && !!ecConfigInfo.companyCode, [ecConfigInfo]);

    useEffect(() => {
        if (ecConfigInfo) {
            let { ecUserName, companyName, adminName, adminEmail, adminPhone, companyCode = "" } = ecConfigInfo;
            setFieldsValue({ ecUserName, companyName, adminName, adminEmail, adminPhone, companyCode });
        }
    }, [ecConfigInfo]);

    const handleSendEmail = useCallback(() => {
        let validateFieldList = ["ecUserName", "companyName", "adminName", "adminEmail", "adminPhone"];
        validateFields(validateFieldList, (error, values) => {
            if (!error) {
                confirmInfo(values, validateFieldList, async () => {
                    let res = await sendEcEmail(values);
                });
            }
        });
    });

    const handleConfig = useCallback(() => {
        validateFields(["companyCode"], (error, values) => {
            if (!error) {
                saveEcConfig(values);
            }
        });
    });

    return (
        <Fragment>
            <ConfigTarget logo={connectorInfo.logo} title={connectorInfo.name} intro={connectorInfo.intro} />

            <ConfigInfoList>
                {isEdit ? null : (
                    <ConfigInfoItem title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050597") /* "注册成为米思米会员" */}>
                        <p className="config-text">
                            <span>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050298") /* "点击" */}</span>
                            {/* <a fieldid="ublinker-routes-misumi-components-IndexView-index-1114301-a" href="https://www.misumi-ec.com/cn/useradmin/US006NewAccountPaymentSelectCmd.do?commandCode=NO_AUTH_REGIST_WOS_USER" target="_blank">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050547") /* "米思米官网注册地址" *}</a> */}
                            <span>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050420") /* "进入米思米官网注册界面，注册成为米思米会员。" */}</span>
                        </p>
                        <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050640") /* "输入管理员信息，进行注册。" */}</p>
                        <p>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050510") /* "注意：如果已有米思米账号，则可以跳过注册环节，直接进入第二步。" */}</p>
                        <div className="config-img">
                            <img
                                fieldid="ublinker-routes-misumi-components-IndexView-index-6589402-img"
                                src={getLocalImg("connector/misumi-reginfo.jpg")}
                                alt=""
                            />
                        </div>
                    </ConfigInfoItem>
                )}

                {isEdit ? null : (
                    <ConfigInfoItem title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050334") /* "输入第一步中注册时使用的用户信息" */}>
                        <FormList
                            fieldid="ublinker-routes-misumi-components-IndexView-index-183871-FormList"
                            className="config-action-form"
                            layoutOpt={{ md: 12 }}
                        >
                            <FormItem
                                fieldid="ublinker-routes-misumi-components-IndexView-index-3901761-FormItem"
                                label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050462") /* "用户名" */}
                                required
                                labelCol={labelCol}
                                error={getFieldError("ecUserName")}
                            >
                                <FormControl
                                    fieldid="ublinker-routes-misumi-components-IndexView-index-2880126-FormControl"
                                    placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050469") /* "注册米思米时填写的用户名" */}
                                    {...getFieldProps("ecUserName", {
                                        initialValue: "",
                                        rules: [
                                            {
                                                required: true,
                                                message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050427") /* "请输入用户名" */,
                                            },
                                        ],
                                    })}
                                />
                            </FormItem>

                            <FormItem
                                fieldid="ublinker-routes-misumi-components-IndexView-index-5375197-FormItem"
                                label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050533") /* "公司名称" */}
                                required
                                labelCol={labelCol}
                                error={getFieldError("companyName")}
                            >
                                <FormControl
                                    fieldid="ublinker-routes-misumi-components-IndexView-index-2965941-FormControl"
                                    placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050572") /* "注册米思米时填写的公司名称" */}
                                    {...getFieldProps("companyName", {
                                        initialValue: "",
                                        rules: [
                                            {
                                                required: true,
                                                message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050561") /* "请输入公司名称" */,
                                            },
                                        ],
                                    })}
                                />
                            </FormItem>

                            <FormItem
                                fieldid="ublinker-routes-misumi-components-IndexView-index-9451344-FormItem"
                                label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050473") /* "联系人姓名" */}
                                required
                                labelCol={labelCol}
                                error={getFieldError("adminName")}
                            >
                                <FormControl
                                    fieldid="ublinker-routes-misumi-components-IndexView-index-9442408-FormControl"
                                    placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050400") /* "请输入您的姓名，方便米思米人员联系您" */}
                                    {...getFieldProps("adminName", {
                                        initialValue: "",
                                        rules: [
                                            {
                                                required: true,
                                                message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050633") /* "请输入联系人姓名" */,
                                            },
                                        ],
                                    })}
                                />
                            </FormItem>

                            <FormItem
                                fieldid="ublinker-routes-misumi-components-IndexView-index-4838752-FormItem"
                                label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050466") /* "联系人邮箱" */}
                                required
                                labelCol={labelCol}
                                error={getFieldError("adminEmail")}
                            >
                                <FormControl
                                    fieldid="ublinker-routes-misumi-components-IndexView-index-2776540-FormControl"
                                    placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050322") /* "请输入您的邮箱，方便米思米人员联系您" */}
                                    {...getFieldProps("adminEmail", {
                                        initialValue: "",
                                        rules: [
                                            {
                                                required: true,
                                                message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050622") /* "请输入联系人邮箱" */,
                                            },
                                            {
                                                pattern: emailReg,
                                                message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050618") /* "请输入正确的邮箱" */,
                                            },
                                        ],
                                    })}
                                />
                            </FormItem>

                            <FormItem
                                fieldid="ublinker-routes-misumi-components-IndexView-index-9608636-FormItem"
                                label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050258") /* "联系人手机号" */}
                                required
                                labelCol={labelCol}
                                error={getFieldError("adminPhone")}
                            >
                                <FormControl
                                    fieldid="ublinker-routes-misumi-components-IndexView-index-1120439-FormControl"
                                    placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050262") /* "请输入您的手机号码，方便米思米人员联系您" */}
                                    {...getFieldProps("adminPhone", {
                                        initialValue: "",
                                        rules: [
                                            {
                                                required: true,
                                                message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050635") /* "请输入联系人手机号" */,
                                            },
                                            {
                                                pattern: phoneReg,
                                                message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050441") /* "请输入正确的手机号码" */,
                                            },
                                        ],
                                    })}
                                />
                            </FormItem>

                            <FormItem fieldid="ublinker-routes-misumi-components-IndexView-index-5805899-FormItem" labelCol={labelCol}>
                                <Button fieldid="ublinker-routes-misumi-components-IndexView-index-9462867-Button" colors="primary" onClick={handleSendEmail}>
                                    {
                                        emailStatus
                                            ? window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050340") /* "重新发送" */
                                            : window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050386") /* "发邮件给米思米" */
                                    }
                                </Button>
                            </FormItem>

                            <p className="config-text">
                                {
                                    window.lang.template(
                                        "MIX_UBL_ALL_UBL_FE_LOC_00050249"
                                    ) /* "注意：米思米运营人员会给您发邮件，告知您下一步操作所需使用的内容。直到您收到邮件后，方可进行下一步的操作。" */
                                }
                            </p>
                        </FormList>
                    </ConfigInfoItem>
                )}

                <ConfigInfoItem title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050643") /* "配置米思米返回的企业账号" */}>
                    <FormList
                        fieldid="ublinker-routes-misumi-components-IndexView-index-5557564-FormList"
                        className="config-action-form"
                        layoutOpt={{ md: 12 }}
                    >
                        <FormItem
                            fieldid="ublinker-routes-misumi-components-IndexView-index-6606893-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050465") /* "米思米企业账号" */}
                            required
                            labelCol={labelCol}
                            error={getFieldError("companyCode")}
                        >
                            <FormControl
                                fieldid="ublinker-routes-misumi-components-IndexView-index-4541267-FormControl"
                                placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050532") /* "米思米邮件中提供的companyCode" */}
                                {...getFieldProps("companyCode", {
                                    initialValue: "",
                                    rules: [
                                        {
                                            required: true,
                                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050292") /* "请输入米思米企业账号" */,
                                        },
                                    ],
                                })}
                            />
                        </FormItem>

                        <FormItem fieldid="ublinker-routes-misumi-components-IndexView-index-7268551-FormItem" labelCol={labelCol}>
                            <Button fieldid="ublinker-routes-misumi-components-IndexView-index-6190441-Button" colors="primary" onClick={handleConfig}>
                                {
                                    hasOpen
                                        ? window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050446") /* "修改配置" */
                                        : window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050579") /* "绑定账号" */
                                }
                            </Button>
                        </FormItem>
                    </FormList>
                </ConfigInfoItem>
            </ConfigInfoList>

            <ProtocolModal connectorType={connectorType} name={connectorInfo.name} protocolStatus={protocolStatus} setProtocolStatus={setProtocolStatus} />
        </Fragment>
    );
};

export default FormList.createForm()(IndexView);
