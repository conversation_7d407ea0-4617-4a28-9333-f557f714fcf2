import React, { Fragment, useMemo } from "react";
import { getLocalImg } from "utils/index";
import ConfigTarget from "../../../components/ConfigTarget";
import { ConfigInfoList, ConfigInfoItem } from "../../../components/ConfigInfo";
import { useLocationParamsMemo } from "../../../common/utilHooks";
import { connectorInfoMap } from "../../../common/constants";

const IndexView = (props) => {
    let { location } = props;
    const { queryParams, connectorType } = useLocationParamsMemo();
    const connectorInfo = useMemo(() => connectorInfoMap[connectorType], [connectorType]);

    return (
        <Fragment>
            <ConfigTarget logo={connectorInfo.logo} title={connectorInfo.name} intro={connectorInfo.intro} />

            <ConfigInfoList>
                <ConfigInfoItem title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050396") /* "注册钉钉企业号" */} step="1">
                    {/* <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050037") /* "登录" *}<a fieldid="ublinker-routes-dingding-components-IndexView-index-5807585-a" href="https://www.dingtalk.com/" target="_blank">https://www.dingtalk.com/</a><span>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050656") /* "点击右上角的注册。" *}</span></p> */}
                    <div className="config-img">
                        <img
                            fieldid="ublinker-routes-dingding-components-IndexView-index-6219142-img"
                            src={getLocalImg("connector/dingtalk/dingtalk_register.png")}
                            alt=""
                        />
                    </div>

                    <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050626") /* "输入基本信息，进行注册。" */}</p>
                    <div className="config-img">
                        <img
                            fieldid="ublinker-routes-dingding-components-IndexView-index-8013149-img"
                            src={getLocalImg("connector/dingtalk/dingtalk_regis_info.png")}
                            alt=""
                        />
                    </div>
                </ConfigInfoItem>

                <ConfigInfoItem title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050608") /* "获取微应用ID" */} step="2">
                    <p className="config-text">
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050512") /* "登入企业号之后，点击企业应用，进入企业应用后再点击新建应用" */}
                    </p>
                    <div className="config-img">
                        <img
                            fieldid="ublinker-routes-dingding-components-IndexView-index-458722-img"
                            src={getLocalImg("connector/dingtalk/dingtalk_app.png")}
                            alt=""
                        />
                    </div>

                    <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050500") /* "输入相应信息点击提交" */}</p>
                    <div className="config-img">
                        <img
                            fieldid="ublinker-routes-dingding-components-IndexView-index-9802698-img"
                            src={getLocalImg("connector/dingtalk/dingtalk_add.png")}
                            alt=""
                        />
                    </div>

                    <p className="config-text">
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050493") /* "首页地址请填写填写 http:" */}
                        //yc.yonyou.com/gwmanage/htmls/taskindex.html?tenantId=&gatewayId={" "}
                    </p>
                    <p className="config-text">
                        {
                            window.lang.template(
                                "MIX_UBL_ALL_UBL_FE_LOC_00050657"
                            ) /* "当点击微应用时会跳转到该地址 (待审批列表) 参数： tenantId 辨识访问者的身份 必输 gatewayId 指定某一网关 如果使用默认网关可忽略该参数" */
                        }
                    </p>
                    <p className="config-text">
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050613") /* "在已启用应用中找到刚刚新建的应用，并将鼠标放置在logo上，点击设置" */}
                    </p>
                    <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050444") /* "在弹框中，找到AgentID，并复制下来" */}</p>
                    <div className="config-img">
                        <img
                            fieldid="ublinker-routes-dingding-components-IndexView-index-1408685-img"
                            src={getLocalImg("connector/dingtalk/dingtalk_agent.png")}
                            alt=""
                        />
                    </div>
                </ConfigInfoItem>

                <ConfigInfoItem title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050277") /* "获取CorpID与CorpSecret" */} step="3">
                    <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050588") /* "在企业应用左侧选择微应用设置" */}</p>
                    <div className="config-img">
                        <img
                            fieldid="ublinker-routes-dingding-components-IndexView-index-4069051-img"
                            src={getLocalImg("connector/dingtalk/dingtalk_corp.png")}
                            alt=""
                        />
                    </div>

                    <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050523") /* "复制CorpID和CorpSecret" */}</p>
                </ConfigInfoItem>

                <ConfigInfoItem title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050315") /* "登录友企连官网，配置钉钉微应用" */} step="4">
                    <p className="config-text">
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050303") /* "选择左侧组织消息协同的子菜单，钉钉微应用配置" */}
                    </p>
                    <div className="config-img">
                        <img
                            fieldid="ublinker-routes-dingding-components-IndexView-index-1967374-img"
                            src={getLocalImg("connector/dingtalk/dingtalk_portal.png")}
                            alt=""
                        />
                    </div>

                    <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050506") /* "选择需要的服务进行点击开通" */}</p>
                    <div className="config-img">
                        <img
                            fieldid="ublinker-routes-dingding-components-IndexView-index-943026-img"
                            src={getLocalImg("connector/dingtalk/dingtalk_select.png")}
                            alt=""
                        />
                    </div>

                    <p className="config-text">
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050409") /* "输入从钉钉官网获取的AgentID、CorpID与CorpSecret" */}
                    </p>
                    <div className="config-img">
                        <img
                            fieldid="ublinker-routes-dingding-components-IndexView-index-3303622-img"
                            src={getLocalImg("connector/dingtalk/dingtalk_save.png")}
                            alt=""
                        />
                    </div>

                    <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050616") /* "填写好之后点击保存" */}</p>
                </ConfigInfoItem>
            </ConfigInfoList>
        </Fragment>
    );
};

export default IndexView;
