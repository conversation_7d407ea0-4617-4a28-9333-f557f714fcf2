import { getInvokeService, getServicePath } from "utils/service";

/**
 * 开通嘟嘟
 * @param {Object} data
 * @param {String} data.tenantName
 * @param {String} data.userName
 * @param {String} data.userMobile
 * @param {String} data.userEmail
 * @return {Promise<unknown>}
 */
export const openDuduService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/mygwapp/connect/dudu/open",
        },
        data
    );
};

/**
 * 重置密码
 * @return {Promise<unknown>}
 */
export const resetKeyService = function () {
    return getInvokeService({
        method: "POST",
        path: "/mygwapp/connect/dudu/orgacct/resettempkey",
    });
};
