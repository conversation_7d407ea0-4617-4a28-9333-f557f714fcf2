import React, { Fragment, useMemo, useState, useCallback, useEffect } from "react";
import ConfigTarget from "../../../components/ConfigTarget";
import { ConfigInfoList, ConfigInfoItem } from "../../../components/ConfigInfo";
import { useLocationParamsMemo } from "../../../common/utilHooks";
import { connectorInfoMap } from "../../../common/constants";
import { autoServiceMessage } from "utils/service";
import { getUserInfoService, getTenantInfoService } from "services/common";
import { Button, FormControl, Select } from "components/TinperBee";
import FormList from "components/TinperBee/Form";
import { emailReg, phoneReg } from "utils/regExp";
import { openDuduService, resetKeyService } from "../services";
import commonText from "constants/commonText";
import { useNavigate } from "react-router";

const useOpenDuduHook = () => {
    let [configInfo, setConfigInfo] = useState({
        tenantName: "",
        userName: "",
        userMobile: "",
        userEmail: "",
    });

    const getDefaultConfigInfo = useCallback(async () => {
        let userRes = await autoServiceMessage({
            service: getUserInfoService(),
        });
        let tenantRes = await autoServiceMessage({
            service: getTenantInfoService(),
        });
        let _configInfo = {};
        if (userRes) {
            let userData = userRes.data;
            _configInfo.userName = userData.userName;
            _configInfo.userMobile = userData.userMobile;
            _configInfo.userEmail = userData.userEmail;
        }
        if (tenantRes) {
            let tenantData = tenantRes.data;
            _configInfo.tenantName = tenantData.tenantName;
        }
        setConfigInfo(_configInfo);
    }, []);

    const openDudu = useCallback(async (data) => {
        let res = autoServiceMessage({
            service: openDuduService(data),
            success: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050299") /* "开通成功" */,
        });
        return res;
    }, []);

    const resetKey = useCallback(async (data) => {
        let res = autoServiceMessage({
            service: resetKeyService(data),
            success: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050415") /* "重置成功" */,
        });
        return res;
    }, []);
    useEffect(() => {
        getDefaultConfigInfo();
    }, []);

    return {
        configInfo,
        openDudu,
        resetKey,
    };
};

const FormItem = FormList.Item;

const labelCol = 180;
const IndexView = (props) => {
    const navigate = useNavigate();
    let {
        form: { setFieldsValue, getFieldProps, validateFields, getFieldError, getFieldValue },
    } = props;
    const { queryParams, connectorType } = useLocationParamsMemo();
    const connectorInfo = useMemo(() => connectorInfoMap[connectorType], [connectorType]);

    const { configInfo, openDudu, resetKey } = useOpenDuduHook();

    const handleOpen = useCallback(() => {
        validateFields((errors, values) => {
            if (!errors) {
                openDudu(values).then((res) => {
                    if (res) {
                        navigate(-1);
                    }
                });
            }
        });
    }, []);

    const handleReset = useCallback(() => {
        resetKey().then((res) => {
            if (res) {
                navigate(-1);
            }
        });
    }, []);

    useEffect(() => {
        setFieldsValue(configInfo);
    }, [configInfo]);

    let isAdd = useMemo(() => !queryParams.id, queryParams);

    return (
        <Fragment>
            <ConfigTarget logo={connectorInfo.logo} title={connectorInfo.name} intro={connectorInfo.intro} />

            <ConfigInfoList>
                <ConfigInfoItem title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050524") /* "开通嘟嘟服务" */}>
                    <FormList fieldid="ublinker-routes-dudu-components-IndexView-index-3686075-FormList" className="config-action-form" layoutOpt={{ md: 12 }}>
                        {isAdd ? (
                            <Fragment>
                                <FormItem
                                    fieldid="ublinker-routes-dudu-components-IndexView-index-352642-FormItem"
                                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050546") /* "客户" */}
                                    required
                                    labelCol={labelCol}
                                    error={getFieldError("tenantName")}
                                >
                                    <FormControl
                                        fieldid="ublinker-routes-dudu-components-IndexView-index-4744425-FormControl"
                                        placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050389") /* "企业名称" */}
                                        {...getFieldProps("tenantName", {
                                            initialValue: "",
                                            rules: [
                                                {
                                                    required: true,
                                                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050470") /* "请输入企业名称" */,
                                                },
                                            ],
                                        })}
                                    />
                                </FormItem>

                                <FormItem
                                    fieldid="ublinker-routes-dudu-components-IndexView-index-2842506-FormItem"
                                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050319") /* "客户联系人" */}
                                    required
                                    labelCol={labelCol}
                                    error={getFieldError("userName")}
                                >
                                    <FormControl
                                        fieldid="ublinker-routes-dudu-components-IndexView-index-7596531-FormControl"
                                        placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050511") /* "客户组织设定的联系负责人" */}
                                        {...getFieldProps("userName", {
                                            initialValue: "",
                                            rules: [
                                                {
                                                    required: true,
                                                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050395") /* "请输入客户联系人名称" */,
                                                },
                                            ],
                                        })}
                                    />
                                </FormItem>

                                <FormItem
                                    fieldid="ublinker-routes-dudu-components-IndexView-index-7844635-FormItem"
                                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050480") /* "手机" */}
                                    required
                                    labelCol={labelCol}
                                    error={getFieldError("userMobile")}
                                >
                                    <FormControl
                                        fieldid="ublinker-routes-dudu-components-IndexView-index-3436934-FormControl"
                                        placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050349") /* "客户联系人手机号" */}
                                        {...getFieldProps("userMobile", {
                                            initialValue: "",
                                            rules: [
                                                {
                                                    required: true,
                                                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050594") /* "请输入客户联系人手机号" */,
                                                },
                                                {
                                                    pattern: phoneReg,
                                                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050333") /* "客户联系人手机号填写错误" */,
                                                },
                                            ],
                                        })}
                                    />
                                </FormItem>

                                <FormItem
                                    fieldid="ublinker-routes-dudu-components-IndexView-index-2164747-FormItem"
                                    label={window.lang.template(commonText.email)}
                                    required
                                    labelCol={labelCol}
                                    error={getFieldError("userEmail")}
                                >
                                    <FormControl
                                        fieldid="ublinker-routes-dudu-components-IndexView-index-5190007-FormControl"
                                        placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050526") /* "用于接收注册后的嘟嘟运营平台账号和密码" */}
                                        {...getFieldProps("userEmail", {
                                            initialValue: "",
                                            rules: [
                                                {
                                                    required: true,
                                                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050457") /* "请输入邮箱" */,
                                                },
                                                {
                                                    pattern: emailReg,
                                                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050559") /* "邮箱填写错误" */,
                                                },
                                            ],
                                        })}
                                    />
                                </FormItem>
                            </Fragment>
                        ) : null}

                        <FormItem fieldid="ublinker-routes-dudu-components-IndexView-index-8457813-FormItem" labelCol={labelCol}>
                            {isAdd ? (
                                <Button fieldid="ublinker-routes-dudu-components-IndexView-index-8877887-Button" colors="primary" onClick={handleOpen}>
                                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050524") /* "开通嘟嘟服务" */}
                                </Button>
                            ) : (
                                <Button fieldid="ublinker-routes-dudu-components-IndexView-index-3725485-Button" colors="primary" onClick={handleReset}>
                                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050610") /* "重置临时密钥" */}
                                </Button>
                            )}
                        </FormItem>
                    </FormList>
                </ConfigInfoItem>
            </ConfigInfoList>
        </Fragment>
    );
};

export default FormList.createForm()(IndexView);
