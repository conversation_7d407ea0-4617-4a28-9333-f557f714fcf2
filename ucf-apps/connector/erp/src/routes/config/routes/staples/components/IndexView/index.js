import React, { Fragment, useMemo, useEffect, useCallback } from "react";
import FormList from "components/TinperBee/Form";
import ConfigTarget from "../../../components/ConfigTarget";
import { ConfigInfoList, ConfigInfoItem } from "../../../components/ConfigInfo";
import { useLocationParamsMemo } from "../../../common/utilHooks";
import { connectorInfoMap } from "../../../common/constants";
import { useEcConfigHook } from "../../../zkh360/hooks";
import { Button, FormControl } from "components/TinperBee";
import { emailReg, phoneReg } from "utils/regExp";
import confirmInfo from "../../../zkh360/confirmInfo";

const FormItem = FormList.Item;
const labelCol = 180;
const IndexView = (props) => {
    const {
        location,
        form: { setFieldsValue, getFieldProps, validateFields, getFieldError, getFieldValue },
    } = props;
    const { queryParams, connectorType } = useLocationParamsMemo();
    const connectorInfo = useMemo(() => connectorInfoMap[connectorType], [connectorType]);
    const useEcConfig = useEcConfigHook(connectorType);

    const { ecConfigInfo, protocolStatus, setProtocolStatus, emailStatus, sendEcEmail, saveEcConfig } = useEcConfig;

    const isEdit = useMemo(() => !!queryParams.id, [queryParams]);

    const hasOpen = useMemo(() => ecConfigInfo && !!ecConfigInfo.groupCode, [ecConfigInfo]);

    useEffect(() => {
        if (ecConfigInfo) {
            let { companyName, companyAddress, companyTelNo, companyFaxNo, companyBankAccount, adminName, adminEmail, adminPhone, adminTelNo } = ecConfigInfo;
            let { groupName, groupCode, customerName, customerCode } = ecConfigInfo;
            if (isEdit) {
                setFieldsValue({ groupName, groupCode, customerName, customerCode });
            } else {
                setFieldsValue({
                    companyName,
                    companyAddress,
                    companyTelNo,
                    companyFaxNo,
                    companyBankAccount,
                    adminName,
                    adminEmail,
                    adminPhone,
                    adminTelNo,
                    groupName,
                    groupCode,
                    customerName,
                    customerCode,
                });
            }
        }
    }, [ecConfigInfo, isEdit]);

    const handleSendEmail = useCallback(() => {
        let validateFieldList = [
            "companyName",
            "companyAddress",
            "companyTelNo",
            "companyFaxNo",
            "companyBankAccount",
            "adminName",
            "adminEmail",
            "adminPhone",
            "adminTelNo",
        ];
        validateFields(validateFieldList, (error, values) => {
            if (!error) {
                confirmInfo(values, validateFieldList, async () => {
                    let res = await sendEcEmail(values);
                });
            }
        });
    });

    const handleConfig = useCallback(() => {
        validateFields(["groupName", "groupCode", "customerName", "customerCode"], (error, values) => {
            if (!error) {
                saveEcConfig(values);
            }
        });
    });

    return (
        <Fragment>
            <ConfigTarget logo={connectorInfo.logo} title={connectorInfo.name} intro={connectorInfo.intro} />

            <ConfigInfoList>
                {isEdit ? null : (
                    <ConfigInfoItem title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050459") /* "填写基本信息，提交开通申请" */}>
                        <FormList
                            fieldid="ublinker-routes-staples-components-IndexView-index-8966540-FormList"
                            className="config-action-form config-action-form-80"
                            layoutOpt={{ md: 12 }}
                        >
                            <FormItem
                                fieldid="ublinker-routes-staples-components-IndexView-index-7267114-FormItem"
                                label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050546") /* "客户" */}
                                required
                                labelCol={labelCol}
                                error={getFieldError("companyName")}
                            >
                                <FormControl
                                    fieldid="ublinker-routes-staples-components-IndexView-index-1689607-FormControl"
                                    placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050389") /* "企业名称" */}
                                    {...getFieldProps("companyName", {
                                        initialValue: "",
                                        rules: [
                                            {
                                                required: true,
                                                message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050470") /* "请输入企业名称" */,
                                            },
                                        ],
                                    })}
                                />
                            </FormItem>

                            <FormItem
                                fieldid="ublinker-routes-staples-components-IndexView-index-651569-FormItem"
                                label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050586") /* "联系地址" */}
                                required
                                labelCol={labelCol}
                                error={getFieldError("companyAddress")}
                            >
                                <FormControl
                                    fieldid="ublinker-routes-staples-components-IndexView-index-8994346-FormControl"
                                    placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050607") /* "企业联系地址" */}
                                    {...getFieldProps("companyAddress", {
                                        initialValue: "",
                                        rules: [
                                            {
                                                required: true,
                                                message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050477") /* "请输入联系地址" */,
                                            },
                                        ],
                                    })}
                                />
                            </FormItem>

                            <FormItem
                                fieldid="ublinker-routes-staples-components-IndexView-index-2853960-FormItem"
                                label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050517") /* "办公电话" */}
                                required
                                labelCol={labelCol}
                                error={getFieldError("companyTelNo")}
                            >
                                <FormControl
                                    fieldid="ublinker-routes-staples-components-IndexView-index-7256507-FormControl"
                                    placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050354") /* "企业办公电话" */}
                                    {...getFieldProps("companyTelNo", {
                                        initialValue: "",
                                        rules: [
                                            {
                                                required: true,
                                                message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050636") /* "请输入办公电话" */,
                                            },
                                        ],
                                    })}
                                />
                            </FormItem>

                            <FormItem
                                fieldid="ublinker-routes-staples-components-IndexView-index-9245288-FormItem"
                                label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050648") /* "传真" */}
                                required
                                labelCol={labelCol}
                                error={getFieldError("companyFaxNo")}
                            >
                                <FormControl
                                    fieldid="ublinker-routes-staples-components-IndexView-index-1449483-FormControl"
                                    placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050439") /* "企业传真" */}
                                    {...getFieldProps("companyFaxNo", {
                                        initialValue: "",
                                        rules: [
                                            {
                                                required: true,
                                                message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050520") /* "请输入企业传真" */,
                                            },
                                        ],
                                    })}
                                />
                            </FormItem>

                            <FormItem
                                fieldid="ublinker-routes-staples-components-IndexView-index-8952666-FormItem"
                                label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050437") /* "银行账号" */}
                                required
                                labelCol={labelCol}
                                error={getFieldError("companyBankAccount")}
                            >
                                <FormControl
                                    fieldid="ublinker-routes-staples-components-IndexView-index-2354554-FormControl"
                                    placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050257") /* "企业银行账号" */}
                                    {...getFieldProps("companyBankAccount", {
                                        initialValue: "",
                                        rules: [
                                            {
                                                required: true,
                                                message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050558") /* "请输入银行账号" */,
                                            },
                                        ],
                                    })}
                                />
                            </FormItem>

                            <FormItem
                                fieldid="ublinker-routes-staples-components-IndexView-index-2171354-FormItem"
                                label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050473") /* "联系人姓名" */}
                                required
                                labelCol={labelCol}
                                error={getFieldError("adminName")}
                            >
                                <FormControl
                                    fieldid="ublinker-routes-staples-components-IndexView-index-8535650-FormControl"
                                    placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050545") /* "请输入您的姓名，方便史泰博人员联系您" */}
                                    {...getFieldProps("adminName", {
                                        initialValue: "",
                                        rules: [
                                            {
                                                required: true,
                                                message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050633") /* "请输入联系人姓名" */,
                                            },
                                        ],
                                    })}
                                />
                            </FormItem>

                            <FormItem
                                fieldid="ublinker-routes-staples-components-IndexView-index-6701291-FormItem"
                                label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050466") /* "联系人邮箱" */}
                                required
                                labelCol={labelCol}
                                error={getFieldError("adminEmail")}
                            >
                                <FormControl
                                    fieldid="ublinker-routes-staples-components-IndexView-index-4942056-FormControl"
                                    placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050297") /* "请输入您的邮箱，方便史泰博人员联系您" */}
                                    {...getFieldProps("adminEmail", {
                                        initialValue: "",
                                        rules: [
                                            {
                                                required: true,
                                                message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050622") /* "请输入联系人邮箱" */,
                                            },
                                            {
                                                pattern: emailReg,
                                                message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050618") /* "请输入正确的邮箱" */,
                                            },
                                        ],
                                    })}
                                />
                            </FormItem>

                            <FormItem
                                fieldid="ublinker-routes-staples-components-IndexView-index-7101116-FormItem"
                                label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050258") /* "联系人手机号" */}
                                required
                                labelCol={labelCol}
                                error={getFieldError("adminPhone")}
                            >
                                <FormControl
                                    fieldid="ublinker-routes-staples-components-IndexView-index-7333320-FormControl"
                                    placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050272") /* "请输入您的手机号码，方便史泰博人员联系您" */}
                                    {...getFieldProps("adminPhone", {
                                        initialValue: "",
                                        rules: [
                                            {
                                                required: true,
                                                message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050635") /* "请输入联系人手机号" */,
                                            },
                                            {
                                                pattern: phoneReg,
                                                message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050441") /* "请输入正确的手机号码" */,
                                            },
                                        ],
                                    })}
                                />
                            </FormItem>

                            <FormItem
                                fieldid="ublinker-routes-staples-components-IndexView-index-8048645-FormItem"
                                label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050435") /* "联系人办公电话" */}
                                required
                                labelCol={labelCol}
                                error={getFieldError("adminTelNo")}
                            >
                                <FormControl
                                    fieldid="ublinker-routes-staples-components-IndexView-index-7201207-FormControl"
                                    placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050269") /* "请输入您的办公电话，方便史泰博人员联系您" */}
                                    {...getFieldProps("adminTelNo", {
                                        initialValue: "",
                                        rules: [
                                            {
                                                required: true,
                                                message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050635") /* "请输入联系人手机号" */,
                                            },
                                        ],
                                    })}
                                />
                            </FormItem>

                            <FormItem fieldid="ublinker-routes-staples-components-IndexView-index-7189039-FormItem" labelCol={labelCol}>
                                <Button fieldid="ublinker-routes-staples-components-IndexView-index-365775-Button" colors="primary" onClick={handleSendEmail}>
                                    {
                                        emailStatus
                                            ? window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050340") /* "重新发送" */
                                            : window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050386") /* "发邮件给米思米" */
                                    }
                                </Button>
                            </FormItem>

                            <p className="config-text">
                                {
                                    window.lang.template(
                                        "MIX_UBL_ALL_UBL_FE_LOC_00050471"
                                    ) /* "注意：史泰博客服通过电话4000xxxx与您联系，请注意接听，邮箱**************" */
                                }
                            </p>
                        </FormList>
                    </ConfigInfoItem>
                )}

                <ConfigInfoItem title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050479") /* "配置史泰博账户信息" */}>
                    <FormList
                        fieldid="ublinker-routes-staples-components-IndexView-index-8717726-FormList"
                        className="config-action-form config-action-form-80"
                        layoutOpt={{ md: 12 }}
                    >
                        <FormItem
                            fieldid="ublinker-routes-staples-components-IndexView-index-3922617-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050305") /* "集团名称" */}
                            required
                            labelCol={labelCol}
                            error={getFieldError("groupName")}
                        >
                            <FormControl
                                fieldid="ublinker-routes-staples-components-IndexView-index-4785017-FormControl"
                                placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050555") /* "史泰博提供的groupname" */}
                                {...getFieldProps("groupName", {
                                    initialValue: "",
                                    rules: [
                                        {
                                            required: true,
                                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050483") /* "请输入集团名称" */,
                                        },
                                    ],
                                })}
                            />
                        </FormItem>

                        <FormItem
                            fieldid="ublinker-routes-staples-components-IndexView-index-2768910-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050283") /* "集团编码" */}
                            required
                            labelCol={labelCol}
                            error={getFieldError("groupCode")}
                        >
                            <FormControl
                                fieldid="ublinker-routes-staples-components-IndexView-index-9953874-FormControl"
                                placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050598") /* "史泰博提供的groupcode" */}
                                {...getFieldProps("groupCode", {
                                    initialValue: "",
                                    rules: [
                                        {
                                            required: true,
                                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050508") /* "请输入集团编码" */,
                                        },
                                    ],
                                })}
                            />
                        </FormItem>

                        <FormItem
                            fieldid="ublinker-routes-staples-components-IndexView-index-302506-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050426") /* "会员名称" */}
                            required
                            labelCol={labelCol}
                            error={getFieldError("customerName")}
                        >
                            <FormControl
                                fieldid="ublinker-routes-staples-components-IndexView-index-6024797-FormControl"
                                placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050321") /* "史泰博提供的customerName" */}
                                {...getFieldProps("customerName", {
                                    initialValue: "",
                                    rules: [
                                        {
                                            required: true,
                                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050456") /* "请输入会员名称" */,
                                        },
                                    ],
                                })}
                            />
                        </FormItem>

                        <FormItem
                            fieldid="ublinker-routes-staples-components-IndexView-index-7913661-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050454") /* "会员号" */}
                            required
                            labelCol={labelCol}
                            error={getFieldError("customerCode")}
                        >
                            <FormControl
                                fieldid="ublinker-routes-staples-components-IndexView-index-9260552-FormControl"
                                placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050275") /* "史泰博提供的customerCode" */}
                                {...getFieldProps("customerCode", {
                                    initialValue: "",
                                    rules: [
                                        {
                                            required: true,
                                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050327") /* "请输入会员号" */,
                                        },
                                    ],
                                })}
                            />
                        </FormItem>

                        <FormItem fieldid="ublinker-routes-staples-components-IndexView-index-6836653-FormItem" labelCol={labelCol}>
                            <Button fieldid="ublinker-routes-staples-components-IndexView-index-9607094-Button" colors="primary" onClick={handleConfig}>
                                {
                                    hasOpen
                                        ? window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050446") /* "修改配置" */
                                        : window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050579") /* "绑定账号" */
                                }
                            </Button>
                        </FormItem>
                    </FormList>
                </ConfigInfoItem>
            </ConfigInfoList>
        </Fragment>
    );
};

export default FormList.createForm()(IndexView);
