import { useMemo, useState, useEffect, useCallback } from "react";
import query from "query-string";
import { getMyConnectorInfoService } from "./services";
import { autoServiceMessage } from "utils/service";
import { useLocation } from "react-router";

const typeReg = /\w+$/;

export function useLocationParamsMemo() {
    let location = useLocation();
    let updateParams = [location.search, location.pathname];
    let queryParams = useMemo(() => query.parse(location.search), updateParams);
    let connectorType = useMemo(() => location.pathname.match(typeReg)[0], updateParams);

    return { queryParams, connectorType };
}

/**
 * 获取我的连接器信息
 * @param queryParams
 * @return {{myConnectorInfo: *, setMyConnectorInfo: *, getMyConnectorInfo: *}}
 */
export function useMyConnectorInfoHook(queryParams) {
    let { id, tenantId } = queryParams;
    const [myConnectorInfo, setMyConnectorInfo] = useState(null);

    const getMyConnectorInfo = useCallback(async (data) => {
        let res = await autoServiceMessage({
            service: getMyConnectorInfoService(data),
        });
        if (res) {
            setMyConnectorInfo(res.data || null);
        }
    }, []);

    useEffect(() => {
        if (!myConnectorInfo && id && tenantId) {
            getMyConnectorInfo({ id, tenantId });
        }
    }, [id, tenantId]);
    return { myConnectorInfo, setMyConnectorInfo, getMyConnectorInfo };
}
