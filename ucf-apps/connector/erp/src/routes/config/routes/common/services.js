import { getInvokeService, getServicePath } from "utils/service";

/**
 * 获取我的连接器数据
 * @param {Object} data
 * @param {String} data.id -我的连接器ID
 * @param {String} data.tenantId
 * @return {Promise<unknown>}
 */
export const getMyConnectorInfoService = function (data) {
    let { id, ..._data } = data;
    return getInvokeService(
        {
            method: "GET",
            path: "/mygwapp/connector/common/config/" + id,
        },
        _data
    );
};
