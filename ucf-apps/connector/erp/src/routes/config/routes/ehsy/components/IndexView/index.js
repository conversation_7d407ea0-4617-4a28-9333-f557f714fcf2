import React, { Fragment, useMemo, useEffect, useCallback } from "react";
import FormList from "components/TinperBee/Form";
import ConfigTarget from "../../../components/ConfigTarget";
import { ConfigInfoList, ConfigInfoItem } from "../../../components/ConfigInfo";
import { useLocationParamsMemo } from "../../../common/utilHooks";
import { connectorInfoMap } from "../../../common/constants";
import { useEcConfigHook } from "../../../zkh360/hooks";
import ProtocolModal from "../../../zkh360/components/ProtocolModal";
import { Button, FormControl, Select } from "components/TinperBee";
import confirmInfo from "../../../zkh360/confirmInfo";
import { phoneReg } from "utils/regExp";
import commonText from "constants/commonText";

const FormItem = FormList.Item;
const labelCol = 120;
const IndexView = (props) => {
    const {
        form: { setFieldsValue, getFieldProps, validateFields, getFieldError, getFieldValue },
    } = props;
    const { queryParams, connectorType } = useLocationParamsMemo();
    const connectorInfo = useMemo(() => connectorInfoMap[connectorType], [connectorType]);

    const useEcConfig = useEcConfigHook(connectorType);

    const { ecConfigInfo, protocolStatus, setProtocolStatus, openEc } = useEcConfig;

    useEffect(() => {
        if (ecConfigInfo) {
            let { companyName, adminPhone, adminName, isMerchant = "0" } = ecConfigInfo;
            setFieldsValue({ companyName, adminPhone, adminName, isMerchant });
        }
    }, [ecConfigInfo]);

    const hasOpen = useMemo(() => ecConfigInfo && !!ecConfigInfo.custUserCode, [ecConfigInfo]);

    const handleOpen = useCallback(() => {
        validateFields((error, values) => {
            confirmInfo(values, ["companyName", "adminPhone", "adminName", "isMerchant"], async () => {
                if (!error) {
                    let requestData = {
                        companyName: values.companyName,
                        userName: values.adminName,
                        userPhone: values.adminPhone,
                        isMerchant: values.isMerchant,
                    };
                    let res = await openEc(requestData);
                }
            });
        });
    });

    return (
        <Fragment>
            <ConfigTarget logo={connectorInfo.logo} title={connectorInfo.name} intro={connectorInfo.intro} />

            <ConfigInfoList>
                <ConfigInfoItem title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050421") /* "填写基本信息，开通西域会员" */}>
                    <FormList fieldid="ublinker-routes-ehsy-components-IndexView-index-658073-FormList" className="config-action-form" layoutOpt={{ md: 12 }}>
                        <FormItem
                            fieldid="ublinker-routes-ehsy-components-IndexView-index-6560405-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050389") /* "企业名称" */}
                            required
                            labelCol={labelCol}
                            error={getFieldError("companyName")}
                        >
                            <FormControl
                                fieldid="ublinker-routes-ehsy-components-IndexView-index-1328457-FormControl"
                                placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050389") /* "企业名称" */}
                                disabled={hasOpen}
                                {...getFieldProps("companyName", {
                                    initialValue: "",
                                    rules: [
                                        {
                                            required: true,
                                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050470") /* "请输入企业名称" */,
                                        },
                                    ],
                                })}
                            />
                        </FormItem>

                        <FormItem
                            fieldid="ublinker-routes-ehsy-components-IndexView-index-4474296-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050258") /* "联系人手机号" */}
                            required
                            labelCol={labelCol}
                            error={getFieldError("adminPhone")}
                        >
                            <FormControl
                                fieldid="ublinker-routes-ehsy-components-IndexView-index-2233090-FormControl"
                                placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050382") /* "请输入您的手机号" */}
                                disabled={hasOpen}
                                {...getFieldProps("adminPhone", {
                                    initialValue: "",
                                    rules: [
                                        {
                                            required: true,
                                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050635") /* "请输入联系人手机号" */,
                                        },
                                        {
                                            pattern: phoneReg,
                                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050441") /* "请输入正确的手机号码" */,
                                        },
                                    ],
                                })}
                            />
                        </FormItem>

                        <FormItem
                            fieldid="ublinker-routes-ehsy-components-IndexView-index-90205-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050473") /* "联系人姓名" */}
                            required
                            labelCol={labelCol}
                            error={getFieldError("adminName")}
                        >
                            <FormControl
                                fieldid="ublinker-routes-ehsy-components-IndexView-index-3015094-FormControl"
                                placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050509") /* "请输入您的姓名" */}
                                {...getFieldProps("adminName", {
                                    initialValue: "",
                                    rules: [
                                        {
                                            required: true,
                                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050633") /* "请输入联系人姓名" */,
                                        },
                                    ],
                                })}
                            />
                        </FormItem>

                        <FormItem
                            fieldid="ublinker-routes-ehsy-components-IndexView-index-5919138-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050428") /* "是否贸易商" */}
                            required
                            labelCol={labelCol}
                        >
                            <Select
                                fieldid="ublinker-routes-ehsy-components-IndexView-index-7770493-Select"
                                data={[
                                    {
                                        value: "0",
                                        key: window.lang.template(commonText.not),
                                    },
                                    {
                                        value: "1",
                                        key: window.lang.template(commonText.yes),
                                    },
                                ]}
                                {...getFieldProps("isMerchant", {
                                    initialValue: "0",
                                })}
                            />
                        </FormItem>

                        {hasOpen ? null : (
                            <FormItem fieldid="ublinker-routes-ehsy-components-IndexView-index-3332812-FormItem" labelCol={labelCol}>
                                {hasOpen ? null : (
                                    <Button fieldid="ublinker-routes-ehsy-components-IndexView-index-3696925-Button" colors="primary" onClick={handleOpen}>
                                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050366") /* "账号开通" */}
                                    </Button>
                                )}
                            </FormItem>
                        )}

                        <p className="config-text">
                            {
                                window.lang.template(
                                    "MIX_UBL_ALL_UBL_FE_LOC_00050538"
                                ) /* "注意：西域运营人员会给您发邮件，告知您下一步操作所需使用的内容。直到您收到邮件后，方可进行下一步的操作。" */
                            }
                        </p>
                    </FormList>
                </ConfigInfoItem>
            </ConfigInfoList>

            <ProtocolModal connectorType={connectorType} name={connectorInfo.name} protocolStatus={protocolStatus} setProtocolStatus={setProtocolStatus} />
        </Fragment>
    );
};

export default FormList.createForm()(IndexView);
