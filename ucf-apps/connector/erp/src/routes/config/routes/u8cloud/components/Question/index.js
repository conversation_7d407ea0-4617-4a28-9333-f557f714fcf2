import React from "react";
import { getLocalImg } from "utils/index";
import { ConfigInfoItem, QuestionItem } from "../../../components/ConfigInfo";

const Question = () => {
    return (
        <ConfigInfoItem
            step="question"
            title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050290") /* "常见问题" */}
            subTitle={
                <a
                    fieldid="ublinker-routes-u8cloud-components-Question-index-841820-a"
                    href="/ublinker-fe/html/ubl-doc/index.html"
                    className="ucg-mar-l-10"
                    target="_blank"
                >
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050791") /* "查看更多" */}
                </a>
            }
            expandType="multiple"
        >
            <QuestionItem>
                <p className="ques-title">
                    <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050391") /* "问题一：" */}</strong>
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050501") /* "下载适配器后，没有将补丁打入NC，或没有重启中间件，会出现以下提示：" */}
                </p>
                <div className="ques-img">
                    <img fieldid="ublinker-routes-u8cloud-components-Question-index-3721341-img" src={getLocalImg("connector/help6.jpg")} alt="" />
                </div>
                <p>
                    <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050339") /* "解决方案：" */}</strong>
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050317") /* "将补丁打入NC，并重启中间件:" */}
                </p>
            </QuestionItem>

            <QuestionItem>
                <p className="ques-title">
                    <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050410") /* "问题二：" */}</strong>
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050589") /* "Windows环境，启动 nccloud-gateway-start.bat 时出现闪退" */}
                </p>
                <div className="ques-img"></div>
                <p>
                    <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050339") /* "解决方案：" */}</strong>
                    {
                        window.lang.template(
                            "MIX_UBL_ALL_UBL_FE_LOC_00050574"
                        ) /* "win + R 打开运行，输入CMD，点击回车。然后将nccloud-gateway-start.bat文件拖入该命令窗口执行" */
                    }
                </p>
            </QuestionItem>

            <QuestionItem>
                <p className="ques-title">
                    <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050505") /* "问题三：" */}</strong>
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050430") /* "网关启动时，会弹出拦截信息。" */}
                </p>
                <div className="ques-img">
                    <img fieldid="ublinker-routes-u8cloud-components-Question-index-6670687-img" src={getLocalImg("connector/help4.jpg")} alt="" />
                </div>
                <p>
                    <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050339") /* "解决方案：" */}</strong>
                    {
                        window.lang.template(
                            "MIX_UBL_ALL_UBL_FE_LOC_00050525"
                        ) /* "Windows首次启动时候会设置开机自启动，如果有杀毒软件，需要允许操作。Linux首次启动时候需要当前用户有su权限或者是root用户。" */
                    }
                </p>
            </QuestionItem>

            <QuestionItem>
                <p className="ques-title">
                    <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050645") /* "问题四：" */}</strong>
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050595") /* "测试连接，出现如下错误提示：" */}
                </p>
                <div className="ques-img">
                    <img fieldid="ublinker-routes-u8cloud-components-Question-index-7782944-img" src={getLocalImg("connector/problem_nc_connect.png")} alt="" />
                </div>
                <p>
                    <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050339") /* "解决方案：" */}</strong>
                </p>
                <p>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050411") /* "该种情况说明NC地址未通，需要确认NC地址。确认方式：" */}</p>
                <p>
                    {
                        window.lang.template(
                            "MIX_UBL_ALL_UBL_FE_LOC_00050375"
                        ) /* "Windows：在命令窗口 ping nc的ip 或者 telnet ip 端口。如：ping 127.0.0.1 或者 telnet 127.0.0.1 8001" */
                    }
                </p>
                <p>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050404") /* "Linux: ping ip 或者 telnet ip 端口" */}</p>
                <p>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050279") /* "请注意，ip指的是NC的内网访问地址" */}</p>
            </QuestionItem>

            <QuestionItem>
                <p className="ques-title">
                    <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050519") /* "问题五：" */}</strong>
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050369") /* "NC请求云业务不通" */}
                </p>
                <div className="ques-img">
                    <img fieldid="ublinker-routes-u8cloud-components-Question-index-8109442-img" src={getLocalImg("connector/update_port.png")} alt="" />
                </div>
                <p>
                    <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050339") /* "解决方案：" */}</strong>
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050265") /* "在“高级设置”里，修改“端口号”，需要确保该端口号未被占用，点击“保存修改”" */}
                </p>
            </QuestionItem>

            <QuestionItem>
                <p className="ques-title">
                    <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050596") /* "问题六：" */}</strong>
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050651") /* "启动网关报错时，会出现如下提示:" */}
                </p>
                <div className="ques-img">
                    <img fieldid="ublinker-routes-u8cloud-components-Question-index-8198543-img" src={getLocalImg("connector/not_connected.png")} alt="" />
                </div>
                <p>
                    <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050339") /* "解决方案：" */}</strong>
                    {
                        window.lang.template(
                            "MIX_UBL_ALL_UBL_FE_LOC_00050535"
                        ) /* "检查网络是否连通。ping ublws.yonyoucloud.com ubl.yonyoucloud.com 确保以上两个地址可以连通，也可以通过浏览器直接打开网页确认。" */
                    }
                </p>
            </QuestionItem>

            <QuestionItem>
                <p className="ques-title">
                    <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050361") /* "问题七：" */}</strong>
                    {
                        window.lang.template(
                            "MIX_UBL_ALL_UBL_FE_LOC_00050412"
                        ) /* "获取参数时，默认获取根集团编码，如需要某个子集团数据，请手动修改“集团编码”，如图：" */
                    }
                </p>
                <div className="ques-img">
                    <img fieldid="ublinker-routes-u8cloud-components-Question-index-1214438-img" src={getLocalImg("connector/problem_group_code.png")} alt="" />
                </div>
            </QuestionItem>

            <QuestionItem>
                <p className="ques-title">
                    <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050515") /* "问题八：" */}</strong>
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050274") /* "当存在多个网关需要设置一个默认的网关时，请点击“设置默认”按钮，如图：" */}
                </p>
                <div className="ques-img">
                    <img
                        fieldid="ublinker-routes-u8cloud-components-Question-index-5446695-img"
                        src={getLocalImg("connector/problem_default_gateway.png")}
                        alt=""
                    />
                </div>
                <p>
                    <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050339") /* "解决方案：" */}</strong>
                </p>
            </QuestionItem>

            <QuestionItem>
                <p className="ques-title">
                    <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050264") /* "问题九：" */}</strong>
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050368") /* "如出现下图提示，说明网关端口已经被占用。" */}
                </p>
                <div className="ques-img">
                    <img fieldid="ublinker-routes-u8cloud-components-Question-index-5662508-img" src={getLocalImg("connector/q9.png")} alt="" />
                </div>
                <p>
                    <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050339") /* "解决方案：" */}</strong>
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050385") /* "在高级设置里，修改端口号，点击保存修改。" */}
                </p>
            </QuestionItem>

            <QuestionItem>
                <p className="ques-title">
                    <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050663") /* "问题十：" */}</strong>
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050669") /* "如何确定正确的网关IP？" */}
                </p>
                <p>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050661") /* "1、为什么要选择网关IP？" */}</p>
                <p>
                    {
                        window.lang.template(
                            "MIX_UBL_ALL_UBL_FE_LOC_00050660"
                        ) /* "部署网关的服务器要求既可以访问内网，又可以访问外网，所以该服务器会安装配置多个网卡， 且在混合云模式下，ERP和网关的通信是双向的，ERP服务器须选择配置正确的网关服务器IP。" */
                    }
                </p>
                <p>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050667") /* "2、如何确定正确的网关IP？" */}</p>
                <p>
                    {
                        window.lang.template(
                            "MIX_UBL_ALL_UBL_FE_LOC_00050670"
                        ) /* "网关IP的必须是一个能够被ERP所处的服务器访问的IP地址，建议选择内网IP即可。 如果不能确认网关ID能够被ERP所在服务器访问，可以在ERP服务器通过 "ping" 命令确认，如果连接失败，说明两台机器网络连接不通，需要联系对应的服务器管理人员解决网络连接问题。" */
                    }
                </p>
                <div className="ques-img">
                    <img fieldid="ublinker-routes-u8cloud-components-Question-index-6654710-img" src={getLocalImg("connector/q10.png")} alt="" />
                </div>
            </QuestionItem>
        </ConfigInfoItem>
    );
};

export default Question;
