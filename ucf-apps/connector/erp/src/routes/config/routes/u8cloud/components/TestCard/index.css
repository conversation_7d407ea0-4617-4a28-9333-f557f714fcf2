.test-card-wrap {
  background: #FFFFFF;
  padding: 20px 20px 16px;
  margin-top: 16px;
  border: 1px solid #D9D9D9;
  border-radius: 4px;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
  position: relative;
}
.test-card-wrap .card-arrow {
  width: 10px;
  height: 10px;
  border-left: 1px solid #D9D9D9;
  border-top: 1px solid #D9D9D9;
  position: absolute;
  top: -6px;
  background: #FFf;
  transform: rotate(40deg) skewX(-5deg);
}
.test-card-wrap .card-close {
  position: absolute;
  right: 5px;
  top: 5px;
  cursor: pointer;
  color: #666666;
  font-size: 12px;
  line-height: 22px;
  z-index: 2;
}
.test-card-step-list {
  text-align: center;
}
.test-card-step-list > li {
  display: inline-block;
  vertical-align: top;
}
.test-card-step-item {
  color: #666666;
  width: 60px;
}
.test-card-step-item .step-icon {
  margin: 0 auto;
  width: 40px;
  height: 40px;
  border: 1px solid #D9D9D9;
  border-radius: 50%;
<<<<<<< HEAD
  line-height: 40px;
=======
  line-height: 36px;
>>>>>>> develop
  padding: 0 10px;
}
.test-card-step-item .step-icon > * {
  width: 100%;
  height: auto;
}
.test-card-step-item .step-name {
  font-size: 12px;
  line-height: 16px;
  margin-top: 2px;
  text-align: center;
  white-space: nowrap;
  width: 200%;
  margin-left: -50%;
}
.test-card-step-between {
  display: list-item;
  min-width: 90px;
  padding: 0 0 5px;
  text-align: center;
  font-size: 12px;
  line-height: 16px;
  border-bottom: 1px solid #505766;
  position: relative;
}
.test-card-step-between .text {
  display: inline-block;
<<<<<<< HEAD
  height: 16px;
=======
  height: 14px;
>>>>>>> develop
}
.test-card-step-between .text.success {
  color: #18B681;
}
.test-card-step-between .text.fail {
  color: #EE2223;
}
.test-card-step-between .text.pending {
  color: #588CE9;
}
.test-card-step-between .arrow {
  width: 8px;
  height: 8px;
  border-top: 1px solid #505766;
  position: absolute;
  right: 2px;
  bottom: -4px;
  transform: rotate(40deg) skewX(-5deg);
}
.test-card-error {
  color: #EE2223;
  font-size: 12px;
  line-height: 17px;
  padding-top: 16px;
}
.test-card-error i.cl {
  float: left;
  margin-right: 2px;
}
.test-card-error p {
  overflow: hidden;
<<<<<<< HEAD
=======
  word-break: break-word;
>>>>>>> develop
}
/*# sourceMappingURL=index.css.map */