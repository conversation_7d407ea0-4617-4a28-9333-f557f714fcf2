import React, { Fragment, useState, useEffect, useCallback, useMemo } from "react";
import { PRIVATE } from "utils/util";
import { <PERSON>ton, Modal, FormControl, Select, FormList } from "components/TinperBee";
import { autoServiceMessage } from "utils/service";
import Grid from "components/TinperBee/Grid";
import { GridActions, GridAction } from "components/TinperBee/Grid/GridActions";
import { portReg, originReg } from "utils/regExp";
import { injectRootStore } from "core/addStore";
import useBackConfirm from "hooks/useBackConfirm";
import { ConfigInfoItem } from "../../../components/ConfigInfo";
import { getSycParamsService, deleteSysParamService, editSysParamService, getGwKeyService, saveGwConfigService } from "../../services";
import commonText from "constants/commonText";

// import FormList from "components/TinperBee/Form";
const FormItem = FormList.Item;
export function useHighSetConfig(instanceInfo) {
    let { gatewayId } = instanceInfo;
    //port whitelist gwAddr gwSecret
    let [systemParams, setSystemParams] = useState([]);

    const getSystemParams = useCallback(async () => {
        let res = await autoServiceMessage({
            service: getSycParamsService({
                gwId: instanceInfo.gatewayId,
                pageNo: 1,
                pageSize: 100,
            }),
        });
        if (res) {
            let resData = res.data || {};
            setSystemParams(resData.items || []);
        }
    }, [instanceInfo]);

    const getGwKey = useCallback(
        async (gwaddr, cb) => {
            let res = await autoServiceMessage({
                service: getGwKeyService(gwaddr),
            });
            if (res) {
                if (cb) {
                    cb(res.gwsecret);
                }
            }
        },
        [instanceInfo]
    );

    const saveGatewayConfig = useCallback(
        async (data) => {
            data.gatewayId = instanceInfo.gatewayId;
            return await autoServiceMessage({
                service: saveGwConfigService(data),
                success: window.lang.template(commonText.operateSuccess),
            });
        },
        [instanceInfo]
    );

    const deleteParams = useCallback(
        (pk_id) => {
            Modal.confirm({
                fieldid: "202306091527",
                title: window.lang.template(commonText.confirmDelete),
                onOk: async () => {
                    let res = await autoServiceMessage({
                        service: deleteSysParamService(pk_id),
                    });
                    if (res) {
                        getSystemParams();
                    }
                },
            });
        },
        [instanceInfo]
    );

    //保存档案编辑项
    const saveParam = useCallback(
        async (data, index) => {
            let res = await autoServiceMessage({
                service: editSysParamService({
                    gwid: gatewayId,
                    pk_id: data.pk_id,
                    paramcode: data._paramcode,
                    paramname: data._paramname,
                    paramvalue: data._paramvalue,
                }),
                success: window.lang.template(commonText.saveSuccess),
            });
            if (res) {
                let docData = systemParams[index];
                docData.isEdit = false;
                docData.paramcode = data._paramcode;
                docData.paramname = data._paramname;
                docData.paramvalue = data._paramvalue;
                delete param._paramcode;
                delete param._paramname;
                delete param._paramvalue;
                setSystemParams([...systemParams]);
            }
        },
        [gatewayId, systemParams]
    );

    const renderCell = useCallback((field, _systemParams) => {
        let _field = "_" + field;
        return (value, record, index) => {
            if (record.isEdit) {
                return (
                    <FormControl
                        fieldid="ublinker-routes-u8cloud-components-HighSet-index-1223476-FormControl"
                        value={record[_field]}
                        showClose
                        onChange={(value) => {
                            _systemParams[index][_field] = value;
                            setSystemParams([..._systemParams]);
                        }}
                    />
                );
            } else {
                return value;
            }
        };
    }, []);

    const systemGirdColumns = useMemo(() => {
        return [
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050402") /* "参数编码" */,
                dataIndex: "paramcode",
                render: renderCell("paramcode", systemParams),
            },
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050498") /* "参数名称" */,
                dataIndex: "paramname",
                render: renderCell("paramname", systemParams),
            },
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050376") /* "参数值" */,
                dataIndex: "paramvalue",
                render: renderCell("paramvalue", systemParams),
            },
            {
                title: "",
                dataIndex: "$$save",
                width: 140,
                render: (value, record, index) => {
                    return record.isEdit ? (
                        <Fragment>
                            <Button
                                fieldid="ublinker-routes-u8cloud-components-HighSet-index-3817474-Button"
                                {...Grid.hoverButtonPorps}
                                onClick={saveParam.bind(null, record, index)}
                            >
                                {window.lang.template(commonText.save)}
                            </Button>
                            <Button
                                fieldid="ublinker-routes-u8cloud-components-HighSet-index-6119397-Button"
                                {...Grid.hoverButtonPorps}
                                colors="secondary"
                                onClick={() => {
                                    let param = systemParams[index];
                                    delete param._paramcode;
                                    delete param._paramname;
                                    delete param._paramvalue;
                                    param.isEdit = false;
                                    setSystemParams([...systemParams]);
                                }}
                            >
                                {window.lang.template(commonText.cancel)}
                            </Button>
                        </Fragment>
                    ) : null;
                },
            },
            {
                title: window.lang.template(commonText.actions),
                dataIndex: "$$actions",
                width: 110,
                render: (value, record, index) => {
                    let { tenantedit, issys, pk_id } = record;
                    let noEdit = !tenantedit;
                    return (
                        <GridActions>
                            <GridAction
                                fieldid="UCG-FE-routes-u8cloud-components-HighSet-index-3145455-GridAction"
                                disabled={noEdit}
                                onClick={() => {
                                    let param = systemParams[index];
                                    let { paramcode, paramname, paramvalue } = param;
                                    param._paramcode = paramcode;
                                    param._paramname = paramname;
                                    param._paramvalue = paramvalue;
                                    param.isEdit = true;
                                    setSystemParams([...systemParams]);
                                }}
                            >
                                {window.lang.template(commonText.edit)}
                            </GridAction>
                            <GridAction
                                fieldid="UCG-FE-routes-u8cloud-components-HighSet-index-7111111-GridAction"
                                disabled={noEdit || issys}
                                onClick={deleteParams.bind(null, pk_id)}
                            >
                                {window.lang.template(commonText.deletion)}
                            </GridAction>
                        </GridActions>
                    );
                },
            },
        ];
    }, [systemParams]);

    useEffect(() => {
        if (instanceInfo.gatewayId) {
            getSystemParams();
        }
    }, [instanceInfo]);

    return {
        systemParams,
        systemGirdColumns,
        getGwKey,
        saveGatewayConfig,
        instanceInfo,
    };
}

const fields = ["port", "whitelist", "gwaddr", "gwSecret"];

const HighSet = (props) => {
    const [form] = FormList.useForm();
    const formItemLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 12 },
    };
    const { setFieldsValue, getFieldProps, validateFields, getFieldError, getFieldValue, getFieldsValue, isFieldsTouched } = form;
    let { _useHighSetConfig, hasCreate, labelCol, gwConfigInfo } = props;
    let { systemParams, systemGirdColumns, getGwKey, saveGatewayConfig, instanceInfo } = _useHighSetConfig;

    const _isFieldsTouched = isFieldsTouched();

    const { setBackConfirm } = useBackConfirm(props, _isFieldsTouched);

    useEffect(() => {
        let { port, whitelist, gwAddr, gwSecret } = gwConfigInfo;
        let fields = { whitelist };
        if (!PRIVATE) {
            fields.port = port;
        }
        setFieldsValue(fields);
    }, [gwConfigInfo]);

    // const handleGetKey = useCallback(() => {
    //   validateFields(['gwaddr'], (error, values) => {
    //     if (!error) {
    //       getGwKey(values.gwaddr, (gwSecret) => {
    //         setFieldsValue({gwSecret})
    //       })
    //     }
    //   })
    // }, [])

    const handleSave = useCallback(() => {
        let fieldNames = [];
        if (!PRIVATE) {
            fieldNames.push("port");
        }
        // validateFields(fieldNames, (error, values) => {
        //   if (!error) {
        //     let data = getFieldsValue(fields);
        //     saveGatewayConfig(data)
        //       .then((res) => {
        //         if (res) {
        //           setBackConfirm(false)
        //         }
        //       })
        //   }
        // })
        validateFields().then((values) => {
            let data = getFieldsValue(fields);
            saveGatewayConfig(data).then((res) => {
                if (res) {
                    setBackConfirm(false);
                }
            });
        });
    }, [instanceInfo]);

    return (
        <ConfigInfoItem step="5" title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050474") /* "高级设置" */}>
            <Grid fieldid="ublinker-routes-u8cloud-components-HighSet-index-7947448-Grid" rowKey="pk_id" columns={systemGirdColumns} data={systemParams} />
            <FormList
                fieldid="ublinker-routes-u8cloud-components-HighSet-index-6576221-FormList"
                className="config-action-form ucg-mar-t-20"
                form={form}
                name="form122"
                labelAlign="right"
                {...formItemLayout}
                // layoutOpt={{md: 12}}
            >
                {PRIVATE ? null : (
                    <FormItem
                        fieldid="ublinker-routes-u8cloud-components-HighSet-index-8103694-FormItem"
                        label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050342") /* "网关端口号" */}
                        // labelCol={labelCol} required
                        // error={getFieldError('port')}
                        name="port"
                        rules={[
                            {
                                required: true,
                                message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050419") /* "请输入端口号" */,
                            },
                            {
                                pattern: portReg,
                                message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050527") /* "请输入正确的端口号" */,
                            },
                        ]}
                    >
                        <FormControl
                            fieldid="ublinker-routes-u8cloud-components-HighSet-index-2281707-FormControl"
                            placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050614") /* "系统调用网关使用端口号" */}
                            // {...getFieldProps('port', {
                            //   rules: [{
                            //     required: true, message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050419") /* "请输入端口号" */
                            //   }, {
                            //     pattern: portReg, message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050527") /* "请输入正确的端口号" */
                            //   }]
                            // })}
                        />
                    </FormItem>
                )}

                <FormItem
                    fieldid="ublinker-routes-u8cloud-components-HighSet-index-5487219-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050296") /* "IP白名单" */}
                    // labelCol={labelCol}
                    name="whitelist"
                >
                    <FormControl
                        fieldid="ublinker-routes-u8cloud-components-HighSet-index-7581890-FormControl"
                        placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050638") /* "网关限制指定IP的NC系统访问,为空时允许任意的IP访问" */}
                        // {...getFieldProps('whitelist')}
                    />
                </FormItem>

                {/*<FormItem*/}
                {/*  label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050652") /* "轻应用网关地址" *!/*/}
                {/*  labelCol={labelCol}*/}
                {/*  error={getFieldError('gwaddr')}*/}
                {/*>*/}
                {/*  <FormControl*/}
                {/*    placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050641") /* "可通过轻应用网关名称获取混合云密钥" *!/*/}
                {/*    {...getFieldProps('gwaddr', {*/}
                {/*      initialValue: '',*/}
                {/*      rules: [{*/}
                {/*        pattern: originReg, message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050491") /* "请输入正确的轻应用网关地址" */}
                {/*      }]*/}
                {/*    })}*/}
                {/*  />*/}
                {/*</FormItem>*/}
                {/*<FormItem fieldid="ublinker-routes-u8cloud-components-HighSet-index-8239784-FormItem" label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050289") /* "混合云密钥" *!/ labelCol={labelCol}>*/}
                {/*  <FormControl*/}
                {/*    placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050311") /* "混合云密钥" *!/*/}
                {/*    {...getFieldProps('gwSecret')}*/}
                {/*  />*/}
                {/*  <Button*/}
                {/*    className="ucg-mar-l-5"*/}
                {/*    disabled={!getFieldValue('gwaddr')}*/}
                {/*    bordered*/}
                {/*    onClick={handleGetKey}*/}
                {/*  >{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050312") /* "获取混合云密钥" *!/</Button>*/}
                {/*</FormItem>*/}

                <FormItem
                    fieldid="ublinker-routes-u8cloud-components-HighSet-index-8800407-FormItem"
                    // labelCol={labelCol}
                    label=" "
                >
                    <Button
                        fieldid="ublinker-routes-u8cloud-components-HighSet-index-7676738-Button"
                        disabled={!hasCreate}
                        colors="primary"
                        onClick={handleSave}
                    >
                        {window.lang.template(commonText.saveChange)}
                    </Button>
                </FormItem>
            </FormList>
        </ConfigInfoItem>
    );
};

const FormView = HighSet;

export default injectRootStore()(FormView);
