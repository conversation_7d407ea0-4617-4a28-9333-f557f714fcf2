import React, { useState, useEffect, useCallback } from "react";
import { PRIVATE } from "utils/util";
import { Button, FormControl, Modal, Select, FormList } from "components/TinperBee";
import ModalView from "components/TinperBee/Modal";
import Grid from "components/TinperBee/Grid";
import { autoServiceMessage } from "utils/service";
import { injectRootStore } from "core/addStore";
import useBackConfirm from "hooks/useBackConfirm";
import { ConfirmDefaultGwToTask } from "services/gwServices";
import { ConfigInfoItem } from "../../../components/ConfigInfo";
import PushGwConfigToApp from "../../../nc/components/SetGwConfig/PushGwConfigToApp";
import { getConfigService, getHasConfigInfoService, saveConfigService, pushYhtService, testConnectService, setDefaultService } from "../../services";

// import FormList from "components/TinperBee/Form";
import commonText from "constants/commonText";

const FormItem = FormList.Item;

export function useConnectorConfig(_useSetConnector, connectorType, queryParams) {
    let { instanceInfo, connectorName, erpVersion } = _useSetConnector;
    let { gatewayId, configId, entitryid } = instanceInfo;
    let [connectorConfig, setConnectorConfig] = useState({ ipaddr: "", busicode: "", groupcode: "", groupadmin: "" });

    //点击获取参数按钮 相关state
    let [oldConfig, setOldConfig] = useState({ configList: [], selectedList: [], oldConfigModalShow: false });

    let [buttonDisabled, setButtonDisabled] = useState(!_useSetConnector.hasCreate);

    useEffect(() => {
        setButtonDisabled(!_useSetConnector.hasCreate);
    }, [_useSetConnector.hasCreate]);

    let [shouldSave, setShouldSave] = useState(!queryParams.configId);

    let [shouldTest, setShouldTest] = useState(!queryParams.configId);

    const getConfig = useCallback(async (gwId, data) => {
        let res = await autoServiceMessage({ service: getConfigService(gwId, data) });
        if (res) {
            let { ncipaddr = "", ncbusicode = "", ncgroupcode = "", ncgroupadmin = "" } = res.data || {};
            setConnectorConfig({
                ipaddr: ncipaddr,
                busicode: ncbusicode,
                groupcode: ncgroupcode,
                groupadmin: ncgroupadmin,
            });
        }
    });

    const getOldConfig = useCallback(
        async (values) => {
            autoServiceMessage({
                service: getHasConfigInfoService({
                    ncurl1: values.ipaddr,
                    gatewayId: gatewayId,
                }),
            }).then((res) => {
                if (res && res.data && res.data.length > 0) {
                    setOldConfig({
                        configList: res.data,
                        selectedList: [],
                        oldConfigModalShow: true,
                    });
                }
            });
        },
        [gatewayId]
    );

    const saveConfig = useCallback(
        async (values) => {
            let requestData = {
                configid: configId,
                // entitryid: entitryid || gatewayId,
                gwguid: gatewayId,
                servertype: connectorType,
                alias: connectorName,
                ...values,
            };
            let saveRes = await autoServiceMessage({
                service: saveConfigService(requestData, { connectId: instanceInfo.connectId }),
                success: window.lang.template(commonText.saveSuccess),
            });
            if (saveRes) {
                setShouldSave(false);
                setConnectorConfig({
                    ...values,
                });
                if (!configId) {
                    configId = saveRes.data.configid;
                    _useSetConnector.createConnect({
                        ...instanceInfo,
                        configId,
                    });
                }

                requestData.configid = configId;
                requestData.u8cversion = erpVersion;
                // requestData.version = 'nc5x'
                await autoServiceMessage({
                    service: pushYhtService(requestData),
                });
            }
            return saveRes;
        },
        [gatewayId, configId, entitryid, connectorName, erpVersion]
    );

    const testConnect = useCallback(async () => {
        let res = await autoServiceMessage({
            service: testConnectService({
                nccfgid: configId,
                gatewayGuID: gatewayId,
                servertype: connectorType,
            }),
            success: window.lang.template(commonText.testSuccess),
        });
        if (res) {
            setShouldTest(false);
        }
    }, [configId, gatewayId]);

    const setDefault = useCallback(
        async (isInitTaskView) => {
            let res = await autoServiceMessage({
                service: setDefaultService({ configid: configId, gatewayId: gatewayId }),
                success: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050370") /* "设置默认成功!" */,
            });
            if (res && isInitTaskView) {
                ConfirmDefaultGwToTask(gatewayId);
            }
        },
        [configId, gatewayId]
    );

    useEffect(() => {
        if (instanceInfo.gatewayId) {
            getConfig(instanceInfo.gatewayId, { connectId: instanceInfo.connectId });
        }
    }, [instanceInfo.gatewayId]);

    return {
        connectorConfig,
        setConnectorConfig,
        oldConfig,
        setOldConfig,
        getOldConfig,
        buttonDisabled,
        setButtonDisabled,
        shouldSave,
        setShouldSave,
        shouldTest,
        setShouldTest,
        saveConfig,
        testConnect,
        setDefault,
    };
}

const ConnectorConfig = (props) => {
    const [form] = FormList.useForm();
    const formItemLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 12 },
    };
    const { setFieldsValue, getFieldProps, validateFields, getFieldError, getFieldValue, isFieldsTouched } = form;
    const { hasCreate, connectorInfo, labelCol, erpVersion, erpVersions, _useConnectorConfig, isInitTaskView } = props;

    const { setBackConfirm } = useBackConfirm(props, isFieldsTouched());

    const {
        connectorConfig,
        oldConfig,
        getOldConfig,
        setOldConfig,
        saveConfig,
        buttonDisabled,
        shouldSave,
        setShouldSave,
        shouldTest,
        setShouldTest,
        testConnect,
        setDefault,
    } = _useConnectorConfig;
    const [ipaddrDisable, setIpaddrDisable] = useState("");
    useEffect(() => {
        //当配置信息发生变化，重新设置配置信息form值
        setFieldsValue(connectorConfig);
        setIpaddrDisable(connectorConfig.ipaddr);
    }, [connectorConfig]);

    const handleGetOldConfig = useCallback(async (showType) => {
        if (showType) {
            // validateFields(['ipaddr'], (error, values) => {
            //   if (!error) {
            //     getOldConfig(values);
            //   }
            // })
            validateFields(["ipaddr"]).then((values) => {
                getOldConfig(values);
            });
        } else {
            setOldConfig({
                configList: [],
                oldConfigModalShow: false,
                selectedList: [],
            });
        }
    });

    const handleOldConfigModalOk = useCallback(() => {
        let { selectedList } = oldConfig;
        let data = selectedList[0];
        setFieldsValue({
            busicode: data.accountcode,
            groupcode: data.groupcode,
        });
        setShouldSave(true);
        setBackConfirm(true);
        handleGetOldConfig(false);
    }, [oldConfig]);

    const getOldConfigSelectedDataFunc = useCallback(
        (selectedList) => {
            setOldConfig({ ...oldConfig, selectedList: selectedList });
        },
        [oldConfig]
    );

    const handleConfigChange = useCallback((value, type) => {
        if (type == "ipaddr") {
            setIpaddrDisable(value);
        }
        setShouldSave(true);
        setShouldTest(true);
    }, []);

    const handleSave = useCallback(() => {
        // validateFields((error, values) => {
        //   if (!error) {
        //     values.version = erpVersion.split('_')[1];
        //     saveConfig(values).then(res => {
        //       if (res) {
        //         setBackConfirm(false)
        //       }
        //     });
        //   }
        // })
        validateFields().then((values) => {
            values.version = erpVersion.split("_")[1];
            saveConfig(values).then((res) => {
                if (res) {
                    setBackConfirm(false);
                }
            });
        });
    });

    return (
        <ConfigInfoItem title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050495") /* "输入<%= name %>配置并测试连接" */} connectorInfo={connectorInfo}>
            <FormList
                fieldid="ublinker-routes-u8cloud-components-ConnectorConfig-index-5425240-FormList"
                className="config-action-form"
                form={form}
                name="form122"
                labelAlign="right"
                {...formItemLayout}
                // layoutOpt={{md: 12}}
            >
                <div className="ipaddrBox">
                    <FormItem
                        fieldid="ublinker-routes-u8cloud-components-ConnectorConfig-index-1586237-FormItem"
                        label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050637") /* "服务器地址" */}
                        // required labelCol={labelCol}
                        // error={getFieldError('ipaddr')}
                        name="ipaddr"
                        rules={[
                            {
                                required: true,
                                message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050571") /* "请输入服务器地址" */,
                            },
                        ]}
                    >
                        {/* <div> */}
                        <FormControl
                            fieldid="ublinker-routes-u8cloud-components-ConnectorConfig-index-8280761-FormControl"
                            className="ucg-mar-r-5"
                            disabled={buttonDisabled}
                            placeholder={
                                window.lang.template(
                                    "MIX_UBL_ALL_UBL_FE_LOC_00050367",
                                    connectorInfo
                                ) /* "请输入<%= name %>内网可以访问地址如http://************:3265" */
                            }
                            onChange={(value) => {
                                handleConfigChange(value, "ipaddr");
                            }}
                            // {...getFieldProps('ipaddr', {
                            //   initialValue: '',
                            //   rules: [{
                            //     required: true, message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050571") /* "请输入服务器地址" */
                            //   }],
                            //   onChange: handleConfigChange
                            // })}
                        />

                        {/* </div> */}
                    </FormItem>
                    <div className="ipaddrBox-btn">
                        <Button
                            fieldid="ublinker-routes-u8cloud-components-ConnectorConfig-index-5724849-Button"
                            // disabled={!getFieldValue('ipaddr')}
                            disabled={!ipaddrDisable}
                            bordered
                            onClick={handleGetOldConfig.bind(null, true)}
                        >
                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050255") /* "获取参数" */}
                        </Button>
                    </div>
                </div>

                <FormItem
                    fieldid="ublinker-routes-u8cloud-components-ConnectorConfig-index-7783033-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050443") /* "版本" */}
                    // labelCol={labelCol}
                >
                    <Select fieldid="ublinker-routes-u8cloud-components-ConnectorConfig-index-4482693-Select" disabled data={erpVersions} value={erpVersion} />
                </FormItem>

                <FormItem
                    fieldid="ublinker-routes-u8cloud-components-ConnectorConfig-index-5650835-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050433") /* "账套编码" */}
                    // required labelCol={labelCol}
                    // error={getFieldError('busicode')}
                    name="busicode"
                    initialValue="nc65"
                    rules={[
                        {
                            required: true,
                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050553") /* "请输入账套编码" */,
                        },
                    ]}
                >
                    <FormControl
                        fieldid="ublinker-routes-u8cloud-components-ConnectorConfig-index-986500-FormControl"
                        disabled={buttonDisabled}
                        placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050585") /* "系统帐套编码" */}
                        onChange={handleConfigChange}
                        // {...getFieldProps('busicode', {
                        //   initialValue: 'nc65',
                        //   rules: [{
                        //     required: true,  message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050553") /* "请输入账套编码" */
                        //   }],
                        //   onChange: handleConfigChange
                        // })}
                    />
                </FormItem>

                <FormItem
                    fieldid="ublinker-routes-u8cloud-components-ConnectorConfig-index-9187509-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050283") /* "集团编码" */}
                    // required labelCol={labelCol}
                    // error={getFieldError('groupcode')}
                    name="groupcode"
                    initialValue="nc65"
                    rules={[
                        {
                            required: true,
                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050540") /* "请输入默认集团编码" */,
                        },
                    ]}
                >
                    <FormControl
                        fieldid="ublinker-routes-u8cloud-components-ConnectorConfig-index-3310695-FormControl"
                        disabled={buttonDisabled}
                        placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050318") /* "默认集团编码" */}
                        onChange={handleConfigChange}
                        // {...getFieldProps('groupcode', {
                        //   initialValue: 'nc65',
                        //   rules: [{
                        //     required: true, message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050540") /* "请输入默认集团编码" */
                        //   }],
                        //   onChange: handleConfigChange
                        // })}
                    />
                </FormItem>

                <FormItem
                    fieldid="ublinker-routes-u8cloud-components-ConnectorConfig-index-7771390-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050582") /* "用户编码" */}
                    // labelCol={labelCol}
                    name="groupadmin"
                >
                    <FormControl
                        fieldid="ublinker-routes-u8cloud-components-ConnectorConfig-index-2630126-FormControl"
                        disabled={buttonDisabled}
                        placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050564") /* "默认用户编码" */}
                        onChange={handleConfigChange}
                        // {...getFieldProps('groupadmin', {
                        //   initialValue: '',
                        //   onChange: handleConfigChange
                        // })}
                    />
                </FormItem>

                <FormItem
                    fieldid="ublinker-routes-u8cloud-components-ConnectorConfig-index-2323840-FormItem"
                    // labelCol={labelCol}
                    label=" "
                >
                    <Button
                        fieldid="ublinker-routes-u8cloud-components-ConnectorConfig-index-5019183-Button"
                        className="ucg-mar-r-10"
                        disabled={buttonDisabled}
                        colors="primary"
                        onClick={handleSave}
                    >
                        {window.lang.template(commonText.saveChange)}
                    </Button>
                    <Button
                        fieldid="ublinker-routes-u8cloud-components-ConnectorConfig-index-6890328-Button"
                        className="ucg-mar-r-10"
                        disabled={buttonDisabled || shouldSave}
                        colors="secondary"
                        onClick={testConnect}
                    >
                        {window.lang.template(commonText.testConnect)}
                    </Button>
                    <Button
                        fieldid="ublinker-routes-u8cloud-components-ConnectorConfig-index-1237318-Button"
                        disabled={buttonDisabled || shouldSave || shouldTest}
                        colors="secondary"
                        onClick={setDefault.bind(null, isInitTaskView)}
                    >
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050481") /* "设置默认" */}
                    </Button>
                    {PRIVATE ? (
                        <PushGwConfigToApp>
                            <Button
                                fieldid="ublinker-routes-u8cloud-components-ConnectorConfig-index-1802054-Button"
                                className="ucg-mar-l-10"
                                colors="secondary"
                                disabled={buttonDisabled || shouldSave || shouldTest}
                            >
                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050921") /* "推送网关配置" */}
                            </Button>
                        </PushGwConfigToApp>
                    ) : null}
                </FormItem>
            </FormList>

            <ModalView
                title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050621") /* "选择配置参数" */}
                show={oldConfig.oldConfigModalShow}
                onCancel={handleGetOldConfig.bind(null, false)}
                onOk={handleOldConfigModalOk}
                size="md"
            >
                {oldConfig.oldConfigModalShow ? (
                    <Grid
                        fieldid="ublinker-routes-u8cloud-components-ConnectorConfig-index-9342808-Grid"
                        columns={[
                            {
                                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050433") /* "账套编码" */,
                                dataIndex: "accountcode",
                            },
                            {
                                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050283") /* "集团编码" */,
                                dataIndex: "groupcode",
                            },
                        ]}
                        radioSelect
                        rowKey="accountcode"
                        selectedList={oldConfig.selectedList}
                        getSelectedDataFunc={getOldConfigSelectedDataFunc}
                        data={oldConfig.configList}
                    />
                ) : null}
            </ModalView>
        </ConfigInfoItem>
    );
};

const FormConnectorConfig = ConnectorConfig;

export default injectRootStore()(FormConnectorConfig);
