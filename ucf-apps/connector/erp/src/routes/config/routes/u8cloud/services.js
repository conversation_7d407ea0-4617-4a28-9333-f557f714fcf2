import { getInvokeService } from "utils/service";

/**
 * 获取已配置连接器配置版本号
 * @param {String} gwId -gatewayId
 * @return {Promise<unknown>}
 */
export const getErpVersionService = function (gwId) {
    return getInvokeService({
        method: "GET",
        path: "/diwork/version/getErpVersion/" + gwId,
        showLoading: false,
    });
};

/**
 * 创建连接器实例
 * @return {Promise<unknown>}
 */
export const createInstanceService = function () {
    return getInvokeService({
        method: "GET",
        path: "/diwork/ncconnect/creatInstance",
    });
};

/**
 * 创建连接器
 * @param {Object} data
 * @param {String} data.connectorType
 * @param {String} data.gatewayId
 * @param {String} data.configid
 * @param {String} data.alias -连接器名称
 * @return {Promise<unknown>}
 */
export const createConnectService = function (data) {
    let { connectorType, ..._data } = data;
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/ncconnect/createConnect/" + connectorType,
            showLoading: false,
        },
        _data
    );
};

/**
 * 确定网关中的 连接器内容（nc u8c等）版本号
 * @param {Object} data
 * @param {String} data.erpversion
 * @param {String} data.gatewayId
 * @return {Promise<unknown>}
 */
export const saveErpVersionService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/version/determineErpVeriosn/",
            showLoading: false,
        },
        data
    );
};

/***
 * 获取高级设置中系统参数
 * @param {Object} data
 * @param {String} data.gwId
 * @param {String|Number} data.pageNo
 * @param {String|Number} data.pageSize
 * @return {Promise<unknown>}
 */
export const getSycParamsService = function (data) {
    let { gwId, ..._data } = data;
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/gwruntimeparam/page/" + gwId,
        },
        _data
    );
};

/**
 * 删除高级设置参数
 * @param pk_id
 * @return {Promise<unknown>}
 */
export const deleteSysParamService = function (pk_id) {
    return getInvokeService({
        method: "POST",
        path: "/diwork/gwruntimeparam/delete/" + pk_id,
    });
};

/**
 * 编辑高级设置参数
 * @param {Object} data
 * @param {String} data.pk_id  -record.pk_id,
 * @param {String} data.gwid
 * @param {String} data.paramcode -record.paramcode,
 * @param {String} data.paramname -record.paramcode,
 * @param {String} data.paramvalue -record.paramcode,
 * @return {Promise<unknown>}
 */
export const editSysParamService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/gwruntimeparam/edit",
        },
        data
    );
};

/**
 * 获取混合云轻应用网关密钥
 * @param {String} gwaddr
 * @return {Promise<unknown>}
 */
export const getGwKeyService = function (gwaddr) {
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/querySecret",
        },
        { gwaddr }
    );
};

/**
 * 保存网关高级配置
 * @param {Object} data
 * @param {String} data.gatewayId
 * @param {String} data.port
 * @param {String} data.whitelist
 * @param {String} data.gwaddr
 * @param {String} data.gwSecret
 * @return {Promise<unknown>}
 */
export const saveGwConfigService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/gateway/gatewayconfig/save",
        },
        data
    );
};

/**
 * 获取连接器配置信息
 * @param gwId
 */
export const getConfigService = function (gwId, data) {
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/ncconnect/getNCConfig/" + gwId,
        },
        data
    );
};

/**
 * 获取已配置信息列表
 * @param {Object} data
 * @param {String} data.ncurl1 -config.ipaddr
 * @param {String} data.gatewayId
 * @return {Promise<unknown>}
 */
export const getHasConfigInfoService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/ncconnect/getcode",
        },
        data
    );
};

/**
 * 保存配置信息
 * @param {Object} data
 * @param {String=} data.configid  - instanceInfo.configId
 * @param {String=} data.entitryid  - instanceInfo.entitryid
 * @param {String=} data.gwguid  - instanceInfo.gatewayId
 * @param {String=} data.ipaddr  -
 * @param {String=} data.version  - erpVersion
 * @param {String=} data.busicode
 * @param {String=} data.groupcode
 * @param {String=} data.groupadmin
 * @param {String=} data.servertype - connectorType
 * @param {String=} data.alias - queryParams.alias
 * @return {Promise<unknown>}
 */
export const saveConfigService = function (data, param) {
    let saveType = data.configid ? "update" : "add";
    let p = data.configid ? param : {};
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/ncconnect/ncconfig/" + saveType,
        },
        data,
        p
    );
};

/**
 * 将配置推送友互通
 * @param data -同配置保存参数
 * @return {Promise<unknown>}
 */
export const pushYhtService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/ncconnect/ncconfig/pushtoyht/",
            showLoading: false,
        },
        data
    );
};

/**
 * 测试配置连接
 * @param {Object} data
 * @param {String} data.nccfgid -instanceInfo.configId
 * @param {String} data.gatewayGuID -instanceInfo.gatewayId
 * @param {String=} data.servertype - connectorType
 * @return {Promise<unknown>}
 */
export const testConnectService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/ncconfig/testnc",
        },
        data
    );
};

/**
 * 设置默认
 * @param {Object} data
 * @param {String} data.configid -instanceInfo.configId
 * @param {String} data.gatewayId -instanceInfo.gatewayId
 * @return {Promise<unknown>}
 */
export const setDefaultService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/ncconnect/setDefaultNCConnector",
        },
        data
    );
};

/**
 * 获取默认档案初始化状态
 * @param erpVersion
 * @return {Promise<unknown>}
 */
export const initDocStatusService = function (erpVersion) {
    return getInvokeService({
        method: "GET",
        path: "/diwork/defDoc/datamapping/initState/" + erpVersion,
        showLoading: false,
    });
};

/**
 * 获取默认档案列表
 * @param {Object} data
 * @param {String} data.erpVersion
 * @param {String} data.gatewayId
 * @return {Promise<unknown>}
 */
export const getDefDocService = function (data) {
    let { erpVersion, ..._data } = data;
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/defDoc/datamapping/get/" + erpVersion,
            timeout: 20000,
        },
        _data
    );
};

/**
 * 编辑自定义档案
 * @param {Object} data
 * @param {String} data.cloudName
 * @param {String} data.cloudPk
 * @param {String} data.erpName
 * @param {String} data.erpPk
 * @param {String} data.erpversion -erpVersion
 * @return {Promise<unknown>}
 */
export const editDefDocService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/defDoc/datamapping/set/",
        },
        data
    );
};

/**
 * 保存自定义档案列表
 * @param {Object} data
 * @param {String} data.erpVersion
 * @param {Array} data.erpPks
 * @return {Promise<unknown>}
 */
export const pushDefDocService = function (data) {
    let { erpVersion, erpPks } = data;
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/defDoc/datamapping/push/" + erpVersion,
            showLoading: false,
        },
        erpPks
    );
};

/**
 * 判断视图任务是否已经初始化
 * @param gwId
 * @return {Promise<unknown>}
 */
export const isViewInitService = function (gwId) {
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/erpdata/task/isViewInit/get",
            showLoading: false,
        },
        { gatewayId: gwId }
    );
};

/**
 * 获取数据视图
 * @param {Object} data
 * @param {String} data.erpversion
 * @param {String=} data.doc=[category|sequence]
 * @return {Promise<unknown>}
 */
export const getDataViewService = function (data) {
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/erpdata/initTask/dataview/get",
        },
        data
    );
};

export const initDataViewService = function (data) {
    let { gatewayId, tableViews } = data;
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/erpdata/task/init/selectedView/" + gatewayId,
        },
        tableViews
    );
};

/**
 * 更新同步任务
 * @param {Object} data
 * @param {String} data.taskId -task.pk_id
 * @param {String} data.dataversion
 * @param {String} data.increment
 * @param {String} data.sqlwhere
 * @return {Promise<unknown>}
 */
export const updateSyncTaskService = function (data) {
    let { taskId, ..._data } = data;
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/erpdata/task/update/" + taskId,
        },
        _data
    );
};
