import React, { Fragment, useCallback, useMemo, useRef } from "react";
import { Button, FormControl, Modal, Select, FormList } from "components/TinperBee";
// import FormList from "components/TinperBee/Form";
import { connectorInfoMap } from "../../../common/constants";
import ConfigTarget from "../../../components/ConfigTarget";
import { useSetConnector } from "../../../nc/components/IndexView/hooks";
import useErpVersions from "../../../nc/components/IndexView/useErpVersions";
import { ConfigInfoList, ConfigInfoItem } from "../../../components/ConfigInfo";
import { useLocationParamsMemo } from "../../../common/utilHooks";
import ConnectorConfig, { useConnectorConfig } from "../ConnectorConfig";
import { useGatewayConfig } from "hooks/useGateway";
import SetGwConfigView from "../../../nc/components/SetGwConfig";
import InitDoc, { useInitDoc } from "../../../nc/components/InitDoc";
import InitTaskView, { useInitTask } from "../../../nc/components/InitTask";
import HighSet, { useHighSetConfig } from "../HighSet";
import Question from "../Question";
import DownloadView from "../../../nc/components/DownloadView";
import AdaptersView from "../../../nc/components/Adapters";
import HealthCheckView from "../../../nc/components/HealthCheck";
import { Warning } from "utils/feedback";
import { PRIVATE } from "utils/util";

const labelCol = 120;
const FormItem = FormList.Item;
const IndexView = (props) => {
    const [form] = FormList.useForm();
    const formItemLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 12 },
    };
    const { location } = props;
    const { queryParams, connectorType } = useLocationParamsMemo();

    const connectorInfo = useMemo(() => {
        return connectorInfoMap[connectorType];
    }, [connectorType]);

    const { erpVersions, defaultErpVersion } = useErpVersions(connectorInfo, connectorType);

    const _useSetConnector = useSetConnector(queryParams, connectorType, defaultErpVersion);
    const { connectorName, setConnectorName, erpVersion, setErpVersion, instanceInfo, createInstance, hasCreate } = _useSetConnector;

    const _useConnectorConfig = useConnectorConfig(_useSetConnector, connectorType, queryParams);

    const _useInitDoc = useInitDoc(_useSetConnector, _useConnectorConfig.connectorConfig);

    const _useInitTask = useInitTask(_useSetConnector, _useInitDoc, connectorType, _useConnectorConfig.connectorConfig);

    const [gatewayConfig, getGatewayConfig] = useGatewayConfig(instanceInfo.gatewayId);

    const _useHighSetConfig = useHighSetConfig(instanceInfo);

    const handleCreateInstance = useCallback(() => {
        if (connectorName.length > 20) {
            Warning(window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050922") /* "连接器名称长度不能超过20个字符" */);
        } else {
            Modal.confirm({
                fieldid: "202306091528",
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050627", connectorInfo) /* "确认使用该<%= name %>版本吗？" */,
                content: window.lang.template(
                    "MIX_UBL_ALL_UBL_FE_LOC_00050356",
                    connectorInfo
                ) /* "该操作会决定你的网关支持的<%= name %>版本, 且版本确定后无法更改" */,
                onOk: createInstance,
            });
        }
    }, [erpVersion, connectorName]);

    const adapterViewRef = useRef(null);

    return (
        <Fragment>
            <ConfigTarget logo={connectorInfo.logo} title={connectorInfo.name} intro={connectorInfo.intro} />
            <ConfigInfoList>
                <ConfigInfoItem
                    step="1"
                    title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050537", connectorInfo) /* "设置<%= name %>连接器名称" */}
                    connectorInfo={connectorInfo}
                >
                    <FormList
                        fieldid="ublinker-routes-u8cloud-components-IndexView-index-8998864-FormList"
                        className="config-action-form config-action-form-80"
                        form={form}
                        name="form122"
                        labelAlign="right"
                        {...formItemLayout}
                        //  layoutOpt={{ md: 12 }}
                    >
                        <FormItem
                            fieldid="ublinker-routes-u8cloud-components-IndexView-index-4492939-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050551") /* "连接器名称" */}
                            //  labelCol={160}
                        >
                            <FormControl
                                fieldid="ublinker-routes-u8cloud-components-IndexView-index-7896305-FormControl"
                                disabled={hasCreate}
                                value={connectorName}
                                onChange={setConnectorName}
                            />
                        </FormItem>

                        <FormItem
                            fieldid="ublinker-routes-u8cloud-components-IndexView-index-9631036-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050360") /* "请选择版本" */}
                            // labelCol={160}
                        >
                            <Select
                                fieldid="ublinker-routes-u8cloud-components-IndexView-index-9529125-Select"
                                disabled={hasCreate}
                                data={erpVersions}
                                value={erpVersion}
                                onChange={setErpVersion}
                            />
                        </FormItem>

                        {hasCreate ? null : (
                            <FormItem
                                fieldid="ublinker-routes-u8cloud-components-IndexView-index-4819877-FormItem"
                                // labelCol={160}
                                label=" "
                            >
                                <Button
                                    fieldid="ublinker-routes-u8cloud-components-IndexView-index-6502752-Button"
                                    disabled={!connectorName}
                                    colors="primary"
                                    onClick={handleCreateInstance}
                                >
                                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050352") /* "确认" */}
                                </Button>
                            </FormItem>
                        )}
                    </FormList>
                </ConfigInfoItem>

                <DownloadView
                    connectorInfo={connectorInfo}
                    connectorType={connectorType}
                    hasCreate={hasCreate}
                    instanceInfo={instanceInfo}
                    erpVersion={erpVersion}
                    gwConfigInfo={gatewayConfig}
                />

                {/* 专属化去除健康检查 */}
                {PRIVATE ? null : <HealthCheckView hasCreate={hasCreate} instanceInfo={_useSetConnector.instanceInfo} adapterViewRef={adapterViewRef} />}

                {PRIVATE ? (
                    <SetGwConfigView instanceInfo={instanceInfo} gwConfigInfo={gatewayConfig} setErpConfigDisabled={_useConnectorConfig.setButtonDisabled} />
                ) : null}

                <ConnectorConfig
                    hasCreate={hasCreate}
                    connectorInfo={connectorInfo}
                    labelCol={labelCol}
                    erpVersions={erpVersions}
                    erpVersion={erpVersion}
                    _useConnectorConfig={_useConnectorConfig}
                    isInitTaskView={_useInitTask.isInitTaskView}
                />

                <InitDoc _useInitDoc={_useInitDoc} />

                <InitTaskView _useInitTask={_useInitTask} labelCol={labelCol} />

                <HighSet hasCreate={hasCreate} labelCol={labelCol} _useHighSetConfig={_useHighSetConfig} gwConfigInfo={gatewayConfig} />

                <AdaptersView ref={adapterViewRef} instanceInfo={_useSetConnector.instanceInfo} getGatewayConfig={getGatewayConfig} />

                <Question />
            </ConfigInfoList>
        </Fragment>
    );
};

export default IndexView;
