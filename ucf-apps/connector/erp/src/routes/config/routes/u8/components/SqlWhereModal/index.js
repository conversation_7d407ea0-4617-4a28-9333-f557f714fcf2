import React, { Component } from "react";
import Modal from "components/TinperBee/Modal";
import { Checkbox, FormControl } from "components/TinperBee";

class SqlWhereModal extends Component {
    constructor() {
        super();
    }

    handleOk = () => {
        let { taskInfo } = this.props;
        let data = {
            sqlwhere: btoa(encodeURIComponent(this.sqlwhereInput.state.value)),
            dataversion: taskInfo.dataversion,
            increment: taskInfo.increment,
            taskId: taskInfo.pk_id,
        };
        this.props.onOk(data);
    };

    render() {
        let { show, onCancel, taskInfo } = this.props;

        return (
            <Modal
                fieldid="ublinker-routes-u8-components-SqlWhereModal-index-5214974-Modal"
                show={show}
                title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050152") /* "视图任务编辑" */}
                onCancel={onCancel}
                onOk={this.handleOk}
            >
                {show && taskInfo ? (
                    <div className="ucg-pad-20">
                        <p>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050124") /* "SQL条件" */}</p>
                        <FormControl.TextArea
                            rows={4}
                            // componentClass="textarea"
                            defaultValue={taskInfo.sqlwhere || ""}
                            placeholder={
                                window.lang.template(
                                    "MIX_UBL_ALL_UBL_FE_LOC_00050183"
                                ) /* "查询条件，无需输入where关键词，示例：code="2212" and ts>@lastupdatetime[@lastupdatetime为最后一次成功同步时间" */
                            }
                            ref={(node) => (this.sqlwhereInput = node)}
                            style={{ height: "auto" }}
                            // autoSize={{
                            //   minRows: 4
                            // }}
                        />
                        <Checkbox
                            fieldid="ublinker-routes-u8-components-SqlWhereModal-index-211975-Checkbox"
                            defaultChecked={taskInfo.increment}
                            ref={(node) => (this.incrementCheckBox = node)}
                        >
                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050186") /* "是否启用增量" */}
                        </Checkbox>
                    </div>
                ) : null}
            </Modal>
        );
    }
}

export default SqlWhereModal;
