import React, { Fragment, useState, useEffect, useCallback, useMemo } from "react";
import queryString from "query-string";
import { a_download } from "utils/index";
import { Button, FormControl, Select, Modal, FormList } from "components/TinperBee";
import { getLocalImg } from "utils/index";
import { getServicePath } from "utils/service";
import Grid from "components/TinperBee/Grid";
import { GridActions, GridAction } from "components/TinperBee/Grid/GridActions";
// import FormList from "components/TinperBee/Form";
import { autoServiceMessage } from "utils/service";
import { Success } from "utils/feedback";
import { batchSyncTaskService } from "services/taskServices";
import useBackConfirm from "hooks/useBackConfirm";
import { injectRootStore } from "core/addStore";
import { useNavigate } from "react-router";

import ConfigTarget from "../../../components/ConfigTarget";
import { ConfigInfoList, ConfigInfoItem } from "../../../components/ConfigInfo";

import {
    getDefaultConfigInfoService,
    getOrgsService,
    getConfigDataService,
    getU8DataViewService,
    initU8DataViewService,
    saveConfigService,
    testConnectService,
    setDefaultService,
    downloadClientService,
    updateSyncTaskService,
} from "../service";
import SqlWhereModal from "../SqlWhereModal";
import { useLocationParamsMemo } from "../../../common/utilHooks";
import { connectorInfoMap } from "../../../common/constants";
import commonText from "constants/commonText";

import InitDoc, { useInitDoc } from "../InitDoc";
import "./index.less";

import { Error } from "utils/feedback";
const FormItem = FormList.Item;
const versionArr = [
    {
        key: "U8v12.5",
        value: "U8v12.5",
    },
    {
        key: "U8v13.0",
        value: "U8v13.0",
    },
    {
        key: "U8v15.1",
        value: "U8v15.1",
    },
];
const versionArr2 = [
    {
        key: "u8_multi",
        value: "u8_multi",
    },
];
const downloadVersionArr = [
    {
        key: "1.7",
        value: "1.7",
    },
    {
        key: "1.8",
        value: "1.8",
    },
];

const dbUrlReg = /^(((\d{1,2})|(1\d{2})|(2[0-4]\d)|(25[0-5]))\.){3}((\d{1,2})|(1\d{2})|(2[0-4]\d)|(25[0-5]))$/;

/**
 * u8 openApi 系统默认信息，包含 {tenantEmail: "", pk_tenantid: "", openapicode: "",} 等有用信息
 * @returns {{getDefaultConfig: Function, defaultConfigError: String, defaultConfigInfo: Object}}
 */
function useDefaultConfigHook() {
    // 默认信息
    let [defaultConfigInfo, setDefaultConfigInfo] = useState({
        tenantEmail: "",
        pk_tenantid: "",
        openapicode: "",
    });
    let [accountType, setAccountType] = useState(false);
    // defaultConfig 后天返回错误信息
    let [defaultConfigError, setDefaultConfigError] = useState("");
    const getDefaultConfig = useCallback(async (tenantId) => {
        let res = await autoServiceMessage({
            service: getDefaultConfigInfoService({ tenantId }),
        });
        if (res) {
            let { status, data, accountType } = res;
            //当statue=1 data返回 {tenantEmail: "", pk_tenantid: "", openapicode: "",}等有用信息
            //否则 data 返回 一个报错信息
            if (status === 1) {
                setDefaultConfigInfo(data);
                setAccountType(accountType);
            } else {
                setDefaultConfigError(data);
            }
        }
    }, []);
    return { accountType, defaultConfigInfo, defaultConfigError, getDefaultConfig };
}

function useOrgListHook() {
    //组织编码选择框
    let [orgList, setOrgList] = useState([]);
    const getOrgList = useCallback(async () => {
        let res = await autoServiceMessage({
            service: getOrgsService(),
        });
        if (res) {
            let _orgList = res.data || [];
            setOrgList(
                _orgList.map((org) => {
                    return {
                        value: org.code,
                        key: `${org.code}(${org.name})`,
                        name: org.name,
                        id: org.id,
                    };
                })
            );
        }
    }, []);
    return { orgList, getOrgList };
}

function useConfigDataHook() {
    //form表单回显
    let [isInit, setIsInit] = useState(true);
    let [configData, setConfigData] = useState({});
    const getConfig = useCallback(async (configId, setFieldsValue) => {
        if (!configId) {
            setIsInit(false);
            return;
        }
        let res = await autoServiceMessage({
            service: getConfigDataService(configId),
        });
        if (res) {
            let _configData = res.data;
            setIsInit(JSON.parse(res.isInit));
            setConfigData(_configData);
            setFieldsValue(_configData);
        }
    }, []);
    const saveConfig = useCallback(
        async (data) => {
            let res = await autoServiceMessage({
                service: saveConfigService(data),
            });
            if (res) {
                let _configData = {
                    ...configData,
                    ...data,
                    identifies: res.identifies,
                    orgcode: res.orgcode,
                    id: res.id,
                };
                Success(res.errormsg);
                setConfigData(_configData);
            }
            return res;
        },
        [configData]
    );
    const testConnect = useCallback(
        async (data) => {
            let res = await autoServiceMessage({
                service: testConnectService(data),
            });
            if (res) {
                configData.identifies = res.identifies;
                Success(res.errormsg);
                setConfigData({ ...configData });
            }
        },
        [configData]
    );

    const setDefault = useCallback(async () => {
        let res = await autoServiceMessage({
            service: setDefaultService({ id: configData.id }),
            success: window.lang.template(commonText.operateSuccess),
        });
        if (res) {
            configData.isdefault = res.data.isdefault;
            setConfigData({ ...configData });
        }
    }, [configData]);
    return { isInit, configData, getConfig, saveConfig, testConnect, setDefault, setConfigData };
}

const u8DataViewColumns = [
    {
        title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050199") /* "数据视图" */,
        dataIndex: "datatype.typename",
    },
    {
        title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050121") /* "视图所属应用" */,
        dataIndex: "appcode",
    },
    {
        title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050126") /* "视图版本" */,
        dataIndex: "tableview",
    },
];

function useInitU8DataViewHook(_useConfigData) {
    //初始化任务
    let [u8DataViews, setU8DataViews] = useState([]);
    let [u8SyncTasks, setU8SyncTasks] = useState([]);
    let [updateTaskModalShow, setUpdateTaskModalShow] = useState(false);
    let [editTaskInfo, setEditTaskInfo] = useState(false);
    let [syncTasksBatchEnd, setSyncTaskBatchEnd] = useState(false); //视图任务是否已执行同步
    let { isInit } = _useConfigData;
    useEffect(() => {
        setSyncTaskBatchEnd(isInit);
    }, [isInit]);

    const getU8DataView = useCallback(async (data) => {
        let res = await autoServiceMessage({
            service: getU8DataViewService(data),
        });
        if (res) {
            setU8DataViews(res.data || []);
        }
    }, []);
    const getU8SyncTasks = useCallback(
        async (orgcode) => {
            let res = await autoServiceMessage({
                service: initU8DataViewService({
                    orgCode: orgcode,
                    tableViews: u8DataViews.map((item) => item.tableview),
                }),
            });
            if (res) {
                setU8SyncTasks(res.data || []);
            }
        },
        [u8DataViews]
    );

    const updateSyncTask = useCallback(
        async (data, index) => {
            let res = await autoServiceMessage({
                service: updateSyncTaskService(data),
                success: window.lang.template(commonText.changeSuccess),
            });
            if (res) {
                let _task = u8SyncTasks.find((item) => item.tableview === editTaskInfo.tableview);
                _task.sqlwhere = data.sqlwhere;
                setU8SyncTasks([...u8SyncTasks]);
                setUpdateTaskModalShow(false);
                setEditTaskInfo(null);
            }
        },
        [u8SyncTasks, editTaskInfo]
    );

    const handleEdit = useCallback((taskInfo) => {
        setUpdateTaskModalShow(true);
        setEditTaskInfo(taskInfo);
    }, []);

    //同步任务列信息
    const u8SyncTaskColumns = [
        {
            title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050308") /* "视图名称" */,
            dataIndex: "dataview.datatype.typename",
        },
        {
            title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050325") /* "是否增量" */,
            dataIndex: "incrementValue",
            width: 100,
        },
        {
            title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050488") /* "编辑数据条件" */,
            dataIndex: "sqlwhere",
        },
        {
            title: window.lang.template(commonText.actions),
            dataIndex: "$$actions",
            render: (value, record) => {
                return (
                    <GridActions>
                        <GridAction fieldid="UCG-FE-routes-u8-components-IndexView-index-1207815-GridAction" onClick={handleEdit.bind(null, record)}>
                            {window.lang.template(commonText.edit)}
                        </GridAction>
                    </GridActions>
                );
            },
        },
    ];

    //执行同步任务
    const batchSyncTask = useCallback(async () => {
        let taskIds = u8SyncTasks.map((item) => item.pk_id);
        let res = await autoServiceMessage({
            service: batchSyncTaskService(taskIds),
            success: window.lang.template(commonText.operateSuccess),
        });
        if (res) {
            setSyncTaskBatchEnd(true);
        }
    }, [u8SyncTasks]);

    return {
        u8DataViews,
        getU8DataView,
        u8SyncTasks,
        getU8SyncTasks,
        batchSyncTask,
        u8SyncTaskColumns,
        updateTaskModalShow,
        editTaskInfo,
        updateSyncTask,
        setUpdateTaskModalShow,
        syncTasksBatchEnd,
    };
}

const labelCol = 130;

const IndexView = (props) => {
    const formItemLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 12 },
    };
    const [form] = FormList.useForm();
    const { getFieldProps, setFieldsValue, validateFields, getFieldError, isFieldsTouched } = form;
    const navigate = useNavigate();
    const { queryParams, connectorType } = useLocationParamsMemo();
    const connectorInfo = useMemo(() => connectorInfoMap[connectorType], [connectorType]);
    const { accountType, defaultConfigInfo, defaultConfigError, getDefaultConfig } = useDefaultConfigHook();
    const { orgList, getOrgList } = useOrgListHook();
    const _useConfigData = useConfigDataHook();
    const { isInit, configData, getConfig, saveConfig, testConnect, setDefault } = _useConfigData;
    const {
        u8DataViews,
        getU8DataView,
        u8SyncTasks,
        getU8SyncTasks,
        batchSyncTask,
        u8SyncTaskColumns,
        updateTaskModalShow,
        editTaskInfo,
        updateSyncTask,
        setUpdateTaskModalShow,
        syncTasksBatchEnd,
    } = useInitU8DataViewHook(_useConfigData);

    const { setBackConfirm } = useBackConfirm(props, isFieldsTouched());

    const [clickedDownloadClient, setClickedDownloadClient] = useState(false);

    let docObj = useMemo(
        () => ({ erpVersion: configData.u8version, instanceInfo: { configId: queryParams.configId, gatewayId: queryParams.configId } }),
        [configData]
    );
    const _useInitDoc = useInitDoc(docObj, {}); //版本号,网关ID

    useEffect(() => {
        let { configId, appId, tenantId } = queryParams;
        getDefaultConfig(tenantId);
        getOrgList();
        getConfig(configId, setFieldsValue);
    }, [queryParams]);

    useEffect(() => {
        if (defaultConfigError) {
            Modal.warning({
                title: window.lang.template(commonText.warning),
                content: `${defaultConfigError}${window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050654") /* ", 需返回添加邮箱" */}`,
                backdrop: false,
                confirmType: "one",
                onOk: () => navigate(-1),
            });
        }
    }, [defaultConfigError]);

    useEffect(() => {
        if ((configData.identifies === "SYSDB" || configData.identifies === "DBSYS") && !isInit) {
            getU8DataView({
                tenantId: defaultConfigInfo.pk_tenantid,
                appid: queryParams.appId,
                erpversion: configData.u8version,
                configid: configData.id,
            });
        }
    }, [configData, isInit, defaultConfigInfo]);

    const handleConfigBtn = useCallback(
        async (type) => {
            // validateFields((err, values) => {
            //   if (!err) {
            //     const selectedOrg = orgList.find(item => item.value === values.orgcode);
            //     values.u8openapicode = defaultConfigInfo.openapicode;
            //     values.orgname = selectedOrg ? selectedOrg.name : configData.orgname;
            //     values.pk_org = selectedOrg ? selectedOrg.id : configData.pk_org;
            //     values.id = configData.id || '';
            //     values.isdefault = configData.isdefault;
            //     if (type === 'save') {
            //       saveConfig(values).then(res => {
            //         if (res) setBackConfirm(false)
            //       })
            //     }else {
            //       values.testType = type;
            //       testConnect(values)
            //     }
            //     // let res = await autoServiceMessage()
            //   }
            // })
            validateFields().then((values) => {
                const selectedOrg = orgList.find((item) => item.value === values.orgcode);
                values.u8openapicode = defaultConfigInfo.openapicode;
                values.orgname = selectedOrg ? selectedOrg.name : configData.orgname;
                values.pk_org = selectedOrg ? selectedOrg.id : configData.pk_org;
                values.id = configData.id || "";
                values.isdefault = configData.isdefault;
                if (type === "save") {
                    saveConfig(values).then((res) => {
                        if (res) setBackConfirm(false);
                    });
                } else {
                    values.testType = type;
                    testConnect(values);
                }
                // let res = await autoServiceMessage()
            });
        },
        [configData, orgList, defaultConfigInfo]
    );

    let isNotSetConfig = useMemo(() => !configData.id, [configData]);
    let canInitTask = useMemo(() => configData.identifies === "SYSDB" || configData.identifies === "DBSYS", [configData]);

    const [downloadVersion, setDownloadVersion] = useState();
    const changeDownloadVersion = (a, b) => {
        console.log(a, b);
        setDownloadVersion(a);
    };
    const downloadClient = async (a, b) => {
        setClickedDownloadClient(true);
        // if(!downloadVersion){
        //   Error(window.lang.template('MIX_UBL_ALL_UBL_FE_LOC_00050360') /* "请选择版本" */);
        //   return;
        // }
        let res = await autoServiceMessage({
            service: downloadClientService(1.8),
        });
        if (res) {
            setClickedDownloadClient(false);
            const blob = res.data;
            const reader = new FileReader();
            reader.readAsDataURL(blob);
            reader.onload = (e) => {
                const a = document.createElement("a");
                console.log(res);
                console.log(res.headers);
                a.download = res.headers["content-disposition"].split("=")[1];
                // a.download = `文件名称.zip`;
                // 后端设置的文件名称在res.headers的 "content-disposition": "form-data; name=\"attachment\"; filename=\"20181211191944.zip\"",
                a.href = e.target.result;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
            };
            // a_download(res.data,true)
            // a_download(res.msg);
        } else {
            setClickedDownloadClient(false);
        }
    };
    return (
        <Fragment>
            <ConfigTarget logo={connectorInfo.logo} title={connectorInfo.name} intro={connectorInfo.intro} />

            <ConfigInfoList>
                <ConfigInfoItem step="1" title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050398") /* "下载U8 OpenAPI客户端" */}>
                    <div className="config-action-content">
                        {/* <FormItem fieldid="ublinker-routes-u8-components-IndexView-index-1588406-FormItem" 
              label={window.lang.template('MIX_UBL_ALL_UBL_FE_LOC_00050569')  "选择版本" }
              labelCol={{ span: 2 }}
              wrapperCol={ { span: 6 }}
              name='u8version'>
              <Select fieldid="ublinker-routes-u8-components-IndexView-index-5867538-Select" 
                data={downloadVersionArr}
                onSelect={changeDownloadVersion}/>
            </FormItem> */}
                        <Button
                            fieldid="ublinker-routes-u8-components-IndexView-index-9688886-Button"
                            className="ucg-mar-b-10"
                            disabled={clickedDownloadClient}
                            onClick={downloadClient}
                        >
                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050541") /* "点击下载" */}
                            {/* <a fieldid="ublinker-routes-u8-components-IndexView-index-806475-a" href={window.location.origin+'/iuap-ipaas-dataintegration'+getServicePath('/static/pages/connector/statics/openapi-client-setup-product.zip')}></a> */}
                        </Button>
                        <p className="config-tip">
                            {
                                window.lang.template(
                                    "MIX_UBL_ALL_UBL_FE_LOC_00050394"
                                ) /* "下载友企连U8 OpenAPI客户端，并安装在可同时访问U8服务器与外网的Windows系统中" */
                            }
                        </p>
                    </div>
                </ConfigInfoItem>

                <ConfigInfoItem step="2" title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050266") /* "登录U8 OpenAPI客户端" */}>
                    <p className="config-text">
                        <span>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050397") /* "安装客户端后，请使用" */}</span>
                        <a>{defaultConfigInfo.tenantEmail}</a>
                        <span></span>
                        <span>
                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050253") /* "登录客户端。只有开放企业数据的用户、开发者的测试账户才能登录。" */}
                        </span>
                    </p>
                    <div className="config-img">
                        <img fieldid="ublinker-routes-u8-components-IndexView-index-3247200-img" src={getLocalImg("eclogo/clientPara2.png")} alt="" />
                    </div>
                </ConfigInfoItem>

                <ConfigInfoItem step="3" title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050461") /* "U8 OpenAPI 配置说明" */}>
                    <p className="config-text">
                        {
                            window.lang.template(
                                "MIX_UBL_ALL_UBL_FE_LOC_00050260"
                            ) /* "如果应用用到了U8的API，必须配置系统类型为U8+的参数。配置U8+的参数，需要先配置好U8+的EAI。" */
                        }
                    </p>
                    <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050324") /* "系统编码为：999" */}</p>
                    <div className="config-img">
                        <img fieldid="ublinker-routes-u8-components-IndexView-index-2345896-img" src={getLocalImg("eclogo/clientPara411.png")} alt="" />
                    </div>
                    <div className="config-img">
                        <img fieldid="ublinker-routes-u8-components-IndexView-index-5111156-img" src={getLocalImg("eclogo/clientPara412.png")} alt="" />
                    </div>
                </ConfigInfoItem>

                <ConfigInfoItem step="4" title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050521") /* "输入U8配置并测试连接" */}>
                    <FormList
                        fieldid="ublinker-routes-u8-components-IndexView-index-4994344-FormList"
                        className="config-action-form"
                        form={form}
                        name="form122"
                        labelAlign="right"
                        {...formItemLayout}
                    >
                        <FormItem
                            fieldid="ublinker-routes-u8-components-IndexView-index-1386506-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050522") /* "OpenAPI账号编码" */}
                        >
                            <FormControl
                                fieldid="ublinker-routes-u8-components-IndexView-index-1017744-FormControl"
                                value={defaultConfigInfo.tenantEmail}
                                disabled
                            />
                        </FormItem>

                        <FormItem
                            fieldid="ublinker-routes-u8-components-IndexView-index-1190995-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050569") /* "选择版本" */}
                            // labelCol={labelCol} required
                            // error={getFieldError('u8version')}
                            name="u8version"
                            rules={[
                                {
                                    required: true,
                                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050360") /* "请选择版本" */,
                                },
                            ]}
                        >
                            <Select
                                fieldid="ublinker-routes-u8-components-IndexView-index-6513867-Select"
                                data={accountType ? versionArr2 : versionArr}
                                disabled={!isNotSetConfig}
                            />
                        </FormItem>

                        <FormItem
                            fieldid="ublinker-routes-u8-components-IndexView-index-5427215-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050344") /* "组织编码" */}
                            // labelCol={labelCol} required
                            // error={getFieldError('orgcode')}
                            name="orgcode"
                            rules={[
                                {
                                    required: true,
                                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050623") /* "请选择组织" */,
                                },
                            ]}
                        >
                            <Select fieldid="ublinker-routes-u8-components-IndexView-index-8112810-Select" data={orgList} disabled={!isNotSetConfig} />
                        </FormItem>

                        <FormItem
                            fieldid="ublinker-routes-u8-components-IndexView-index-1353324-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050379") /* "系统编码" */}
                            rules={[
                                {
                                    required: true,
                                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202111301358") /* "请输入" */,
                                },
                            ]}
                            name="u8code"
                        >
                            <FormControl fieldid="ublinker-routes-u8-components-IndexView-index-7289100-FormControl" />
                        </FormItem>

                        <FormItem
                            fieldid="ublinker-routes-u8-components-IndexView-index-1838948-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050492") /* "服务地址" */}
                            rules={[
                                {
                                    required: true,
                                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202111301358") /* "请输入" */,
                                },
                            ]}
                            name="u8ipaddr"
                        >
                            <FormControl fieldid="ublinker-routes-u8-components-IndexView-index-1430601-FormControl" />
                        </FormItem>

                        <FormItem
                            fieldid="ublinker-routes-u8-components-IndexView-index-5376350-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050259") /* "服务识别码" */}
                            rules={[
                                {
                                    required: true,
                                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202111301358") /* "请输入" */,
                                },
                            ]}
                            name="serviceCode"
                        >
                            <FormControl fieldid="ublinker-routes-u8-components-IndexView-index-5471562-FormControl" />
                        </FormItem>

                        <FormItem
                            fieldid="ublinker-routes-u8-components-IndexView-index-5643902-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050556") /* "EAI数据源" */}
                            rules={[
                                {
                                    required: true,
                                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202111301358") /* "请输入" */,
                                },
                            ]}
                            name="eaiDataSource"
                        >
                            <FormControl fieldid="ublinker-routes-u8-components-IndexView-index-2915863-FormControl" />
                        </FormItem>

                        <FormItem
                            fieldid="ublinker-routes-u8-components-IndexView-index-6515103-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050580") /* "数据库地址" */}
                            rules={[
                                {
                                    required: true,
                                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202111301358") /* "请输入" */,
                                },
                            ]}
                            // error={getFieldError('dbipaddr')}
                            name="dbipaddr"
                        >
                            <FormControl fieldid="ublinker-routes-u8-components-IndexView-index-213476-FormControl" />
                        </FormItem>

                        <FormItem
                            fieldid="ublinker-routes-u8-components-IndexView-index-1686656-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050458") /* "数据库账套" */}
                            rules={[
                                {
                                    required: true,
                                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202111301358") /* "请输入" */,
                                },
                            ]}
                            name="dbcode"
                        >
                            <FormControl fieldid="ublinker-routes-u8-components-IndexView-index-6517703-FormControl" />
                        </FormItem>

                        <FormItem
                            fieldid="ublinker-routes-u8-components-IndexView-index-6924948-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050323") /* "数据库账号" */}
                            rules={[
                                {
                                    required: true,
                                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202111301358") /* "请输入" */,
                                },
                            ]}
                            name="dbadmin"
                        >
                            <FormControl fieldid="ublinker-routes-u8-components-IndexView-index-2168668-FormControl" />
                        </FormItem>

                        <FormItem
                            fieldid="ublinker-routes-u8-components-IndexView-index-645123-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050448") /* "数据库密码" */}
                            rules={[
                                {
                                    required: true,
                                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202111301358") /* "请输入" */,
                                },
                            ]}
                            name="dbpwd"
                        >
                            <FormControl fieldid="ublinker-routes-u8-components-IndexView-index-5514749-FormControl" type="password" />
                        </FormItem>

                        <FormItem fieldid="ublinker-routes-u8-components-IndexView-index-9097364-FormItem" label=" " wrapperCol={{ span: 16 }}>
                            <Button
                                fieldid="ublinker-routes-u8-components-IndexView-index-6820603-Button"
                                className="ucg-mar-r-10"
                                colors="primary"
                                onClick={handleConfigBtn.bind(null, "save")}
                            >
                                {window.lang.template(commonText.save)}
                            </Button>
                            <Button
                                fieldid="ublinker-routes-u8-components-IndexView-index-3503972-Button"
                                className="ucg-mar-r-10"
                                bordered
                                disabled={isNotSetConfig}
                                onClick={handleConfigBtn.bind(null, "SYS")}
                            >
                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050482") /* "测试服务器连接" */}
                            </Button>
                            <Button
                                fieldid="ublinker-routes-u8-components-IndexView-index-7545647-Button"
                                className="ucg-mar-r-10"
                                bordered
                                disabled={isNotSetConfig}
                                onClick={handleConfigBtn.bind(null, "DB")}
                            >
                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050371") /* "测试数据库连接" */}
                            </Button>
                            <Button
                                fieldid="ublinker-routes-u8-components-IndexView-index-3134315-Button"
                                bordered
                                disabled={isNotSetConfig}
                                onClick={setDefault}
                            >
                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050280") /* "设置为主账号" */}
                            </Button>
                        </FormItem>
                    </FormList>
                </ConfigInfoItem>

                <InitDoc _useInitDoc={_useInitDoc} />

                <ConfigInfoItem step="5" title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050188") /* "初始化任务" */}>
                    {!isInit ? (
                        <Fragment>
                            <p className="">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050199") /* "数据视图" */}</p>
                            <Grid
                                fieldid="ublinker-routes-u8-components-IndexView-index-4034476-Grid"
                                empty={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050278") /* "无数据" */}
                                columns={u8DataViewColumns}
                                data={u8DataViews}
                                rowKey="tableview"
                                footer={
                                    <Button
                                        fieldid="ublinker-routes-u8-components-IndexView-index-372266-Button"
                                        disabled={!canInitTask || u8DataViews.length <= 0}
                                        colors="primary"
                                        onClick={getU8SyncTasks.bind(null, configData.orgcode)}
                                    >
                                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050188") /* "初始化任务" */}
                                    </Button>
                                }
                            />
                            <div className="config-img"></div>
                            <p className="">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050485") /* "同步任务" */}</p>
                            <Grid
                                fieldid="ublinker-routes-u8-components-IndexView-index-8443396-Grid"
                                empty={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050278") /* "无数据" */}
                                columns={u8SyncTaskColumns}
                                data={u8SyncTasks}
                                rowKey="pk_id"
                                footer={
                                    syncTasksBatchEnd ? null : (
                                        <Button
                                            fieldid="ublinker-routes-u8-components-IndexView-index-6052235-Button"
                                            disabled={!canInitTask || u8SyncTasks.length <= 0}
                                            colors="primary"
                                            onClick={batchSyncTask}
                                        >
                                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050536") /* "执行同步任务" */}
                                        </Button>
                                    )
                                }
                            />
                        </Fragment>
                    ) : null}
                    {syncTasksBatchEnd ? null : null}
                </ConfigInfoItem>
            </ConfigInfoList>

            <SqlWhereModal show={updateTaskModalShow} taskInfo={editTaskInfo} onCancel={setUpdateTaskModalShow.bind(null, false)} onOk={updateSyncTask} />
        </Fragment>
    );
};

const FormView = IndexView;

export default injectRootStore()(FormView);
