import { getInvokeService, getServicePath } from "utils/service";
import { Success, Error } from "utils/feedback";
import commonText from "constants/commonText";
/**
 * 获取默认信息 用于email tenantId等
 * @param {Object=} data
 * @param {String=} data.tenantId
 * @return {Promise<unknown>}
 */
export const getDefaultConfigInfoService = function (data) {
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/queryOpenapiConfig",
        },
        data
    );
};

/**
 * 获取组织列表
 * @return {Promise<unknown>}
 */
export const getOrgsService = function () {
    return getInvokeService({
        method: "GET",
        path: "/diwork/queryOrgvos",
    });
};

/**
 * 获取已配置信息
 * @param configId -配置id
 * @return {Promise<unknown>}
 */
export const getConfigDataService = function (configId) {
    return getInvokeService({
        method: "GET",
        path: "/diwork/ncconnect/queryU8ConfigById/" + configId,
    });
};

/**
 * 获取初始化任务表格数据
 * @param {Object} data
 * @param {String} data.tenantId
 * @param {String} data.appid
 * @param {String} data.erpversion
 * @param {String} data.configid
 * @return {Promise<unknown>}
 */
export const getU8DataViewService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/erpdata/initTask/u8dataview/get",
        },
        {},
        data
    );
};

/**
 * 初始化任务
 * @param {Object} data
 * @param {String} data.orgCode
 * @param {Array} data.tableViews
 * @return {Promise<unknown>}
 */
export const initU8DataViewService = function (data) {
    let { orgCode, tableViews } = data;
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/erpdata/task/initu8/selectedView/" + orgCode,
        },
        tableViews
    );
};

/**
 * 更新同步任务
 * @param {Object} data
 * @param {String} data.taskId
 * @param {String} data.dataversion
 * @param {String} data.increment
 * @param {String} data.sqlwhere
 * @return {Promise<unknown>}
 */
export const updateSyncTaskService = function (data) {
    let { taskId, ..._data } = data;
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/erpdata/task/update/" + taskId,
        },
        _data
    );
};

/**
 * 保存配置
 * @param {Object} data
 * @example
 {
  dbadmin: _this.dbadmin,
            dbcode: _this.dbcode,
            dbipaddr: _this.dbipaddr,
            dbpwd: _this.dbpwd,
            u8ipaddr: _this.u8ipaddr,
            serviceCode: _this.serviceCode,
            eaiDataSource: _this.eaiDataSource,
            u8version: _this.u8version,
            orgcode: _this.orgcode,
            db_scheme:'',
            sys_id:'',
            u8openapicode: _this.u8openapicode,
            orgname: _this.orgname,
            enablelog: true,
            u8code: _this.u8code,
            apiinfoid: "",
            culturename: "a",
            id: _this.u8id,
            isdefault: _this.isdefault,
            pk_org: _this.pkOrg,
 }
 */
export const saveConfigService = function (data) {
    let _data = Object.assign(data, {
        apiinfoid: "",
        culturename: "a",
        enablelog: true,
        db_scheme: "",
        sys_id: "",
    });
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/u8config/save",
        },
        _data
    );
};

/**
 * 测试连接
 * @param {Object} data
 * @param {String} data.testType=[SYS|DB] 服务器或者数据库
 * @return {Promise<unknown>}
 */
export const testConnectService = function (data) {
    let { testType, ..._data } = data;
    _data = Object.assign(_data, {
        apiinfoid: "",
        culturename: "a",
        enablelog: true,
        db_scheme: "",
        sys_id: "",
    });
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/u8config/testdiworku8/" + testType,
            timeout: 20000,
        },
        _data
    );
};

export const setDefaultService = function (data) {
    return getInvokeService({
        method: "GET",
        path: "/diwork/u8config/setdefault/" + data.id,
    });
};

//自定义档案
/**
 * 获取默认档案初始化状态
 * @param erpVersion
 * @return {Promise<unknown>}
 */
export const initDocStatusService = function (erpVersion) {
    return getInvokeService({
        method: "GET",
        path: "/diwork/defDoc/datamapping/initState/" + erpVersion,
        showLoading: false,
    });
};

/**
 * 获取默认档案列表
 * @param {Object} data
 * @param {String} data.erpVersion
 * @param {String} data.gatewayId
 * @return {Promise<unknown>}
 */
export const getDefDocService = function (data) {
    let { erpVersion, ..._data } = data;
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/defDoc/datamapping/get/" + erpVersion,
            timeout: 20000,
        },
        _data
    );
};

/**
 * 编辑自定义档案
 * @param {Object} data
 * @param {String} data.cloudName
 * @param {String} data.cloudPk
 * @param {String} data.erpName
 * @param {String} data.erpPk
 * @param {String} data.erpversion -erpVersion
 * @return {Promise<unknown>}
 */
export const editDefDocService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/defDoc/datamapping/set/",
        },
        data
    );
};

/**
 * 保存自定义档案列表
 * @param {Object} data
 * @param {String} data.erpVersion
 * @param {Array} data.erpPks
 * @return {Promise<unknown>}
 */
export const pushDefDocService = function (data) {
    let { erpVersion, erpPks } = data;
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/defDoc/datamapping/push/" + erpVersion,
            showLoading: false,
        },
        erpPks
    );
};

/**
 * 下载U8 OpenAPI客户端
 * @param {Object} data
 * @param {String} data.erpVersion
 * @param {Array} data.erpPks
 * @return {Promise<unknown>}
 */
export const downloadClientService = function (version) {
    return getInvokeService({
        method: "GET",
        path: `/mygwapp/ncconnect/openapiclient/download/ossurl/${version}`,
        responseType: "blob",
        timeout: 300000,
        showLoading: () => {
            Success(window.lang.template(commonText.downing));
        },
    });
};
