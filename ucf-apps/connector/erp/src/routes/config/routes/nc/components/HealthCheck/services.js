import { getInvokeService } from "utils/service";

/***
 * 检查网关运行状态
 * @param {Object} data
 * @param {String} data.gatewayId
 * @returns {*}
 */
export const gatewayRunStateService = function (data) {
    return getInvokeService(
        {
            method: "GET",
            path: "/mygwapp/gateway/state",
            showLoading: false,
        },
        data
    );
};

/***
 * 租户检查系统级适配器运行状态
 * @param {Object} data
 * @param {String} data.gatewayId
 * @returns {*}
 */
export const gwAdapterRunStatusService = function (data) {
    return getInvokeService(
        {
            method: "GET",
            path: "/mygwapp/gateway/adapter/state",
            showLoading: false,
        },
        data
    );
};
