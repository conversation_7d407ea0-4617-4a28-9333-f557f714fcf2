import React, { useState, useEffect, useMemo, useCallback, forwardRef, useImperativeHandle } from "react";
import Grid from "components/TinperBee/Grid";
import { GridActions, GridAction } from "components/TinperBee/Grid/GridActions";
import { ConfigInfoItem } from "../../../components/ConfigInfo";
import { getAdapterListService, checkAdapterStatusService } from "./services";
import commonText from "constants/commonText";
import ConfigModal from "./ConfigModal";
import { PRIVATE } from "utils/util";
import { Success, Error } from "utils/feedback";
import { formatTextArea } from "utils/index";
const useAdapters = (instanceInfo, _useGwConfigInfo) => {
    const { gatewayId, useMainTenantGateway } = instanceInfo;
    /** 适配器列表 */
    const [adapterList, setAdapterList] = useState([]);

    /** 待更新/部署 适配器 */
    const [updateAdapterInfo, setUpdateAdapter] = useState(null);

    /** 适配器操作action hide|deploy|undeploy|config */
    const [updateAction, setUpdateAction] = useState("hide");

    /** 适配器组件相关hooks 参数变化列表 */
    const useInputs = [gatewayId];

    const getAdapterList = useCallback((gwId) => {
        getAdapterListService(gwId).then((res) => {
            setAdapterList(res.data || []);
        });
    }, []);

    useEffect(() => {
        if (gatewayId) {
            getAdapterList(gatewayId);
        }
    }, useInputs);

    const handleCancel = useCallback((res) => {
        setUpdateAction("hide");
        setUpdateAdapter(null);
        if (res) {
            getAdapterList(gatewayId);
            getGatewayConfig();
        }
    }, useInputs);

    const handleUpdate = useCallback(async (adapter, action) => {
        setUpdateAdapter(adapter);
        setUpdateAction(action);
    }, useInputs);

    const checkAdapterStatus = useCallback(() => {
        checkAdapterStatusService({ gatewayId, useMainTenantGateway })
            .then(() => {
                Success(window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050764") /* "适配器运行正常" */);
            })
            .catch((err) => {
                Error(formatTextArea(err.msg), 5);
            });
    }, useInputs);

    const adapterGridColumns = useMemo(() => {
        return [
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050170") /* "名称" */,
                dataIndex: "name",
                width: 300,
                render: (value, record) => {
                    return value + `(${record.appid})`;
                },
            },
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050004") /* "状态" */,
                dataIndex: "state",
                width: 100,
                render: (value) => {
                    return value === "ONLINE" ? (
                        <span className="mix-status-tag do-success">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050743") /* "已部署" */}</span>
                    ) : (
                        "-"
                    );
                },
            },
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050741") /* "运行版本" */,
                dataIndex: "adapterversion",
                width: 110,
                render: Grid.renderText,
            },
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050780") /* "最新版本" */,
                dataIndex: "adapternewversion",
                width: 110,
                render: Grid.renderText,
            },
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050734") /* "部署时间" */,
                dataIndex: "starton",
                width: 180,
            },
            {
                title: window.lang.template(commonText.actions),
                dataIndex: "$$actions",
                width: 200,
                fixed: "right",
                render: (value, record) => {
                    return (
                        <GridActions>
                            <GridAction
                                fieldid="UCG-FE-routes-nc-components-Adapters-index-8854134-GridAction"
                                onClick={handleUpdate.bind(null, record, "deploy")}
                            >
                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050750") /* "部署" */}
                            </GridAction>
                            <GridAction
                                fieldid="UCG-FE-routes-nc-components-Adapters-index-6894606-GridAction"
                                onClick={handleUpdate.bind(null, record, "config")}
                            >
                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050766") /* "更新" */}
                            </GridAction>
                            <GridAction
                                fieldid="UCG-FE-routes-nc-components-Adapters-index-938362-GridAction"
                                onClick={handleUpdate.bind(null, record, "undeploy")}
                            >
                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050744") /* "卸载" */}
                            </GridAction>
                        </GridActions>
                    );
                },
            },
        ];
    }, useInputs);

    return {
        adapterGridColumns,
        adapterList,
        updateAdapterInfo,
        updateAction,
        handleCancel,
        checkAdapterStatus,
        handleUpdate,
    };
};

const AdaptersView = (props, ref) => {
    const { instanceInfo, getGatewayConfig } = props;
    const { adapterGridColumns, adapterList, updateAdapterInfo, updateAction, handleCancel, checkAdapterStatus, handleUpdate } = useAdapters(
        instanceInfo,
        getGatewayConfig
    );

    useImperativeHandle(ref, () => {
        return {
            handleUpdateAdapter: handleUpdate,
        };
    });

    return (
        <>
            {PRIVATE ? null : (
                <ConfigInfoItem
                    title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050771") /* '网关适配器管理' */}
                    action={
                        <GridActions className="ucg-float-r">
                            <GridAction fieldid="UCG-FE-routes-nc-components-Adapters-index-623683-GridAction" onClick={checkAdapterStatus}>
                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050787") /*'检查适配器状态' */}
                            </GridAction>
                        </GridActions>
                    }
                >
                    <Grid fieldid="ublinker-routes-nc-components-Adapters-index-2168742-Grid" columns={adapterGridColumns} data={adapterList} />

                    <ConfigModal
                        gatewayId={instanceInfo.gatewayId}
                        adapterId={updateAdapterInfo ? updateAdapterInfo.appid : ""}
                        adapterName={updateAdapterInfo ? updateAdapterInfo.name : ""}
                        action={updateAction}
                        onCancel={handleCancel}
                    />
                </ConfigInfoItem>
            )}
        </>
    );
};

export default forwardRef(AdaptersView);
