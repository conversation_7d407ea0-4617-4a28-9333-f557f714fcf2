import React, { useRef } from "react";
import { Tooltip } from "components/TinperBee";
import { ConfigInfoItem } from "../../../components/ConfigInfo";
import GatewayConfigForm from "connector/erp/components/GatewayConfig";

const SetGwConfigView = (props) => {
    const { instanceInfo, gwConfigInfo, setErpConfigDisabled } = props;

    return (
        <ConfigInfoItem
            title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050751") /* "输入网关配置并测试网关连接" */}
            subTitle={
                <Tooltip
                    fieldid="ublinker-routes-nc-components-SetGwConfig-index-6131771-Tooltip"
                    className="erp-config-tooltip"
                    inverse
                    placement="topLeft"
                    overlay={
                        <div>
                            <p>
                                {
                                    window.lang.template(
                                        "MIX_UBL_ALL_UBL_FE_LOC_00050748"
                                    ) /* "ERP需要通过网关访问云服务，因此要确保网关IP和端口对ERP服务器开放。" */
                                }
                            </p>
                            <p>
                                {
                                    window.lang.template(
                                        "MIX_UBL_ALL_UBL_FE_LOC_00050770"
                                    ) /* "所以请务必选择（或手动输入）部署网关的服务器的正确IP地址和端口。" */
                                }
                            </p>
                            <p>
                                {
                                    window.lang.template(
                                        "MIX_UBL_ALL_UBL_FE_LOC_00050762"
                                    ) /* "IP和端口仅用于在内网中ERP与网关通信，不需要对外开放，安全性可保障。" */
                                }
                            </p>
                            <p>
                                {
                                    window.lang.template(
                                        "MIX_UBL_ALL_UBL_FE_LOC_00050738"
                                    ) /* "如果测试网关连接异常，将只影响ERP访问云端的业务，基础档案数据同步不会受到影响。" */
                                }
                            </p>
                        </div>
                    }
                >
                    <i fieldid="ublinker-routes-nc-components-SetGwConfig-index-276958-i" className="cl cl-Q ucg-mar-l-10" style={{ color: "#505766" }} />
                </Tooltip>
            }
        >
            <GatewayConfigForm
                setErpConfigDisabled={setErpConfigDisabled}
                gatewayId={instanceInfo.gatewayId}
                useMainTenantGateway={instanceInfo.useMainTenantGateway}
                gatewayConfig={gwConfigInfo}
                layoutOpt={{ md: 12 }}
                className="config-action-form"
            />
        </ConfigInfoItem>
    );
};

export default SetGwConfigView;
