import React, { useState, useEffect, useCallback, useRef, useMemo } from "react";
import { PRIVATE } from "utils/util";
import { Button, FormControl, Modal, Select, Icon, Tooltip, FormList } from "components/TinperBee";
import ModalView from "components/TinperBee/Modal";
import Grid from "components/TinperBee/Grid";
import { autoServiceMessage } from "utils/service";

import { injectRootStore } from "core/addStore";
import useBackConfirm from "hooks/useBackConfirm";
import { ConfirmDefaultGwToTask } from "services/gwServices";
import { ConfigInfoItem } from "../../../components/ConfigInfo";
import PushGwConfigToApp from "../SetGwConfig/PushGwConfigToApp";
import TestCard from "connector/erp/components/TestCard";
import { testGwToErpService } from "services/gwServices";
import { getConfigService, getHasConfigInfoService, saveConfigService, pushYhtService, testConnectService, setDefaultService } from "../../services";
import { originReg } from "utils/regExp";
import erpIcon from "connector/erp/images/erp_icon.png";
import gwIcon from "connector/erp/images/gw_icon.png";
import serIcon from "connector/erp/images/ser_icon.png";

import commonText from "constants/commonText";

import "./index.less";
import { useStaticRendering } from "mobx-react";
const FormItem = FormList.Item;

export function useErpConfig(_useSetConnector, connectorType, queryParams) {
    let { instanceInfo, connectorName, erpVersion } = _useSetConnector;
    let { gatewayId, useMainTenantGateway } = instanceInfo;

    const [configId, setConfigId] = useState("");

    /** 测试连接接口返回的groupPk 推送友互通接口需要 */
    const [testGroupPk, setTestGroupPk] = useState("");

    /** erp配置信息 */
    let [erpConfig, setErpConfig] = useState({ ipaddr: "", busicode: "", groupcode: "", groupadmin: "" });

    /** 点击获取参数按钮 可选参列表 */
    let [oldConfig, setOldConfig] = useState({ configList: [], selectedList: [], oldConfigModalShow: false });

    let [buttonDisabled, setButtonDisabled] = useState(!_useSetConnector.hasCreate);

    useEffect(() => {
        setButtonDisabled(!_useSetConnector.hasCreate);
    }, [_useSetConnector.hasCreate]);

    /** 是否应该点击测试按钮 */
    let [shouldSave, setShouldSave] = useState(!configId);

    /**  是否应该点击保存按钮 */
    let [shouldTest, setShouldTest] = useState(!configId);

    /** 获取已配置erp信息 */
    const getConfig = useCallback(async (gwId, data) => {
        let res = await autoServiceMessage({ service: getConfigService(gwId, data) });
        if (res) {
            let { ncipaddr = "", ncbusicode = "", ncgroupcode = "", ncgroupadmin = "" } = res.data || {};
            setConfigId(res.data ? res.data.id : null);
            setErpConfig({
                ipaddr: ncipaddr,
                busicode: ncbusicode,
                groupcode: ncgroupcode,
                groupadmin: ncgroupadmin,
                appPubKey: connectorType == "YonBipMajorHandler" && res.data.appPubKey,
                appSecret: connectorType == "YonBipMajorHandler" && res.data.appSecret,
            });
        }
    });

    /** 自动获取erp配置信息 */
    useEffect(() => {
        if (instanceInfo.gatewayId) {
            getConfig(instanceInfo.gatewayId, { connectId: instanceInfo.connectId });
        }
    }, [instanceInfo.gatewayId]);

    /** 获取nc/ncc/u8c系统配置配置列表 账套编码 集团编码 等 */
    const getOldConfig = useCallback(
        (values) => {
            return getHasConfigInfoService(
                {
                    ncurl1: values.ipaddr,
                    gatewayId: gatewayId,
                },
                false,
                useMainTenantGateway
            ).then((res) => {
                if (res && res.data && res.data.length > 0) {
                    setOldConfig({
                        configList: res.data,
                        selectedList: [],
                        oldConfigModalShow: true,
                    });
                }
            });
        },
        [gatewayId]
    );

    /** 测试erp连接 */
    const testInputs = [gatewayId];
    const testConnect = useCallback(async (values) => {
        let res = await autoServiceMessage({
            service: testConnectService(
                {
                    gatewayId: gatewayId,
                    ...values,
                },
                useMainTenantGateway
            ),
            success: window.lang.template(commonText.testSuccess),
        });
        if (res) {
            const resData = res.data || {};
            setShouldTest(false);
            setTestGroupPk(resData.groupPk || "");
        }
    }, testInputs);

    /** 保存erp配置 */
    const saveInputs = [gatewayId, configId, connectorName, testGroupPk, erpVersion];

    /** 将配置推送友互通 */
    const pushYht = useCallback(async (configId, groupPk) => {
        return await autoServiceMessage({
            service: pushYhtService({ configId, groupPk }, useMainTenantGateway),
        });
    }, []);

    const saveConfig = useCallback(async (values) => {
        let requestData = {
            configid: configId,
            gwguid: gatewayId,
            servertype: connectorType,
            alias: connectorName,
            ...values,
        };
        let saveRes = await autoServiceMessage({
            service: saveConfigService(requestData, { connectId: instanceInfo.connectId }),
            success: window.lang.template(commonText.saveSuccess),
        });
        if (saveRes) {
            setShouldSave(false);
            setErpConfig({
                ...values,
            });

            let noConfigId = !configId;
            let tempConfigId = "";

            if (noConfigId) {
                setConfigId(saveRes.data.configid);
                noConfigId = true;
                tempConfigId = saveRes.data.configid;
                /** 保存后将configId 同步到连接器 */
            } else {
                tempConfigId = configId;
            }

            //推送yht
            let res = await pushYht(tempConfigId, testGroupPk);

            if (res) {
                _useSetConnector.createConnect({
                    ...instanceInfo,
                    configId: tempConfigId,
                });
            }
        }
        return saveRes;
    }, saveInputs);

    const setDefault = useCallback(async (isInitTaskView) => {
        let res = await autoServiceMessage({
            service: setDefaultService({ configid: configId, gatewayId: gatewayId }),
            success: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050370") /* "设置默认成功!" */,
        });

        if (res) {
            //推送yht
            const pushYhtRes = await pushYht(configId, testGroupPk);
            if (pushYhtRes && isInitTaskView) {
                ConfirmDefaultGwToTask(gatewayId);
            }
        }
    }, saveInputs);

    return {
        instanceInfo,
        erpConfig,
        setErpConfig,
        oldConfig,
        setOldConfig,
        getOldConfig,
        buttonDisabled,
        setButtonDisabled,
        shouldSave,
        setShouldSave,
        shouldTest,
        setShouldTest,
        saveConfig,
        saveInputs,
        testConnect,
        testInputs,
        setDefault,
        testGroupPk,
    };
}

const steps = [
    {
        name: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050144") /* "网关" */,
        icon: <img fieldid="ublinker-routes-nc-components-ErpConfig-index-6477316-img" src={gwIcon} />,
    },
    {
        name: "",
        icon: <img fieldid="ublinker-routes-nc-components-ErpConfig-index-8616205-img" src={serIcon} />,
    },
    {
        name: "ERP",
        icon: <img fieldid="ublinker-routes-nc-components-ErpConfig-index-8120982-img" src={erpIcon} />,
    },
];

const ErpConfigView = (props) => {
    const [form] = FormList.useForm();

    const formItemLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 12 },
    };
    const { connectorInfo, labelCol, erpVersion, erpVersions, _useErpConfig, isInitTaskView, gwIpNotSave, connectorType } = props;
    const { setFieldsValue, getFieldProps, validateFields, getFieldError, getFieldValue, getFieldsValue, isFieldsTouched } = form;
    const [testCardShow, setTestCardShow] = useState(false);
    const handleTestShowChange = useCallback((showState) => {
        setTestCardShow(showState);
    }, []);

    const { setBackConfirm } = useBackConfirm(props, isFieldsTouched());

    const {
        instanceInfo,
        erpConfig,
        oldConfig,
        setOldConfig,
        getOldConfig,
        buttonDisabled,
        shouldSave,
        setShouldSave,
        shouldTest,
        setShouldTest,
        saveConfig,
        saveInputs,
        testConnect,
        testInputs,
        setDefault,
    } = _useErpConfig;
    const [ipaddrDisable, setIpaddrDisable] = useState("");

    /** 当配置信息发生变化，重新设置配置信息form值 */
    useEffect(() => {
        setFieldsValue(erpConfig);
        setIpaddrDisable(erpConfig.ipaddr);
    }, [erpConfig]);

    useEffect(() => {
        setFieldsValue({ version: erpVersion });
    }, [erpVersion]);

    /** 测试连接窗口ref */
    const erpTestCardNode = useRef(null);

    /** 点击获取参数 */
    const handleGetOldConfig = useCallback(() => {
        // validateFields(['ipaddr'], (error, values) => {
        //     if (!error) {
        //         if (testCardShow) {
        //             erpTestCardNode.current.refresh()
        //         } else {
        //             handleTestShowChange(true)
        //         }
        //     }
        // })
        validateFields(["ipaddr"]).then((values) => {
            if (testCardShow) {
                erpTestCardNode.current.refresh();
            } else {
                handleTestShowChange(true);
            }
        });
    }, [testCardShow]);

    /** 参数配置列表窗台取消onCancel */
    const handleOldConfigModalCancel = useCallback(() => {
        setOldConfig({
            configList: [],
            oldConfigModalShow: false,
            selectedList: [],
        });
    }, []);

    /** 参数配置列表窗台确认onOk */
    const handleOldConfigModalOk = useCallback(() => {
        let { selectedList } = oldConfig;
        let data = selectedList[0];
        setFieldsValue({
            busicode: data.accountcode,
            groupcode: data.groupcode,
        });
        setShouldSave(true);
        setShouldTest(true);
        setBackConfirm(true);
        handleOldConfigModalCancel();
    }, [oldConfig]);

    /** 参数配置列表选择回调 */
    const getOldConfigSelectedDataFunc = useCallback(
        (selectedList) => {
            setOldConfig({ ...oldConfig, selectedList: selectedList });
        },
        [oldConfig]
    );

    const handleConfigChange = useCallback((value, type) => {
        console.log(value, type);
        if (type == "ipaddr") {
            setIpaddrDisable(value);
        }
        setShouldSave(true);
        setShouldTest(true);
    }, []);

    /** 点击测试连接按钮 */
    const handleTestConnect = useCallback(() => {
        // validateFields((error, values) => {
        //     if (!error) {
        //         values.address = values.ipaddr;
        //         delete values.ipaddr
        //         testConnect(values)
        //     }
        // })
        validateFields().then((values) => {
            values.address = values.ipaddr;
            values.connectId = instanceInfo.connectId;
            delete values.ipaddr;
            testConnect(values);
        });
    }, testInputs);

    /** 点击保存按钮 */
    const handleSave = useCallback(() => {
        // validateFields((error, values) => {
        //     if (!error) {
        //         values.version = erpVersion;
        //         saveConfig(values).then(res => {
        //             if (res) {
        //                 setBackConfirm(false)
        //             }
        //         });
        //     }
        // })
        validateFields().then((values) => {
            values.version = erpVersion;
            saveConfig(values).then((res) => {
                if (res) {
                    setBackConfirm(false);
                }
            });
        });
    }, saveInputs);

    const erpTestCardTargetNode = useRef(null);

    const stepExpresses = useMemo(() => {
        return [
            {
                successText: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050779") /* "网络连接成功" */,
                failText: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050777") /* "网络连接失败" */,
                pendingText: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050765") /* "连接中" */,
                promise: () => {
                    return new Promise((resolve, reject) => {
                        testGwToErpService(
                            {
                                gatewayId: instanceInfo.gatewayId,
                                erpAddress: getFieldValue("ipaddr"),
                            },
                            false,
                            instanceInfo.useMainTenantGateway
                        )
                            .then((res) => {
                                resolve("success", "");
                            })
                            .catch(reject);
                    });
                },
            },
            {
                successText: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050786") /* "参数获取成功" */,
                failText: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050754") /* "参数获取失败" */,
                pendingText: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050794") /* "获取中" */,
                promise: () => {
                    return getOldConfig({ ipaddr: getFieldValue("ipaddr") });
                },
            },
        ];
    }, [instanceInfo.gatewayId]);

    const isNCC2005 = useMemo(
        () => erpVersion === "ncc_2005_native" || erpVersion === "ncc_2105_native" || erpVersion === "ncc_2111_native" || erpVersion === "yonbip_202207",
        [erpVersion]
    );

    return (
        <ConfigInfoItem
            title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050495", connectorInfo) /* "输入<%= name %>配置并测试连接" */}
            subTitle={
                <Tooltip
                    fieldid="ublinker-routes-nc-components-ErpConfig-index-8026402-Tooltip"
                    className="erp-config-tooltip"
                    inverse
                    placement="topLeft"
                    overlay={
                        <div>
                            <p>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050793") /* "云服务需要通过网关访问ERP服务，请输入正确的ERP配置。" */}</p>
                            <p>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050797") /* "如果测试连接异常，将会影响从云端访问ERP的相关业务。" */}</p>
                        </div>
                    }
                >
                    <i fieldid="ublinker-routes-nc-components-ErpConfig-index-4137641-i" className="cl cl-Q ucg-mar-l-10" style={{ color: "#505766" }} />
                </Tooltip>
            }
            connectorInfo={connectorInfo}
        >
            <FormList
                fieldid="ublinker-routes-nc-components-ErpConfig-index-2787473-FormList"
                className="config-action-form"
                form={form}
                name="form122"
                labelAlign="right"
                {...formItemLayout}
                layoutOpt={{ md: 12 }}
            >
                <div className="ipaddrBox">
                    <FormItem
                        fieldid="ublinker-routes-nc-components-ErpConfig-index-8291494-FormItem"
                        label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050637") /* "服务器地址" */}
                        // required
                        // labelCol={labelCol}
                        // error={getFieldError('ipaddr')}
                        // labelAlign={'top'}
                        name="ipaddr"
                        rules={[
                            {
                                required: true,
                                message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050571") /* "请输入服务器地址" */,
                            },
                            {
                                pattern: originReg,
                                message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050774") /* "服务器地址格式错误，请检查" */,
                            },
                        ]}
                    >
                        <FormControl
                            fieldid="ublinker-routes-nc-components-ErpConfig-index-9220133-FormControl"
                            className="ucg-mar-r-5"
                            disabled={buttonDisabled}
                            placeholder={
                                window.lang.template(
                                    "MIX_UBL_ALL_UBL_FE_LOC_00050367",
                                    connectorInfo
                                ) /* "请输入<%= name %>内网可以访问地址如http://************:3265" */
                            }
                            // {...getFieldProps('ipaddr', {
                            //     initialValue: '',
                            //     rules: [{
                            //         required: true, message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050571") /* "请输入服务器地址" */,
                            //     }, {
                            //         pattern: originReg, message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050774") /* "服务器地址格式错误，请检查" */
                            //     }],
                            // normalize:value => value.trim()
                            // onChange:handleConfigChange
                            // })}
                            normalize={(value) => value.trim()}
                            onChange={(value) => {
                                handleConfigChange(value, "ipaddr");
                            }}
                            ref={erpTestCardTargetNode}
                        />
                    </FormItem>
                    <div className="ipaddrBox-btn">
                        <Button
                            fieldid="ublinker-routes-nc-components-ErpConfig-index-4410023-Button"
                            bordered
                            // disabled={!getFieldsValue(['ipaddr'])}
                            disabled={!ipaddrDisable}
                            onClick={handleGetOldConfig}
                        >
                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050255") /* "获取参数" */}
                        </Button>
                    </div>
                    <div className="ipaddrBox-card">
                        <TestCard
                            ref={erpTestCardNode}
                            targetNode={erpTestCardTargetNode}
                            style={{ width: 410 }}
                            show={testCardShow}
                            steps={steps}
                            stepExpresses={stepExpresses}
                            onClose={handleTestShowChange.bind(null, false)}
                        />
                    </div>
                </div>

                <FormItem
                    fieldid="ublinker-routes-nc-components-ErpConfig-index-9912833-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050443") /* "版本" */}
                    name="version"
                    //  labelCol={labelCol}
                >
                    <Select
                        fieldid="ublinker-routes-nc-components-ErpConfig-index-4728344-Select"
                        disabled
                        data={erpVersions}
                        value={erpVersion}
                        // {...getFieldProps('version')}
                    />
                </FormItem>

                <FormItem
                    fieldid="ublinker-routes-nc-components-ErpConfig-index-8043637-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050433") /* "账套编码" */}
                    // required
                    // labelCol={labelCol}
                    // error={getFieldError('busicode')}
                    name="busicode"
                    initialValue="nc65"
                    rules={[
                        {
                            required: true,
                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050553") /* "请输入账套编码" */,
                        },
                    ]}
                >
                    <FormControl
                        fieldid="ublinker-routes-nc-components-ErpConfig-index-4147595-FormControl"
                        disabled={buttonDisabled}
                        placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050585") /* "系统帐套编码" */}
                        // {...getFieldProps('busicode', {
                        //     initialValue: 'nc65',
                        //     rules: [{
                        //         required: true, message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050553") /* "请输入账套编码" */
                        //     }],
                        //     onChange: handleConfigChange
                        // })}
                        onChange={handleConfigChange}
                    />
                </FormItem>

                <FormItem
                    fieldid="ublinker-routes-nc-components-ErpConfig-index-3299717-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050283") /* "集团编码" */}
                    // required
                    // labelCol={labelCol}
                    // error={getFieldError('groupcode')}
                    name="groupcode"
                    initialValue="nc65"
                    rules={[
                        {
                            required: true,
                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050540") /* "请输入默认集团编码" */,
                        },
                    ]}
                >
                    <FormControl
                        fieldid="ublinker-routes-nc-components-ErpConfig-index-1652844-FormControl"
                        disabled={buttonDisabled}
                        placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050318") /* "默认集团编码" */}
                        // {...getFieldProps('groupcode', {
                        //     initialValue: 'nc65',
                        //     rules: [{
                        //         required: true, message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050540") /* "请输入默认集团编码" */
                        //     }],
                        //     onChange: handleConfigChange
                        // })}
                        onChange={handleConfigChange}
                    />
                </FormItem>

                <div className="groupadminBox">
                    <FormItem
                        fieldid="ublinker-routes-nc-components-ErpConfig-index-817405-FormItem"
                        label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050582") /* "用户编码" */}
                        // labelCol={labelCol}
                        // required={isNCC2005}
                        // error={getFieldError('groupadmin')}
                        name="groupadmin"
                        rules={[
                            {
                                required: isNCC2005,
                                message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050740") /* "请输入用户编码" */,
                            },
                        ]}
                    >
                        <FormControl
                            fieldid="ublinker-routes-nc-components-ErpConfig-index-6696127-FormControl"
                            disabled={buttonDisabled}
                            placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050564") /* "默认用户编码" */}
                            // {...getFieldProps('groupadmin', {
                            //     initialValue: '',
                            //     rules: [{
                            //         required: isNCC2005, message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050740") /* "请输入用户编码" */
                            //     }],
                            //     onChange: handleConfigChange
                            // })}
                            onChange={handleConfigChange}
                        />
                    </FormItem>
                    <div className="groupadminBox-tip">
                        <Tooltip
                            fieldid="ublinker-routes-nc-components-ErpConfig-index-1347627-Tooltip"
                            inverse
                            overlay={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050768") /* "尽量输入稳定不变的用户，比如系统管理员或者集团管理员。" */}
                        >
                            <i fieldid="ublinker-routes-nc-components-ErpConfig-index-6783196-i" className="cl cl-Q ucg-pad-l-5" style={{ color: "#505766" }} />
                        </Tooltip>
                    </div>
                </div>
                {connectorType == "YonBipMajorHandler" && (
                    <>
                        <div className="groupadminBox">
                            <FormItem
                                fieldid="ublinker-routes-nc-components-ErpConfig-index-8281853-FormItem"
                                label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226241628") /* "应用公钥" */}
                                name="appPubKey"
                                rules={[
                                    {
                                        required: true,
                                        message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226241629") /* "请输入应用公钥" */,
                                    },
                                ]}
                            >
                                <FormControl
                                    fieldid="ublinker-routes-nc-components-ErpConfig-index-1279620-FormControl"
                                    disabled={buttonDisabled}
                                    placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226241628") /* "应用公钥" */}
                                    onChange={handleConfigChange}
                                />
                            </FormItem>
                            <div className="groupadminBox-tip">
                                <Tooltip
                                    fieldid="ublinker-routes-nc-components-ErpConfig-index-1347627-Tooltip"
                                    inverse
                                    overlay={
                                        window.lang.template(
                                            "MIX_UBL_ALL_UBL_FE_LOC_20229221840"
                                        ) /* "尽量输入稳定不变的用户，比如系统管理员或者集团管理员。" */
                                    }
                                >
                                    <i
                                        fieldid="ublinker-routes-nc-components-ErpConfig-index-6783196-i"
                                        className="cl cl-Q ucg-pad-l-5"
                                        style={{ color: "#505766" }}
                                    />
                                </Tooltip>
                            </div>
                        </div>
                        <div className="groupadminBox">
                            <FormItem
                                fieldid="ublinker-routes-nc-components-ErpConfig-index-7485024-FormItem"
                                label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226241630") /* "应用密钥" */}
                                name="appSecret"
                                rules={[
                                    {
                                        required: true,
                                        message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226241631") /* "请输入应用密钥" */,
                                    },
                                ]}
                            >
                                <FormControl
                                    fieldid="ublinker-routes-nc-components-ErpConfig-index-2003197-FormControl"
                                    disabled={buttonDisabled}
                                    placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226241630") /* "应用密钥" */}
                                    onChange={handleConfigChange}
                                />
                            </FormItem>
                            <div className="groupadminBox-tip">
                                <Tooltip
                                    fieldid="ublinker-routes-nc-components-ErpConfig-index-1347627-Tooltip"
                                    inverse
                                    overlay={
                                        window.lang.template(
                                            "MIX_UBL_ALL_UBL_FE_LOC_20229221841"
                                        ) /* "尽量输入稳定不变的用户，比如系统管理员或者集团管理员。" */
                                    }
                                >
                                    <i
                                        fieldid="ublinker-routes-nc-components-ErpConfig-index-6783196-i"
                                        className="cl cl-Q ucg-pad-l-5"
                                        style={{ color: "#505766" }}
                                    />
                                </Tooltip>
                            </div>
                        </div>
                    </>
                )}
                <FormItem fieldid="ublinker-routes-nc-components-ErpConfig-index-1489210-FormItem" label=" " wrapperCol={{ span: 14 }}>
                    {/* 测试链接 */}
                    <Button
                        fieldid="ublinker-routes-nc-components-ErpConfig-index-6420131-Button"
                        className="ucg-mar-r-10"
                        colors="primary"
                        disabled={buttonDisabled}
                        onClick={handleTestConnect}
                    >
                        {window.lang.template(commonText.testConnect)}
                    </Button>
                    {/* 保存修改 */}
                    <Button
                        fieldid="ublinker-routes-nc-components-ErpConfig-index-8817801-Button"
                        className="ucg-mar-r-10"
                        colors="secondary"
                        disabled={buttonDisabled || shouldTest}
                        onClick={handleSave}
                    >
                        {window.lang.template(commonText.saveChange)}
                    </Button>
                    {instanceInfo.useMainTenantGateway !== "true" ? (
                        <Button
                            fieldid="ublinker-routes-nc-components-ErpConfig-index-2701270-Button"
                            colors="secondary"
                            disabled={buttonDisabled || shouldSave || shouldTest}
                            onClick={setDefault.bind(null, isInitTaskView)}
                        >
                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050481") /* "设置默认" */}
                        </Button>
                    ) : null}
                    {PRIVATE ? (
                        <PushGwConfigToApp gatewayId={instanceInfo.gatewayId}>
                            <Button
                                fieldid="ublinker-routes-nc-components-ErpConfig-index-134416-Button"
                                className="ucg-mar-l-10"
                                colors="secondary"
                                disabled={buttonDisabled || shouldSave || shouldTest}
                            >
                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050921") /* "推送网关配置" */}
                            </Button>
                        </PushGwConfigToApp>
                    ) : null}
                </FormItem>
            </FormList>

            <ModalView
                title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050621") /* "选择配置参数" */}
                show={oldConfig.oldConfigModalShow}
                onCancel={handleOldConfigModalCancel}
                onOk={handleOldConfigModalOk}
                size="md"
            >
                {oldConfig.oldConfigModalShow ? (
                    <Grid
                        fieldid="ublinker-routes-nc-components-ErpConfig-index-8873502-Grid"
                        columns={[
                            {
                                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050433") /* "账套编码" */,
                                dataIndex: "accountcode",
                            },
                            {
                                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050283") /* "集团编码" */,
                                dataIndex: "groupcode",
                            },
                        ]}
                        radioSelect
                        rowKey="id"
                        selectedList={oldConfig.selectedList}
                        getSelectedDataFunc={getOldConfigSelectedDataFunc}
                        data={oldConfig.configList}
                    />
                ) : null}
            </ModalView>
        </ConfigInfoItem>
    );
};

const FormErpConfigView = ErpConfigView;

export default injectRootStore()(FormErpConfigView);
