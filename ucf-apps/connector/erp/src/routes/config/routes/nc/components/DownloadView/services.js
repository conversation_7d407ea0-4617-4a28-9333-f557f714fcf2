import { getInvokeService, getServicePath } from "utils/service";
import { a_download } from "utils/index";
import _template from "lodash/template";

/**
 * 获取nc|ncc|u8c补丁下载地址
 * @param erpVersion
 * @returns {*}
 */
export const downloadAdapterService = function (erpVersion) {
    let download_path = "";
    if (erpVersion.startsWith("nc")) {
        download_path = "ncpath";
    } else if (erpVersion.startsWith("u8c")) {
        download_path = "u8cpath";
    }
    return getInvokeService({
        method: "GET",
        path: `/mygwapp/ncconnect/${download_path}/download/ossurl/${erpVersion}`,
        responseType: "blob",
    });
};
export const downloadAdapter = function (erpVersion) {
    let download_path = "";
    if (erpVersion.startsWith("nc")) {
        download_path = "ncpath";
    } else if (erpVersion.startsWith("u8c")) {
        download_path = "u8cpath";
    }
    let url =
        window.location.origin + "/iuap-ipaas-dataintegration" + getServicePath("/mygwapp/ncconnect/<%= download_path %>/download/ossurl/<%= erpVersion %>");
    a_download(_template(url)({ download_path, erpVersion }), true);
};
