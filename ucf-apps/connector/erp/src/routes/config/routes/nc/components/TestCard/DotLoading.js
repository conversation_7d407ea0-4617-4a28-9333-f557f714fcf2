import React, { useEffect, useState, useCallback } from "react";

const DotLoading = (props) => {
    const { dotNum = 3 } = props;
    const [dot, setDot] = useState("");

    const dotTimerFunc = useCallback(() => {
        if (dot.length === dotNum) {
            setDot("");
        } else {
            setDot(dot + ".");
        }
    }, [dot]);

    useEffect(() => {
        let _dotTimer = setTimeout(dotTimerFunc, 500);
        return () => {
            clearTimeout(_dotTimer);
        };
    }, [dot]);

    return <span>{dot}</span>;
};

export default DotLoading;
