import React, { useCallback, useEffect, useMemo, useState } from "react";
import { autoServiceMessage } from "utils/service";
import { createConnectService, getErpVersionService, saveErpVersionService } from "../../services";

import { createGatewayInstanceService } from "services/gwServices";

/**
 * 穿件网关实例相关
 * @param queryParams
 * @param defaultErpVersion
 * @param connectorType
 * @return {{
    instanceInfo: *, erpVersion: *, createInstance: *, setConnectorName: *,
    createConnect: *, connectorName: *, setErpVersion: *, setInstanceInfo: *,
    hasCreate: *
  }}
 */
export function useSetConnector(queryParams, connectorType, defaultErpVersion = null) {
    let [connectorName, setConnectorName] = useState(queryParams.alias || "");
    let [erpVersion, setErpVersion] = useState(null);
    //网关实例信息 instanceInfo {entitryid, gatewayId, configId}
    let [instanceInfo, setInstanceInfo] = useState({
        gatewayId: queryParams.gatewayId || "",
        configId: queryParams.configId || "",
        connectId: queryParams.id || "",
        useMainTenantGateway: queryParams.fromMainTenant || "",
    });

    const createInstance = useCallback(
        async (create = true, name) => {
            let res = await autoServiceMessage({ service: createGatewayInstanceService(name) });
            if (res) {
                let { id, guid } = res.data;
                let info = { entitryid: id, gatewayId: id, configId: "" };
                if (create) {
                    createConnect(info);
                } else {
                    setInstanceInfo(info);
                }
            }
            return res;
        },
        [erpVersion, connectorName]
    );

    /**
     * @param {Object} data
     * @param {String} data.gatewayId
     * @param {String} data.configid
     */
    const createConnect = useCallback(
        async (data, hasAlias = true) => {
            if (hasAlias) {
                data.alias = connectorName;
            }
            const { gatewayId, configid, configId, useMainTenantGateway, ...otherData } = data;
            const _configId = configid || configId;
            let res = await autoServiceMessage({
                service: createConnectService({
                    gatewayId,
                    configid: _configId,
                    connectorType,
                    ...otherData,
                }),
            });
            if (res) {
                if (erpVersion) {
                    saveErpVersion(data.gatewayId);
                }
                const resData = res.data || {};
                setInstanceInfo({
                    gatewayId,
                    configId: _configId,
                    entitryid: gatewayId,
                    connectId: resData._id || "",
                    useMainTenantGateway,
                });
            }
        },
        [connectorName, erpVersion]
    );

    const saveErpVersion = useCallback(
        async (gatewayId) => {
            await autoServiceMessage({
                service: saveErpVersionService({
                    gatewayId: gatewayId,
                    erpversion: erpVersion,
                }),
            });
        },
        [erpVersion]
    );

    const getErpVersion = useCallback(
        async (gwId) => {
            let res = await autoServiceMessage({
                service: getErpVersionService(gwId),
                error: () => {
                    setErpVersion(defaultErpVersion);
                },
            });
            if (res) {
                setErpVersion(res.erpversion);
            }
        },
        [defaultErpVersion]
    );

    let hasCreate = useMemo(() => !!instanceInfo.gatewayId, [instanceInfo.gatewayId]);

    useEffect(() => {
        if (defaultErpVersion) {
            if (queryParams.gatewayId) {
                getErpVersion(queryParams.gatewayId);
            } else {
                setErpVersion(defaultErpVersion);
            }
        }
    }, [defaultErpVersion]);
    return {
        connectorName,
        setConnectorName,
        erpVersion,
        setErpVersion,
        setInstanceInfo,
        instanceInfo,
        createInstance,
        createConnect,
        hasCreate,
    };
}
