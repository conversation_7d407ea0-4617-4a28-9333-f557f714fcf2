@card-border: 1px solid #D9D9D9;
.test-card-wrap {
    background: #FFFFFF;
    padding: 20px 20px 16px;
    margin-top: 16px;
    border: @card-border;
    border-radius: 4px;
    box-shadow:0px 2px 4px 0px rgba(0, 0, 0, 0.1);
    position: relative;
    .card-arrow {
        width: 10px;
        height: 10px;
        border-left: @card-border;
        border-top: @card-border;
        position: absolute;
        top: -6px;
        background: #FFf;
        transform: rotate(40deg) skewX(-5deg)
    }
    .card-close {
        position: absolute;
        right: 5px;
        top: 5px;
        cursor: pointer;
        color: #666666;
        font-size: 12px;
        line-height: 22px;
        z-index: 2;
    }
}

.test-card-step{
    &-list {
        text-align: center;
        &>li {
            display: inline-block;
            vertical-align: top;
        }
    }

    &-item {
        color: #666666;
        width: 60px;
        .step-icon {
            margin: 0 auto;
            width: 40px;
            height: 40px;
            border: 1px solid #D9D9D9;
            border-radius: 50%;
            line-height: 36px;
            padding: 0 10px;;
            &>* {
                width: 100%;
                height: auto;
            }
        }
        .step-name {
            font-size: 12px;
            line-height: 16px;
            margin-top: 2px;
            text-align: center;
            white-space: nowrap;
            width: 200%;
            margin-left: -50%;
        }
    }
    &-between {
        display: list-item;
        width: 90px;
        padding: 2px 0 2px;
        text-align: center;
        font-size: 12px;
        line-height: 16px;
        border-bottom: 1px solid #505766;
        position: relative;
        height: 20px;
        .text {
            display: inline-block;
            height: 14px;
            &.success {
                color: #18B681;
            }
            &.fail {
                color: #EE2223;
            }
            &.pending {
                color: #588CE9;
            }
        }
        .arrow {
            width: 8px;
            height: 8px;
            border-top: 1px solid #505766;
            position: absolute;
            right: 2px;
            bottom: -4px;
            transform: rotate(40deg) skewX(-5deg);
        }

    }
}


.test-card-error {
    color: #EE2223;
    font-size: 12px;
    line-height: 17px;
    padding-top: 16px;
    i.cl {
        float: left;
        margin-right: 2px;
    }
    p {
        overflow: hidden;
        word-break: break-word;
    }
}
