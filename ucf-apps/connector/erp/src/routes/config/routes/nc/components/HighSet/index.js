import React, { Fragment, useState, useEffect, useCallback, useMemo } from "react";
import { <PERSON>ton, Modal, FormControl, Select, FormList, Tooltip } from "components/TinperBee";
import { autoServiceMessage } from "utils/service";
import Grid from "components/TinperBee/Grid";
import { GridActions, GridAction } from "components/TinperBee/Grid/GridActions";
import { originReg } from "utils/regExp";
import { injectRootStore } from "core/addStore";
import useBackConfirm from "hooks/useBackConfirm";
import { ConfigInfoItem } from "../../../components/ConfigInfo";
import {
    getSycParamsService,
    deleteSysParamService,
    editSysParamService,
    getGwAppSecretService,
    saveGwHighConfigService,
    saveGwHighConfigService_yonbip_pro,
} from "../../services";
import commonText from "constants/commonText";
import "./index.less";

// import FormList from "components/TinperBee/Form";
const FormItem = FormList.Item;
export function useHighSetConfig(instanceInfo) {
    let { gatewayId } = instanceInfo;

    let [systemParams, setSystemParams] = useState([]);

    const getSystemParams = useCallback(async () => {
        let res = await autoServiceMessage({
            service: getSycParamsService({
                gwId: instanceInfo.gatewayId,
                pageNo: 1,
                pageSize: 100,
            }),
        });
        if (res) {
            let resData = res.data || {};
            setSystemParams(resData.items || []);
        }
    }, [instanceInfo]);

    //获取轻应用网关密钥
    const getGwAppKey = useCallback(
        async (gwaddr, cb) => {
            let res = await autoServiceMessage({
                service: getGwAppSecretService(gwaddr),
            });
            if (res) {
                if (cb) {
                    cb(res.gwsecret);
                }
            }
        },
        [instanceInfo]
    );

    // 保存网关配置高级配置
    const saveGatewayHighConfig = useCallback(
        async (data) => {
            data.gatewayId = instanceInfo.gatewayId;
            return await autoServiceMessage({
                service: saveGwHighConfigService(data),
                success: window.lang.template(commonText.operateSuccess),
            });
        },
        [instanceInfo]
    );
    // 保存网关配置高级配置————yonbip_pro类型的保存
    const saveGatewayHighConfig_yonbip_pro = useCallback(
        async (data) => {
            data.gatewayId = instanceInfo.gatewayId;
            data.connectId = instanceInfo.connectId;
            return await autoServiceMessage({
                service: saveGwHighConfigService_yonbip_pro(data, instanceInfo.useMainTenantGateway),
                success: window.lang.template(commonText.operateSuccess),
            });
        },
        [instanceInfo]
    );

    const deleteParams = useCallback(
        (pk_id) => {
            Modal.confirm({
                fieldid: "202306091523",
                title: window.lang.template(commonText.confirmDelete),
                onOk: async () => {
                    let res = await autoServiceMessage({
                        service: deleteSysParamService(pk_id),
                    });
                    if (res) {
                        getSystemParams();
                    }
                },
            });
        },
        [instanceInfo]
    );

    //保存档案编辑项
    const saveParam = useCallback(
        async (data, index) => {
            let res = await autoServiceMessage({
                service: editSysParamService({
                    gwid: gatewayId,
                    pk_id: data.pk_id,
                    paramcode: data._paramcode,
                    paramname: data._paramname,
                    paramvalue: data._paramvalue,
                }),
                success: window.lang.template(commonText.saveSuccess),
            });
            if (res) {
                let param = systemParams[index];
                param.isEdit = false;
                param.paramcode = data._paramcode;
                param.paramname = data._paramname;
                param.paramvalue = data._paramvalue;
                delete param._paramcode;
                delete param._paramname;
                delete param._paramvalue;
                setSystemParams([...systemParams]);
            }
        },
        [gatewayId, systemParams]
    );

    const renderCell = useCallback((field, _systemParams) => {
        let _field = "_" + field;
        return (value, record, index) => {
            if (record.isEdit) {
                return (
                    <FormControl
                        fieldid="ublinker-routes-nc-components-HighSet-index-6822294-FormControl"
                        value={record[_field]}
                        showClose
                        onChange={(value) => {
                            _systemParams[index][_field] = value;
                            setSystemParams([..._systemParams]);
                        }}
                    />
                );
            } else {
                return value;
            }
        };
    }, []);

    const systemGirdColumns = useMemo(() => {
        return [
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050402") /* "参数编码" */,
                dataIndex: "paramcode",
                render: renderCell("paramcode", systemParams),
            },
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050498") /* "参数名称" */,
                dataIndex: "paramname",
                render: renderCell("paramname", systemParams),
            },
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050376") /* "参数值" */,
                dataIndex: "paramvalue",
                render: renderCell("paramvalue", systemParams),
            },
            {
                title: "",
                dataIndex: "$$save",
                width: 140,
                render: (value, record, index) => {
                    return record.isEdit ? (
                        <Fragment>
                            <Button
                                fieldid="ublinker-routes-nc-components-HighSet-index-7911087-Button"
                                {...Grid.hoverButtonPorps}
                                onClick={saveParam.bind(null, record, index)}
                            >
                                {window.lang.template(commonText.save)}
                            </Button>
                            <Button
                                fieldid="ublinker-routes-nc-components-HighSet-index-5254306-Button"
                                {...Grid.hoverButtonPorps}
                                colors="secondary"
                                onClick={() => {
                                    let param = systemParams[index];
                                    delete param._paramcode;
                                    delete param._paramname;
                                    delete param._paramvalue;
                                    param.isEdit = false;
                                    setSystemParams([...systemParams]);
                                }}
                            >
                                {window.lang.template(commonText.cancel)}
                            </Button>
                        </Fragment>
                    ) : null;
                },
            },
            {
                title: window.lang.template(commonText.actions),
                dataIndex: "$$actions",
                width: 110,
                render: (value, record, index) => {
                    let { tenantedit, issys, pk_id } = record;
                    let noEdit = !tenantedit;
                    return (
                        <GridActions>
                            <GridAction
                                fieldid="UCG-FE-routes-nc-components-HighSet-index-3758321-GridAction"
                                disabled={noEdit}
                                onClick={() => {
                                    let param = systemParams[index];
                                    let { paramcode, paramname, paramvalue } = param;
                                    param._paramcode = paramcode;
                                    param._paramname = paramname;
                                    param._paramvalue = paramvalue;
                                    param.isEdit = true;
                                    setSystemParams([...systemParams]);
                                }}
                            >
                                {window.lang.template(commonText.edit)}
                            </GridAction>
                            <GridAction
                                fieldid="UCG-FE-routes-nc-components-HighSet-index-9942299-GridAction"
                                disabled={noEdit || issys}
                                onClick={deleteParams.bind(null, pk_id)}
                            >
                                {window.lang.template(commonText.deletion)}
                            </GridAction>
                        </GridActions>
                    );
                },
            },
        ];
    }, [systemParams]);

    useEffect(() => {
        if (instanceInfo.gatewayId) {
            getSystemParams();
        }
    }, [instanceInfo]);

    return {
        systemParams,
        systemGirdColumns,
        getGwAppKey,
        saveGatewayHighConfig,
        saveGatewayHighConfig_yonbip_pro,
        instanceInfo,
    };
}

const fields = ["appAddress", "appSecret"];
const fields_yonbip_pro = ["mobilelightappUrl", "mobilelightappPort"];

const HighSet = (props) => {
    const formItemLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 12 },
    };
    const [form] = FormList.useForm();
    const { setFieldsValue, getFieldProps, validateFields, getFieldError, getFieldValue, getFieldsValue, isFieldsTouched } = form;
    const [appAddressValue, setAppAddressValue] = useState("");
    let { _useHighSetConfig, hasCreate, labelCol, gwConfigInfo, connectorType } = props;
    let { systemParams, systemGirdColumns, getGwAppKey, saveGatewayHighConfig, saveGatewayHighConfig_yonbip_pro, instanceInfo } = _useHighSetConfig;
    const _isFieldsTouched = isFieldsTouched();

    const { setBackConfirm } = useBackConfirm(props, _isFieldsTouched);

    useEffect(() => {
        let { gwAddr, gwSecret, mobilelightappUrl, mobilelightappPort } = gwConfigInfo;
        setFieldsValue({
            appAddress: gwAddr,
            appSecret: gwSecret,
            mobilelightappUrl,
            mobilelightappPort: mobilelightappPort || "33377",
        });
        setAppAddressValue(gwAddr);
    }, [gwConfigInfo]);

    const handleGetAppKey = useCallback(() => {
        // validateFields(['appAddress'], (error, values) => {
        //   if (!error) {
        //     getGwAppKey(values.appAddress, (appSecret) => {
        //       setFieldsValue({appSecret})
        //     })
        //   }
        // })
        validateFields(["appAddress"]).then((values) => {
            getGwAppKey(values.appAddress, (appSecret) => {
                setFieldsValue({ appSecret });
            });
        });
    }, []);

    const handleSave = useCallback(() => {
        let data = getFieldsValue(connectorType == "yonbip_pro" || connectorType == "YonBipMajorHandler" ? fields_yonbip_pro : fields);
        if (connectorType == "yonbip_pro" || connectorType == "YonBipMajorHandler") {
            saveGatewayHighConfig_yonbip_pro(data).then((res) => {
                if (res) {
                    setBackConfirm(false);
                }
            });
        } else {
            saveGatewayHighConfig(data).then((res) => {
                if (res) {
                    setBackConfirm(false);
                }
            });
        }
    }, [instanceInfo]);
    const changeAppAddress = (value) => {
        console.log(value);
        setAppAddressValue(value);
    };
    return (
        <ConfigInfoItem step="5" title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050474") /* "高级设置" */}>
            <Grid fieldid="ublinker-routes-nc-components-HighSet-index-2293961-Grid" rowKey="pk_id" columns={systemGirdColumns} data={systemParams} />
            <FormList
                fieldid="ublinker-routes-nc-components-HighSet-index-7419580-FormList"
                className="config-action-form ucg-mar-t-20"
                form={form}
                name="form122"
                labelAlign="right"
                {...formItemLayout}
            >
                {connectorType == "yonbip_pro" || connectorType == "YonBipMajorHandler" ? ( //yonbip_pro类似的显示不同的form
                    <>
                        <div className="groupadminBox">
                            <FormItem
                                fieldid="ublinker-routes-nc-components-HighSet-index-2258016-FormItem"
                                label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_2022411427") /* "网关外网IP地址" */}
                                name="mobilelightappUrl"
                                rules={[
                                    {
                                        pattern: originReg,
                                        message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_2022411428") /* "请输入网关外网IP地址" */,
                                    },
                                ]}
                            >
                                <FormControl fieldid="ublinker-routes-nc-components-HighSet-index-9349477-FormControl" onChange={changeAppAddress} />
                            </FormItem>
                            <div className="groupadminBox-tip">
                                <Tooltip
                                    fieldid="ublinker-routes-nc-components-HighSet-index-5152589-Tooltip"
                                    inverse
                                    overlay={
                                        <div>
                                            <div>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20227221429")}</div>
                                            <div>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20227221430")}</div>
                                            <div>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20227221431")}</div>
                                        </div>
                                    }
                                >
                                    <i
                                        fieldid="ublinker-routes-nc-components-HighSet-index-2323990-i"
                                        className="cl cl-Q ucg-pad-l-5"
                                        style={{ color: "#505766" }}
                                    />
                                </Tooltip>
                            </div>
                        </div>
                        <FormItem
                            fieldid="ublinker-routes-nc-components-HighSet-index-6729651-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_2022411429") /* "轻应用网关端口" */}
                            name="mobilelightappPort"
                        >
                            <FormControl fieldid="ublinker-routes-nc-components-HighSet-index-979263-FormControl" />
                        </FormItem>

                        <FormItem fieldid="ublinker-routes-nc-components-HighSet-index-9002820-FormItem" label=" ">
                            <Button
                                fieldid="ublinker-routes-nc-components-HighSet-index-2205898-Button"
                                disabled={!hasCreate}
                                colors="primary"
                                onClick={handleSave}
                            >
                                {window.lang.template(commonText.saveChange)}
                            </Button>
                        </FormItem>
                    </>
                ) : (
                    <>
                        <FormItem
                            fieldid="ublinker-routes-nc-components-HighSet-index-5791143-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050652") /* "轻应用网关地址" */}
                            name="appAddress"
                            rules={[
                                {
                                    pattern: originReg,
                                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050491") /* "请输入正确的轻应用网关地址" */,
                                },
                            ]}
                        >
                            <FormControl
                                fieldid="ublinker-routes-nc-components-HighSet-index-1030657-FormControl"
                                placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050641") /* "可通过轻应用网关名称获取混合云密钥" */}
                                onChange={changeAppAddress}
                            />
                        </FormItem>
                        <div className="appSecretBox">
                            <FormItem
                                fieldid="ublinker-routes-nc-components-HighSet-index-7759297-FormItem"
                                label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050289") /* "混合云密钥" */}
                                name="appSecret"
                            >
                                <FormControl
                                    fieldid="ublinker-routes-nc-components-HighSet-index-5535561-FormControl"
                                    placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050311") /* "混合云密钥" */}
                                />
                            </FormItem>
                            <div className="appSecretBox-btn">
                                <Button
                                    fieldid="ublinker-routes-nc-components-HighSet-index-6061833-Button"
                                    className="ucg-mar-l-5"
                                    disabled={!appAddressValue}
                                    bordered
                                    onClick={handleGetAppKey}
                                >
                                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050312") /* "获取混合云密钥" */}
                                </Button>
                            </div>
                        </div>

                        <FormItem fieldid="ublinker-routes-nc-components-HighSet-index-3202036-FormItem" label=" ">
                            <Button
                                fieldid="ublinker-routes-nc-components-HighSet-index-288840-Button"
                                disabled={!hasCreate}
                                colors="primary"
                                onClick={handleSave}
                            >
                                {window.lang.template(commonText.saveChange)}
                            </Button>
                        </FormItem>
                    </>
                )}
            </FormList>
        </ConfigInfoItem>
    );
};

const FormView = HighSet;

export default injectRootStore()(FormView);
