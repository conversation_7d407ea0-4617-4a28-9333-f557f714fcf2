import React, { useState, useEffect, useMemo, useCallback } from "react";
import { FormControl, InputNumber, Radio, FormList } from "components/TinperBee";
import Modal from "components/TinperBee/Modal";
// import FormList from "components/TinperBee/Form";
import commonText from "constants/commonText";
import { autoServiceMessage } from "utils/service";
import { getAdapterConfigByTemplateService, updateAdapterByTemplateService } from "../services";
import { getLocalImg } from "utils/index";

const FormItem = FormList.Item;
const labelCol = 100;
/**
 * 适配器配置弹窗
 * @param {Object} props
 * @param {String} props.gatewayId -网关Id
 * @param {String} props.adapterId -适配列表中的appId
 * @param {String} [props.action=deploy部署|config更新|undeploy卸载|hide隐藏]
 * @returns {*}
 * @constructor
 */
const ConfigModal = (props) => {
    const [form] = FormList.useForm();
    const { getFieldProps, getFieldError, validateFields, setFieldsValue } = form;
    const formItemLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 12 },
    };
    const { gatewayId, adapterId, adapterName, action } = props;
    const [show, setShow] = useState(false);
    const [templates, setTemplates] = useState([]);

    const getTemplates = useCallback(async () => {
        let res = await autoServiceMessage({
            service: getAdapterConfigByTemplateService({
                adapterId,
                gatewayId,
            }),
        });
        if (res) {
            setTemplates(res.data);
        }
    }, [gatewayId, adapterId]);

    const updateAdapterInputs = [action, gatewayId, adapterId, templates];

    const updateAdapter = useCallback(async (values) => {
        const _values = Object.keys(values).map((item) => {
            const isNumber = templates.find((tem) => tem.code === item).type === "number";
            return {
                code: item,
                value: isNumber ? values[item] : values[item],
            };
        });
        let res = await autoServiceMessage({
            service: updateAdapterByTemplateService({
                gatewayId,
                action,
                adapterId,
                values: _values,
            }),
            success: window.lang.template(commonText.operateSuccess),
        });
        if (res) {
            props.onCancel(res);
        }
    }, updateAdapterInputs);

    useEffect(() => {
        if (action === "hide") {
            setShow(false);
            setTemplates([]);
        } else {
            if ((action === "deploy" || action === "config") && adapterId) {
                getTemplates();
            }
            setShow(true);
        }
    }, [action, adapterId, gatewayId]);

    const modalTitle = useMemo(() => {
        let title = "";
        switch (action) {
            case "deploy":
                title = window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050785") /* "部署：" */ + adapterName;
                break;
            case "config":
                title = window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050782") /* "更新：" */ + adapterName;
                break;
            case "undeploy":
                title = window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050798") /* "卸载：" */ + adapterName;
                break;
            case "hide":
            default:
                title = "-";
                break;
        }
        return title;
    }, [action, adapterName]);

    const getRules = (templateItem) => {
        const { type, code, defaultValue, required, name, note } = templateItem;
        let rules = [
            {
                required: required,
                message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050795") /* "请输入" */ + name,
            },
        ];
        const isNumber = type === "number";
        if (isNumber) {
            let { max, min } = templateItem;
            rules = rules.concat([
                {
                    type: "number",
                    min,
                    max,
                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050769", { min, max }) /* "请输入 <%= min %>~<%= num %> 范围内的数字" */,
                    transform: (value) => {
                        let _value = Number(value);
                        return isNaN(_value) ? value : _value;
                    },
                },
            ]);
        }
        return rules;
    };
    const renderFormItem = useCallback((templateItem) => {
        let InputNode = null;
        const { type, code, defaultValue, required, name, note } = templateItem;
        // let rules = [{
        // 	required: required,
        // 	message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050795") /* "请输入" */ + name
        // }]
        // const isNumber = type === 'number';
        // if (isNumber) {
        // 	let { max, min } = templateItem;
        // 	rules = rules.concat([{
        // 		type: 'number', min, max,
        // 		message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050769", {min, max}) /* "请输入 <%= min %>~<%= num %> 范围内的数字" */,
        // 		transform: (value) => {
        // 			let _value = Number(value);
        // 			return isNaN(_value) ? value : _value
        // 		}
        // 	}])
        // }

        // const fieldProps = getFieldProps(code, {
        // 	initialValue: defaultValue,
        // 	rules: rules,
        // })
        switch (type) {
            case "number":
            case "text":
            case "password":
                InputNode = (
                    <FormControl
                        fieldid="ublinker-nc-components-Adapters-ConfigModal-index-8001096-FormControl"
                        // {...fieldProps}
                        type={type}
                        placeholder={note}
                    />
                );
                break;
            case "radio":
                const { list } = templateItem;
                InputNode = (
                    <Radio.Group
                    // {...fieldProps}
                    >
                        {list.map((item) => {
                            const { value, name } = item;
                            return (
                                <Radio fieldid="ublinker-nc-components-Adapters-ConfigModal-index-7909045-Radio" value={value} key={value}>
                                    {name}
                                </Radio>
                            );
                        })}
                    </Radio.Group>
                );
                break;
        }

        return InputNode;
    }, []);

    const renderTemplates = (templates) => {
        let templateItems = templates.map((templateItem) => {
            const { tenantShow } = templateItem;
            if (tenantShow) {
                const { name, required, code, defaultValue } = templateItem;
                return (
                    <FormItem
                        fieldid="ublinker-nc-components-Adapters-ConfigModal-index-7902370-FormItem"
                        key={code}
                        label={name}
                        // required={required}
                        // labelCol={labelCol}
                        // error={getFieldError(code)}
                        name={code}
                        initialValue={defaultValue}
                        rules={getRules(templateItem)}
                        // // rules={[{
                        // // 	required:required,
                        // // 	message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050795") /* "请输入" */ + name
                        // // }]
                        // }
                    >
                        {renderFormItem(templateItem)}
                    </FormItem>
                );
            } else {
                return null;
            }
        });

        if (templateItems.length > 0) {
            return (
                <FormList
                    fieldid="ublinker-nc-components-Adapters-ConfigModal-index-1394534-FormList"
                    form={form}
                    name="form122"
                    labelAlign="right"
                    {...formItemLayout}
                    className="ucg-pad-20-30"
                >
                    {templateItems}
                </FormList>
            );
        } else {
            return (
                <div style={{ textAlign: "center" }} className="ucg-pad-20-30">
                    <div className="ucg-pad-b-10">
                        <img
                            fieldid="ublinker-nc-components-Adapters-ConfigModal-index-5109664-img"
                            style={{ width: "64px", height: "auto" }}
                            src={getLocalImg("empty.png")}
                            alt=""
                        />
                    </div>
                    <p style={{ fontSize: "14px" }}>
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050758") /* "此适配器无需配置配置参数，请点击确认直接保存" */}
                    </p>
                </div>
            );
        }
    };

    const getContent = () => {
        if (action === "hide") {
            return null;
        } else if (action === "undeploy") {
            return <p className="ucg-pad-20-30">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050776") /* "确认卸载此适配器？" */}</p>;
        } else {
            return renderTemplates(templates);
        }
    };

    const handleOk = useCallback(() => {
        // validateFields((error, values) => {
        // 	if (!error) {
        // 		updateAdapter(values)
        // 	}
        // })
        validateFields().then((values) => {
            updateAdapter(values);
        });
    }, updateAdapterInputs);

    return (
        <Modal
            fieldid="ublinker-nc-components-Adapters-ConfigModal-index-7311880-Modal"
            show={show}
            title={modalTitle}
            onCancel={props.onCancel}
            onOk={handleOk}
            // width={500}
            size="sm"
        >
            {getContent()}
        </Modal>
    );
};

export default ConfigModal;
