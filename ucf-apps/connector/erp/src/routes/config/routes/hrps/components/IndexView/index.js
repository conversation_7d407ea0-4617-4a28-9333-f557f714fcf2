import React, { Fragment, useCallback, useRef } from "react";
import { PRIVATE } from "utils/util";
import { Button, FormControl, Modal, Select, FormList } from "components/TinperBee";
// import FormList from "components/TinperBee/Form";
import { Warning } from "utils/feedback";
import { connectorInfoMap } from "../../../common/constants";
import ConfigTarget from "../../../components/ConfigTarget";
import { useSetConnector } from "./hooks";
import { useGatewayConfig } from "hooks/useGateway";
import useErpVersions from "./useErpVersions";
import { ConfigInfoList, ConfigInfoItem } from "../../../components/ConfigInfo";
import { useLocationParamsMemo } from "../../../common/utilHooks";
import DownloadView from "../DownloadView";
import ErpConfigView, { useErpConfig } from "../ErpConfig";
import InitDoc, { useInitDoc } from "../InitDoc";
import InitTaskView, { useInitTask } from "../InitTask";
import HighSet, { useHighSetConfig } from "../HighSet";
import Question from "../Question";
import SetGwConfigView from "../SetGwConfig";
// import AdaptersView from "../Adapters";
import AdaptersView from "../../../nc/components/Adapters";
import HealthCheckView from "../HealthCheck";

const labelCol = 130;
const FormItem = FormList.Item;
const IndexView = (props) => {
    const [form] = FormList.useForm();
    const formItemLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 12 },
    };
    const { location } = props;
    const { queryParams, connectorType } = useLocationParamsMemo();

    const connectorInfo = connectorInfoMap[connectorType];

    const { erpVersions, defaultErpVersion } = useErpVersions(connectorInfo, connectorType);

    const _useSetConnector = useSetConnector(queryParams, connectorType, defaultErpVersion);
    const { connectorName, setConnectorName, erpVersion, setErpVersion, instanceInfo, createInstance, hasCreate } = _useSetConnector;

    const _useErpConfig = useErpConfig(_useSetConnector, connectorType, queryParams);

    const _useInitDoc = useInitDoc(_useSetConnector, _useErpConfig.erpConfig);

    const _useInitTask = useInitTask(_useSetConnector, _useInitDoc, connectorType, _useErpConfig.erpConfig);

    const [gatewayConfig, getGatewayConfig] = useGatewayConfig(instanceInfo.gatewayId, instanceInfo.useMainTenantGateway);

    const _useHighSetConfig = useHighSetConfig(instanceInfo);

    const handleCreateInstance = useCallback(() => {
        if (connectorName.length > 20) {
            Warning(window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050922") /* "连接器名称长度不能超过20个字符" */);
        } else {
            Modal.confirm({
                fieldid: "202306091521",
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050627", connectorInfo) /* "确认使用该<%= name %>版本吗？" */,
                content: window.lang.template(
                    "MIX_UBL_ALL_UBL_FE_LOC_00050356",
                    connectorInfo
                ) /* "该操作会决定你的网关支持的<%= name %>版本, 且版本确定后无法更改" */,
                onOk: createInstance,
            });
        }
    }, [erpVersion, connectorName]);

    const adapterViewRef = useRef(null);

    return (
        <Fragment>
            <ConfigTarget logo={connectorInfo.logo} title={connectorInfo.name} intro={connectorInfo.intro} />
            <ConfigInfoList>
                <ConfigInfoItem
                    step="1"
                    title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050537", connectorInfo) /* "设置<%= name %>连接器名称" */}
                    connectorInfo={connectorInfo}
                >
                    <FormList
                        fieldid="ublinker-routes-hrps-components-IndexView-index-3573154-FormList"
                        className="config-action-form config-action-form-80"
                        form={form}
                        name="form122"
                        labelAlign="right"
                        {...formItemLayout}
                        // layoutOpt={{ md: 12 }}
                    >
                        <FormItem
                            fieldid="ublinker-routes-hrps-components-IndexView-index-3832613-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050551") /* "连接器名称" */}
                        >
                            <FormControl
                                fieldid="ublinker-routes-hrps-components-IndexView-index-9717580-FormControl"
                                disabled={hasCreate}
                                value={connectorName}
                                onChange={setConnectorName}
                            />
                        </FormItem>

                        <FormItem
                            fieldid="ublinker-routes-hrps-components-IndexView-index-2988392-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050360") /* "请选择版本" */}
                        >
                            <Select
                                fieldid="ublinker-routes-hrps-components-IndexView-index-120380-Select"
                                disabled={hasCreate}
                                data={erpVersions}
                                value={erpVersion}
                                onChange={setErpVersion}
                            />
                        </FormItem>

                        {hasCreate ? null : (
                            <FormItem fieldid="ublinker-routes-hrps-components-IndexView-index-6241539-FormItem" label=" ">
                                <Button
                                    fieldid="ublinker-routes-hrps-components-IndexView-index-6384911-Button"
                                    disabled={!connectorName}
                                    colors="primary"
                                    onClick={handleCreateInstance}
                                >
                                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050352") /* "确认" */}
                                </Button>
                            </FormItem>
                        )}
                    </FormList>
                </ConfigInfoItem>

                <DownloadView
                    connectorInfo={connectorInfo}
                    connectorType={connectorType}
                    hasCreate={hasCreate}
                    instanceInfo={instanceInfo}
                    erpVersion={erpVersion}
                    gwConfigInfo={gatewayConfig}
                />

                {/* 专属化去除健康检查 */}
                {PRIVATE ? null : <HealthCheckView hasCreate={hasCreate} instanceInfo={_useSetConnector.instanceInfo} adapterViewRef={adapterViewRef} />}
                {/* 输入NCCloud配置并测试连接 */}
                <ErpConfigView
                    connectorInfo={connectorInfo}
                    labelCol={labelCol}
                    erpVersions={erpVersions}
                    erpVersion={erpVersion}
                    _useErpConfig={_useErpConfig}
                    isInitTaskView={_useInitTask.isInitTaskView}
                />
                {instanceInfo.useMainTenantGateway !== "true" ? (
                    <AdaptersView ref={adapterViewRef} instanceInfo={_useSetConnector.instanceInfo} getGatewayConfig={getGatewayConfig} />
                ) : null}
            </ConfigInfoList>
        </Fragment>
    );
};

export default IndexView;
