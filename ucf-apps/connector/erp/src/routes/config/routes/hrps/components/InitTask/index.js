import React, { useState, useEffect, useCallback, use<PERSON>emo, Fragment } from "react";
import classnames from "classnames";
import { Tooltip } from "components/TinperBee";
import { Button, Icon, Modal, Checkbox, Radio } from "components/TinperBee";
import Grid from "components/TinperBee/Grid";
import { autoServiceMessage } from "utils/service";
import { batchSyncTaskService } from "services/taskServices";
import { GridAction, GridActions } from "components/TinperBee/Grid/GridActions";
import commonText from "constants/commonText";
import { QUERY_PARAMS } from "utils/util";

import { getDataViewService, initDataViewService, updateSyncTaskService, getInitMessageService } from "../../services";

import { ConfigInfoItem } from "../../../components/ConfigInfo";
import SqlWhereModal from "../SqlWhereModal";
import FormList from "components/TinperBee/Form";
import "./index.less";

const FormItem = FormList.Item;
const CheckboxGroup = Checkbox.Group;
const dataViewColumns = [
    {
        title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050199") /* "数据视图" */,
        dataIndex: "datatype.typename",
    },
    {
        title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050121") /* "视图所属应用" */,
        dataIndex: "appcode",
    },
    {
        title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050126") /* "视图版本" */,
        dataIndex: "tableview",
    },
];

export function useInitTask(_useSetConnector, _useInitDoc, connectorType, connectorConfig) {
    const { instanceInfo, erpVersion } = _useSetConnector;
    const [isInitTaskView, setIsInitTaskView] = useState(true); //视图任务是否初始化
    const [isInitConfigView, setIsIniConfigView] = useState(false); //初始化设置项是否显示
    const [isSetConfigView, setIsSetConfigView] = useState(false); //初始化ERP任务是否已保存设置
    const [dataViews, setDataViews] = useState([]); //数据视图列表
    const [enableHR, setEnableHR] = useState(false); //是否是hr版
    const [rankStandard, setRankStandard] = useState([]); //职级体系标准
    const [classesSystem, setClassesSystem] = useState([]); //系统级任务分类
    const [classesTenant, setClassesTenant] = useState([]); //租户级任务分类
    const [rankStandardSelect, setRankStandardSelect] = useState(""); //职级体系标准 选择select
    const [classesSystemSelect, setClassesSystemSelect] = useState([]); //系统级任务分类 选择select
    const [classesTenantSelect, setClassesTenantSelect] = useState([]); //租户级任务分类 选择select
    const [syncTasks, setSyncTasks] = useState([]); //同步任务列表
    const [updateTaskModalShow, setUpdateTaskModalShow] = useState(false); //编辑任务视图条件Modal显示隐藏
    const [editTaskInfo, setEditTaskInfo] = useState(false); //当前编辑视图任务数据
    const [syncTasksBatchEnd, setSyncTaskBatchEnd] = useState(false); //视图任务是否已执行同步
    const [isInitTask, setIsInitTask] = useState(false); //是否已初始化，若已初始化不能再点击初始化按钮

    // //获取视图任务是否已经初始化
    // const getIsViewInitService = useCallback(async (gatewayId) => {
    //   let res = await autoServiceMessage({
    //     service: isViewInitService(gatewayId)
    //   })
    //   if (res) {
    //     //let { isViewInit } = res;
    //     setIsInitTaskView(true);
    //     setSyncTaskBatchEnd(true)
    //   }
    // }, [])

    // //gatewayId更新后，自动获取 视图任务更新与否状态
    // useEffect(() => {
    //   if (instanceInfo.gatewayId) {
    //     getIsViewInitService(instanceInfo.gatewayId)
    //   }
    // }, [instanceInfo.gatewayId])

    const initDisabled = useCallback(() => {
        if (erpVersion && instanceInfo.gatewayId && instanceInfo.configId && connectorConfig.ipaddr) {
            return false;
        } else {
            return true;
        }
    }, [erpVersion, instanceInfo, connectorConfig]);

    //查询初始化信息
    const getInitConfigViewService = useCallback(async () => {
        let param = {
            erpType: connectorType,
            detailType: erpVersion,
        };
        let res = await autoServiceMessage({
            service: getInitMessageService(param),
        });
        if (res) {
            let { isHr, classes, rank = [] } = res.data;
            let { System, Tenant = [] } = classes;
            let classesSystemSelect = System.filter((item) => item.select).map((item) => item.code);
            let classesTenantSelect = Tenant.filter((item) => item.select).map((item) => item.code);
            let rankStandardSelect = "";
            rank.forEach((item) => {
                if (item.select) {
                    rankStandardSelect = item.code;
                }
            });
            setEnableHR(isHr);
            setRankStandard(rank);
            setClassesSystem(System);
            setClassesTenant(Tenant);
            setClassesSystemSelect(classesSystemSelect);
            setClassesTenantSelect(classesTenantSelect);
            setRankStandardSelect(rankStandardSelect);
        }
    }, [isInitConfigView]);

    //点击初始化任务，显示初始化任务选项
    useEffect(() => {
        if (isInitConfigView) {
            getInitConfigViewService();
        }
    }, [isInitConfigView]);

    /**
     * 获取数据视图, 需要确认hr视图档案类型，再次调用此接口时传入doc参数
     * @param {String=} doc=[category|sequence]
     */
    const getDataViews = useCallback(
        async (doc) => {
            const requestData = {
                erpversion: erpVersion,
                connectId: instanceInfo.connectId,
                classes: classesSystemSelect.concat(classesTenantSelect),
                isHr: enableHR,
                doc,
            };
            let res = await autoServiceMessage({
                service: getDataViewService(requestData),
            });
            if (res) {
                //res.enableHR=true 如果需要确认hr视图档案类型，再次调用此接口
                const { data = [], enableHR } = res;
                //setEnableHR(!!enableHR);
                setDataViews(data);
                setIsIniConfigView(false);
                setIsSetConfigView(true);
            }
        },
        [erpVersion, instanceInfo.connectId, classesSystemSelect, classesTenantSelect, enableHR]
    );

    // //当没有初始化任务视图 并且初始化档案已结束
    // useEffect(() => {
    //   if (!isInitTaskView && instanceInfo.configId && _useInitDoc.stepDone) {
    //     getDataViews()
    //   }
    // }, [isInitTaskView, _useInitDoc.stepDone, erpVersion, instanceInfo.configId])

    //根据视图获取同步任务
    const getSyncTasks = useCallback(async () => {
        let res = await autoServiceMessage({
            service: initDataViewService(
                {
                    gatewayId: instanceInfo.gatewayId,
                    tableViewNames: dataViews.map((item) => item.tableview),
                    isHr: enableHR,
                    rankType: rankStandardSelect,
                    erpType: connectorType,
                    detailType: erpVersion,
                    classes: {
                        System: classesSystemSelect,
                        Tenant: classesTenantSelect,
                    },
                },
                instanceInfo.useMainTenantGateway
            ),
            success: window.lang.template(commonText.saveSuccess),
        });
        if (res) {
            setSyncTaskBatchEnd(false);
            setIsInitTask(true);
            setSyncTasks(res.data || []);
        }
    }, [instanceInfo, dataViews, enableHR, rankStandardSelect, connectorType, erpVersion, classesSystemSelect, classesTenantSelect]);

    //编辑视图条件
    const updateSyncTask = useCallback(async (data, index) => {
        data.sqlwhere = btoa(encodeURIComponent(data.sqlwhere));
        let res = await autoServiceMessage({
            service: updateSyncTaskService(data),
            success: window.lang.template(commonText.changeSuccess),
        });
        if (res) {
            let _task = syncTasks.find((item) => item.pk_id === editTaskInfo.pk_id);
            _task.sqlwhere = data.sqlwhere;
            setSyncTasks([...syncTasks]);
            setUpdateTaskModalShow(false);
            setEditTaskInfo(null);
        }
    });

    const handleEdit = useCallback((taskInfo) => {
        setUpdateTaskModalShow(true);
        setEditTaskInfo(taskInfo);
    });

    //同步任务列信息
    const syncTaskColumns = [
        {
            title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050308") /* "视图名称" */,
            dataIndex: "dataview.datatype.typename",
        },
        {
            title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050325") /* "是否增量" */,
            dataIndex: "incrementValue",
            width: 100,
        },
        {
            title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050488") /* "编辑数据条件" */,
            dataIndex: "sqlwhere",
        },
        {
            title: window.lang.template(commonText.actions),
            dataIndex: "$$actions",
            render: (value, record) => {
                return (
                    <GridActions>
                        <GridAction fieldid="UCG-FE-routes-hrps-components-InitTask-index-4876483-GridAction" onClick={handleEdit.bind(null, record)}>
                            {window.lang.template(commonText.edit)}
                        </GridAction>
                    </GridActions>
                );
            },
        },
    ];

    //执行同步任务
    const batchSyncTask = useCallback(async () => {
        let taskIds = syncTasks.map((item) => item.pk_id);
        let res = await autoServiceMessage({
            service: batchSyncTaskService(taskIds),
            success: window.lang.template(commonText.operateSuccess),
        });
        if (res) {
            setSyncTaskBatchEnd(true);
        }
    });

    return {
        dataViews,
        getDataViews,
        enableHR,
        syncTaskColumns,
        syncTasks,
        getSyncTasks,
        batchSyncTask,
        updateTaskModalShow,
        editTaskInfo,
        updateSyncTask,
        setUpdateTaskModalShow,
        isInitTaskView,
        syncTasksBatchEnd,
        isInitConfigView,
        setIsIniConfigView,
        classesSystem,
        classesTenant,
        rankStandard,
        classesSystemSelect,
        setClassesSystemSelect,
        classesTenantSelect,
        setClassesTenantSelect,
        rankStandardSelect,
        setRankStandardSelect,
        isSetConfigView,
        setIsSetConfigView,
        initDisabled,
        isInitTask,
    };
}

// const syncTaskHtml = process.env.CONTEXT + '/data/sync-task/index.html' + window.location.search;

const navToSyncTask = () => {
    if (QUERY_PARAMS.from === "diwork") {
        window.jDiwork.openWin({
            id: "ublinker-data-sync-task",
            title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050666") /* "数据同步任务" */,
            // url: window.location.origin + syncTaskHtml
        });
    }
    // else {
    //   window.location.href = syncTaskHtml;
    // }
};

const SelectTag = ({ children, onClick, active = false }) => {
    const [selfActive, setSelfActive] = useState(active);

    const handleClick = useCallback(() => {
        setSelfActive(!selfActive);
        onClick && onClick();
    }, [selfActive]);

    const cls = useMemo(() => {
        return classnames("ucg-select-tag-card", {
            active: selfActive,
        });
    }, [selfActive]);

    return (
        <li className={cls} onClick={handleClick}>
            <span className="tag-card-check">
                <Icon fieldid="ublinker-routes-hrps-components-InitTask-index-919025-Icon" type="uf-correct-2" />
            </span>
            {children}
        </li>
    );
};

const InitTaskView = (props) => {
    const {
        _useInitTask,
        labelCol,
        form: { getFieldProps, getFieldError, validateFields },
    } = props;
    const {
        dataViews,
        getDataViews,
        enableHR,
        syncTaskColumns,
        syncTasks,
        getSyncTasks,
        batchSyncTask,
        updateTaskModalShow,
        editTaskInfo,
        updateSyncTask,
        setUpdateTaskModalShow,
        isInitTaskView,
        isInitConfigView,
        setIsIniConfigView,
        syncTasksBatchEnd,
        classesSystem,
        classesTenant,
        rankStandard,
        classesSystemSelect,
        setClassesSystemSelect,
        classesTenantSelect,
        setClassesTenantSelect,
        rankStandardSelect,
        setRankStandardSelect,
        isSetConfigView,
        initDisabled,
        isInitTask,
    } = _useInitTask;
    let rankDisabled = rankStandard.some((item) => item.select);
    /** 点击保存按钮 */
    const handleSave = () => {
        validateFields((error, values) => {
            if (!error) {
                getDataViews(rankStandardSelect);
            }
        });
    };
    return (
        <Fragment>
            <ConfigInfoItem step="6" title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050286") /* "初始化任务及同步任务" */}>
                {!isInitConfigView && !isSetConfigView ? (
                    <Button
                        fieldid="ublinker-routes-hrps-components-InitTask-index-2275888-Button"
                        colors="primary"
                        onClick={setIsIniConfigView.bind(null, true)}
                        disabled={initDisabled()}
                    >
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050188") /* "初始化任务" */}
                    </Button>
                ) : null}
                {isInitConfigView ? (
                    <FormList fieldid="ublinker-routes-hrps-components-InitTask-index-910424-FormList" className="init-task-form" layoutOpt={{ md: 12 }}>
                        {classesSystem.length ? (
                            <FormItem
                                fieldid="ublinker-routes-hrps-components-InitTask-index-1410422-FormItem"
                                label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050869") /* "系统级任务分类" */}
                                required
                                labelCol={labelCol}
                                error={getFieldError("classesSystem")}
                            >
                                <CheckboxGroup
                                    value={classesSystemSelect}
                                    {...getFieldProps("classesSystem", {
                                        initialValue: classesSystemSelect,
                                        onChange(value) {
                                            setClassesSystemSelect(value);
                                        },
                                        rules: [
                                            { required: true, message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050866") /* "请选择系统级任务分类" */ },
                                        ],
                                    })}
                                >
                                    {classesSystem.map((item) => {
                                        return (
                                            <Checkbox
                                                fieldid="ublinker-routes-hrps-components-InitTask-index-2127-Checkbox"
                                                key={item.code}
                                                value={item.code}
                                                disabled={item.select}
                                            >
                                                {item.name}
                                            </Checkbox>
                                        );
                                    })}
                                </CheckboxGroup>
                            </FormItem>
                        ) : null}
                        {classesTenant.length ? (
                            <FormItem
                                fieldid="ublinker-routes-hrps-components-InitTask-index-9605583-FormItem"
                                label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050868") /* "租户级任务分类" */}
                                required
                                labelCol={labelCol}
                                error={getFieldError("classesTenant")}
                            >
                                <CheckboxGroup
                                    value={classesTenantSelect}
                                    {...getFieldProps("classesTenant", {
                                        initialValue: classesTenantSelect,
                                        onChange(value) {
                                            setClassesTenantSelect(value);
                                        },
                                        rules: [
                                            { required: true, message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050865") /* "请选择租户级任务分类" */ },
                                        ],
                                    })}
                                >
                                    {classesTenant.map((item) => {
                                        return (
                                            <Checkbox
                                                fieldid="ublinker-routes-hrps-components-InitTask-index-4374713-Checkbox"
                                                key={item.code}
                                                value={item.code}
                                                disabled={item.select}
                                            >
                                                {item.name}
                                            </Checkbox>
                                        );
                                    })}
                                </CheckboxGroup>
                            </FormItem>
                        ) : null}
                        {enableHR && rankStandard.length > 0 ? (
                            <FormItem
                                fieldid="ublinker-routes-hrps-components-InitTask-index-3681181-FormItem"
                                label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050864") /* "职级体系标准" */}
                                required
                                labelCol={labelCol}
                                error={getFieldError("rank")}
                            >
                                <Radio.Group
                                    selectedValue={rankStandardSelect}
                                    {...getFieldProps("rank", {
                                        initialValue: rankStandardSelect,
                                        onChange(value) {
                                            setRankStandardSelect(value);
                                        },
                                        rules: [
                                            { required: true, message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050867") /* "请选择职级体系标准" */ },
                                        ],
                                    })}
                                >
                                    {rankStandard.map((item) => {
                                        return (
                                            <Radio
                                                fieldid="ublinker-routes-hrps-components-InitTask-index-4222070-Radio"
                                                key={item.code}
                                                value={item.code}
                                                disabled={rankDisabled}
                                            >
                                                {item.name}
                                            </Radio>
                                        );
                                    })}
                                </Radio.Group>
                                <Tooltip
                                    fieldid="ublinker-routes-hrps-components-InitTask-index-1113943-Tooltip"
                                    inverse
                                    overlay={
                                        <div>
                                            <p>
                                                {
                                                    window.lang.template(
                                                        "MIX_UBL_ALL_UBL_FE_LOC_00050924"
                                                    ) /* "ERP需要通过网关访问云服务，因此要确保网关IP和端口对ERP服务器开放。" */
                                                }
                                            </p>
                                            <p>
                                                {
                                                    window.lang.template(
                                                        "MIX_UBL_ALL_UBL_FE_LOC_00050925"
                                                    ) /* "所以请务必选择（或手动输入）部署网关的服务器的正确IP地址和端口。" */
                                                }
                                            </p>
                                            <p>
                                                {
                                                    window.lang.template(
                                                        "MIX_UBL_ALL_UBL_FE_LOC_00050926"
                                                    ) /* "IP和端口仅用于在内网中ERP与网关通信，不需要对外开放，安全性可保障。" */
                                                }
                                            </p>
                                            <p>
                                                {
                                                    window.lang.template(
                                                        "MIX_UBL_ALL_UBL_FE_LOC_00050927"
                                                    ) /* "如果测试网关连接异常，将只影响ERP访问云端的业务，基础档案数据同步不会受到影响。" */
                                                }
                                            </p>
                                        </div>
                                    }
                                >
                                    <i
                                        fieldid="ublinker-routes-hrps-components-InitTask-index-7158188-i"
                                        className="cl cl-Q ucg-pad-l-5"
                                        style={{ color: "#505766" }}
                                    />
                                </Tooltip>
                            </FormItem>
                        ) : null}

                        {classesSystem.length ? (
                            <FormItem fieldid="ublinker-routes-hrps-components-InitTask-index-8931861-FormItem" labelCol={labelCol}>
                                <Button
                                    fieldid="ublinker-routes-hrps-components-InitTask-index-8400421-Button"
                                    className="ucg-mar-r-10"
                                    colors="primary"
                                    onClick={handleSave}
                                >
                                    {window.lang.template(commonText.save)}
                                </Button>
                                <Button
                                    fieldid="ublinker-routes-hrps-components-InitTask-index-310161-Button"
                                    border
                                    onClick={setIsIniConfigView.bind(null, false)}
                                >
                                    {window.lang.template(commonText.cancel)}
                                </Button>
                            </FormItem>
                        ) : null}
                    </FormList>
                ) : null}
                {isSetConfigView ? (
                    <Fragment>
                        <p className="">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050199") /* "数据视图" */}</p>
                        <Grid
                            fieldid="ublinker-routes-hrps-components-InitTask-index-9716374-Grid"
                            empty={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050278") /* "无数据" */}
                            columns={dataViewColumns}
                            data={dataViews}
                            rowKey="tableview"
                            scroll={{ y: 300 }}
                            footer={
                                <Button
                                    fieldid="ublinker-routes-hrps-components-InitTask-index-195265-Button"
                                    disabled={dataViews.length <= 0 || isInitTask}
                                    colors="primary"
                                    onClick={getSyncTasks.bind(null)}
                                >
                                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050822") /* "初始化" */}
                                </Button>
                            }
                        />
                        <div className="config-img"></div>
                        <p className="">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050485") /* "同步任务" */}</p>
                        <Grid
                            fieldid="ublinker-routes-hrps-components-InitTask-index-6206712-Grid"
                            empty={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050278") /* "无数据" */}
                            columns={syncTaskColumns}
                            data={syncTasks}
                            rowKey="pk_id"
                            scroll={{ y: 300 }}
                            footer={
                                syncTasksBatchEnd ? null : (
                                    <Button
                                        fieldid="ublinker-routes-hrps-components-InitTask-index-9801253-Button"
                                        disabled={syncTasks.length <= 0}
                                        colors="primary"
                                        onClick={batchSyncTask}
                                    >
                                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050536") /* "执行同步任务" */}
                                    </Button>
                                )
                            }
                        />
                    </Fragment>
                ) : null}
                {syncTasksBatchEnd ? (
                    <>
                        {
                            //   <Button fieldid="ublinker-routes-hrps-components-InitTask-index-8669825-Button"
                            //   bordered
                            //   className='ucg-mar-t-5'
                            //   onClick={navToSyncTask}
                            // ><a>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050216")/* 查看任务 */}</a></Button>
                        }
                    </>
                ) : null}
            </ConfigInfoItem>
            <SqlWhereModal show={updateTaskModalShow} taskInfo={editTaskInfo} onCancel={setUpdateTaskModalShow.bind(null, false)} onOk={updateSyncTask} />
        </Fragment>
    );
};
const FormInitTaskView = FormList.createForm()(InitTaskView);

export default FormInitTaskView;
