import React, { useState, useEffect, useCallback, useRef, useMemo } from "react";
import { PRIVATE } from "utils/util";
import { Button, FormControl, Modal, Select, Icon, Tooltip, FormList } from "components/TinperBee";
import ModalView from "components/TinperBee/Modal";
import Grid from "components/TinperBee/Grid";
import { autoServiceMessage } from "utils/service";

import { injectRootStore } from "core/addStore";
import useBackConfirm from "hooks/useBackConfirm";
import { ConfirmDefaultGwToTask } from "services/gwServices";
import { ConfigInfoItem } from "../../../components/ConfigInfo";
import PushGwConfigToApp from "../SetGwConfig/PushGwConfigToApp";
import TestCard from "connector/erp/components/TestCard";
import { testGwToErpService } from "services/gwServices";
import {
    getConfigService,
    getHasConfigInfoService,
    saveConfigService,
    pushYhtService,
    testConnectService,
    setDefaultService,
    deploymentService,
    deploymentServiceNew,
} from "../../services";
import { originReg } from "utils/regExp";
import erpIcon from "connector/erp/images/erp_icon.png";
import gwIcon from "connector/erp/images/gw_icon.png";
import serIcon from "connector/erp/images/ser_icon.png";

// import FormList from "components/TinperBee/Form";
import commonText from "constants/commonText";

import "./index.less";
const FormItem = FormList.Item;

export function useErpConfig(_useSetConnector, connectorType, queryParams) {
    let { instanceInfo, connectorName, erpVersion } = _useSetConnector;
    let { gatewayId, useMainTenantGateway, connectId } = instanceInfo;

    const [configId, setConfigId] = useState("");

    /** 测试连接接口返回的groupPk 推送友互通接口需要 */
    const [testGroupPk, setTestGroupPk] = useState("");

    /** erp配置信息 */
    let [erpConfig, setErpConfig] = useState({
        ipaddr: "",
        busicode: "",
        groupcode: "",
        groupadmin: "",
        host: queryParams.host,
        account: queryParams.account,
        accountYear: queryParams.accountYear,
    });

    /** 点击获取参数按钮 可选参列表 */
    let [oldConfig, setOldConfig] = useState({ configList: [], selectedList: [], oldConfigModalShow: false });

    let [buttonDisabled, setButtonDisabled] = useState(!_useSetConnector.hasCreate);

    useEffect(() => {
        setButtonDisabled(!_useSetConnector.hasCreate);
    }, [_useSetConnector.hasCreate]);

    /** 是否应该点击测试按钮 */
    let [shouldSave, setShouldSave] = useState(!configId);

    /**  是否应该点击保存按钮 */
    let [shouldTest, setShouldTest] = useState(!configId);

    /** 获取已配置erp信息 */
    // const getConfig = useCallback(async (gwId) => {
    //     let res = await autoServiceMessage({ service: getConfigService(gwId) })
    //     if (res) {
    //         let { ncipaddr = '', ncbusicode = '', ncgroupcode = '', ncgroupadmin = '' } = res.data || {};
    //         setConfigId(res.data ? res.data.id : null);
    //         setErpConfig({
    //             ipaddr: ncipaddr,
    //             busicode: ncbusicode,
    //             groupcode: ncgroupcode,
    //             groupadmin: ncgroupadmin,
    //             host: 1,
    //             account: 1,
    //             accountYear: 1,
    //         })
    //     }
    // })

    /** 自动获取erp配置信息 */
    // useEffect(() => {
    //     if (instanceInfo.gatewayId) {
    //         getConfig(instanceInfo.gatewayId)
    //     }
    // }, [instanceInfo.gatewayId])

    /** 获取nc/ncc/u8c系统配置配置列表 账套编码 集团编码 等 */
    const getOldConfig = useCallback(
        (values) => {
            return getHasConfigInfoService(
                {
                    ncurl1: values.ipaddr,
                    gatewayId: gatewayId,
                },
                false,
                useMainTenantGateway
            ).then((res) => {
                if (res && res.data && res.data.length > 0) {
                    setOldConfig({
                        configList: res.data,
                        selectedList: [],
                        oldConfigModalShow: true,
                    });
                }
            });
        },
        [gatewayId]
    );

    /** 测试erp连接 */
    const testInputs = [gatewayId];
    const testConnect = useCallback(async (values) => {
        let res = await autoServiceMessage({
            service: testConnectService(
                {
                    gatewayId: gatewayId,
                    ...values,
                },
                useMainTenantGateway
            ),
            success: window.lang.template(commonText.testSuccess),
        });
        if (res) {
            const resData = res.data || {};
            setShouldTest(false);
            setTestGroupPk(resData.groupPk || "");
        }
    }, testInputs);

    /** 保存erp配置 */
    const saveInputs = [gatewayId, configId, connectorName, testGroupPk, erpVersion];

    /** 将配置推送友互通 */
    const pushYht = useCallback(async (configId, groupPk) => {
        return await autoServiceMessage({
            service: pushYhtService({ configId, groupPk }, useMainTenantGateway),
        });
    }, []);

    const saveConfig = useCallback(async (values) => {
        let requestData = {
            // configid: configId,
            // gwguid: gatewayId,
            // servertype: connectorType,
            // alias: connectorName,
            gatewayId,
            connectId,
            ...values,
        };
        let saveRes = await autoServiceMessage({
            service: saveConfigService(requestData),
            success: window.lang.template(commonText.saveSuccess),
        });
        if (saveRes) {
            // setShouldSave(false);
            setErpConfig({
                ...values,
            });

            let noConfigId = !configId;
            let tempConfigId = "";

            if (noConfigId) {
                setConfigId(saveRes.data.configid);
                noConfigId = true;
                tempConfigId = saveRes.data.configid;
                /** 保存后将configId 同步到连接器 */
            } else {
                tempConfigId = configId;
            }

            //推送yht
            // let res = await pushYht(tempConfigId, testGroupPk)

            // if (res) {
            //     _useSetConnector.createConnect({
            //         ...instanceInfo,
            //         configId: tempConfigId
            //     })
            // }
        }
        return saveRes;
    }, saveInputs);

    const setDefault = useCallback(async (isInitTaskView) => {
        let res = await autoServiceMessage({
            service: setDefaultService({ configid: configId, gatewayId: gatewayId }),
            success: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050370") /* "设置默认成功!" */,
        });

        if (res) {
            //推送yht
            const pushYhtRes = await pushYht(configId, testGroupPk);
            if (pushYhtRes && isInitTaskView) {
                ConfirmDefaultGwToTask(gatewayId);
            }
        }
    }, saveInputs);

    return {
        instanceInfo,
        erpConfig,
        setErpConfig,
        oldConfig,
        setOldConfig,
        getOldConfig,
        buttonDisabled,
        setButtonDisabled,
        shouldSave,
        setShouldSave,
        shouldTest,
        setShouldTest,
        saveConfig,
        saveInputs,
        testConnect,
        testInputs,
        setDefault,
        testGroupPk,
    };
}

const steps = [
    {
        name: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050144") /* "网关" */,
        icon: <img fieldid="ublinker-routes-hrps-components-ErpConfig-index-8209044-img" src={gwIcon} />,
    },
    {
        name: "",
        icon: <img fieldid="ublinker-routes-hrps-components-ErpConfig-index-2671208-img" src={serIcon} />,
    },
    {
        name: "ERP",
        icon: <img fieldid="ublinker-routes-hrps-components-ErpConfig-index-835276-img" src={erpIcon} />,
    },
];

const ErpConfigView = (props) => {
    const [form] = FormList.useForm();
    const formItemLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 12 },
    };
    const { setFieldsValue, getFieldProps, validateFields, getFieldError, getFieldValue, isFieldsTouched } = form;
    const { connectorInfo, labelCol, erpVersion, erpVersions, _useErpConfig, isInitTaskView, gwIpNotSave } = props;

    const [testCardShow, setTestCardShow] = useState(false);
    const handleTestShowChange = useCallback((showState) => {
        setTestCardShow(showState);
    }, []);

    const { setBackConfirm } = useBackConfirm(props, isFieldsTouched());

    const {
        instanceInfo,
        erpConfig,
        oldConfig,
        setOldConfig,
        getOldConfig,
        buttonDisabled,
        shouldSave,
        setShouldSave,
        shouldTest,
        setShouldTest,
        saveConfig,
        saveInputs,
        testConnect,
        testInputs,
        setDefault,
    } = _useErpConfig;

    /** 当配置信息发生变化，重新设置配置信息form值 */
    useEffect(() => {
        setFieldsValue(erpConfig);
    }, [erpConfig]);

    useEffect(() => {
        setFieldsValue({ version: erpVersion });
    }, [erpVersion]);

    /** 测试连接窗口ref */
    const erpTestCardNode = useRef(null);

    /** 点击获取参数 */
    const handleGetOldConfig = useCallback(() => {
        // validateFields(['ipaddr'], (error, values) => {
        //     if (!error) {
        //         if (testCardShow) {
        //             erpTestCardNode.current.refresh()
        //         } else {
        //             handleTestShowChange(true)
        //         }
        //     }
        // })
        validateFields(["ipaddr"]).then((values) => {
            if (testCardShow) {
                erpTestCardNode.current.refresh();
            } else {
                handleTestShowChange(true);
            }
        });
    }, [testCardShow]);

    /** 参数配置列表窗台取消onCancel */
    const handleOldConfigModalCancel = useCallback(() => {
        setOldConfig({
            configList: [],
            oldConfigModalShow: false,
            selectedList: [],
        });
    }, []);

    /** 参数配置列表窗台确认onOk */
    const handleOldConfigModalOk = useCallback(() => {
        let { selectedList } = oldConfig;
        let data = selectedList[0];
        setFieldsValue({
            busicode: data.accountcode,
            groupcode: data.groupcode,
        });
        setShouldSave(true);
        setShouldTest(true);
        setBackConfirm(true);
        handleOldConfigModalCancel();
    }, [oldConfig]);

    /** 参数配置列表选择回调 */
    const getOldConfigSelectedDataFunc = useCallback(
        (selectedList) => {
            setOldConfig({ ...oldConfig, selectedList: selectedList });
        },
        [oldConfig]
    );

    const handleConfigChange = useCallback(() => {
        setShouldSave(true);
        setShouldTest(true);
    }, []);

    /** 点击测试连接按钮 */
    const handleTestConnect = useCallback(() => {
        validateFields().then((values) => {
            values.address = values.ipaddr;
            delete values.ipaddr;
            testConnect(values);
        });
    }, testInputs);

    /** 点击保存按钮 */
    const handleSave = useCallback(() => {
        validateFields().then((values) => {
            values.version = erpVersion;
            saveConfig(values).then((res) => {
                if (res) {
                    setBackConfirm(false);
                }
            });
        });
    }, saveInputs);

    // /布置适配器/
    const handleDeployment = useCallback(() => {
        validateFields().then(async (values) => {
            let requestData = {
                gatewayGuID: instanceInfo.gatewayId,
                action: "deploy",
                port: "12345",
                instances: "1",
                worker: false,
                infos: JSON.stringify({
                    host: values.host,
                    accountYear: values.accountYear,
                    account: values.account,
                }),
            };
            let saveRes = await autoServiceMessage({
                service: deploymentServiceNew(requestData),
                success: window.lang.template(commonText.saveSuccess),
            });
        });
    }, saveInputs);

    const erpTestCardTargetNode = useRef(null);

    const stepExpresses = useMemo(() => {
        return [
            {
                successText: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050779") /* "网络连接成功" */,
                failText: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050777") /* "网络连接失败" */,
                pendingText: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050765") /* "连接中" */,
                promise: () => {
                    return new Promise((resolve, reject) => {
                        testGwToErpService(
                            {
                                gatewayId: instanceInfo.gatewayId,
                                erpAddress: getFieldValue("ipaddr"),
                            },
                            false,
                            instanceInfo.useMainTenantGateway
                        )
                            .then((res) => {
                                resolve("success", "");
                            })
                            .catch(reject);
                    });
                },
            },
            {
                successText: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050786") /* "参数获取成功" */,
                failText: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050754") /* "参数获取失败" */,
                pendingText: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050794") /* "获取中" */,
                promise: () => {
                    return getOldConfig({ ipaddr: getFieldValue("ipaddr") });
                },
            },
        ];
    }, [instanceInfo.gatewayId]);

    const isNCC2005 = useMemo(() => erpVersion === "ncc_2005_native" || erpVersion === "ncc_2105_native" || erpVersion === "ncc_2111_native", [erpVersion]);

    return (
        <ConfigInfoItem
            title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202111301361", connectorInfo) /* "输入<%= name %>配置" */}
            // subTitle={
            //     <Tooltip fieldid="ublinker-routes-hrps-components-ErpConfig-index-9639733-Tooltip"
            //         className="erp-config-tooltip"
            //         inverse placement="topLeft"
            //         overlay={
            //             <div>
            //                 <p>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050793")  "云服务需要通过网关访问ERP服务，请输入正确的ERP配置。" }</p>
            //                 <p>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050797")  "如果测试连接异常，将会影响从云端访问ERP的相关业务。" }</p>
            //             </div>
            //         }>
            //         <i fieldid="ublinker-routes-hrps-components-ErpConfig-index-8730067-i" className="cl cl-Q ucg-mar-l-10" style={{ color: '#505766' }} />
            //     </Tooltip>
            // }
            connectorInfo={connectorInfo}
        >
            <FormList
                fieldid="ublinker-routes-hrps-components-ErpConfig-index-5426610-FormList"
                className="config-action-form"
                form={form}
                name="form122"
                labelAlign="right"
                {...formItemLayout}
                //   layoutOpt={{ md: 12 }}
            >
                <FormItem
                    fieldid="ublinker-routes-hrps-components-ErpConfig-index-9183785-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202111301355") /* "hrps地址" */}
                    // required labelCol={labelCol}
                    // error={getFieldError('host')}
                    // labelAlign={'top'}
                    name="host"
                    rules={[
                        {
                            required: true,
                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202111301356") /* "请输入hrps地址" */,
                        },
                        {
                            pattern: originReg,
                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050774") /* "服务器地址格式错误，请检查" */,
                        },
                    ]}
                >
                    <FormControl
                        fieldid="ublinker-routes-hrps-components-ErpConfig-index-3988837-FormControl"
                        className="ucg-mar-r-5"
                        disabled={buttonDisabled}
                        placeholder={
                            window.lang.template(
                                "MIX_UBL_ALL_UBL_FE_LOC_00050367",
                                connectorInfo
                            ) /* "请输入<%= name %>内网可以访问地址如http://************:3265" */
                        }
                        // {...getFieldProps('host', {
                        //     initialValue: '',
                        //     rules: [{
                        //         required: true, message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202111301356") /* "请输入hrps地址" */,
                        //     }, {
                        //         pattern: originReg, message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050774") /* "服务器地址格式错误，请检查" */
                        //     }],
                        //     // normalize: value => value.trim(),
                        //     // onChange: handleConfigChange
                        // })}
                        ref={erpTestCardTargetNode}
                    />
                    {/* "获取参数"
                    <Button fieldid="ublinker-routes-hrps-components-ErpConfig-index-1849660-Button" 
                        bordered
                        disabled={!getFieldValue('ipaddr')}
                        onClick={handleGetOldConfig}
                    >{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050255")}</Button> */}

                    {/* <TestCard
                        ref={erpTestCardNode}
                        targetNode={erpTestCardTargetNode}
                        style={{ width: 410 }}
                        show={testCardShow}
                        steps={steps}
                        stepExpresses={stepExpresses}
                        onClose={handleTestShowChange.bind(null, false)}
                    /> */}
                </FormItem>
                {/* "版本" 
                <FormItem fieldid="ublinker-routes-hrps-components-ErpConfig-index-9143759-FormItem" label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050443") } labelCol={labelCol}>
                    <Select fieldid="ublinker-routes-hrps-components-ErpConfig-index-8848433-Select" 
                        disabled data={erpVersions} value={erpVersion}
                        {...getFieldProps('version')}
                    />
                </FormItem> */}

                <FormItem
                    fieldid="ublinker-routes-hrps-components-ErpConfig-index-9030352-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050433") /* "账套编码" */}
                    // required labelCol={labelCol}
                    // error={getFieldError('account')}
                    name="account"
                    initialValue="nc65"
                    rules={[
                        {
                            required: true,
                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050553") /* "请输入账套编码" */,
                        },
                    ]}
                >
                    <FormControl
                        fieldid="ublinker-routes-hrps-components-ErpConfig-index-5183006-FormControl"
                        disabled={buttonDisabled}
                        placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202111301358") /* "请输入" */}
                        onChange={handleConfigChange}
                        // {...getFieldProps('account', {
                        //     initialValue: 'nc65',
                        //     rules: [{
                        //         required: true, message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050553") /* "请输入账套编码" */
                        //     }],
                        //     onChange: handleConfigChange
                        // })}
                    />
                </FormItem>

                {/* <FormItem fieldid="ublinker-routes-hrps-components-ErpConfig-index-3509318-FormItem" 
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050283")  "集团编码" }
                    required labelCol={labelCol}
                    error={getFieldError('groupcode')}
                >
                    <FormControl fieldid="ublinker-routes-hrps-components-ErpConfig-index-4129273-FormControl" 
                        disabled={buttonDisabled}
                        placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050318") "默认集团编码" }
                        {...getFieldProps('groupcode', {
                            initialValue: 'nc65',
                            rules: [{
                                required: true, message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050540")  "请输入默认集团编码" 
                            }],
                            onChange: handleConfigChange
                        })}
                    />

                </FormItem> */}

                <FormItem
                    fieldid="ublinker-routes-hrps-components-ErpConfig-index-1208728-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202111301357") /* "账套年份" */}
                    // labelCol={labelCol}
                    // required={isNCC2005}
                    // error={getFieldError('accountYear')}
                    name="accountYear"
                    rules={[
                        {
                            required: isNCC2005,
                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202111301359") /* "请输入账套年份" */,
                        },
                    ]}
                >
                    <FormControl
                        fieldid="ublinker-routes-hrps-components-ErpConfig-index-953113-FormControl"
                        disabled={buttonDisabled}
                        placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202111301358") /* "请输入" */}
                        onChange={handleConfigChange}
                        // {...getFieldProps('accountYear', {
                        //     initialValue: '',
                        //     rules: [{
                        //         required: isNCC2005, message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202111301359") /* "请输入账套年份" */
                        //     }],
                        //     onChange: handleConfigChange
                        // })}
                    />
                </FormItem>

                <FormItem fieldid="ublinker-routes-hrps-components-ErpConfig-index-3230354-FormItem" label=" ">
                    {/* 测试链接 */}
                    {/* <Button fieldid="ublinker-routes-hrps-components-ErpConfig-index-3115942-Button" 
                        className="ucg-mar-r-10" colors="primary"
                        disabled={buttonDisabled}
                        onClick={handleTestConnect}
                    >{window.lang.template(commonText.testConnect)}</Button> */}
                    {/* 保存修改 */}
                    <Button
                        fieldid="ublinker-routes-hrps-components-ErpConfig-index-5626403-Button"
                        className="ucg-mar-r-10"
                        colors="secondary"
                        // disabled={buttonDisabled || shouldTest}
                        disabled={buttonDisabled}
                        onClick={handleSave}
                    >
                        {window.lang.template(commonText.saveChange)}
                    </Button>

                    <Button
                        fieldid="ublinker-routes-hrps-components-ErpConfig-index-612026-Button"
                        className="ucg-mar-r-10"
                        colors="secondary"
                        disabled={buttonDisabled}
                        onClick={handleDeployment}
                    >
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202111301360") /*部署适配器*/}
                    </Button>

                    {/* "设置默认"  */}
                    {/* {
                        instanceInfo.useMainTenantGateway !== "true"?
                            <Button fieldid="ublinker-routes-hrps-components-ErpConfig-index-9957116-Button" 
                                colors="secondary"
                                disabled={buttonDisabled || (shouldSave || shouldTest)}
                                onClick={setDefault.bind(null, isInitTaskView)}
                            >{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050481") }</Button>
                            : null
                    } */}
                    {/* "推送网关配置" */}
                    {/* {PRIVATE ? (
                        <PushGwConfigToApp
                            gatewayId={instanceInfo.gatewayId}
                        >
                            <Button fieldid="ublinker-routes-hrps-components-ErpConfig-index-5281888-Button" 
                                className="ucg-mar-l-10"
                                colors="secondary"
                                disabled={buttonDisabled || (shouldSave || shouldTest)}
                            >{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050921")}</Button>
                        </PushGwConfigToApp>
                    ) : null} */}
                </FormItem>
            </FormList>

            <ModalView
                title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050621") /* "选择配置参数" */}
                show={oldConfig.oldConfigModalShow}
                onCancel={handleOldConfigModalCancel}
                onOk={handleOldConfigModalOk}
                size="md"
            >
                {oldConfig.oldConfigModalShow ? (
                    <Grid
                        fieldid="ublinker-routes-hrps-components-ErpConfig-index-5684381-Grid"
                        columns={[
                            {
                                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050433") /* "账套编码" */,
                                dataIndex: "accountcode",
                            },
                            {
                                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050283") /* "集团编码" */,
                                dataIndex: "groupcode",
                            },
                        ]}
                        radioSelect
                        rowKey="accountcode"
                        selectedList={oldConfig.selectedList}
                        getSelectedDataFunc={getOldConfigSelectedDataFunc}
                        data={oldConfig.configList}
                    />
                ) : null}
            </ModalView>
        </ConfigInfoItem>
    );
};

const FormErpConfigView = ErpConfigView;

export default injectRootStore()(FormErpConfigView);
