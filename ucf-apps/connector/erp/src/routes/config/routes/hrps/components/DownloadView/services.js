import { getInvokeService } from "utils/service";

/**
 * 获取nc|ncc|u8c补丁下载地址
 * @param erpVersion
 * @returns {*}
 */
export const downloadAdapterService = function (erpVersion) {
    let download_path = "";
    if (erpVersion.startsWith("nc")) {
        download_path = "ncpath";
    } else if (erpVersion.startsWith("u8c")) {
        download_path = "u8cpath";
    }
    return getInvokeService({
        method: "GET",
        path: `/mygwapp/ncconnect/${download_path}/download/ossurl/${erpVersion}`,
        responseType: "blob",
    });
};
