import React, { useState, useCallback, useMemo } from "react";
// import ProgressBar from 'tinper-bee/lib/ProgressBar';
// import LoadingState from 'tinper-bee/lib/LoadingState'
import { Button, Progress } from "components/TinperBee";
import Grid from "components/TinperBee/Grid";
import { autoServiceMessage } from "utils/service";
import { gatewayRunStateService, gwAdapterRunStatusService } from "./services";
import { ConfigInfoItem } from "../../../components/ConfigInfo";
import commonText from "constants/commonText";
import { GridAction, GridActions } from "components/TinperBee/Grid/GridActions";
import { formatTextArea, getUuid } from "utils/index";
import { Warning } from "utils/feedback";
import "./index.less";

const emptyFunc = () => {};

const HealthCheckView = (props) => {
    const { instanceInfo, adapterViewRef, hasCreate } = props;
    const [progressKey, setProgressKey] = useState(getUuid);
    const [checkState, setCheckState] = useState("init"); //init || checking || success || fail
    const [checkSuccessNum, setCheckSuccessNum] = useState(0);
    const [errorAdapters, setErrorAdapters] = useState([]);
    const updateAdapterFunc = adapterViewRef.current ? adapterViewRef.current.handleUpdateAdapter : emptyFunc;

    const useInputs = [instanceInfo.gatewayId];

    const handleCheck = useCallback(async () => {
        setProgressKey(getUuid());
        setCheckSuccessNum(0);
        setCheckState("checking");
        setErrorAdapters([]);
        const reqData = { gatewayId: instanceInfo.gatewayId, useMainTenantGateway: instanceInfo.useMainTenantGateway };
        let res = await autoServiceMessage({
            service: gatewayRunStateService(reqData),
            error: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050735") /* "网关已离线，请启动网关" */,
            errorType: "Warning",
        });
        if (res) {
            setCheckSuccessNum(50);
        } else {
            setCheckState("fail");
            return false;
        }
        let checkAdapterRes = await autoServiceMessage({
            service: gwAdapterRunStatusService(reqData),
            error: (error) => {
                let { data, msg } = error;
                Warning(formatTextArea(msg), 5);
                if (data && data.length > 0) {
                    setErrorAdapters(data);
                }
            },
        });
        if (checkAdapterRes) {
            setCheckSuccessNum(100);
            setCheckState("success");
        } else {
            setCheckState("fail");
        }
    }, useInputs);

    const errorAdapterColumns = useMemo(() => {
        return [
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050767") /* "适配器名称" */,
                dataIndex: "name",
                width: "60%",
                render: (value, record) => {
                    return value + `(${record.appid})`;
                },
            },
            {
                title: window.lang.template(commonText.actions),
                dataIndex: "$$actions",
                width: "40%",
                render: (value, record) => {
                    return (
                        <GridActions>
                            {/*<GridAction fieldid="UCG-FE-routes-hrps-components-HealthCheck-index-8254544-GridAction" onClick={updateAdapterFunc.bind(null, record, 'deploy')}>部署</GridAction>*/}
                            <GridAction
                                fieldid="UCG-FE-routes-hrps-components-HealthCheck-index-7534341-GridAction"
                                onClick={updateAdapterFunc.bind(null, record, "config")}
                            >
                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050766") /* "更新" */}
                            </GridAction>
                        </GridActions>
                    );
                },
            },
        ];
    }, [...useInputs, adapterViewRef.current]);

    return (
        <ConfigInfoItem title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050763") /* "网关健康检查" */}>
            <div className="ucg-align-cen ucg-w-50" style={{ margin: "0 auto" }}>
                <Button
                    fieldid="ublinker-routes-hrps-components-HealthCheck-index-6576737-Button"
                    loading={checkState === "checking"}
                    loadingText={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050775") /* "检查中" */}
                    // show={checkState === 'checking'}
                    colors="primary"
                    disabled={!hasCreate}
                    onClick={handleCheck}
                >
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050792") /* "健康检查" */}
                </Button>
                <div className="ucg-mar-t-xs gw-check-progress-wrap">
                    <Progress
                        fieldid="ublinker-routes-hrps-components-HealthCheck-index-9174099-Progress"
                        key={progressKey}
                        now={checkSuccessNum}
                        size="sm"
                        max={2}
                        percent={checkSuccessNum}
                    />
                    {checkState === "fail" ? (
                        <i fieldid="ublinker-routes-hrps-components-HealthCheck-index-2368989-i" className="cl cl-notice-p gw-check-icon fail"></i>
                    ) : null}
                    {checkState === "success" ? (
                        <i fieldid="ublinker-routes-hrps-components-HealthCheck-index-5210367-i" className="cl cl-right gw-check-icon success"></i>
                    ) : null}
                </div>
                {errorAdapters.length > 0 ? (
                    <Grid fieldid="ublinker-routes-hrps-components-HealthCheck-index-5243608-Grid" columns={errorAdapterColumns} data={errorAdapters} />
                ) : null}
            </div>
        </ConfigInfoItem>
    );
};

export default HealthCheckView;
