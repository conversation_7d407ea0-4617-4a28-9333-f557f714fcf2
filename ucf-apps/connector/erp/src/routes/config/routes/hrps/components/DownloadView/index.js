import React, { use<PERSON><PERSON>back, useMemo, useState, useEffect } from "react";
import { PRIVATE } from "utils/util";
import { a_download } from "utils/index";
import { Button, FormControl } from "components/TinperBee";
import { ConfigInfoItem } from "../../../components/ConfigInfo";
import { getLocalImg } from "utils/index";
import { autoServiceMessage } from "utils/service";
import { DownloadGateway } from "components/DownloadGateway";
import { downloadAdapterService } from "./services";

import { downloadGatewayKey } from "services/gwServices";

function useDownLoad(instanceInfo, erpVersion) {
    let { gatewayId } = instanceInfo;

    const downloadKeyClick = useCallback(() => {
        downloadGatewayKey(gatewayId, PRIVATE);
    }, [gatewayId]);

    let downloadNcAdapterClick = useCallback(async () => {
        let res = await autoServiceMessage({
            service: downloadAdapterService(erpVersion),
        });
        if (res) {
            const blob = res.data;
            const reader = new FileReader();
            reader.readAsDataURL(blob);
            reader.onload = (e) => {
                const a = document.createElement("a");
                console.log(res);
                console.log(res.headers);
                a.download = res.headers["content-disposition"].split("=")[1];
                // a.download = `文件名称.zip`;
                // 后端设置的文件名称在res.headers的 "content-disposition": "form-data; name=\"attachment\"; filename=\"20181211191944.zip\"",
                a.href = e.target.result;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
            };
            // a_download(res.data,true)
        }
    }, [erpVersion]);

    return { downloadKeyClick, downloadNcAdapterClick };
}

const DownloadView = (props) => {
    const { hasCreate, connectorType, connectorInfo, instanceInfo, erpVersion } = props;
    const { downloadKeyClick, downloadNcAdapterClick } = useDownLoad(instanceInfo, erpVersion);
    const hasAdapter = useMemo(() => ["nc", "nccloud", "u8cloud"].includes(connectorType), [connectorType]);

    const locale = useMemo(() => {
        return {
            patchTitle: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050288") /* "安装补丁" */,
            patchButton: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050357") /* "下载补丁" */,
            patchIntro: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050617", connectorInfo) /* "把下载的补丁打到<%= name %>中，重启<%= name %>" */,
            gwTitle: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050575") /* "下载网关" */,
        };
    }, [connectorInfo]);

    return (
        <>
            {hasAdapter ? (
                <ConfigInfoItem step="2" title={locale.patchTitle}>
                    <div className="config-action-form">
                        <Button
                            fieldid="ublinker-routes-hrps-components-DownloadView-index-7615422-Button"
                            bordered
                            disabled={!hasCreate}
                            onClick={downloadNcAdapterClick}
                        >
                            {locale.patchButton}
                        </Button>
                        <p className="config-text-third">{locale.patchIntro}</p>
                    </div>
                </ConfigInfoItem>
            ) : null}
            <ConfigInfoItem step="3" title={locale.gwTitle}>
                <p className="config-text">
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050304") /* "找一台7x24小时都能访问您ERP系统，且能访问互联网的机器，下载网关。" */}
                </p>
                <p className="config-text">
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050413") /* "下载完成后解压，需保证解压后的文件目录不存在中文字符。" */}
                </p>
                <div className="config-text ucg-mar-t-10">
                    {PRIVATE ? (
                        <div>
                            <p className="config-text" style={{ color: "red" }}>
                                {
                                    window.lang.template(
                                        "MIX_UBL_ALL_UBL_FE_LOC_20219101640"
                                    ) /* "请复制相应的连接到能访问外网的机器中下载对应版本的网关客户端" */
                                }
                            </p>
                            <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20219101641") /*【Windows服务器启动网关】*/}</p>
                            <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20219101642") /*下载方式：可复制到浏览器中下载。*/}</p>
                            <p className="config-text">
                                {
                                    window.lang.template(
                                        "MIX_UBL_ALL_UBL_FE_LOC_20219101643"
                                    ) /*下载地址：https://ncc-pub-ublim.oss-cn-beijing.aliyuncs.com/ubl/gateway/private/gatewayClient_windows_64.zip*/
                                }
                            </p>
                            <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20219101644") /*例如：*/}</p>
                            <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20219101645") /*【Linux服务器启动网关】*/}</p>
                            <p className="config-text">
                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20219101646") /*下载方式：可使用命令‘curl -# -O 下载地址'  */}
                            </p>
                            <p className="config-text">
                                {
                                    window.lang.template(
                                        "MIX_UBL_ALL_UBL_FE_LOC_20219101647"
                                    ) /*下载地址：https://ncc-pub-ublim.oss-cn-beijing.aliyuncs.com/ubl/gateway/private/gatewayClient_linux_64.zip*/
                                }
                            </p>
                            <p className="config-text">
                                {
                                    window.lang.template(
                                        "MIX_UBL_ALL_UBL_FE_LOC_20219101648"
                                    ) /*例如：curl -# -O https://ncc-pub-ublim.oss-cn-beijing.aliyuncs.com/ubl/gateway/private/gatewayClient_linux_64.zip*/
                                }
                            </p>
                        </div>
                    ) : (
                        <DownloadGateway gatewayId={instanceInfo.gatewayId}>
                            <Button fieldid="ublinker-routes-hrps-components-DownloadView-index-1335777-Button" bordered disabled={!hasCreate}>
                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050291") /* "请先下载网关" */}
                            </Button>
                        </DownloadGateway>
                    )}
                </div>
            </ConfigInfoItem>

            <ConfigInfoItem step="4" title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050487") /* "下载密钥并启动网关" */}>
                <Button fieldid="ublinker-routes-hrps-components-DownloadView-index-8018014-Button" bordered disabled={!hasCreate} onClick={downloadKeyClick}>
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050425") /* "下载密钥" */}
                </Button>
                <p className="config-text ucg-mar-t-10">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050364") /* "首先下载密钥" */}</p>
                <p className="config-text">
                    <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050451") /* "Windows环境：" */}</strong>{" "}
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050615") /* "将下载的密钥解压至网关解压目录的config文件夹下" */}
                </p>
                <p className="config-text">
                    <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050436") /* "启动网关：" */}</strong>
                    {
                        window.lang.template(
                            "MIX_UBL_ALL_UBL_FE_LOC_000504291"
                        ) /* "在config的同级文件夹找到bin文件夹，例如：F:gateway ccpub-gateway-client-1.0.0-SNAPSHOTin，运行startup.bat。" */
                    }
                </p>
                <p className="config-text">
                    <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050350") /* "Linux环境：" */}</strong>
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050615") /* "将下载的密钥解压至网关解压目录的config文件夹下" */}
                </p>
                <p className="config-text">
                    <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050436") /* "启动网关：" */}</strong>
                    {
                        window.lang.template(
                            "MIX_UBL_ALL_UBL_FE_LOC_000504291"
                        ) /* "在config的同级文件夹找到bin文件夹，例如：F:gateway ccpub-gateway-client-1.0.0-SNAPSHOTin，运行startup.bat。" */
                    }
                </p>
                <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050544") /* "启动成功后，选择网关语言。如图" */}</p>
                <div className="config-img">
                    <img fieldid="ublinker-routes-hrps-components-DownloadView-index-7147071-img" src={getLocalImg("connector/gaojiban.png")} alt="" />
                </div>
            </ConfigInfoItem>
        </>
    );
};

export default DownloadView;
