import React, { useState, useEffect, useCallback, useMemo, Fragment } from "react";
import { Button, FormControl, Modal, Select, Progress } from "components/TinperBee";
// import ProgressBar from 'tinper-bee/lib/ProgressBar'
import Grid from "components/TinperBee/Grid";
import { ConfigInfoItem } from "../../../components/ConfigInfo";
import { autoServiceMessage } from "utils/service";
import commonText from "constants/commonText";
import { initDocStatusService, getDefDocService, editDefDocService, pushDefDocService } from "../../services";

export function useInitDoc(_useSetConnector, connectorConfig) {
    let [defDocList, setDefDocList] = useState([]); //档案列表
    let [selectedDefDoc, setSelectedDefDoc] = useState([]); //已选档案列表
    let [cloudDocSelectData, setCloudDocSelectedData] = useState([]); //diwork档案下拉数据
    let [docGridLoading, setDocGridLoading] = useState(false); //档案表格loading控制
    let [docPushProgress, setDocPushProgress] = useState(0); //档案推送进度
    let [docPushProgressMax, setDocPushProgressMax] = useState(0); //档案推送进度最大值
    let [errPushDocList, setErrPushDocList] = useState([]); //推送错误doc列
    let [stepDone, setStepDone] = useState(false);
    let { erpVersion, instanceInfo } = _useSetConnector;

    //获取档案初始化状态
    const getDefaultStatus = useCallback(async () => {
        let res = await autoServiceMessage({
            service: initDocStatusService(erpVersion),
        });
        if (res) {
            setStepDone(res.init);
        }
    }, [erpVersion]);

    useEffect(() => {
        if (erpVersion && instanceInfo.configId) {
            getDefaultStatus();
        }
    }, [erpVersion, instanceInfo.configId]);

    //获取档案列表
    const getDefaultDoc = useCallback(async () => {
        let res = await autoServiceMessage({
            service: getDefDocService({ gatewayId: instanceInfo.gatewayId, erpVersion }),
        });
        if (res) {
            let { ncMappings = [], diworkData = [] } = res;
            let selectedList = [];
            ncMappings.forEach((item) => {
                if (item._checked) {
                    selectedList.push(item);
                }
            });
            let _cloudDocSelectData = diworkData.map((item) => {
                return {
                    key: item.cloudName,
                    value: item.cloudPk,
                };
            });
            setDefDocList(ncMappings);
            setSelectedDefDoc(selectedList);
            setCloudDocSelectedData(_cloudDocSelectData);
        }
    }, [erpVersion, instanceInfo]);

    useEffect(() => {
        if (erpVersion && instanceInfo.gatewayId && instanceInfo.configId && connectorConfig.ipaddr) {
            getDefaultDoc();
        }
    }, [erpVersion, instanceInfo, connectorConfig.ipaddr]);

    //保存档案编辑项
    const saveEditDoc = useCallback(
        async (data, index) => {
            let _cloudData = cloudDocSelectData.find((item) => item.value === data._cloudPk);
            let res = await autoServiceMessage({
                service: editDefDocService({
                    cloudName: _cloudData.key,
                    cloudPk: _cloudData.value,
                    erpName: data.erpName,
                    erpPk: data.erpPk,
                    erpversion: erpVersion,
                }),
                success: window.lang.template(commonText.saveSuccess),
            });
            if (res) {
                let docData = defDocList[index];
                docData.isEdit = false;
                docData.cloudPk = _cloudData.value;
                docData.cloudName = _cloudData.key;
                delete docData._cloudPk;
                setDefDocList([...defDocList]);
            }
        },
        [erpVersion, cloudDocSelectData, defDocList]
    );

    const pushDoc = useCallback(
        async (erpPks, total = docPushProgressMax, nowProgress = 0, errorList = []) => {
            let _erpPks = erpPks.splice(0, 1);
            let res = await autoServiceMessage({
                service: pushDefDocService({
                    erpVersion: erpVersion,
                    erpPks: _erpPks,
                }),
                error: () => {},
            });

            let errorDocList = errorList;
            if (!res) {
                errorDocList = errorDocList.concat(_erpPks);
                setErrPushDocList(errorDocList);
            }

            let _nowProgress = nowProgress + 1;
            setDocPushProgress(_nowProgress);
            if (_nowProgress < total) {
                pushDoc(erpPks, total, _nowProgress, errorDocList);
            } else {
                let errLength = errorDocList.length;
                Modal.info({
                    title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050576") /* "完成情况统计" */,
                    backdropClosable: false,
                    content: (
                        <div>
                            <p>
                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050562") /* "成功：" */}
                                <strong style={{ color: "#19be6b" }}>{total - errLength}</strong>
                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050603") /* "条" */}
                            </p>
                            <p>
                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050550") /* "失败：" */}
                                <strong style={{ color: "#ed4014" }}>{errLength}</strong>
                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050603") /* "条" */}
                            </p>
                            {errLength > 0 ? (
                                <p>
                                    {
                                        window.lang.template(
                                            "MIX_UBL_ALL_UBL_FE_LOC_00050300"
                                        ) /* "已帮您记录下了保存错误的档案，您可以点击“再次提交保存失败的档案”按钮，再次保存它们。" */
                                    }
                                </p>
                            ) : null}
                        </div>
                    ),
                    confirmType: "one",
                    onOk: () => {
                        setDocPushProgress(0);
                        setDocPushProgressMax(0);
                        setDocGridLoading(false);
                        if (!stepDone) {
                            setStepDone(true);
                        }
                    },
                });
            }
        },
        [erpVersion, docPushProgress, errPushDocList, stepDone]
    );

    const againPushDoc = useCallback(() => {
        setDocGridLoading(true);
        setDocPushProgressMax(errPushDocList.length);
        pushDoc([...errPushDocList], errPushDocList.length);
    }, [errPushDocList]);

    const handleSave = useCallback(() => {
        let selectedDocLength = selectedDefDoc.length;
        console.log(selectedDocLength);
        Modal.confirm({
            fieldid: "202306091522",
            title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050583") /* "是否保存自定义档案？" */,
            content: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050630", { length: selectedDocLength }) /* "当前共选择 <%= length %> 条" */,
            onOk: () => {
                setDocGridLoading(true);
                setDocPushProgressMax(selectedDocLength);
                let erpPks = selectedDefDoc.map((item) => item.erpPk);
                pushDoc(erpPks, selectedDocLength);
            },
        });
    }, [selectedDefDoc]);

    const defDocGridColumns = useMemo(
        () => [
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050581") /* "ERP档案" */,
                dataIndex: "erpName",
            },
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050254") /* "diwork档案" */,
                dataIndex: "cloudPk",
                render: (cloudPk, record, index) => {
                    if (record.isEdit) {
                        return (
                            <Select
                                fieldid="ublinker-routes-hrps-components-InitDoc-index-4287884-Select"
                                data={cloudDocSelectData}
                                value={record.cloudName}
                                onChange={(value) => {
                                    defDocList[index]._cloudPk = value;
                                    defDocList[index].cloudName = cloudDocSelectData.find((item) => {
                                        return item.value === value;
                                    }).key;
                                    setDefDocList([...defDocList]);
                                }}
                            />
                        );
                    } else {
                        return record.cloudName || cloudPk;
                    }
                },
            },
            {
                title: "",
                dataIndex: "$$save",
                render: (value, record, index) => {
                    return record.isEdit ? (
                        <Fragment>
                            <Button
                                fieldid="ublinker-routes-hrps-components-InitDoc-index-406568-Button"
                                {...Grid.hoverButtonPorps}
                                onClick={saveEditDoc.bind(null, record, index)}
                            >
                                {window.lang.template(commonText.save)}
                            </Button>
                            <Button
                                fieldid="ublinker-routes-hrps-components-InitDoc-index-6543736-Button"
                                {...Grid.hoverButtonPorps}
                                colors="secondary"
                                onClick={() => {
                                    let docData = defDocList[index];
                                    docData.isEdit = false;
                                    delete docData._cloudPk;
                                    setDefDocList([...defDocList]);
                                }}
                            >
                                {window.lang.template(commonText.cancel)}
                            </Button>
                        </Fragment>
                    ) : null;
                },
            },
            {
                title: window.lang.template(commonText.actions),
                dataIndex: "$$actions",
                width: 80,
                render: (value, record, index) => {
                    return (
                        <a
                            fieldid="ublinker-routes-hrps-components-InitDoc-index-3994757-a"
                            onClick={() => {
                                let docData = defDocList[index];
                                docData.isEdit = true;
                                docData._cloudPk = record.cloudPk;
                                setDefDocList([...defDocList]);
                            }}
                        >
                            {window.lang.template(commonText.edit)}
                        </a>
                    );
                },
            },
        ],
        [defDocList, cloudDocSelectData]
    );

    return {
        defDocGridColumns,
        defDocList,
        setDefDocList,
        selectedDefDoc,
        setSelectedDefDoc,
        cloudDocSelectData,
        handleSave,
        docGridLoading,
        docPushProgress,
        docPushProgressMax,
        errPushDocList,
        againPushDoc,
        stepDone,
    };
}

const InitDoc = (props) => {
    let { _useInitDoc } = props;
    let {
        defDocList,
        selectedDefDoc,
        setSelectedDefDoc,
        defDocGridColumns,
        handleSave,
        docGridLoading,
        docPushProgress,
        docPushProgressMax,
        errPushDocList,
        againPushDoc,
    } = _useInitDoc;

    const disabled = useMemo(() => selectedDefDoc.length <= 0, [selectedDefDoc]);

    return (
        <ConfigInfoItem step="5" title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050418") /* "修改自定义档案" */}>
            <p className="config-text ucg-mar-b-10">
                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050434") /* "如无需配置自定义档案，请直接点击“保存自定义档案”按钮以保存此步骤" */}
            </p>
            <Grid
                fieldid="ublinker-routes-hrps-components-InitDoc-index-8147333-Grid"
                columns={defDocGridColumns}
                data={defDocList}
                multiSelect
                selectedList={selectedDefDoc}
                getSelectedDataFunc={setSelectedDefDoc}
                autoCheckedByClickRows={false}
                scroll={{ y: 300 }}
                empty={" "}
                loading={docGridLoading}
                rowKey="pkId"
            />

            {docGridLoading ? (
                <div className="ucg-mar-t-5">
                    <Progress
                        fieldid="ublinker-routes-hrps-components-InitDoc-index-1377591-Progress"
                        now={docPushProgress}
                        percent={docPushProgress}
                        size="sm"
                        max={docPushProgressMax}
                    />
                </div>
            ) : null}

            <div className="ucg-mar-t-5">
                <Button
                    fieldid="ublinker-routes-hrps-components-InitDoc-index-9330373-Button"
                    colors="primary"
                    disabled={disabled || docGridLoading}
                    onClick={handleSave}
                >
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050549") /* "保存自定义档案" */}
                </Button>
                {!docGridLoading && errPushDocList.length > 0 ? (
                    <Button fieldid="ublinker-routes-hrps-components-InitDoc-index-118534-Button" className="ucg-mar-l-5" bordered onClick={againPushDoc}>
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050377") /* "再次提交保存失败的档案" */}
                    </Button>
                ) : null}
            </div>
        </ConfigInfoItem>
    );
};

export default InitDoc;
