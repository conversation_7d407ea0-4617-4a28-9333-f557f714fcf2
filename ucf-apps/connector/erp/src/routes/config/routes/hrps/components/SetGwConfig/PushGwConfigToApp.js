import React, { useCallback } from "react";
import { pushGwConfigToApp } from "./services";
import { autoServiceMessage } from "utils/service";

const PushGwConfigToApp = (props) => {
    const { children, gatewayId } = props;
    const handleChildrenClick = useCallback(async () => {
        await autoServiceMessage({
            service: pushGwConfigToApp(gatewayId),
            success: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050920") /* "网关配置推送成功!" */,
        });
    }, [gatewayId]);

    return (
        <>
            {React.cloneElement(children, {
                onClick: handleChildrenClick,
            })}
        </>
    );
};

export default PushGwConfigToApp;
