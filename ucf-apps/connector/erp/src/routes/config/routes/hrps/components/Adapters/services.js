import { getInvokeService } from "utils/service";

/**
 * 获取网关适配列表
 * @param {String} gatewayId
 * @returns {*}
 */
export const getAdapterListService = function (gatewayId) {
    return getInvokeService({
        method: "GET",
        path: `/mygwapp/gateway/${gatewayId}/adapter`,
        showLoading: false,
    });
};

/**
 * 获取适配器信息
 * @param {Object} data
 * @param {String} data.appId -适配器列表中的appid
 * @param {String} data.gatewayId
 * @returns {*}
 */
export const getAdapterInfoService = function (data) {
    let { appId, gatewayId } = data;
    return getInvokeService(
        {
            method: "GET",
            path: "/mygwapp/gateway/adapter/info/" + appId,
        },
        { gatewayGuID: gatewayId }
    );
};

/**
 * 部署、更新以及卸载适配器
 * @param {Object} data
 * @param {String} data.appId -适配器列表中的appid
 * @param {String} data.gatewayId
 * @param {String} [data.action=deploy部署|config更新|undeploy卸载]
 * @param {String} data.port
 * @param {String=} data.whitelist
 * @returns {*}
 */
export const updateAdapterService = function (data) {
    let { appId, gatewayId, ...other } = data;
    other.gatewayGuID = gatewayId;
    return getInvokeService(
        {
            method: "POST",
            path: "/mygwapp/gateway/adapter/" + appId,
        },
        other
    );
};

/**
 * 租户获取适配器配置(根据适配器配置模板),
 * 如果某些配置项用户已经修改过，那个defaultValue会是已经修改过的值
 * @param {Object} data
 * @param {String} data.adapterId -适配器列表中的appid
 * @param {String} data.gatewayId
 * @returns {Promise | Promise<unknown>}
 */
export const getAdapterConfigByTemplateService = function (data) {
    return getInvokeService(
        {
            method: "GET",
            path: "/mygwapp/gateway/adapter/detail",
        },
        data
    );
};

/**
 * 部署、更新以及卸载适配器(根据适配器模板)
 * @param {Object} data
 * @param {String} data.adapterId -适配器列表中的appid
 * @param {String} data.gatewayId
 * @param {String} [data.action=deploy部署|config更新|undeploy卸载]
 * @param {Array} data.values -根据模板列表
 * @example [
    {code: 'name', value: '123'}
 ]
 * @returns {*}
 */
export const updateAdapterByTemplateService = function (data) {
    let { values, ...params } = data;
    return getInvokeService(
        {
            method: "POST",
            path: "/mygwapp/gateway/adapter/action",
        },
        values,
        params
    );
};

/***
 * 检查适配器运行状态
 * @param {Object} data
 * @param {String} data.gatewayId
 * @returns {Promise | Promise<unknown>}
 */
export const checkAdapterStatusService = function (data) {
    return getInvokeService(
        {
            method: "GET",
            path: "/mygwapp/gateway/adapter/state",
        },
        data
    );
};
