import { useState, useEffect, useMemo, useCallback } from "react";
import { getErpVersionsService } from "services/gwServices";

/**
 * erp版本列表以及 默认版本
 * @param {Object} connectorInfo
 * @param {String} connectorType
 * @returns {{defaultErpVersion: *, erpVersions: *}}
 */
export default function (connectorInfo, connectorType) {
    const [erpVersions, setErpVersions] = useState([]);
    const [defaultErpVersion, setDefaultErpVersion] = useState(null);
    const isNc = useMemo(() => {
        return connectorType === "nc" || connectorType === "nccloud" || connectorType === "hrps";
    }, [connectorType]);

    const setVersion = useCallback(
        (versions) => {
            let _versions = versions || connectorInfo.erpVersions || [];
            setErpVersions(_versions || []);
            let defaultVersion = _versions[0];
            if (defaultVersion) {
                setDefaultErpVersion(defaultVersion.value);
            }
        },
        [connectorType, connectorInfo]
    );

    useEffect(() => {
        if (isNc) {
            let _versionType = connectorType === "nccloud" ? "ncc" : connectorType;
            getErpVersionsService(_versionType)
                .then((res) => {
                    let versions = res.data || [];
                    setVersion(
                        versions.map((item) => {
                            return {
                                key: item.versionName,
                                value: item.versionCode,
                            };
                        })
                    );
                })
                .catch(() => {
                    setVersion();
                });
        } else {
            setVersion();
        }
    }, [isNc, connectorType]);

    return {
        erpVersions,
        defaultErpVersion,
    };
}
