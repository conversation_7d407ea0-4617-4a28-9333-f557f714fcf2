import React, { Fragment, useState, useEffect, useCallback, useMemo } from "react";
import { <PERSON>ton, Modal, FormControl, Select } from "components/TinperBee";
import { autoServiceMessage } from "utils/service";
import Grid from "components/TinperBee/Grid";
import { GridActions, GridAction } from "components/TinperBee/Grid/GridActions";
import { originReg } from "utils/regExp";
import { injectRootStore } from "core/addStore";
import useBackConfirm from "hooks/useBackConfirm";
import { ConfigInfoItem } from "../../../components/ConfigInfo";
import { getSycParamsService, deleteSysParamService, editSysParamService, getGwAppSecretService, saveGwHighConfigService } from "../../services";
import commonText from "constants/commonText";

import FormList from "components/TinperBee/Form";
const FormItem = FormList.Item;
export function useHighSetConfig(instanceInfo) {
    let { gatewayId } = instanceInfo;

    let [systemParams, setSystemParams] = useState([]);

    const getSystemParams = useCallback(async () => {
        let res = await autoServiceMessage({
            service: getSycParamsService({
                gwId: instanceInfo.gatewayId,
                pageNo: 1,
                pageSize: 100,
            }),
        });
        if (res) {
            let resData = res.data || {};
            setSystemParams(resData.items || []);
        }
    }, [instanceInfo]);

    //获取轻应用网关密钥
    const getGwAppKey = useCallback(
        async (gwaddr, cb) => {
            let res = await autoServiceMessage({
                service: getGwAppSecretService(gwaddr),
            });
            if (res) {
                if (cb) {
                    cb(res.gwsecret);
                }
            }
        },
        [instanceInfo]
    );

    // 保存网关配置高级配置
    const saveGatewayHighConfig = useCallback(
        async (data) => {
            data.gatewayId = instanceInfo.gatewayId;
            return await autoServiceMessage({
                service: saveGwHighConfigService(data),
                success: window.lang.template(commonText.operateSuccess),
            });
        },
        [instanceInfo]
    );

    const deleteParams = useCallback(
        (pk_id) => {
            Modal.confirm({
                fieldid: "202306091520",

                title: window.lang.template(commonText.confirmDelete),
                onOk: async () => {
                    let res = await autoServiceMessage({
                        service: deleteSysParamService(pk_id),
                    });
                    if (res) {
                        getSystemParams();
                    }
                },
            });
        },
        [instanceInfo]
    );

    //保存档案编辑项
    const saveParam = useCallback(
        async (data, index) => {
            let res = await autoServiceMessage({
                service: editSysParamService({
                    gwid: gatewayId,
                    pk_id: data.pk_id,
                    paramcode: data._paramcode,
                    paramname: data._paramname,
                    paramvalue: data._paramvalue,
                }),
                success: window.lang.template(commonText.saveSuccess),
            });
            if (res) {
                let param = systemParams[index];
                param.isEdit = false;
                param.paramcode = data._paramcode;
                param.paramname = data._paramname;
                param.paramvalue = data._paramvalue;
                delete param._paramcode;
                delete param._paramname;
                delete param._paramvalue;
                setSystemParams([...systemParams]);
            }
        },
        [gatewayId, systemParams]
    );

    const renderCell = useCallback((field, _systemParams) => {
        let _field = "_" + field;
        return (value, record, index) => {
            if (record.isEdit) {
                return (
                    <FormControl
                        fieldid="ublinker-routes-hrps-components-HighSet-index-43481-FormControl"
                        value={record[_field]}
                        showClose
                        onChange={(value) => {
                            _systemParams[index][_field] = value;
                            setSystemParams([..._systemParams]);
                        }}
                    />
                );
            } else {
                return value;
            }
        };
    }, []);

    const systemGirdColumns = useMemo(() => {
        return [
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050402") /* "参数编码" */,
                dataIndex: "paramcode",
                render: renderCell("paramcode", systemParams),
            },
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050498") /* "参数名称" */,
                dataIndex: "paramname",
                render: renderCell("paramname", systemParams),
            },
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050376") /* "参数值" */,
                dataIndex: "paramvalue",
                render: renderCell("paramvalue", systemParams),
            },
            {
                title: "",
                dataIndex: "$$save",
                width: 140,
                render: (value, record, index) => {
                    return record.isEdit ? (
                        <Fragment>
                            <Button
                                fieldid="ublinker-routes-hrps-components-HighSet-index-4382472-Button"
                                {...Grid.hoverButtonPorps}
                                onClick={saveParam.bind(null, record, index)}
                            >
                                {window.lang.template(commonText.save)}
                            </Button>
                            <Button
                                fieldid="ublinker-routes-hrps-components-HighSet-index-4622413-Button"
                                {...Grid.hoverButtonPorps}
                                colors="secondary"
                                onClick={() => {
                                    let param = systemParams[index];
                                    delete param._paramcode;
                                    delete param._paramname;
                                    delete param._paramvalue;
                                    param.isEdit = false;
                                    setSystemParams([...systemParams]);
                                }}
                            >
                                {window.lang.template(commonText.cancel)}
                            </Button>
                        </Fragment>
                    ) : null;
                },
            },
            {
                title: window.lang.template(commonText.actions),
                dataIndex: "$$actions",
                width: 110,
                render: (value, record, index) => {
                    let { tenantedit, issys, pk_id } = record;
                    let noEdit = !tenantedit;
                    return (
                        <GridActions>
                            <GridAction
                                fieldid="UCG-FE-routes-hrps-components-HighSet-index-8223565-GridAction"
                                disabled={noEdit}
                                onClick={() => {
                                    let param = systemParams[index];
                                    let { paramcode, paramname, paramvalue } = param;
                                    param._paramcode = paramcode;
                                    param._paramname = paramname;
                                    param._paramvalue = paramvalue;
                                    param.isEdit = true;
                                    setSystemParams([...systemParams]);
                                }}
                            >
                                {window.lang.template(commonText.edit)}
                            </GridAction>
                            <GridAction
                                fieldid="UCG-FE-routes-hrps-components-HighSet-index-8717891-GridAction"
                                disabled={noEdit || issys}
                                onClick={deleteParams.bind(null, pk_id)}
                            >
                                {window.lang.template(commonText.deletion)}
                            </GridAction>
                        </GridActions>
                    );
                },
            },
        ];
    }, [systemParams]);

    useEffect(() => {
        if (instanceInfo.gatewayId) {
            getSystemParams();
        }
    }, [instanceInfo]);

    return {
        systemParams,
        systemGirdColumns,
        getGwAppKey,
        saveGatewayHighConfig,
        instanceInfo,
    };
}

const fields = ["appAddress", "appSecret"];

const HighSet = (props) => {
    let {
        _useHighSetConfig,
        hasCreate,
        labelCol,
        gwConfigInfo,
        form: { setFieldsValue, getFieldProps, validateFields, getFieldError, getFieldValue, getFieldsValue, isFieldsTouched },
    } = props;
    let { systemParams, systemGirdColumns, getGwAppKey, saveGatewayHighConfig, instanceInfo } = _useHighSetConfig;

    const _isFieldsTouched = isFieldsTouched();

    const { setBackConfirm } = useBackConfirm(props, _isFieldsTouched);

    useEffect(() => {
        let { gwAddr, gwSecret } = gwConfigInfo;
        setFieldsValue({
            appAddress: gwAddr,
            appSecret: gwSecret,
        });
    }, [gwConfigInfo]);

    const handleGetAppKey = useCallback(() => {
        validateFields(["appAddress"], (error, values) => {
            if (!error) {
                getGwAppKey(values.appAddress, (appSecret) => {
                    setFieldsValue({ appSecret });
                });
            }
        });
    }, []);

    const handleSave = useCallback(() => {
        let data = getFieldsValue(fields);
        saveGatewayHighConfig(data).then((res) => {
            if (res) {
                setBackConfirm(false);
            }
        });
    }, [instanceInfo]);

    return (
        <ConfigInfoItem step="5" title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050474") /* "高级设置" */}>
            <Grid fieldid="ublinker-routes-hrps-components-HighSet-index-565703-Grid" rowKey="pk_id" columns={systemGirdColumns} data={systemParams} />
            <FormList
                fieldid="ublinker-routes-hrps-components-HighSet-index-2933937-FormList"
                className="config-action-form ucg-mar-t-20"
                layoutOpt={{ md: 12 }}
            >
                <FormItem
                    fieldid="ublinker-routes-hrps-components-HighSet-index-8897295-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050652") /* "轻应用网关地址" */}
                    labelCol={labelCol}
                    error={getFieldError("appAddress")}
                >
                    <FormControl
                        fieldid="ublinker-routes-hrps-components-HighSet-index-58668-FormControl"
                        placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050641") /* "可通过轻应用网关名称获取混合云密钥" */}
                        {...getFieldProps("appAddress", {
                            initialValue: "",
                            rules: [
                                {
                                    pattern: originReg,
                                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050491") /* "请输入正确的轻应用网关地址" */,
                                },
                            ],
                        })}
                    />
                </FormItem>
                <FormItem
                    fieldid="ublinker-routes-hrps-components-HighSet-index-841147-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050289") /* "混合云密钥" */}
                    labelCol={labelCol}
                >
                    <FormControl
                        fieldid="ublinker-routes-hrps-components-HighSet-index-1385823-FormControl"
                        placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050311") /* "混合云密钥" */}
                        {...getFieldProps("appSecret")}
                    />
                    <Button
                        fieldid="ublinker-routes-hrps-components-HighSet-index-9226145-Button"
                        className="ucg-mar-l-5"
                        disabled={!getFieldValue("appAddress")}
                        bordered
                        onClick={handleGetAppKey}
                    >
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050312") /* "获取混合云密钥" */}
                    </Button>
                </FormItem>

                <FormItem fieldid="ublinker-routes-hrps-components-HighSet-index-7087515-FormItem" labelCol={labelCol}>
                    <Button fieldid="ublinker-routes-hrps-components-HighSet-index-4405859-Button" disabled={!hasCreate} colors="primary" onClick={handleSave}>
                        {window.lang.template(commonText.saveChange)}
                    </Button>
                </FormItem>
            </FormList>
        </ConfigInfoItem>
    );
};

const FormView = FormList.createForm()(HighSet);

export default injectRootStore()(FormView);
