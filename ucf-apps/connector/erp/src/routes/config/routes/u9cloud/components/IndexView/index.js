import React, { Fragment, useMemo, useCallback } from "react";
import ConfigTarget from "../../../components/ConfigTarget";
import { ConfigInfoList, ConfigInfoItem } from "../../../components/ConfigInfo";
import { useLocationParamsMemo } from "../../../common/utilHooks";
import { connectorInfoMap } from "../../../common/constants";
import { useSetConnector } from "../../../nc/components/IndexView/hooks";
import DownloadGw from "../DownloadGw";
import RunGw from "../RunGw";
import SecretKeysView, { useSecretKeysHook } from "../SecretKeys";
import ConfigView, { useConfigHook } from "../ConfigView";
import Question from "../Quersion";

import { Button } from "components/TinperBee";
import { getLocalImg } from "utils/index";
import { downloadGatewayKey } from "services/gwServices";

const IndexView = (props) => {
    let { location } = props;
    const { queryParams, connectorType } = useLocationParamsMemo();
    const connectorInfo = useMemo(() => connectorInfoMap[connectorType], [connectorType]);

    let _useSetConnector = useSetConnector(queryParams, connectorType);
    // console.log(_useSetConnector)

    let { hasCreate, instanceInfo } = _useSetConnector;

    const useSecretKeys = useSecretKeysHook(queryParams);

    const useConfig = useConfigHook(_useSetConnector, queryParams);

    const downloadKeyClick = useCallback(() => {
        downloadGatewayKey(instanceInfo.gatewayId, false);
    }, [instanceInfo.gatewayId]);

    return (
        <Fragment>
            <ConfigTarget logo={connectorInfo.logo} title={connectorInfo.name} intro={connectorInfo.intro} />

            <ConfigInfoList>
                <DownloadGw _useSetConnector={_useSetConnector} />
                {/* 现在项 */}
                <ConfigInfoItem step="2" title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050587") /* "下载密钥并启动网关" */}>
                    {/*  */}
                    <Button
                        fieldid="ublinker-routes-u9cloud-components-IndexView-index-7180991-Button"
                        bordered
                        disabled={!hasCreate}
                        onClick={downloadKeyClick}
                    >
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050425") /* "下载密钥" */}
                    </Button>
                    <p className="config-text ucg-mar-t-10">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050364") /* "首先下载密钥" */}</p>
                    <p className="config-text">
                        <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050451") /* "Windows环境：" */}</strong>{" "}
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050615") /* "将下载的密钥解压至网关解压目录的config文件夹下" */}
                    </p>
                    <p className="config-text">
                        <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050436") /* "启动网关：" */}</strong>
                        {
                            window.lang.template(
                                "MIX_UBL_ALL_UBL_FE_LOC_000504291"
                            ) /* "在config的同级文件夹找到bin文件夹，例如：F:gateway ccpub-gateway-client-1.0.0-SNAPSHOTin，运行startup.bat。" */
                        }
                    </p>
                    <p className="config-text">
                        <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050350") /* "Linux环境：" */}</strong>
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050615") /* "将下载的密钥解压至网关解压目录的config文件夹下" */}
                    </p>
                    <p className="config-text">
                        <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050436") /* "启动网关：" */}</strong>
                        {
                            window.lang.template(
                                "MIX_UBL_ALL_UBL_FE_LOC_000504291"
                            ) /* "在config的同级文件夹找到bin文件夹，例如：F:gateway ccpub-gateway-client-1.0.0-SNAPSHOTin，运行startup.bat。" */
                        }
                    </p>
                    <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050544") /* "启动成功后，选择网关语言。如图" */}</p>
                    <div className="config-img">
                        <img fieldid="ublinker-routes-u9cloud-components-IndexView-index-3775851-img" src={getLocalImg("connector/gaojiban.png")} alt="" />
                    </div>
                </ConfigInfoItem>

                {/* 原来项 */}
                {/* <RunGw/>

        <SecretKeysView
          useSecretKeys={useSecretKeys}
        /> */}

                <ConfigView hasCreate={hasCreate} useConfig={useConfig} />

                <Question />
            </ConfigInfoList>
        </Fragment>
    );
};

export default IndexView;
