import React from "react";
import { getLocalImg } from "utils/index";
import { ConfigInfoItem } from "../../../components/ConfigInfo";

const RunGw = () => {
    return (
        <ConfigInfoItem title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050335") /* "运行网关" */}>
            <p className="config-text">
                {
                    window.lang.template(
                        "MIX_UBL_ALL_UBL_FE_LOC_00050392"
                    ) /* "找一台7x24小时都能访问您ERP系统，且能访问互联网的机器，操作系统window/linux均可。" */
                }
            </p>
            <p className="config-text">
                <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050451") /* "Windows环境：" */}</strong>{" "}
                {
                    window.lang.template(
                        "MIX_UBL_ALL_UBL_FE_LOC_00050331"
                    ) /* "解压下载好的文件，需保证解压后的文件目录不存在中文字符（例如：F:gateway ccpub-gateway-client-1.0.0-SNAPSHOT）, 运行环境需要JRE8，在解压后的网关安装文件目录下找到bin文件夹，例如F:gateway ccpub-gateway-client-1.0.0-SNAPSHOTin，运行startup.bat。" */
                }
            </p>
            <p className="config-text">
                <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050350") /* "Linux环境：" */}</strong>
                {
                    window.lang.template(
                        "MIX_UBL_ALL_UBL_FE_LOC_00050331"
                    ) /* "解压下载好的文件，需保证解压后的文件目录不存在中文字符（例如：F:gateway ccpub-gateway-client-1.0.0-SNAPSHOT）, 运行环境需要JRE8，在解压后的网关安装文件目录下找到bin文件夹，例如F:gateway ccpub-gateway-client-1.0.0-SNAPSHOTin，运行startup.bat。" */
                }
            </p>
            <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050373") /* "会提示输入网关密钥" */}</p>
            <div className="config-img">
                <img fieldid="ublinker-routes-u9cloud-components-RunGw-index-5370308-img" src={getLocalImg("connector/input_gateway_key.png")} alt="" />
            </div>
        </ConfigInfoItem>
    );
};

export default RunGw;
