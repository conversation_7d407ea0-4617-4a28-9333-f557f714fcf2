import React from "react";
import { getLocalImg } from "utils/index";
import { ConfigInfoItem, QuestionItem } from "../../../components/ConfigInfo";

const Question = () => {
    return (
        <ConfigInfoItem step="question" title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050290") /* "常见问题" */} expandType="multiple">
            <QuestionItem>
                <p className="ques-title">
                    <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050391") /* "问题一：" */}</strong>
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050414") /* "没有找到jre8时，会出现如下提示：" */}
                </p>
                <div className="ques-img">
                    <img fieldid="ublinker-routes-u9cloud-components-Quersion-index-7938951-img" src={getLocalImg("connector/jdk_version.png")} alt="" />
                </div>
                <p>
                    <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050339") /* "解决方案：" */}</strong>{" "}
                    {
                        window.lang.template(
                            "MIX_UBL_ALL_UBL_FE_LOC_00050642"
                        ) /* "安装jre8，并设置环境变量。有时设置完环境变量还是会报这个错，重启一下再打开就可以了。" */
                    }
                </p>
            </QuestionItem>

            <QuestionItem>
                <p className="ques-title">
                    <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050410") /* "问题二：" */}</strong>
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050393") /* "无法连接至网关时，会出现如下提示：" */}
                </p>
                <div className="ques-img">
                    <img fieldid="ublinker-routes-u9cloud-components-Quersion-index-5107752-img" src={getLocalImg("connector/not_connected.png")} alt="" />
                </div>
                <p>
                    <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050339") /* "解决方案：" */}</strong>
                    {
                        window.lang.template(
                            "MIX_UBL_ALL_UBL_FE_LOC_00050574"
                        ) /* "win + R 打开运行，输入CMD，点击回车。然后将nccloud-gateway-start.bat文件拖入该命令窗口执行" */
                    }
                </p>
            </QuestionItem>

            <QuestionItem>
                <p className="ques-title">
                    <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050505") /* "问题三：" */}</strong>
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050416") /* "输入的密钥(AccessKey)有误时，会出现如下提示：" */}
                </p>
                <div className="ques-img">
                    <img
                        fieldid="ublinker-routes-u9cloud-components-Quersion-index-5908983-img"
                        src={getLocalImg("connector/access_key_not_right.png")}
                        alt=""
                    />
                </div>
                <p>
                    <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050339") /* "解决方案：" */}</strong>
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050568") /* "检查密钥输入是否正确。" */}
                </p>
            </QuestionItem>
        </ConfigInfoItem>
    );
};

export default Question;
