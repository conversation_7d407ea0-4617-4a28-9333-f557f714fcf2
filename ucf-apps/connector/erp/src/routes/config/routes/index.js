import React from "react";
import { RoutesRender } from "core";

let routes = [];

let configContainers = require.context("./", true, /container\.js$/);
console.log(configContainers);
console.log(configContainers.keys());
configContainers.keys().forEach((comPath) => {
    let _comPath = comPath.split("/");
    let key = _comPath[1];
    console.log(_comPath, key);

    let ComModule = configContainers(comPath);
    console.log(ComModule);
    console.log("=================");
    let Com = ComModule.default;
    if (Com) {
        routes.push({
            path: "/" + key,
            component: Com,
        });
    }
});

const Routes = () => {
    return <RoutesRender routes={routes} />;
};
export default Routes;
