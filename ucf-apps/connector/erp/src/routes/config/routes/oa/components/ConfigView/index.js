import React, { useState, useCallback, useEffect, useMemo, Fragment } from "react";
import FormList from "components/TinperBee/Form";
import { FormControl, Button, Modal } from "components/TinperBee";
import Grid from "components/TinperBee/Grid";
import { GridActions, GridAction } from "components/TinperBee/Grid/GridActions";
import { ConfigInfoItem } from "../../../components/ConfigInfo";
import { autoServiceMessage } from "utils/service";
import { getUuid } from "utils/index";
import { Warning, Error } from "utils/feedback";
import { saveConfigService } from "../services";
import commonText from "constants/commonText";
const FormItem = FormList.Item;

export function useConfigHook(_useSetContainer, useMyConnectorInfo, queryParams) {
    let { instanceInfo, connectorName } = _useSetContainer;
    let { myConnectorInfo } = useMyConnectorInfo;
    let [host, setHost] = useState("");
    let [params, setParams] = useState([]);
    let [connectorId, setConnectorId] = useState(null);

    useEffect(() => {
        if (queryParams.id) {
            setConnectorId(queryParams.id);
        }
    }, [queryParams]);

    const renderCell = (field, _Params) => {
        let _field = "_" + field;
        return (value, record, index) => {
            if (record.isEdit) {
                return (
                    <FormControl
                        fieldid="ublinker-routes-oa-components-ConfigView-index-9777834-FormControl"
                        value={record[_field]}
                        onChange={(value) => {
                            _Params[index][_field] = value;
                            setParams([..._Params]);
                        }}
                    />
                );
            } else {
                return value;
            }
        };
    };

    const saveConfig = useCallback(async () => {
        let _host = host.trim();
        if (_host === "") {
            Error(window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050464") /* "请输入OA地址" */);
            return;
        }
        let hasEdit = params.find((item) => item.isEdit);
        if (hasEdit) {
            Error(window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050565") /* "您有URL参数未保存，请保存后提交" */);
            return;
        }
        let paras = params.map((item) => {
            return { id: item.id, name: item.name, value: item.value };
        });
        let res = await autoServiceMessage({
            service: saveConfigService({
                connectorId,
                gatewayId: instanceInfo.gatewayId,
                alias: connectorName,
                host: _host,
                paras: JSON.stringify(paras),
            }),
            success: window.lang.template(commonText.saveSuccess),
        });
        if (res) {
            if (!connectorId) {
                setConnectorId(res.data._id);
            }
        }
    }, [params, host, connectorId, instanceInfo]);

    const paramEditActions = useCallback(
        (index, type = "edit") => {
            let _params = [...params];
            let paramData = typeof index === "number" ? _params[index] : null;
            switch (type) {
                case "edit":
                    paramData._name = paramData.name;
                    paramData._value = paramData.value;
                    paramData.isEdit = true;
                    break;
                case "delete":
                    _params.splice(index, 1);
                    break;
                case "cancelEdit":
                    delete paramData._name;
                    delete paramData._value;
                    paramData.isEdit = false;
                    break;
                case "save":
                    let { _name, _value } = paramData;
                    _name = _name.trim();
                    _value = _value.trim();
                    if (_name === "" || _value === "") {
                        Warning(window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050330") /* "参数名称或者参数值不能为空" */);
                        return;
                    } else {
                        let repeatParam = _params.find((item, idx) => idx !== index && item.name === _name);
                        if (repeatParam) {
                            Warning(window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050650") /* "URI参数名有重复，请检查后保存!" */);
                            return;
                        }
                    }
                    paramData.name = _name;
                    paramData.value = _value;
                    delete paramData._name;
                    delete paramData._value;
                    paramData.isEdit = false;
                    break;
                case "add": {
                    _params.push({
                        id: getUuid(),
                        name: "",
                        value: "",
                        isEdit: true,
                    });
                    break;
                }
            }
            setParams(_params);
        },
        [params]
    );

    const paramsGridColumns = useMemo(
        () => [
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050498") /* "参数名称" */,
                dataIndex: "name",
                render: renderCell("name", params),
            },
            {
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050376") /* "参数值" */,
                dataIndex: "value",
                render: renderCell("value", params),
            },
            {
                title: "",
                dataIndex: "$$save",
                render: (value, record, index) => {
                    return record.isEdit ? (
                        <Fragment>
                            <Button
                                fieldid="ublinker-routes-oa-components-ConfigView-index-2096524-Button"
                                {...Grid.hoverButtonPorps}
                                onClick={paramEditActions.bind(null, index, "save")}
                            >
                                {window.lang.template(commonText.save)}
                            </Button>
                            <Button
                                fieldid="ublinker-routes-oa-components-ConfigView-index-9710047-Button"
                                {...Grid.hoverButtonPorps}
                                colors="secondary"
                                onClick={paramEditActions.bind(null, index, "cancelEdit")}
                            >
                                {window.lang.template(commonText.cancel)}
                            </Button>
                        </Fragment>
                    ) : null;
                },
            },
            {
                title: window.lang.template(commonText.actions),
                dataIndex: "$$actions",
                width: 120,
                render: (value, record, index) => {
                    let { isEdit } = record;
                    return (
                        <GridActions>
                            <GridAction
                                fieldid="UCG-FE-routes-oa-components-ConfigView-index-4141751-GridAction"
                                disabled={isEdit}
                                onClick={paramEditActions.bind(null, index, "edit")}
                            >
                                {window.lang.template(commonText.edit)}
                            </GridAction>
                            <GridAction
                                fieldid="UCG-FE-routes-oa-components-ConfigView-index-811362-GridAction"
                                disabled={isEdit}
                                onClick={paramEditActions.bind(null, index, "delete")}
                            >
                                {window.lang.template(commonText.deletion)}
                            </GridAction>
                        </GridActions>
                    );
                },
            },
        ],
        [params]
    );

    useEffect(() => {
        if (myConnectorInfo) {
            let { config } = myConnectorInfo;
            setHost(config.host);
            let params = JSON.parse(config.paras);
            setParams(params);
        }
    }, [myConnectorInfo]);

    return {
        host,
        setHost,
        params,
        paramsGridColumns,
        paramEditActions,
        saveConfig,
    };
}

const ConfigView = (props) => {
    let { useConfig, hasCreate } = props;
    let { host, setHost, params, paramsGridColumns, paramEditActions, saveConfig } = useConfig;
    return (
        <ConfigInfoItem title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050590") /* "输入OA配置" */}>
            <FormList fieldid="ublinker-routes-oa-components-ConfigView-index-8276443-FormList" className="config-action-form sm-action" layoutOpt={{ md: 12 }}>
                <FormItem
                    fieldid="ublinker-routes-oa-components-ConfigView-index-1732806-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050591") /* "OA地址" */}
                    labelCol={120}
                >
                    <FormControl
                        fieldid="ublinker-routes-oa-components-ConfigView-index-8013198-FormControl"
                        value={host}
                        onChange={setHost}
                        placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050631") /* "请输入OA内网可以访问地址如http://************:3265" */}
                    />
                </FormItem>
            </FormList>

            <div className="clearfix ucg-mar-b-20">
                <span className="config-text" style={{ lineHeight: "32px" }}>
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050343") /* "URI参数" */}
                </span>
                <div className="ucg-float-r">
                    <Button
                        fieldid="ublinker-routes-oa-components-ConfigView-index-3364961-Button"
                        className="ucg-mar-r-10"
                        colors="primary"
                        onClick={paramEditActions.bind(null, "", "add")}
                    >
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050332") /* "新增参数" */}
                    </Button>
                    <Button fieldid="ublinker-routes-oa-components-ConfigView-index-6868886-Button" bordered disabled={!hasCreate} onClick={saveConfig}>
                        {window.lang.template(commonText.saveChange)}
                    </Button>
                </div>
            </div>

            <Grid
                fieldid="ublinker-routes-oa-components-ConfigView-index-6114736-Grid"
                dataKey="id"
                columns={paramsGridColumns}
                data={params}
                empty={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050472") /* "暂无参数" */}
            />
        </ConfigInfoItem>
    );
};

export default ConfigView;
