import { getInvokeService, getServicePath } from "utils/service";

/**
 * 获取适配器信息
 * @param {Object} data
 * @param {String} data.adapterCode -适配器类型code
 * @param {String} data.gatewayId -网关ID
 * @return {Promise<unknown>}
 */
export const getAdapterConfigService = function (data) {
    let { adapterCode, gatewayId } = data;
    return getInvokeService(
        {
            method: "GET",
            path: "/mygwapp/gateway/adapter/info/" + adapterCode,
        },
        { gatewayGuID: gatewayId }
    );
};

/**
 * 更新适配器配置
 * @param {Object} data
 * @param {String} data.adapterCode -适配器类型code
 * @param {String} data.gatewayGuID -网关ID
 * @param {String} data.action=[undeploy|deploy] -更新类型 undeploy卸载|deploy安装
 * @param {String} data.port -端口号
 * @param {Number} data.instances -实例数量
 * @param {Boolean} data.worker -实例数量
 * @param {JSON} data.infos -其他参数
 * @return {Promise<unknown>}
 */
export const updateAdapterConfigService = function (data) {
    let { adapterCode, ..._data } = data;
    return getInvokeService(
        {
            method: "POST",
            path: "/mygwapp/gateway/adapter/" + adapterCode,
        },
        _data
    );
};
