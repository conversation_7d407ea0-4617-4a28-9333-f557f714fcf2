import React, { Fragment, useMemo } from "react";
import { Button, FormControl, Select } from "components/TinperBee";
import FormList from "components/TinperBee/Form";
import ConfigTarget from "../../../components/ConfigTarget";
import { ConfigInfoList, ConfigInfoItem } from "../../../components/ConfigInfo";
import { useLocationParamsMemo, useMyConnectorInfoHook } from "../../../common/utilHooks";
import { connectorInfoMap } from "../../../common/constants";

import { useSetConnector } from "../../../nc/components/IndexView/hooks";
import DownloadView from "../../../nc/components/DownloadView";
import ConfigAdapterView, { useConfigAdapterHook } from "../ConfigAdapter";
import ConfigView, { useConfigHook } from "../ConfigView";
const FormItem = FormList.Item;

const labelCol = 120;

const IndexView = (props) => {
    let { location } = props;
    const { queryParams, connectorType } = useLocationParamsMemo();
    const connectorInfo = useMemo(() => connectorInfoMap[connectorType], [connectorType]);

    const useMyConnectorInfo = useMyConnectorInfoHook(queryParams);

    const _useSetContainer = useSetConnector(queryParams, connectorType);

    const { hasCreate, connectorName, setConnectorName, createInstance, instanceInfo } = _useSetContainer;

    const useConfig = useConfigHook(_useSetContainer, useMyConnectorInfo, queryParams);
    let { params, host } = useConfig;
    const adapterParams = useMemo(() => {
        let _params = params.map((item) => {
            return { id: item.id, name: item.name, value: item.value };
        });
        return {
            infos: JSON.stringify({
                host: host,
                params: _params,
            }),
        };
    }, [host, params]);

    const useConfigAdapter = useConfigAdapterHook(_useSetContainer, "nccloud-app-oa", adapterParams);

    return (
        <Fragment>
            <ConfigTarget logo={connectorInfo.logo} title={connectorInfo.name} intro={connectorInfo.intro} />

            <ConfigInfoList>
                <ConfigInfoItem title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050634") /* "设置连接器名称" */} step="1">
                    <FormList fieldid="ublinker-routes-oa-components-IndexView-index-764725-FormList" className="config-action-form" layoutOpt={{ md: 12 }}>
                        <FormItem
                            fieldid="ublinker-routes-oa-components-IndexView-index-5990108-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050551") /* "连接器名称" */}
                            labelCol={labelCol}
                        >
                            <FormControl
                                fieldid="ublinker-routes-oa-components-IndexView-index-4361746-FormControl"
                                disabled={hasCreate}
                                placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050467") /* "请输入连接器的名称，用于标识连接器" */}
                                value={connectorName}
                                onChange={setConnectorName}
                            />
                        </FormItem>

                        {hasCreate ? null : (
                            <FormItem fieldid="ublinker-routes-oa-components-IndexView-index-1112896-FormItem" labelCol={labelCol}>
                                <Button
                                    fieldid="ublinker-routes-oa-components-IndexView-index-4862721-Button"
                                    disabled={!connectorName}
                                    colors="primary"
                                    onClick={createInstance.bind(null, false)}
                                >
                                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050352") /* "确认" */}
                                </Button>
                            </FormItem>
                        )}
                    </FormList>
                </ConfigInfoItem>

                <DownloadView connectorType={connectorType} hasCreate={hasCreate} instanceInfo={instanceInfo} />

                <ConfigView hasCreate={hasCreate} useConfig={useConfig} />

                <ConfigAdapterView hasCreate={hasCreate} useConfigAdapter={useConfigAdapter} />
            </ConfigInfoList>
        </Fragment>
    );
};

export default IndexView;
