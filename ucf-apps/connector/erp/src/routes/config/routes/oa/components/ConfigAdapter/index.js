import React, { useState, useEffect, useCallback, useMemo } from "react";
import FormList from "components/TinperBee/Form";
import { WithButton as ButtonGwRefer } from "../GwRefer";
import { FormControl, Button, Checkbox } from "components/TinperBee";
import { autoServiceMessage } from "utils/service";
import { portReg } from "utils/regExp";
import { ConfigInfoItem } from "../../../components/ConfigInfo";
import { getAdapterConfigService, updateAdapterConfigService } from "./services";
import commonText from "constants/commonText";
export function useConfigAdapterHook(_useSetContainer, adapterCode, adapterParams) {
    let { instanceInfo } = _useSetContainer;
    const [adapterConfigInfo, setAdapterConfigInfo] = useState({
        instances: 1,
        port: "",
        worker: false,
    });

    const [updateAction, setUpdateAction] = useState("deploy");

    const getAdapterConfigInfo = useCallback(async () => {
        let res = await autoServiceMessage({
            service: getAdapterConfigService({
                adapterCode,
                gatewayId: instanceInfo.gatewayId,
            }),
        });
        if (res) {
            let resData = res.data;
            if (resData) {
                setAdapterConfigInfo({
                    port: resData.config.port,
                    worker: resData.worker || false,
                    instances: resData.instances || 1,
                });
                setUpdateAction("undeploy");
            }
        }
    }, [instanceInfo, adapterCode]);

    const handleSelectGw = useCallback(
        (selectedList) => {
            let gwInfo = selectedList[0];
            _useSetContainer.setInstanceInfo({
                ...instanceInfo,
                gatewayId: gwInfo.gatewayID,
            });
            _useSetContainer.setConnectorName(gwInfo.name);
        },
        [instanceInfo]
    );

    const updateAdapter = useCallback(
        async (values) => {
            let requestData = {
                service: updateAdapterConfigService({
                    adapterCode,
                    gatewayGuID: instanceInfo.gatewayId,
                    action: updateAction,
                    ...adapterParams,
                    ...values,
                }),
                success: updateAction === "undeploy" ? undefined : window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050276") /* "部署成功" */,
            };
            let res = await autoServiceMessage(requestData);
            if (res) {
                if (requestData.action === "undeploy") {
                    setUpdateAction("deploy");
                    requestData.action = "deploy";
                    updateAdapter(requestData);
                } else {
                    setUpdateAction("undeploy");
                }
            }
        },
        [instanceInfo, adapterCode, updateAction, adapterParams]
    );

    useEffect(() => {
        if (instanceInfo.gatewayId) {
            getAdapterConfigInfo();
        }
    }, [instanceInfo, adapterCode]);

    return {
        adapterConfigInfo,
        updateAdapter,
        handleSelectGw,
    };
}

const labelCol = 180;

const FormItem = FormList.Item;

const ConfigAdapterView = (props) => {
    let {
        useConfigAdapter,
        hasCreate,
        form: { setFieldsValue, getFieldProps, validateFields, getFieldError, getFieldValue },
    } = props;

    let { adapterConfigInfo, updateAdapter, handleSelectGw } = useConfigAdapter;

    useEffect(() => {
        setFieldsValue(adapterConfigInfo);
    }, [adapterConfigInfo]);

    let handleSave = useCallback(() => {
        validateFields((errors, values) => {
            if (!errors) {
                updateAdapter(values);
            }
        });
    });

    return (
        <ConfigInfoItem title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050293") /* "部署适配器" */}>
            <FormList fieldid="ublinker-routes-oa-components-ConfigAdapter-index-5530693-FormList" className="config-action-form" layoutOpt={{ md: 12 }}>
                <FormItem fieldid="ublinker-routes-oa-components-ConfigAdapter-index-8590728-FormItem" labelCol={labelCol}>
                    <ButtonGwRefer btnProps={{ colors: "secondary" }} onOk={handleSelectGw}>
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050516") /* "选择已有网关" */}
                    </ButtonGwRefer>
                </FormItem>
                <FormItem
                    fieldid="ublinker-routes-oa-components-ConfigAdapter-index-9300312-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050504") /* "端口号" */}
                    required
                    labelCol={labelCol}
                    error={getFieldError("port")}
                >
                    <FormControl
                        fieldid="ublinker-routes-oa-components-ConfigAdapter-index-4558914-FormControl"
                        placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050419") /* "请输入端口号" */}
                        {...getFieldProps("port", {
                            initialValue: "",
                            rules: [
                                {
                                    required: true,
                                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050419") /* "请输入端口号" */,
                                },
                                {
                                    pattern: portReg,
                                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050527") /* "请输入正确的端口号" */,
                                },
                            ],
                        })}
                    />
                </FormItem>
                <FormItem
                    fieldid="ublinker-routes-oa-components-ConfigAdapter-index-3921988-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050449") /* "实例数" */}
                    labelCol={labelCol}
                >
                    <FormControl
                        fieldid="ublinker-routes-oa-components-ConfigAdapter-index-7234347-FormControl"
                        placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050347") /* "请输入实例数" */}
                        disabled
                        {...getFieldProps("instances", {
                            initialValue: 1,
                        })}
                    />
                </FormItem>
                <FormItem fieldid="ublinker-routes-oa-components-ConfigAdapter-index-9178640-FormItem" label="worker" labelCol={labelCol}>
                    <Checkbox
                        fieldid="ublinker-routes-oa-components-ConfigAdapter-index-3990292-Checkbox"
                        {...getFieldProps("worker", {
                            initialValue: false,
                            valuePropName: "checked",
                        })}
                    />
                </FormItem>

                <FormItem fieldid="ublinker-routes-oa-components-ConfigAdapter-index-3347501-FormItem" labelCol={labelCol}>
                    <Button
                        fieldid="ublinker-routes-oa-components-ConfigAdapter-index-6433558-Button"
                        colors="primary"
                        disabled={!hasCreate}
                        onClick={handleSave}
                    >
                        {window.lang.template(commonText.save)}
                    </Button>
                </FormItem>
            </FormList>
        </ConfigInfoItem>
    );
};

export default FormList.createForm()(ConfigAdapterView);
