import React from "react";
import { ucgListService } from "./service";
import { WithButton as WithButtonBase } from "components/Refer";
import commonText from "constants/commonText";

let execReg = /\[(\S+)\]$/;

const columns = [
    {
        title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050128") /* "网关连接实例ID" */,
        dataIndex: "gatewayID",
    },
    {
        title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050140") /* "别名" */,
        dataIndex: "name",
    },
    {
        title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050381") /* "IP地址" */,
        dataIndex: "localaddress",
        render: (value) => {
            let execResult = execReg.exec(value);
            return execResult ? execResult[1] || value : value;
        },
    },
    {
        title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050118") /* "是否在线" */,
        dataIndex: "state",
        render: (value) => (value === "ONLINE" ? window.lang.template(commonText.yes) : window.lang.template(commonText.not)),
    },
    {
        title: window.lang.template(commonText.createTime),
        dataIndex: "createtime",
    },
    {
        title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050329") /* "最后一次上线时间" */,
        dataIndex: "lastupdatetime",
    },
];

let referProps = {
    columns: columns,
    hasPage: false,
    title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050164") /* "切换任务网关" */,
    service: ucgListService,
    pkKey: "gatewayID",
};

export const WithButton = (props) => {
    return <WithButtonBase {...props} {...referProps} />;
};
