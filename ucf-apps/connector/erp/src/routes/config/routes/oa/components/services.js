import { getInvokeService, getServicePath } from "utils/service";

/**
 * 保存配置
 * @param {Object} data
 * @param {String} data.connectorId -我的连接器ID
 * @param {String=} data.alias
 * @param {String} data.gatewayId
 * @param {String} data.host -OA地址
 * @param {JSON} data.paras -URL参数
 * @return {Promise<unknown>}
 */
export const saveConfigService = function (data) {
    let { connectorId, ..._data } = data;
    let path = "";
    if (connectorId) {
        path = "/mygwapp/connector/oa/updateconfig/" + connectorId;
    } else {
        path = "/mygwapp/connector/oa/addconfig";
    }
    return getInvokeService(
        {
            method: "POST",
            path: path,
        },
        _data
    );
};
