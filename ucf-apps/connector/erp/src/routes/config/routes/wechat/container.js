import React, { Component } from "react";
import IndexView from "./components/IndexView";
import { inject, observer } from "mobx-react";

import { storeKey as configCommonStore } from "../../store";

@inject((rootStore) => {
    let configCommonStore = rootStore[configCommonStore];
    return {
        configCommonStore: configCommonStore,
    };
})
@observer
class Container extends Component {
    constructor(props) {
        super(props);
    }

    render() {
        return <IndexView {...this.props} />;
    }
}
export default Container;
