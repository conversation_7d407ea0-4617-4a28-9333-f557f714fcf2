import React, { Fragment, useMemo } from "react";
import { getLocalImg } from "utils/index";
import ConfigTarget from "../../../components/ConfigTarget";
import { ConfigInfoList, ConfigInfoItem } from "../../../components/ConfigInfo";
import { useLocationParamsMemo } from "../../../common/utilHooks";
import { connectorInfoMap } from "../../../common/constants";

const IndexView = (props) => {
    let { location } = props;
    const { queryParams, connectorType } = useLocationParamsMemo();
    const connectorInfo = useMemo(() => connectorInfoMap[connectorType], [connectorType]);

    return (
        <Fragment>
            <ConfigTarget logo={connectorInfo.logo} title={connectorInfo.name} intro={connectorInfo.intro} />

            <ConfigInfoList>
                <ConfigInfoItem title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050267") /* "注册企业微信号" */} step="1">
                    <p className="config-text">
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050037") /* "登录" */}
                        {/* <a fieldid="ublinker-routes-wechat-components-IndexView-index-5045024-a" href="https://qy.weixin.qq.com/" target="_blank">https://qy.weixin.qq.com/</a> */}
                        <span>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050656") /* "点击右上角的注册。" */}</span>
                    </p>
                    <div className="config-img">
                        <img fieldid="ublinker-routes-wechat-components-IndexView-index-9635557-img" src={getLocalImg("connector/wechat/regpage.png")} alt="" />
                    </div>

                    <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050626") /* "输入基本信息，进行注册。" */}</p>
                    <div className="config-img">
                        <img fieldid="ublinker-routes-wechat-components-IndexView-index-1297416-img" src={getLocalImg("connector/wechat/reginfo.png")} alt="" />
                    </div>

                    <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050387") /* "去邮箱激活。" */}</p>
                    <div className="config-img">
                        <img
                            fieldid="ublinker-routes-wechat-components-IndexView-index-8948054-img"
                            src={getLocalImg("connector/wechat/emailavactive.png")}
                            alt=""
                        />
                    </div>

                    <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050592") /* "选择类型时选择企业号。" */}</p>
                    <div className="config-img">
                        <img
                            fieldid="ublinker-routes-wechat-components-IndexView-index-675599-img"
                            src={getLocalImg("connector/wechat/choosetype.png")}
                            alt=""
                        />
                    </div>

                    <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050502") /* "主体类型选择企业。" */}</p>
                    <div className="config-img">
                        <img
                            fieldid="ublinker-routes-wechat-components-IndexView-index-6843046-img"
                            src={getLocalImg("connector/wechat/typeinfo.png")}
                            alt=""
                        />
                    </div>

                    <p className="config-text">
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050301") /* "填写企业及运营者信息。如果没有相应企业信息，可以选择团队" */}
                    </p>
                    <div className="config-img">
                        <img fieldid="ublinker-routes-wechat-components-IndexView-index-80995-img" src={getLocalImg("connector/wechat/editinfo.png")} alt="" />
                    </div>

                    <p className="config-text">
                        {
                            window.lang.template(
                                "MIX_UBL_ALL_UBL_FE_LOC_00050478"
                            ) /* "点击继续，会出现弹框，确认后进入公众微信号信息页面，填写账号名称与介绍。" */
                        }
                    </p>
                    <div className="config-img">
                        <img fieldid="ublinker-routes-wechat-components-IndexView-index-9558496-img" src={getLocalImg("connector/wechat/qyinfo.png")} alt="" />
                    </div>

                    <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050263") /* "确认信息后点击完成。" */}</p>
                    <p className="config-text">
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050542") /* "回到https:" */}//qy.weixin.qq.com/，用微信扫码，输入密码后即可登入企业号。
                    </p>
                </ConfigInfoItem>

                <ConfigInfoItem title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050408") /* "获取应用ID" */} step="2">
                    <p className="config-text">
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050355") /* "登入企业号之后，点击左侧的应用中心，进入应用中心后再点击自建应用" */}
                    </p>
                    <div className="config-img">
                        <img fieldid="ublinker-routes-wechat-components-IndexView-index-2206324-img" src={getLocalImg("connector/wechat/addapp.png")} alt="" />
                    </div>

                    <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050452") /* "选择消息型应用" */}</p>
                    <div className="config-img">
                        <img fieldid="ublinker-routes-wechat-components-IndexView-index-3408922-img" src={getLocalImg("connector/wechat/apptype.png")} alt="" />
                    </div>

                    <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050295") /* "输入应用信息" */}</p>
                    <div className="config-img">
                        <img
                            fieldid="ublinker-routes-wechat-components-IndexView-index-8022251-img"
                            src={getLocalImg("connector/wechat/inputappinfo.png")}
                            alt=""
                        />
                    </div>

                    <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050399") /* "确认后提交" */}</p>
                    <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050528") /* "点击新创建的应用" */}</p>
                    <div className="config-img">
                        <img fieldid="ublinker-routes-wechat-components-IndexView-index-2671660-img" src={getLocalImg("connector/wechat/applist.png")} alt="" />
                    </div>

                    <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050284") /* "可以看到应用的信息，标红处即为应用ID" */}</p>
                    <div className="config-img">
                        <img
                            fieldid="ublinker-routes-wechat-components-IndexView-index-9608860-img"
                            src={getLocalImg("connector/wechat/getappid.png")}
                            alt=""
                        />
                    </div>
                </ConfigInfoItem>

                <ConfigInfoItem title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050408") /* "获取应用ID" */} step="3">
                    <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050432") /* "左侧选择设置，可以在图中2处找到CorpID" */}</p>
                    <div className="config-img">
                        <img
                            fieldid="ublinker-routes-wechat-components-IndexView-index-9895011-img"
                            src={getLocalImg("connector/wechat/getcorpid.png")}
                            alt=""
                        />
                    </div>

                    <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050384") /* "进入设置→权限管理，点击左侧的新建" */}</p>
                    <div className="config-img">
                        <img fieldid="ublinker-routes-wechat-components-IndexView-index-6834778-img" src={getLocalImg("connector/wechat/authmgr.png")} alt="" />
                    </div>

                    <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050604") /* "输入名称，选择管理员，点击下一步" */}</p>
                    <div className="config-img">
                        <img
                            fieldid="ublinker-routes-wechat-components-IndexView-index-2399976-img"
                            src={getLocalImg("connector/wechat/chooseadmin.png")}
                            alt=""
                        />
                    </div>

                    <p className="config-text">
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050573") /* "设置权限中建议通讯录权限为查看+管理，应用权限可以只选择消息协同" */}
                        <span className="ucg-primary-text">
                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050489") /* "（至少得选择用来发消息的应用）" */}
                        </span>
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050609") /* "，权限为发消息+管理。" */}
                        <span className="ucg-primary-text">
                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050463") /* "如果这个没有配置，消息就会收不到。" */}
                        </span>
                    </p>
                    <div className="config-img">
                        <img fieldid="ublinker-routes-wechat-components-IndexView-index-9050595-img" src={getLocalImg("connector/wechat/setauth.png")} alt="" />
                    </div>

                    <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050403") /* "保存后可以看到Secret" */}</p>
                    <div className="config-img">
                        <img
                            fieldid="ublinker-routes-wechat-components-IndexView-index-4841866-img"
                            src={getLocalImg("connector/wechat/viewsecret.png")}
                            alt=""
                        />
                    </div>
                </ConfigInfoItem>

                <ConfigInfoItem title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050390") /* "管理通讯录" */} step="4">
                    <p className="config-text">
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050620") /* "点击左侧通讯录，可以添加部门，只需输入名称即可" */}
                    </p>
                    <div className="config-img">
                        <img fieldid="ublinker-routes-wechat-components-IndexView-index-8145660-img" src={getLocalImg("connector/wechat/adddept.png")} alt="" />
                    </div>

                    <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050531") /* "进入通讯录，选择右侧的新增成员" */}</p>
                    <div className="config-img">
                        <img fieldid="ublinker-routes-wechat-components-IndexView-index-7887280-img" src={getLocalImg("connector/wechat/addpsn.png")} alt="" />
                    </div>

                    <p className="config-text">
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050560") /* "填写信息，需注意，这里的账号是在后面用于用户绑定的" */}
                    </p>
                    <div className="config-img">
                        <img
                            fieldid="ublinker-routes-wechat-components-IndexView-index-9733848-img"
                            src={getLocalImg("connector/wechat/userinfo.png")}
                            alt=""
                        />
                    </div>

                    <p className="config-text">
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050649") /* "填写好之后保存，这时只是添加了，还需要用户进行关注" */}
                    </p>
                </ConfigInfoItem>

                <ConfigInfoItem title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050285") /* "用户关注微信企业号" */} step="5">
                    <p className="config-text">
                        {
                            window.lang.template(
                                "MIX_UBL_ALL_UBL_FE_LOC_00050251"
                            ) /* "被添加的用户并不会收到任何通知，需要去主动关注企业号。 如果关注的用户没有被添加到通讯录中，可以通过手机号和邮箱验证。 正常使用的前提是已经被添加到微信企业号，且用户已经关注并通过身份认证。" */
                        }
                    </p>
                </ConfigInfoItem>
            </ConfigInfoList>
        </Fragment>
    );
};

export default IndexView;
