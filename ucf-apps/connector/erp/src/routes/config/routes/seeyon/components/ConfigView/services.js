import { getInvokeService, getServicePath } from "utils/service";

/**
 * 保存配置
 * @param {Object} data
 * @param {String} data.connectorId
 * @param {String} data.gatewayid
 * @param {String} data.oaurl
 * @param {String} data.oaversion
 * @param {String} data.senderLoginName
 * @param {String} data.username
 * @param {String} data.password
 * @return {Promise<unknown>}
 */
export const saveConfigService = function (data) {
    let { connectorId, ..._data } = data;
    let path = "";
    if (connectorId) {
        path = "/mygwapp/connector/seeyon/updateconfig/" + connectorId;
    } else {
        path = "/mygwapp/connector/seeyon/addconfig";
    }
    return getInvokeService(
        {
            method: "POST",
            path: path,
        },
        _data
    );
};

/**
 * 测试配置连接
 * @param {Object} data
 * @param {String} data.nccfgid -instanceInfo.configId
 * @param {String} data.gatewayGuID -instanceInfo.gatewayId
 * * @param {String=} data.servertype - connectorType
 * @return {Promise<unknown>}
 */
export const testConnectService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/mygwapp/ncconfig/testnc",
        },
        data
    );
};
