import React, { useState, useCallback, useMemo, useEffect } from "react";
import { autoServiceMessage } from "utils/service";
import { ConfigInfoItem } from "../../../components/ConfigInfo";
import { saveConfigService, testConnectService } from "./services";
import { Button, FormControl, Select } from "components/TinperBee";
import FormList from "components/TinperBee/Form";
import commonText from "constants/commonText";

const FormItem = FormList.Item;

const versions = [
    {
        value: "V6.0",
        key: "V6.0",
    },
    {
        value: "V6.1",
        key: "V6.1",
    },
    {
        value: "V5.6",
        key: "V5.6",
    },
];

export function useConfigHook(_useSetConnector, useMyConnectorInfo, queryParams, connectorType) {
    let { myConnectorInfo } = useMyConnectorInfo;
    let { instanceInfo } = _useSetConnector;
    const [shouldSave, setShouldSave] = useState(!queryParams.id);
    let [configInfo, setConfigInfo] = useState({
        oaurl: "",
        oaversion: "V6.0",
        senderLoginName: "",
        username: "",
        password: "",
    });

    const saveConfig = useCallback(
        async (data) => {
            data.connectorId = queryParams.id;
            data.gatewayid = instanceInfo.gatewayId;
            let res = await autoServiceMessage({
                service: saveConfigService(data),
                success: window.lang.template(commonText.saveSuccess),
            });
            if (res) {
                setShouldSave(false);
                if (!instanceInfo.configId) {
                    let _configId = res.data._id;
                    _useSetConnector.setInstanceInfo({
                        ...instanceInfo,
                        configId: _configId,
                    });
                }
            }
        },
        [instanceInfo]
    );

    const testConnect = useCallback(async () => {
        await autoServiceMessage({
            service: testConnectService({
                nccfgid: instanceInfo.configId,
                gatewayGuID: instanceInfo.gatewayId,
                servertype: connectorType,
            }),
            success: window.lang.template(commonText.testSuccess),
        });
    }, [instanceInfo]);

    useEffect(() => {
        if (myConnectorInfo) {
            let {
                config: { oaurl, oaversion, senderLoginName, username, password },
            } = myConnectorInfo;
            setConfigInfo({ oaurl, oaversion, senderLoginName, username, password });
        }
    }, [myConnectorInfo]);
    return {
        configInfo,
        shouldSave,
        setShouldSave,
        saveConfig,
        testConnect,
    };
}

const labelCol = 180;

const ConfigView = (props) => {
    let {
        useConfig,
        hasCreate,
        form: { setFieldsValue, getFieldProps, validateFields, getFieldError, getFieldValue },
    } = props;
    let { configInfo, saveConfig, testConnect, setShouldSave, shouldSave } = useConfig;

    useEffect(() => {
        if (configInfo) {
            setFieldsValue(configInfo);
        }
    }, [configInfo]);

    const handleConfigChange = useCallback(() => {
        setShouldSave(true);
    });

    const handleSave = useCallback(() => {
        validateFields((error, values) => {
            if (!error) {
                saveConfig(values);
            }
        });
    });

    const disabled = !hasCreate;

    return (
        <ConfigInfoItem title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050497") /* "输入OA配置并测试连接" */}>
            <FormList fieldid="ublinker-routes-seeyon-components-ConfigView-index-1454411-FormList" className="config-action-form" layoutOpt={{ md: 12 }}>
                <FormItem
                    fieldid="ublinker-routes-seeyon-components-ConfigView-index-4266493-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050584") /* "致远OA地址" */}
                    required
                    labelCol={labelCol}
                    error={getFieldError("u9ipaddr")}
                >
                    <FormControl
                        fieldid="ublinker-routes-seeyon-components-ConfigView-index-5093712-FormControl"
                        className="ucg-mar-r-5"
                        disabled={disabled}
                        placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050302") /* "请输入致远OA的内网访问地址如http://************:3265" */}
                        {...getFieldProps("oaurl", {
                            initialValue: "",
                            rules: [
                                {
                                    required: true,
                                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050571") /* "请输入服务器地址" */,
                                },
                            ],
                            onChange: handleConfigChange,
                        })}
                    />
                </FormItem>

                <FormItem
                    fieldid="ublinker-routes-seeyon-components-ConfigView-index-9038951-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050606") /* "致远OA版本" */}
                    required
                    labelCol={labelCol}
                    error={getFieldError("u9version")}
                >
                    <Select
                        fieldid="ublinker-routes-seeyon-components-ConfigView-index-186465-Select"
                        className="ucg-mar-r-5"
                        data={versions}
                        {...getFieldProps("oaversion", {
                            initialValue: "",
                            rules: [
                                {
                                    required: true,
                                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050577") /* "请选择致远OA版本" */,
                                },
                            ],
                            onChange: handleConfigChange,
                        })}
                    />
                </FormItem>

                <FormItem
                    fieldid="ublinker-routes-seeyon-components-ConfigView-index-9256860-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050639") /* "登录用户名" */}
                    required
                    labelCol={labelCol}
                    error={getFieldError("culturename")}
                >
                    <FormControl
                        fieldid="ublinker-routes-seeyon-components-ConfigView-index-4156613-FormControl"
                        className="ucg-mar-r-5"
                        disabled={disabled}
                        placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050261") /* "请输入登录用户名" */}
                        {...getFieldProps("senderLoginName", {
                            initialValue: "",
                            rules: [
                                {
                                    required: true,
                                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050261") /* "请输入登录用户名" */,
                                },
                            ],
                            onChange: handleConfigChange,
                        })}
                    />
                </FormItem>

                <FormItem
                    fieldid="ublinker-routes-seeyon-components-ConfigView-index-375143-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050462") /* "用户名" */}
                    labelCol={labelCol}
                    required
                >
                    <FormControl
                        fieldid="ublinker-routes-seeyon-components-ConfigView-index-6651995-FormControl"
                        className="ucg-mar-r-5"
                        disabled={disabled}
                        placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050346") /* "请输入创建的REST用户的用户名" */}
                        {...getFieldProps("username", {
                            initialValue: "",
                            rules: [
                                {
                                    required: true,
                                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050346") /* "请输入创建的REST用户的用户名" */,
                                },
                            ],
                        })}
                    />
                </FormItem>

                <FormItem
                    fieldid="ublinker-routes-seeyon-components-ConfigView-index-914018-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050348") /* "密码" */}
                    labelCol={labelCol}
                    required
                >
                    <FormControl
                        fieldid="ublinker-routes-seeyon-components-ConfigView-index-4666918-FormControl"
                        className="ucg-mar-r-5"
                        disabled={disabled}
                        placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050442") /* "请输入创建的REST用户的密码" */}
                        type="password"
                        {...getFieldProps("password", {
                            initialValue: "",
                            rules: [
                                {
                                    required: true,
                                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050442") /* "请输入创建的REST用户的密码" */,
                                },
                            ],
                        })}
                    />
                </FormItem>

                <FormItem fieldid="ublinker-routes-seeyon-components-ConfigView-index-4698789-FormItem" labelCol={labelCol}>
                    <Button
                        fieldid="ublinker-routes-seeyon-components-ConfigView-index-1045240-Button"
                        className="ucg-mar-r-10"
                        disabled={disabled}
                        colors="primary"
                        onClick={handleSave}
                    >
                        {window.lang.template(commonText.saveChange)}
                    </Button>
                    <Button
                        fieldid="ublinker-routes-seeyon-components-ConfigView-index-2351744-Button"
                        className="ucg-mar-r-10"
                        disabled={disabled || shouldSave}
                        bordered
                        onClick={testConnect}
                    >
                        {window.lang.template(commonText.testConnect)}
                    </Button>
                </FormItem>
            </FormList>
        </ConfigInfoItem>
    );
};

export default FormList.createForm()(ConfigView);
