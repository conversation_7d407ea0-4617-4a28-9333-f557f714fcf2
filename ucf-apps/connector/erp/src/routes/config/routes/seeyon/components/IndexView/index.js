import React, { Fragment, useMemo, useCallback } from "react";
import ConfigTarget from "../../../components/ConfigTarget";
import { ConfigInfoList, ConfigInfoItem } from "../../../components/ConfigInfo";
import { useLocationParamsMemo, useMyConnectorInfoHook } from "../../../common/utilHooks";
import { connectorInfoMap } from "../../../common/constants";
import { useSetConnector } from "../../../nc/components/IndexView/hooks";
import DownloadGw from "../../../u9/components/DownloadGw";
import RunGw from "../../../u9/components/RunGw";
import SecretKeysView, { useSecretKeysHook } from "../../../u9/components/SecretKeys";
import ConfigView, { useConfigHook } from "../ConfigView";
import Question from "../../../u9/components/Quersion";
import ConfigAdapterView, { useConfigAdapterHook } from "../../../oa/components/ConfigAdapter";
const IndexView = (props) => {
    let { location } = props;
    const { queryParams, connectorType } = useLocationParamsMemo();
    const connectorInfo = useMemo(() => connectorInfoMap[connectorType], [connectorType]);

    let _useSetConnector = useSetConnector(queryParams, connectorType);

    const useMyConnectorInfo = useMyConnectorInfoHook(queryParams);

    let { hasCreate } = _useSetConnector;

    const useSecretKeys = useSecretKeysHook(queryParams);

    const useConfigAdapter = useConfigAdapterHook(_useSetConnector, "nccloud-app-seeyonoa", null);

    const useConfig = useConfigHook(_useSetConnector, useMyConnectorInfo, queryParams, connectorType);

    return (
        <Fragment>
            <ConfigTarget logo={connectorInfo.logo} title={connectorInfo.name} intro={connectorInfo.intro} />

            <ConfigInfoList>
                <DownloadGw _useSetConnector={_useSetConnector} />

                <RunGw />

                <SecretKeysView useSecretKeys={useSecretKeys} />

                <ConfigAdapterView hasCreate={hasCreate} useConfigAdapter={useConfigAdapter} />

                <ConfigView hasCreate={hasCreate} useConfig={useConfig} />

                <Question />
            </ConfigInfoList>
        </Fragment>
    );
};

export default IndexView;
