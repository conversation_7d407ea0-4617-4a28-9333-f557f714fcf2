import React, { useState, useCallback, useMemo, useRef } from "react";
import classnames from "classnames";
import { getLocalImg } from "utils/index";
import { autoServiceMessage } from "utils/service";
import Grid from "components/TinperBee/Grid";
import { GridActions, GridAction } from "components/TinperBee/Grid/GridActions";
import Modal from "components/TinperBee/Modal";
import { ConfigInfoItem } from "../../../components/ConfigInfo";
import { Warning } from "utils/feedback";
import { getSecretKeysService, createSecretKeysService, sendAuthMessageService, showSecretKeyService } from "../../services";
import { Button, FormControl } from "components/TinperBee";
import FormList from "components/TinperBee/Form";
import commonText from "constants/commonText";

const FormItem = FormList.Item;
/**
 * 获取网关密钥hooks
 * @return {{
      keyGridColumns: Array,
     getSecretKeys: Function,
      secretKeys: Array,
      secretPhone: String,
      showKeyModalShow: Button,
      showKeyIndex: Number|String,
      changeKeyModalStatus: Function,
      authCodeTime: Number
      sendAuthCode: Function,
      showSecretKey: Function
   }}
 */
export function useSecretKeysHook(queryParams) {
    let [secretKeys, setSecretKeys] = useState([]);
    let [secretPhone, setSecretPhone] = useState();

    let [showKeyModalShow, setShowKeyModalShow] = useState(false);
    let [showKeyIndex, setShowKeyIndex] = useState("");
    let [authCodeTime, setAuthMsgCodeTime] = useState("");

    let authCodeTimer = null,
        startTime = 59;

    const getSecretKeys = useCallback(async () => {
        let res = await autoServiceMessage({
            service: getSecretKeysService(),
        });
        if (res) {
            let { data, phone } = res;
            setSecretKeys(data);
            setSecretPhone(phone);
        }
    }, []);

    const createSecretKeys = useCallback(async () => {
        let res = await autoServiceMessage({
            service: createSecretKeysService(),
            success: window.lang.template(commonText.createSuccess),
        });
        if (res) {
            getSecretKeys();
        }
    });

    const changeKeyModalStatus = useCallback((index) => {
        if (typeof index === "number") {
            setShowKeyIndex(index);
            setShowKeyModalShow(true);
        } else {
            setShowKeyIndex("");
            setShowKeyModalShow(false);
        }
        clearInterval(authCodeTimer);
        authCodeTimer = null;
        startTime = 59;
    }, []);

    const createAuthTimer = () => {
        setAuthMsgCodeTime(startTime);
        authCodeTimer = setInterval(function () {
            startTime -= 1;
            setAuthMsgCodeTime(startTime);
            if (startTime === 0) {
                clearInterval(authCodeTimer);
                authCodeTimer = null;
                startTime = 59;
            }
        }, 1000);
    };

    const sendAuthCode = useCallback(async () => {
        let res = await autoServiceMessage({
            service: sendAuthMessageService(),
            success: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050499") /* "短信验证码已发送" */,
        });
        if (res) {
            createAuthTimer();
        }
    }, []);

    //提交验证码，获取secretKey
    const showSecretKey = useCallback(
        async (code) => {
            let res = await autoServiceMessage({
                service: showSecretKeyService({ mescode: code, appid: queryParams.appId }),
            });
            if (res) {
                let secretData = secretKeys[showKeyIndex];
                secretData["secretkey"] = res.data;
                setSecretKeys([...secretKeys]);
            }
        },
        [queryParams, secretKeys, showKeyIndex]
    );

    const keyGridColumns = useMemo(
        () => [
            {
                title: "Access Key Secret",
                dataIndex: "secretkey",
                render: (secretKey, record, index) => {
                    return secretKey ? (
                        secretKey
                    ) : (
                        <GridAction
                            fieldid="UCG-FE-routes-u9-components-SecretKeys-index-8562418-GridAction"
                            disabled={record.status == 2}
                            onClick={changeKeyModalStatus.bind(null, index)}
                        >
                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050529") /* "显示" */}
                        </GridAction>
                    );
                },
            },
            {
                title: window.lang.template(commonText.status),
                dataIndex: "status",
                width: 100,
                render: (status) => {
                    return (
                        <span>
                            <span
                                className={classnames("mix-status-dot", {
                                    "do-fail": status == 2,
                                    "do-success": status == 1,
                                })}
                            />
                            <span>
                                {
                                    status == 1
                                        ? window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050566") /* "可用" */
                                        : window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050281") /* "已禁用" */
                                }
                            </span>
                        </span>
                    );
                },
            },
            {
                title: window.lang.template(commonText.createTime),
                dataIndex: "createtime",
            },
        ],
        [secretKeys]
    );

    return {
        keyGridColumns,
        secretKeys,
        secretPhone,
        createSecretKeys,
        getSecretKeys,
        showKeyModalShow,
        showKeyIndex,
        changeKeyModalStatus,
        authCodeTime,
        sendAuthCode,
        showSecretKey,
    };
}

const SecretKeysView = (props) => {
    const { useSecretKeys } = props;
    const {
        keyGridColumns,
        secretKeys,
        secretPhone,
        getSecretKeys,
        createSecretKeys,
        showKeyModalShow,
        showKeyIndex,
        changeKeyModalStatus,
        authCodeTime,
        sendAuthCode,
        showSecretKey,
    } = useSecretKeys;
    const showKeyGrid = useMemo(() => secretKeys.length > 0, [secretKeys]);

    const authMsgCodeRef = useRef(null);

    const handleAuthMsgOk = useCallback(() => {
        let authCode = authMsgCodeRef.current.input.value;
        if (authCode === "") {
            Warning(window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050287") /* "短信验证码不能为空" */);
        } else {
            showSecretKey(authCode);
        }
    });

    return (
        <ConfigInfoItem title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050587") /* "获取网关密钥" */}>
            {showKeyGrid ? (
                <Grid
                    fieldid="ublinker-routes-u9-components-SecretKeys-index-2489730-Grid"
                    header={
                        <Button fieldid="ublinker-routes-u9-components-SecretKeys-index-8170172-Button" bordered onClick={createSecretKeys}>
                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050341") /* "创建密钥" */}
                        </Button>
                    }
                    columns={keyGridColumns}
                    data={secretKeys}
                />
            ) : (
                <p className="config-text">
                    <a fieldid="ublinker-routes-u9-components-SecretKeys-index-7402228-a" onClick={createSecretKeys}>
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050380") /* "创建密钥？" */}
                    </a>
                    <span> {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050611") /* "已有密钥" */}</span>
                    <a fieldid="ublinker-routes-u9-components-SecretKeys-index-8078926-a" onClick={getSecretKeys}>
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050601") /* "显示已有密钥" */}
                    </a>
                </p>
            )}
            <p className="config-text ucg-mar-t-5">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050567") /* "把得到的Access key复制到网关启动窗口中" */}</p>
            <div className="config-img">
                <img fieldid="ublinker-routes-u9-components-SecretKeys-index-5911436-img" src={getLocalImg("connector/enter_access_key.png")} alt="" />
            </div>

            <Modal
                fieldid="ublinker-routes-u9-components-SecretKeys-index-8474942-Modal"
                title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050309") /* "短信验证" */}
                show={showKeyModalShow}
                onCancel={changeKeyModalStatus}
                onOk={handleAuthMsgOk}
                okText={window.lang.template(commonText.submit)}
                size="md"
            >
                <FormList fieldid="ublinker-routes-u9-components-SecretKeys-index-2691160-FormList" className="ucg-mar-t-20" layoutOpt={{ md: 12 }}>
                    <FormItem
                        fieldid="ublinker-routes-u9-components-SecretKeys-index-3044018-FormItem"
                        label={window.lang.template(commonText.phoneNumber)}
                        labelCol={120}
                    >
                        <FormControl
                            fieldid="ublinker-routes-u9-components-SecretKeys-index-3453123-FormControl"
                            style={{ width: 200 }}
                            value={secretPhone}
                            disabled
                        />
                        <Button
                            fieldid="ublinker-routes-u9-components-SecretKeys-index-4249359-Button"
                            className="ucg-mar-l-10"
                            bordered
                            disabled={!!authCodeTime}
                            onClick={sendAuthCode}
                        >
                            {authCodeTime === ""
                                ? window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050612") /* "发送验证码" */
                                : window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050340") /* "重新发送" */ + (authCodeTime > 0 ? `(${authCodeTime}s)` : "")}
                        </Button>
                    </FormItem>
                    <FormItem
                        fieldid="ublinker-routes-u9-components-SecretKeys-index-1642849-FormItem"
                        label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050423") /* "验证码" */}
                        labelCol={120}
                    >
                        <FormControl
                            fieldid="ublinker-routes-u9-components-SecretKeys-index-8395749-FormControl"
                            ref={authMsgCodeRef}
                            style={{ width: 200 }}
                            placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050309") /* "短信验证" */}
                        />
                    </FormItem>
                </FormList>
            </Modal>
        </ConfigInfoItem>
    );
};

export default SecretKeysView;
