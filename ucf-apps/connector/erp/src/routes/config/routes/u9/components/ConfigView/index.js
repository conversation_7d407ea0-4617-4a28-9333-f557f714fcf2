import React, { useState, useCallback, useMemo, useEffect } from "react";
import { autoServiceMessage } from "utils/service";
import { ConfigInfoItem } from "../../../components/ConfigInfo";
import { Button, FormControl, FormList } from "components/TinperBee";
// import FormList from "components/TinperBee/Form";
import commonText from "constants/commonText";
import { injectRootStore } from "core/addStore";
import useBackConfirm from "hooks/useBackConfirm";

import { editConfigService, testConnectService, getConfigService } from "../../services";

export function useConfigHook(_useSetConnector, queryParams) {
    let { instanceInfo, connectorName } = _useSetConnector;
    let { gatewayId } = instanceInfo;
    const [configInfo, setConfigInfo] = useState(null);

    const [u9version, setU9version] = useState(null);

    const [shouldSave, setShouldSave] = useState(!queryParams.configId);

    const getConfigInfo = useCallback(async (gwId) => {
        let res = await autoServiceMessage({
            service: getConfigService(gwId),
        });
        if (res) {
            setConfigInfo(res.data || null);
            setU9version(res.data.u9version);
        }
    }, []);

    const saveConfig = useCallback(
        async (data) => {
            setU9version(data.u9version);
            data.id = instanceInfo.configId;
            data.pk_entitry = instanceInfo.gatewayId;
            data.gwguid = instanceInfo.gatewayId;
            let res = await autoServiceMessage({
                service: editConfigService(data),
                success: window.lang.template(commonText.saveSuccess),
            });
            if (res) {
                setShouldSave(false);
                if (!instanceInfo.configId) {
                    let _configId = res.data.id;
                    _useSetConnector.setInstanceInfo({
                        ...instanceInfo,
                        configId: _configId,
                    });
                    _useSetConnector.createConnect(
                        {
                            gatewayId: instanceInfo.gatewayId,
                            configid: _configId,
                            alias: connectorName,
                        },
                        false
                    );
                }
            }
            return res;
        },
        [instanceInfo]
    );

    const testConnect = useCallback(async () => {
        await autoServiceMessage({
            service: testConnectService({
                u9cfgid: instanceInfo.configId,
                gatewayGuID: instanceInfo.gatewayId,
            }),
            success: window.lang.template(commonText.testSuccess),
        });
    }, [instanceInfo]);

    useEffect(() => {
        if (gatewayId) {
            getConfigInfo(gatewayId);
        }
    }, [gatewayId]);

    return {
        configInfo,
        saveConfig,
        testConnect,
        setShouldSave,
        shouldSave,
        u9version,
    };
}

const FormItem = FormList.Item;
const labelCol = 120;

const ConfigView = (props) => {
    const [form] = FormList.useForm();
    const formItemLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 12 },
    };
    const { setFieldsValue, getFieldProps, validateFields, getFieldError, isFieldsTouched } = form;
    let { useConfig, hasCreate } = props;
    let { configInfo, saveConfig, testConnect, setShouldSave, shouldSave } = useConfig;

    const { setBackConfirm } = useBackConfirm(props, isFieldsTouched());

    useEffect(() => {
        if (configInfo) {
            setFieldsValue(configInfo);
        }
    }, [configInfo]);

    const handleConfigChange = useCallback(() => {
        setShouldSave(true);
    }, []);

    const handleSave = useCallback(() => {
        // validateFields((error, values) => {
        //   if (!error) {
        //     saveConfig(values).then(res => {
        //       if (res) {
        //         setBackConfirm(false)
        //       }
        //     });

        //   }
        // })
        validateFields().then((values) => {
            saveConfig(values).then((res) => {
                if (res) {
                    setBackConfirm(false);
                }
            });
        });
    });

    const disabled = !hasCreate;

    return (
        <ConfigInfoItem title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050440") /* "输入U9配置并测试连接" */}>
            <FormList
                fieldid="ublinker-routes-u9-components-ConfigView-index-1513580-FormList"
                FormList
                className="config-action-form"
                form={form}
                name="form122"
                labelAlign="right"
                {...formItemLayout}
                layoutOpt={{ md: 12 }}
            >
                <FormItem
                    fieldid="ublinker-routes-u9-components-ConfigView-index-1838410-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050248") /* "U9服务器地址" */}
                    name="u9ipaddr"
                    rules={[
                        {
                            required: true,
                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050571") /* "请输入服务器地址" */,
                        },
                    ]}
                >
                    <FormControl
                        fieldid="ublinker-routes-u9-components-ConfigView-index-1414160-FormControl"
                        className="ucg-mar-r-5"
                        disabled={disabled}
                        placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050365") /* "请输入U9内网可以访问地址如http://************:3265" */}
                        onChange={handleConfigChange}
                    />
                </FormItem>

                <FormItem
                    fieldid="ublinker-routes-u9-components-ConfigView-index-4752006-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050600") /* "U9版本" */}
                    name="u9version"
                    rules={[
                        {
                            required: true,
                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050455") /* "请输入U9版本" */,
                        },
                    ]}
                    initialValue="u9ce"
                >
                    <FormControl
                        fieldid="ublinker-routes-u9-components-ConfigView-index-7342671-FormControl"
                        className="ucg-mar-r-5"
                        disabled
                        placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050624") /* "请输入U9版本，如：2.0" */}
                        onChange={handleConfigChange}
                    />
                </FormItem>

                <FormItem
                    fieldid="ublinker-routes-u9-components-ConfigView-index-3914898-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050494") /* "语种编码" */}
                    name="culturename"
                    rules={[
                        {
                            required: true,
                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050316") /* "请输入U9系统语种编码" */,
                        },
                    ]}
                >
                    <FormControl
                        fieldid="ublinker-routes-u9-components-ConfigView-index-1958629-FormControl"
                        className="ucg-mar-r-5"
                        disabled={disabled}
                        placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050496") /* "U9系统语种编码" */}
                        onChange={handleConfigChange}
                    />
                </FormItem>

                <FormItem
                    fieldid="ublinker-routes-u9-components-ConfigView-index-8405245-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050424") /* "企业编码" */}
                    name="entcode"
                    rules={[
                        {
                            required: true,
                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050539") /* "默认企业编码" */,
                        },
                    ]}
                >
                    <FormControl
                        fieldid="ublinker-routes-u9-components-ConfigView-index-5375406-FormControl"
                        className="ucg-mar-r-5"
                        disabled={disabled}
                        placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050539") /* "默认企业编码" */}
                    />
                </FormItem>

                <FormItem
                    fieldid="ublinker-routes-u9-components-ConfigView-index-8654922-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050344") /* "组织编码" */}
                    name="orgcode"
                    rules={[
                        {
                            required: true,
                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050513") /* "默认组织编码" */,
                        },
                    ]}
                >
                    <FormControl
                        fieldid="ublinker-routes-u9-components-ConfigView-index-5532377-FormControl"
                        className="ucg-mar-r-5"
                        disabled={disabled}
                        placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050513") /* "默认组织编码" */}
                    />
                </FormItem>

                <FormItem
                    fieldid="ublinker-routes-u9-components-ConfigView-index-2212507-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050582") /* "用户编码" */}
                    name="usercode"
                    rules={[
                        {
                            required: true,
                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050740") /* "请输入用户编码" */,
                        },
                    ]}
                >
                    <FormControl
                        fieldid="ublinker-routes-u9-components-ConfigView-index-9616353-FormControl"
                        className="ucg-mar-r-5"
                        disabled={disabled}
                        placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050740") /* "请输入用户编码" */}
                    />
                </FormItem>

                <FormItem fieldid="ublinker-routes-u9-components-ConfigView-index-438319-FormItem" label=" ">
                    <Button
                        fieldid="ublinker-routes-u9-components-ConfigView-index-6628930-Button"
                        className="ucg-mar-r-10"
                        disabled={disabled}
                        colors="primary"
                        onClick={handleSave}
                    >
                        {window.lang.template(commonText.saveChange)}
                    </Button>
                    <Button
                        fieldid="ublinker-routes-u9-components-ConfigView-index-8964769-Button"
                        className="ucg-mar-r-10"
                        disabled={disabled || shouldSave}
                        bordered
                        onClick={testConnect}
                    >
                        {window.lang.template(commonText.testConnect)}
                    </Button>
                </FormItem>
            </FormList>
        </ConfigInfoItem>
    );
};

const FormConfigView = ConfigView;

export default injectRootStore()(FormConfigView);
