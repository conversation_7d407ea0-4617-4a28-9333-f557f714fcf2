import { getInvokeService, getServicePath } from "utils/service";

/**
 * 下载网关Url
 * @type {string}
 */
export const downloadGwUrl = getServicePath("/diwork/ncconnect/gateway/download/");

/**
 * 获取密钥列表
 * @return {Promise<unknown>}
 */
export const getSecretKeysService = function () {
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/ncconnect/secretkey/get",
        },
        { order: "asc" }
    );
};

/**
 * 创建密钥
 * @return {Promise<unknown>}
 */
export const createSecretKeysService = function () {
    return getInvokeService({
        method: "POST",
        path: "/diwork/secretkey/add",
    });
};

/**
 * 获取短信验证码
 * @return {Promise<unknown>}
 */
export const sendAuthMessageService = function () {
    return getInvokeService({
        method: "POST",
        path: "/diwork/secretkey/message/sendMessage",
    });
};

/**
 * 提交短信验证码获取密钥
 * @param {Object} data
 * @param {String} data.mescode
 * @param {String} data.appid
 * @return {Promise<unknown>}
 */
export const showSecretKeyService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/diwrok/secretkey/show",
        },
        data
    );
};

/**
 * 保存编辑配置
 * @param {Object} data
 * @param {String=} data.id -新建不传
 * @param {String=} data.pk_entitry -gatewayId
 * @param {String=} data.gwguid -gatewayId
 * @param {String=} data.u9ipaddr
 * @param {String=} data.u9version
 * @param {String=} data.culturename
 * @param {String=} data.entcode
 * @param {String=} data.orgcode
 * @return {Promise<unknown>}
 */
export const editConfigService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/ncconnect/u9config/add",
        },
        data
    );
};

/**
 * 测试连接
 * @param {Object} data
 * @param {String} data.u9cfgid
 * @param {String} data.gatewayGuID
 * @return {Promise<unknown>}
 */
export const testConnectService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/u9config/testu9",
        },
        data
    );
};

/**
 * 获取配置信息
 * @param gwId
 * @return {Promise<unknown>}
 */
export const getConfigService = function (gwId) {
    return getInvokeService({
        method: "GET",
        path: "/mygwapp/ncconnect/getU9Config/" + gwId,
    });
};

//初始化同步任务
export const getInitMessageService = function (data) {
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/erpdata/task/getInitMessageByU9",
        },
        data
    );
};
export const getDataViewService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/erpdata/initTask/dataview",
        },
        data
    );
};
export const initDataViewService = function (data, useMainTenantGateway) {
    // let url = `/diwork/erpdata/task/init/selectedTaskByU9/${data.gatewayId}`;
    let url = `/diwork/erpdata/task/init/selectedTask/${data.gatewayId}`;

    if (useMainTenantGateway !== "" && useMainTenantGateway !== undefined) {
        url += "?useMainTenantGateway=" + useMainTenantGateway;
    }
    return getInvokeService(
        {
            method: "POST",
            path: url,
        },
        data
    );
};
export const updateSyncTaskService = function (data) {
    let { taskId, ..._data } = data;
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/erpdata/task/update/" + taskId,
        },
        _data
    );
};
