import React, { Fragment, useMemo, useCallback, useRef } from "react";
import ConfigTarget from "../../../components/ConfigTarget";
import { ConfigInfoList, ConfigInfoItem } from "../../../components/ConfigInfo";
import { useLocationParamsMemo } from "../../../common/utilHooks";
import { connectorInfoMap } from "../../../common/constants";
import { useSetConnector } from "../../../nc/components/IndexView/hooks";
import DownloadGw from "../DownloadGw";
import RunGw from "../RunGw";
import SecretKeysView, { useSecretKeysHook } from "../SecretKeys";
import ConfigView, { useConfigHook } from "../ConfigView";
import Question from "../Quersion";

import { FormList, FormControl } from "components/TinperBee";
import { Button } from "components/TinperBee";
import { getLocalImg } from "utils/index";
import { downloadGatewayKey } from "services/gwServices";
import { Warning } from "utils/feedback";
import InitTaskView, { useInitTask } from "../InitTask";
import AdaptersView from "../../../nc/components/Adapters";
import { useGatewayConfig } from "hooks/useGateway";
const FormItem = FormList.Item;
const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 12 },
};
const labelCol = 130;
const IndexView = (props) => {
    const adapterViewRef = useRef(null);
    const [form] = FormList.useForm();
    let { location } = props;
    const { queryParams, connectorType } = useLocationParamsMemo();
    const connectorInfo = useMemo(() => connectorInfoMap[connectorType], [connectorType]);

    let _useSetConnector = useSetConnector(queryParams, connectorType);
    // console.log(_useSetConnector)

    let { hasCreate, instanceInfo, connectorName, setConnectorName, createInstance } = _useSetConnector;

    const useSecretKeys = useSecretKeysHook(queryParams);

    const useConfig = useConfigHook(_useSetConnector, queryParams);

    const _useInitTask = useInitTask(_useSetConnector, {}, connectorType, {}, useConfig.u9version); //_useErpConfig.erpConfig=>useConfig.configInfo

    const [gatewayConfig, getGatewayConfig] = useGatewayConfig(instanceInfo.gatewayId, instanceInfo.useMainTenantGateway);

    const downloadKeyClick = useCallback(() => {
        downloadGatewayKey(instanceInfo.gatewayId, false);
    }, [instanceInfo.gatewayId]);
    const handleCreateInstance = useCallback(() => {
        if (connectorName.length > 20) {
            Warning(window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050922") /* "连接器名称长度不能超过20个字符" */);
        } else {
            createInstance(false, connectorName);
        }
    }, [connectorName]);

    return (
        <Fragment>
            <ConfigTarget logo={connectorInfo.logo} title={connectorInfo.name} intro={connectorInfo.intro} />

            <ConfigInfoList>
                <ConfigInfoItem
                    step="1"
                    title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050537", connectorInfo) /* "设置<%= name %>连接器名称" */}
                    connectorInfo={connectorInfo}
                >
                    <FormList
                        fieldid="ublinker-routes-u9-components-IndexView-index-350476-FormList"
                        className="config-action-form config-action-form-80"
                        form={form}
                        name="form122"
                        labelAlign="right"
                        {...formItemLayout}
                        layoutOpt={{ md: 12 }}
                    >
                        <FormItem
                            fieldid="ublinker-routes-u9-components-IndexView-index-6639629-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050551") /* "连接器名称" */}
                        >
                            <FormControl
                                fieldid="ublinker-routes-u9-components-IndexView-index-6296158-FormControl"
                                disabled={hasCreate}
                                value={connectorName}
                                onChange={setConnectorName}
                            />
                        </FormItem>

                        {/* <FormItem fieldid="ublinker-routes-u9-components-IndexView-index-4363416-FormItem" label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050360") * "请选择版本" *} >
                            <Select fieldid="ublinker-routes-u9-components-IndexView-index-3782045-Select" 
                                disabled={hasCreate}
                                data={erpVersions}
                                value={erpVersion}
                                onChange={setErpVersion}
                            />
                        </FormItem> */}

                        {hasCreate ? null : (
                            <FormItem fieldid="ublinker-routes-u9-components-IndexView-index-9024012-FormItem" label=" ">
                                <Button
                                    fieldid="ublinker-routes-u9-components-IndexView-index-1894674-Button"
                                    disabled={!connectorName}
                                    colors="primary"
                                    onClick={handleCreateInstance}
                                >
                                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050352") /* "确认" */}
                                </Button>
                            </FormItem>
                        )}
                    </FormList>
                </ConfigInfoItem>

                <DownloadGw _useSetConnector={_useSetConnector} />
                {/* 现在项 */}
                <ConfigInfoItem step="2" title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050587") /* "下载密钥并启动网关" */}>
                    {/*  */}
                    <Button fieldid="ublinker-routes-u9-components-IndexView-index-3984178-Button" bordered disabled={!hasCreate} onClick={downloadKeyClick}>
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050425") /* "下载密钥" */}
                    </Button>
                    <p className="config-text ucg-mar-t-10">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050364") /* "首先下载密钥" */}</p>
                    <p className="config-text">
                        <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050451") /* "Windows环境：" */}</strong>{" "}
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050615") /* "将下载的密钥解压至网关解压目录的config文件夹下" */}
                    </p>
                    <p className="config-text">
                        <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050436") /* "启动网关：" */}</strong>
                        {
                            window.lang.template(
                                "MIX_UBL_ALL_UBL_FE_LOC_000504291"
                            ) /* "在config的同级文件夹找到bin文件夹，例如：F:gateway ccpub-gateway-client-1.0.0-SNAPSHOTin，运行startup.bat。" */
                        }
                    </p>
                    <p className="config-text">
                        <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050350") /* "Linux环境：" */}</strong>
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050615") /* "将下载的密钥解压至网关解压目录的config文件夹下" */}
                    </p>
                    <p className="config-text">
                        <strong>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050436") /* "启动网关：" */}</strong>
                        {
                            window.lang.template(
                                "MIX_UBL_ALL_UBL_FE_LOC_000504291"
                            ) /* "在config的同级文件夹找到bin文件夹，例如：F:gateway ccpub-gateway-client-1.0.0-SNAPSHOTin，运行startup.bat。" */
                        }
                    </p>
                    <p className="config-text">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050544") /* "启动成功后，选择网关语言。如图" */}</p>
                    <div className="config-img">
                        <img fieldid="ublinker-routes-u9-components-IndexView-index-3940201-img" src={getLocalImg("connector/gaojiban.png")} alt="" />
                    </div>
                </ConfigInfoItem>

                {/* 原来项 */}
                {/* <RunGw/>

        <SecretKeysView
          useSecretKeys={useSecretKeys}
        /> */}

                <ConfigView hasCreate={hasCreate} useConfig={useConfig} />
                <InitTaskView _useInitTask={_useInitTask} labelCol={labelCol} />
                {instanceInfo.useMainTenantGateway !== "true" ? (
                    <AdaptersView ref={adapterViewRef} instanceInfo={_useSetConnector.instanceInfo} getGatewayConfig={getGatewayConfig} />
                ) : null}

                <Question />
            </ConfigInfoList>
        </Fragment>
    );
};

export default IndexView;
