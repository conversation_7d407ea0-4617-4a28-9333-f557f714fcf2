import React, { useCallback } from "react";
import { <PERSON><PERSON> } from "components/TinperBee";
import { ConfigInfoItem } from "../../../components/ConfigInfo";
import { a_download } from "utils/index";
import { downloadGwUrl } from "../../services";
import { DownloadGateway } from "components/DownloadGateway";

// const DownloadGw = (props) => {   原下载网关
//   let { _useSetConnector } = props;
//   let { hasCreate, instanceInfo, createInstance } = _useSetConnector;
//   const handleDownloadGw = useCallback(async () => {
//     let instanceRes = await createInstance(false);
//      if (instanceRes) {
//        a_download('/iuap-ipaas-dataintegration'+downloadGwUrl + instanceRes.data.guid)
//      }
//   })
const DownloadGw = (props) => {
    //现下载网关
    let { _useSetConnector } = props;
    let { hasCreate, instanceInfo, createInstance } = _useSetConnector;
    const handleDownloadGw = useCallback(async () => {
        let instanceRes = await createInstance(false);
        return instanceRes.data.guid;
    });
    return (
        <ConfigInfoItem title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050575") /* "下载网关" */}>
            {/* <div className="config-action-form">
        {hasCreate ? (
          <p className="config-text ucg-mar-b-10">
            <i fieldid="ublinker-routes-u9-components-DownloadGw-index-1247715-i" className='cl cl-pass-c ucg-mar-r-5' style={{color: '#18B681'}}/>
            <span>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050605") / "网关已下载，实例ID：" /}</span>
            <a>{instanceInfo.gatewayId}</a>
          </p>
        ) : (
          <Button fieldid="ublinker-routes-u9-components-DownloadGw-index-3611375-Button" onClick={handleDownloadGw}>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050575") / "下载网关" /}</Button>
        )}
        <p className="config-text-third">
          {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050337") / "如果已经下载并运行过网关，可直接跳至第四步" /}
        </p>
      </div> */}
            <p className="config-text">
                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050304") /* "找一台7x24小时都能访问您ERP系统，且能访问互联网的机器，下载网关。" */}
            </p>
            <p className="config-text">
                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050413") /* "下载完成后解压，需保证解压后的文件目录不存在中文字符。" */}
            </p>
            <div className="config-text ucg-mar-t-10">
                <DownloadGateway gatewayId={instanceInfo.gatewayId}>
                    {/* handleDownloadGw={handleDownloadGw} "原本此处先创建实例，再下载网关，现在创建实例提到第一步，所以此处不需要了" */}
                    <Button fieldid="ublinker-routes-u9-components-DownloadGw-index-1248850-Button" bordered disabled={!hasCreate}>
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050291") /* "请先下载网关" */}
                    </Button>
                    {/* disabled={!hasCreate} */}
                </DownloadGateway>
            </div>
        </ConfigInfoItem>
    );
};

export default DownloadGw;
