@import '~styles/base.less';
.sys-connector {
    &-config-breadcrumb {
        padding: 10px 0;
        color: #999;
        background-color: @layout-body-background;
    }

    &-config-content-wrap {
        overflow: hidden;
        width: 1200px;
        margin: 0 auto;
    }

    &-nav-links {
        width: 260px;
        background: #FFFFFF;
        padding: 26px 30px;
        position: relative;
        .config-nav-links {
            font-size: 12px;
            padding-bottom: 25px;
            margin-bottom: 16px;
            border-bottom: 1px dashed #E4E4E4;
            position: relative;
            .nav-link-list {

            }
            .nav-link-item {
                position: relative;
                padding-left: 25px;
                line-height: 24px;

                a {
                    color: #666;
                }
                &.active {
                    color: #588CE9;
                    a {
                        color: #588CE9;
                    }
                    .nav-link-item-dot {
                        display: block;
                    }
                }
            }
            .nav-link-item-dot {
                display: none;
                width: 12px;
                height: 12px;
                padding: 3px;
                border-radius: 50%;
                background:rgba(88,140,233,0.5);
                position: absolute;
                top: 6px;
                left: 0;
                z-index: 10;
                &:after {
                    content: '';
                    display: block;
                    width: 100%;
                    height: 100%;
                    background: #588CE9;
                    border-radius: 50%;
                }
            }
            .nav-link-item-content {
                overflow: hidden;
                display: block;
            }
            .nav-link-num {
                width: 25px;
                float: left;
            }
            .nav-link-text {
                display: block;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                &.ques-nav-text {

                }
            }
            .nav-links-line {
                width: 2px;
                top: 5px;
                left: 5px;
                bottom: 20px;
                position: absolute;
                background-color: #E4E4E4;
                &:before,
                &:after {
                    content: '';
                    position: absolute;
                    width: 10px;
                    height: 10px;
                    background: #FFFFFF;
                    border-radius: 50%;
                    border: 1px solid #E4E4E4;
                    left: -4px;
                    z-index: 9;
                }
                &:before {
                    top: -5px;
                }
                &:after {
                    bottom: -5px;
                }
            }

        }
        .config-nav-link-title {
            font-size: 14px;
            color: #666666;
            font-weight: 600;
            text-align: center;
            margin-bottom: 8px;
        }

        .config-ques {
            font-size: 14px;
            color: #666666;
            line-height: 20px;
            text-align: center;
            font-weight: 600;
            cursor: pointer;
        }
    }

    &-config-info{
        padding-left: 20px;
        overflow: hidden;
        margin-left: 260px;
    }


}
