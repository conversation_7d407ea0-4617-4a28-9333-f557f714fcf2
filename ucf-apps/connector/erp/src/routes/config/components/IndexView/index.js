import React, { useEffect, useState, useCallback, useMemo } from "react";
import query from "query-string";
import Header from "components/PageView/Header";
import classnames from "classnames";
import { Tooltip } from "components/TinperBee";
import Affix from "components/TinperBee/Affix";
import { appContainer, getScrollContainer, scrollContainer } from "utils/containerEle";
import ConfigRoutes from "../../routes";
import _debounce from "lodash/debounce";

import "./index.less";
import withRouter from "decorator/withRouter";

function getStepItemNodes() {
    const nodeList = document.querySelectorAll(".sys-connector-config-info-item.config-item");
    return [...nodeList];
}

function getQuesItemNodes() {
    const nodeList = document.querySelectorAll(".sys-connector-config-ques");
    return [...nodeList];
}

let NAV_CLICK_SCROLL = false;

const IndexView = (props) => {
    const { configCommonState, configCommonStore, rootState, location } = props;
    const [stepNum, setStepNum] = useState(1);
    const [quesNavs, setQuesNavs] = useState([]);

    const { navLinks, configName, questionShow } = configCommonState;
    const { backConfirm } = rootState;
    const queryParams = query.parse(location.search);

    const isEdit = useMemo(() => !!queryParams.configId, [location.search]);

    const handleNav = useCallback(
        (node) => {
            let nodeType = typeof node;
            let isNavClick = false;
            let _nodeNum;
            if (nodeType === "number" || nodeType === "string") {
                _nodeNum = node;
            } else {
                isNavClick = true;
                _nodeNum = node.currentTarget.dataset.step;
            }
            if (_nodeNum != stepNum) {
                if (isNavClick) {
                    setStepNum(_nodeNum * 1);
                    NAV_CLICK_SCROLL = true;
                    let stepItemNodes = [...getStepItemNodes(), ...getQuesItemNodes()];
                    scrollContainer.scrollTop = stepItemNodes[_nodeNum - 1].offsetTop - 90;
                }
            }
        },
        [stepNum]
    );

    useEffect(() => {
        let scrollFun = null;
        let stepItemNodes = getStepItemNodes();
        let quesItemNodes = getQuesItemNodes();
        let allItemNodes = [...stepItemNodes, ...quesItemNodes];
        let _quesNavs = [];
        quesItemNodes.forEach((quesItemNode) => {
            let ques = quesItemNode.firstChild.innerText;
            // \uff1a 中文冒号：Unicode编码
            _quesNavs.push(ques.match(/[:\uff1a](.*)$/)[1]);
        });

        setQuesNavs(_quesNavs);

        if (!scrollFun) {
            scrollFun = _debounce((e) => {
                if (!NAV_CLICK_SCROLL) {
                    let scrollTop = e.target.scrollTop;
                    let nodeTops = [];
                    allItemNodes.forEach((itemNode) => {
                        nodeTops.push(itemNode.offsetTop);
                    });
                    let diffTops = nodeTops.map((offsetTop) => {
                        return Math.abs(scrollTop - offsetTop);
                    });
                    let minDiffTop = Math.min.apply(null, diffTops);
                    let minDiffTopIndex = diffTops.indexOf(minDiffTop);
                    setStepNum(minDiffTopIndex + 1);
                }
                NAV_CLICK_SCROLL = false;
            }, 300);
            scrollContainer.addEventListener("scroll", scrollFun, false);
        }
        return () => {
            scrollContainer.removeEventListener("scroll", scrollFun);
            configCommonStore.init();
        };
    }, []);

    const navLinkLen = navLinks.length;

    return (
        <div className="sys-connector-config-content-wrap">
            <Affix
                fieldid="ublinker-routes-config-components-IndexView-index-4098940-Affix"
                // container={appContainer}
                zIndex={1600}
                getPopupContainer={appContainer}
                target={getScrollContainer}
            >
                <div className="sys-connector-config-breadcrumb"></div>
            </Affix>

            <Affix
                fieldid="ublinker-routes-config-components-IndexView-index-4715328-Affix"
                zIndex={1600}
                className="ucg-float-l"
                // container={appContainer}
                getPopupContainer={appContainer}
                offsetTop={20}
                target={getScrollContainer}
            >
                <div className="sys-connector-nav-links">
                    <div className="config-nav-links">
                        <p className="config-nav-link-title">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050431") /* "设置指南" */}</p>
                        <ul className="nav-link-list">
                            {navLinks.map((nav, idx) => {
                                let step = idx + 1;
                                return (
                                    <li className={classnames("nav-link-item", { active: step == stepNum })} key={step + "set"}>
                                        <div className="nav-link-item-dot"></div>
                                        <Tooltip
                                            fieldid="ublinker-routes-config-components-IndexView-index-1930116-Tooltip"
                                            placement="right"
                                            overlay={nav}
                                            container={appContainer}
                                        >
                                            <a
                                                fieldid="ublinker-routes-config-components-IndexView-index-8259357-a"
                                                className="nav-link-item-content"
                                                onClick={handleNav}
                                                data-step={step}
                                            >
                                                <span className="nav-link-num">{idx + 1}.</span>
                                                <span className="nav-link-text">{nav}</span>
                                            </a>
                                        </Tooltip>
                                    </li>
                                );
                            })}
                        </ul>
                        <div className="nav-links-line"></div>
                    </div>

                    {questionShow ? (
                        <div className="config-nav-links">
                            <p className="config-nav-link-title">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050290") /* "常见问题" */}</p>
                            <ul className="nav-link-list">
                                {quesNavs.map((nav, idx) => {
                                    let step = navLinkLen + idx + 1;
                                    return (
                                        <li className={classnames("nav-link-item", { active: step == stepNum })} key={step + "ques"}>
                                            <div className="nav-link-item-dot"></div>
                                            <Tooltip
                                                fieldid="ublinker-routes-config-components-IndexView-index-4730039-Tooltip"
                                                placement="right"
                                                overlay={nav}
                                                container={appContainer}
                                            >
                                                <a
                                                    fieldid="ublinker-routes-config-components-IndexView-index-4075205-a"
                                                    className="nav-link-item-content"
                                                    onClick={handleNav}
                                                    data-step={step}
                                                >
                                                    <span className="nav-link-num">{idx + 1}.</span>
                                                    <span className="nav-link-text ques-nav-text">{nav}</span>
                                                </a>
                                            </Tooltip>
                                        </li>
                                    );
                                })}
                            </ul>
                            <div className="nav-links-line"></div>
                        </div>
                    ) : null}
                </div>
            </Affix>

            <div className="sys-connector-config-info">
                <Affix
                    fieldid="ublinker-routes-config-components-IndexView-index-2903609-Affix"
                    zIndex={1600}
                    getPopupContainer={appContainer}
                    offsetTop={20}
                    target={getScrollContainer}
                >
                    <Header
                        back
                        backConfirm={backConfirm}
                        bordered
                        fixed={false}
                        title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050752") /* "返回" */}
                    />
                </Affix>

                <ConfigRoutes />
            </div>
        </div>
    );
};

export default withRouter(IndexView);
