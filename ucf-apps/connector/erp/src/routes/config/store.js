import { observable, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import core from "core";
const initState = {
    configName: "-",
    navLinks: [],
    questionShow: false,
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    init = () => {
        this.state = initState;
    };

    setConfigName = (configName) => {
        this.state.configName = configName;
    };

    setNavLinks = (nav) => {
        this.state.navLinks.push(nav);
    };

    setQuestionShow = () => {
        this.state.questionShow = true;
    };
}
/**
 * 根据具体模块修改 storeKey 为保证唯一性
 * storeKey 由 模块文件夹名称 + 页面名称 + 'Store' 小驼峰
 *
 * storeKey是作为当前 Store 的唯一键， storeKey与Store的实例会在 rootStore或者StoreMap中以 键值对的 形式存在
 * 1. 用于在模块的 storeMap（模块routes/store.js）中注册并初始化当前Store
 * 2. 用于在container的 inject 中关联当前Store的实例时，从模块的storeMap或者rootStore中获取对应store实例
 * */
export const storeKey = "systemConnectorConfigCommonStore";

export const addStore = () => {
    core.addStore({
        storeKey: storeKey,
        store: new Store(),
    });
};
export default Store;
