import React, { Component } from "react";
import IndexView from "./components/IndexView";
import { inject, observer } from "mobx-react";
import { injectRootStore } from "core/addStore";
import { storeKey, addStore } from "./store";

addStore();

@injectRootStore()
@inject((store) => {
    let configCommonStore = store[storeKey];
    return {
        configCommonState: configCommonStore.toJS(),
        configCommonStore: configCommonStore,
    };
})
@observer
class Container extends Component {
    constructor(props) {
        super(props);
    }

    render() {
        return <IndexView {...this.props} />;
    }
}

export default Container;
