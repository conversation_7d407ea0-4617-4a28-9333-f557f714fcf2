import React, { Component } from "react";
import query from "query-string";
import { Tabs, Modal, Icon } from "components/TinperBee";
import Affix from "components/TinperBee/Affix";
import { Content } from "components/PageView";
import withRouter from "decorator/withRouter";
import { getScrollContainer, appContainer } from "utils/containerEle";
import ConnectorList from "../ConnecterList";
import commonText from "constants/commonText";
import SearchInput from "components/TinperBee/SearchInput";
import { Warning, Error } from "utils/feedback";
import { PRIVATE, QUERY_PARAMS } from "utils/util";
import "./index.less";

@withRouter
class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            hoverData: null,
        };
    }

    componentDidMount() {
        let { ownerStore } = this.props;
        ownerStore.getRestartEryTypes();
        ownerStore.getDataSource(false);
    }

    componentWillUnmount() {
        this.props.ownerStore.initTab();
    }

    getTabs = (tabs) => {
        return tabs.map((tab) => {
            if (tab.hide) {
                return null;
            } else {
                let { type, key, name } = tab;
                return <Tabs.TabPane fieldid="UCG-FE-routes-home-components-IndexView-index-7873262-Tabs.TabPane" tab={name} key={key} />;
            }
        });
    };

    handleSearch = (searchValue) => {
        this.props.ownerStore.getDataSource(false, searchValue);
    };

    getActiveTab = (tabs, activeTabKey) => {
        return tabs.find((tab) => tab.key === activeTabKey);
    };

    handleDelete = (connector) => {
        let { ownerStore } = this.props;
        Modal.confirm({
            fieldid: "202306091529",
            title: window.lang.template(commonText.confirmDelete),
            content: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050468") /* "请慎重考虑，此操作不可逆！" */,
            onOk: ownerStore.deleteConnector.bind(null, connector),
        });
    };

    handleEdit = (data) => {
        this.props.navigate({
            pathname: "/config/" + data.type,
            search:
                "?" +
                query.stringify({
                    configId: data.config ? data.config.id || data.config.configid : "",
                    gatewayId: data.config ? data.config.gatewayId || data.config.gatewayid : "",
                    tenantId: data.tenantId,
                    alias: data.alias,
                    id: data.id,
                    fromMainTenant: data.fromMainTenant ? data.fromMainTenant : "",
                    //hrps的配置
                    host: data.config ? data.config.host : "",
                    account: data.config ? data.config.account : "",
                    accountYear: data.config ? data.config.accountYear : "",
                }),
        });
    };

    handleLog = (data) => {
        const gatewayId = data.config ? data.config.gatewayId || data.config.gatewayid : "";
        this.props.navigate({
            pathname: "/log",
            search: "?gatewayId=" + gatewayId,
        });
    };

    handleAdd = (data) => {
        if (PRIVATE || data.owned) {
            this.props.navigate({
                pathname: "/config/" + data.appcode,
                search:
                    "?" +
                    query.stringify({
                        appId: data.id,
                    }),
            });
        } else {
            Modal.confirm({
                fieldid: "************",
                title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050737", { data }) /* "<%=name %>-购买提示" */,
                content:
                    window.lang.template(
                        "MIX_UBL_ALL_UBL_FE_LOC_00050753"
                    ) /* "您还没有购买此应用，请前往工作台-我的应用，点击右上角应用市场，搜索友企连，进入详情页面选择购买对应的应用规格！" */,
                // okText: isDiwork ? '前往我的应用' : '前往云市场',
                icon: <Icon fieldid="ublinker-routes-home-components-IndexView-index-7947117-Icon" type="uf-i-c-2" />,
                confirmType: "one",
                onOk: () => {
                    if (QUERY_PARAMS.from === "diwork") {
                        window.jDiwork.openService("GZTSYS010", 2);
                    } else {
                        //f8986ec6-34ef-409a-a45f-08191c2ebe0e
                    }
                },
            });
        }
    };

    handleTabChange = (tabKey) => {
        let { ownerState, ownerStore } = this.props;
        let nextTab = this.getActiveTab(ownerState.tabs, tabKey);
        this.searchNode.setValue(nextTab.searchKey);
        ownerStore.changeActiveTab(tabKey);
    };

    handleRestart = (data) => {
        const {
            state,
            type,
            config: { gatewayId, gatewayid },
        } = data;
        const {
            ownerState: { restartErpTypes },
            ownerStore,
        } = this.props;
        if (state == 0) {
            Warning(window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050746") /* "离线状态的网关不能重启，请先手动启动" */);
            return;
        }
        if (!restartErpTypes.includes(type)) {
            Warning(type + window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050784") /* "暂不支持重启网关" */);
            return;
        }
        if (gatewayId || gatewayid) {
            ownerStore.restartGateway(gatewayId || gatewayid);
        }
    };

    render() {
        let { ownerState, ownerStore } = this.props;
        let { tabs, activeTabKey } = ownerState;
        let activeTabData = this.getActiveTab(tabs, activeTabKey);
        return (
            <Content className="sys-connector-content">
                <Affix
                    fieldid="ublinker-routes-home-components-IndexView-index-2740005-Affix"
                    zIndex={1600}
                    getPopupContainer={appContainer}
                    // container={appContainer}
                    target={getScrollContainer}
                >
                    <Tabs
                        fieldid="ublinker-routes-home-components-IndexView-index-6921140-Tabs"
                        className="sys-connector-tab"
                        activeKey={activeTabKey}
                        onChange={this.handleTabChange}
                        extraContent={
                            <div>
                                {PRIVATE ? null : (
                                    <a
                                        fieldid="ublinker-routes-home-components-IndexView-index-6504930-a"
                                        style={{ verticalAlign: "sub" }}
                                        href="https://docs.diwork.com/l/E8629Cf3Ba4F"
                                        className="ucg-mar-r-10"
                                        target="_blank"
                                    >
                                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050761") /* "帮助文档" */}
                                    </a>
                                )}
                                <SearchInput
                                    className="ucg-mar-r-20 ucg-float-r"
                                    size="lg"
                                    ref={(searchNode) => (this.searchNode = searchNode)}
                                    placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050503") /* "按名称搜索" */}
                                    // value={activeTabData.searchKey}
                                    // onChange={ownerStore.changeSearchKey}
                                    showClear
                                    onSearch={this.handleSearch}
                                />
                            </div>
                        }
                    >
                        {this.getTabs(tabs)}
                    </Tabs>
                </Affix>

                <div className="sys-connector-view-wrap">
                    <ConnectorList
                        {...activeTabData}
                        navToAdd={ownerStore.changeActiveTab}
                        onDelete={this.handleDelete}
                        onAdd={this.handleAdd}
                        onEdit={this.handleEdit}
                        onLog={this.handleLog}
                        onRestart={this.handleRestart}
                    />
                </div>
            </Content>
        );
    }
}

export default IndexView;
