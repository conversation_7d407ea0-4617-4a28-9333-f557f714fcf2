import React, { Fragment, useMemo } from "react";
import classnames from "classnames";
import { getLocalImg, getCompleteImg } from "utils/index";
import Empty from "components/EmptyPage";
import commonText from "constants/commonText";
import Clipboard from "components/TinperBee/Clipboard";
import CardList from "components/Card";
import Highlight from "components/Highlight";
import TextOverflow from "components/TextOverflow";
import { PRIVATE } from "utils/util";

import "./index.less";

const clientHeight = document.body.clientHeight;

const emptyStyle = {
    marginTop: clientHeight / 2 - 158 + "px",
};

const getCardList = (props) => {
    let { dataSource, type: cardType, searchKey, loaded, onDelete, onAdd, onEdit, navToAdd, onLog } = props;
    if (loaded) {
        if (dataSource.length > 0) {
            return dataSource.map((item, index) => {
                let { id, path, name, alias, type, state, intro, config } = item;
                let { isdefault, orgcode, gatewayId, gatewayid } = config || {};
                let online = state == 1;
                let isU8 = type === "u8";
                let _gwId = gatewayId || gatewayid;
                let infoCode = isU8 ? orgcode : _gwId || gatewayId;
                return (
                    <li key={id} className="sys-connector-item">
                        <div className="sys-connector-item-content">
                            <div className="item-add-view">
                                <div className="sys-connector-item-logo item-logo">
                                    <img fieldid="ublinker-routes-home-components-ConnecterList-index-3684305-img" src={getCompleteImg(path)} alt="" />
                                </div>
                                <div className="item-info">
                                    <Highlight className="item-info-name" content={alias || name} keyword={searchKey} />

                                    <TextOverflow
                                        className="item-info-intro"
                                        lineHeight={18}
                                        lineNum={3}
                                        moreText={window.lang.template(commonText.more)}
                                        moreClick={onAdd.bind(null, item)}
                                    >
                                        {intro}
                                    </TextOverflow>
                                </div>
                            </div>
                            <div className="item-footer add-footer">
                                <a
                                    fieldid="ublinker-routes-home-components-ConnecterList-index-5244420-a"
                                    className="footer-btn"
                                    role="button"
                                    style={{ width: "100%" }}
                                    onClick={onAdd.bind(null, item)}
                                >
                                    {" "}
                                    <i fieldid="ublinker-routes-home-components-ConnecterList-index-8231955-i" className="cl cl-add-l-o " />{" "}
                                    {window.lang.template(commonText.add)}{" "}
                                </a>
                            </div>
                            {/*{cardType === 'added' ? (*/}
                            {/*  <Fragment>*/}
                            {/*    <div className="item-added-head">*/}
                            {/*      <div></div>*/}
                            {/*      <div className="sys-connector-item-logo">*/}
                            {/*        <img fieldid="ublinker-routes-home-components-ConnecterList-index-507047-img" src={getLocalImg(path)} alt=""/>*/}
                            {/*      </div>*/}
                            {/*    </div>*/}

                            {/*    <div className="item-added-info">*/}
                            {/*      <div className="info-header">*/}
                            {/*        <Highlight*/}
                            {/*          className="info-header-name"*/}
                            {/*          text={alias || name}*/}
                            {/*          keyword={searchKey}*/}
                            {/*        />*/}
                            {/*        {state >= 0 ? (*/}
                            {/*          <span*/}
                            {/*            className={classnames('info-header-state', {'online': online})}*/}
                            {/*          >{online ?*/}
                            {/*            window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050314") /* "在线" */}
                            {/*            : window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050294") /* "离线" */}
                            {/*          }</span>*/}
                            {/*        ) : null}*/}
                            {/*        {isdefault ? (<span className="info-header-tag">{isU8 ? window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050338") /* "默认主账号"  : window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050602") /* "默认网关" </span>) : null}*/}
                            {/*      </div>*/}

                            {/*      <TextOverflow*/}
                            {/*        className="info-intro"*/}
                            {/*        lineHeight={20}*/}
                            {/*        lineNum={4}*/}
                            {/*        moreText={window.lang.template(commonText.more)}*/}
                            {/*        moreClick={onEdit.bind(null, item)}*/}
                            {/*      >*/}
                            {/*        {intro}*/}
                            {/*      </TextOverflow>*/}

                            {/*      <p className="info-code">*/}
                            {/*        <span className="info-code-text">*/}
                            {/*          {*/}
                            {/*            isU8 ?*/}
                            {/*              window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050306") /* "组织编码：" */}
                            {/*              :*/}
                            {/*              window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050359") /* "网关实例：" */}
                            {/*          }*/}
                            {/*          {infoCode ? (*/}
                            {/*            <Fragment>*/}
                            {/*              {infoCode}*/}
                            {/*              <span className="info-code-copy">*/}
                            {/*                <Clipboard*/}
                            {/*                  action="copy" text={infoCode}*/}
                            {/*                  locale={{*/}
                            {/*                    // lang: window.lang.lang.replace('_', '-').toLocaleLowerCase()*/}
                            {/*                  }}*/}
                            {/*                />*/}
                            {/*              </span>*/}
                            {/*            </Fragment>*/}
                            {/*          ) : window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050438") /* "无"  }*/}
                            {/*        </span>*/}

                            {/*      </p>*/}
                            {/*    </div>*/}

                            {/*    <div className="item-footer">*/}
                            {/*      <a*/}
                            {/*        className="footer-btn" role="button" style={{width: '33.33%'}}*/}
                            {/*        onClick={onEdit.bind(null, item)}*/}
                            {/*      > <i fieldid="ublinker-routes-home-components-ConnecterList-index-2833034-i" className="cl cl-edit-l" /> {window.lang.template(commonText.edit)} </a>*/}
                            {/*      <a*/}
                            {/*        className="footer-btn " role="button" style={{width: '33.33%'}}*/}
                            {/*        onClick={onLog.bind(null, item)}*/}
                            {/*      > <i fieldid="ublinker-routes-home-components-ConnecterList-index-2743999-i" className="cl cl-log" /> {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050757") /* "日志" *!/</a>*/}
                            {/*      <a*/}
                            {/*        className={classnames('footer-btn', {'disabled': !_gwId})} role="button" style={{width: '33.33%'}}*/}
                            {/*        onClick={_gwId ? onDelete.bind(null, item) : undefined}*/}
                            {/*      > <i fieldid="ublinker-routes-home-components-ConnecterList-index-6112772-i" className="cl cl-delet" /> {window.lang.template(commonText.deletion)} </a>*/}
                            {/*    </div>*/}
                            {/*  </Fragment>*/}
                            {/*) : (*/}
                            {/*  <Fragment>*/}
                            {/*    <div className="item-add-view">*/}
                            {/*      <div className="sys-connector-item-logo item-logo">*/}
                            {/*        <img fieldid="ublinker-routes-home-components-ConnecterList-index-1346502-img" src={getLocalImg(path)} alt=""/>*/}
                            {/*      </div>*/}
                            {/*      <div className="item-info">*/}
                            {/*        <Highlight*/}
                            {/*          className="item-info-name"*/}
                            {/*          text={alias || name}*/}
                            {/*          keyword={searchKey}*/}
                            {/*        />*/}

                            {/*        <TextOverflow*/}
                            {/*          className="item-info-intro"*/}
                            {/*          lineHeight={18}*/}
                            {/*          lineNum={3}*/}
                            {/*          moreText={window.lang.template(commonText.more)}*/}
                            {/*          moreClick={onAdd.bind(null, item)}*/}
                            {/*        >*/}
                            {/*          {intro}*/}
                            {/*        </TextOverflow>*/}

                            {/*      </div>*/}
                            {/*    </div>*/}
                            {/*    <div className="item-footer add-footer">*/}
                            {/*      <a*/}
                            {/*        className="footer-btn" role="button" style={{width :'100%'}}*/}
                            {/*        onClick={onAdd.bind(null, item)}*/}
                            {/*      > <i fieldid="ublinker-routes-home-components-ConnecterList-index-7547181-i" className="cl cl-add-l-o " /> {window.lang.template(commonText.add)} </a>*/}
                            {/*    </div>*/}
                            {/*  </Fragment>*/}
                            {/*)}*/}
                        </div>
                    </li>
                );
            });
        } else {
            return (
                <Empty
                    fieldid="ublinker-routes-home-components-ConnecterList-index-640762-Empty"
                    className="sys-connector-empty"
                    style={emptyStyle}
                    info={
                        <span>
                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050353") /* "暂无连接，" */}
                            <a fieldid="ublinker-routes-home-components-ConnecterList-index-281745-a" onClick={navToAdd.bind(null, "erp")}>
                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050358") /* "添加连接" */}
                            </a>
                        </span>
                    }
                />
            );
        }
    } else {
        return null;
    }
};

const ConnectorList = (props) => {
    let { dataSource, type: cardType, loaded, onDelete, onEdit, navToAdd, onLog } = props;
    const columns = [
        {
            title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050551") /* "连接器名称" */,
            dataIndex: "alias",
            $$type: "title",
            render: (value, record) => {
                const { name, alias } = record;
                return alias || name;
            },
        },
        {
            title: "logo",
            dataIndex: "logopath",
            $$type: "logo",
            render: (value, record) => {
                return getCompleteImg(record.path);
            },
        },
        {
            dataIndex: "state",
            $$type: "tag",
            render: (state) => {
                let online = state == 1;
                return (
                    <>
                        {PRIVATE ? null : (
                            <span className={classnames("sys-connector-state", { online: online })}>
                                {
                                    online
                                        ? window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050314") /* "在线" */
                                        : window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050294") /* "离线" */
                                }
                            </span>
                        )}
                    </>
                );
            },
        },
        {
            dataIndex: "config.isdefault",
            $$type: "tag",
            render: (isdefault, record) => {
                return isdefault ? (
                    <span className="sys-connector-tag">
                        {
                            record.type === "u8"
                                ? window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050338") /* "默认主账号" */
                                : window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050602") /* "默认网关" */
                        }
                    </span>
                ) : null;
            },
        },
        {
            title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050923") /* "简介" */,
            dataIndex: "intro",
        },
        {
            title: (record) => {
                return record.typeof === "u8"
                    ? window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050306") /* "组织编码：" */
                    : window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050359") /* "网关实例：" */;
            },
            dataIndex: "code",
            render: (value, record) => {
                let { type, config } = record;
                let { orgcode, gatewayId, gatewayid } = config || {};
                let infoCode = type === "u8" ? orgcode : gatewayid || gatewayId;
                return infoCode ? (
                    <Fragment>
                        {infoCode}
                        <span className="info-code-copy">
                            <Clipboard
                                fieldid="ublinker-routes-home-components-ConnecterList-index-4395860-Clipboard"
                                action="copy"
                                text={infoCode}
                                locale={
                                    {
                                        // lang: window.lang.lang.replace('_', '-').toLocaleLowerCase()
                                    }
                                }
                            />
                        </span>
                    </Fragment>
                ) : (
                    window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050438")
                ) /* "无" */;
            },
        },
    ];

    const footerOptions = [
        {
            key: "edit",
            icon: "cl cl-edit-l",
            name: window.lang.template(commonText.edit),
            onClick: onEdit,
        },
        {
            key: "log",
            icon: "cl cl-log",
            name: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050757") /* "日志" */,
            onClick: onLog,
        },
        {
            key: "delete",
            icon: "cl cl-delet",
            name: window.lang.template(commonText.deletion),
            onClick: onDelete,
        },
    ];

    if (cardType === "added") {
        return (
            //管理现有连接
            <CardList
                fieldid="UCG-FE-routes-home-components-ConnecterList-index-2702338-CardList"
                labelAlign="right"
                dataSource={dataSource}
                actions={footerOptions}
                columns={columns}
                isEmpty={loaded && dataSource.length <= 0}
                emptyInfo={
                    <span>
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050353") /* "暂无连接，" */}
                        <a fieldid="ublinker-routes-home-components-ConnecterList-index-4233213-a" onClick={navToAdd.bind(null, "erp")}>
                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050358") /* "添加连接" */}
                        </a>
                    </span>
                }
            />
        );
    }

    return (
        //添加erp连接
        <ul className="sys-connector-list  fuck you ">{getCardList(props)}</ul>
    );
};

export default ConnectorList;
