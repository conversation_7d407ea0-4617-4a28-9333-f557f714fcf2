@import "~styles/textOverflow.less";
.sys-connector {
    &-list {
        padding-top: 20px;
    }
    &-empty {
        //padding-top: 28%;
    }
    &-item {
        display: inline-block;
        vertical-align: top;
        .item-added-head {
            height: 140px;
            background-image: url("card-bg.png");
            background-size: 105% 140px;
            background-repeat: no-repeat;
            padding-top: 10px;
            .sys-connector-item-logo {
                margin: 60px 0 0 30px;

            }
        }
        .item-added-info {
            height: 210px;
            padding: 33px 30px 26px;
            .info-header {
                line-height: 32px;
                span {
                    vertical-align: middle;
                }
                &-name {
                    font-size: 20px;
                    line-height: 32px;
                    color: #333;
                    font-weight: 500;
                    span {
                        vertical-align: baseline;
                    }
                }
                &-state {
                    display: inline-block;
                    min-width: 40px;
                    height: 18px;
                    padding: 0 8px;
                    border-radius: 4px;
                    background-color: #708091;
                    font-size: 12px;
                    color: #ffffff;
                    line-height: 18px;
                    text-align: center;
                    margin: 0 8px;
                    &.online {
                        background-color: #4CAF50;
                    }
                }
                &-tag {
                    display: inline-block;
                    padding: 0 7px;
                    border: 1px solid #588CE9;
                    border-radius: 4px;
                    color: #588CE9;
                    font-size: 12px;
                    line-height: 16px;
                }
            }
            .info-intro {
                margin-top: 13px;
                font-size: 14px;
                line-height: 20px;
                color: #999;
                .mix-text-overflow-wrap(20px, 4);
            }
            .info-code {
                font-size: 14px;
                line-height: 20px;
                color: #999;
                padding-top: 14px;
                .info-code-text {
                    display: inline-block;
                    max-width: 100%;
                    padding-right: 26px;
                    overflow: hidden;
                    white-space: nowrap;
                    position: relative;
                    text-overflow: ellipsis;
                }
                .info-code-copy {
                    position: absolute;
                    right: 0;
                }
            }
        }

        .item-add-view {
            height: 138px;
            padding: 20px 25px;
            overflow: hidden;
            .item-logo {
                float: left;
                margin-right: 20px;
            }
            .item-info {
                overflow: hidden;
                &-name {
                    font-size: 20px;
                    line-height: 22px;
                    color: #333333;
                }
                &-intro {
                    font-size: 12px;
                    line-height: 18px;
                    color: #999999;
                    margin-top: 6px;
                    .mix-text-overflow-wrap(18px, 3)
                }
            }
        }

        .item-footer {
            border-top: 1px solid #EDEDED;
            height: 60px;
            line-height: 60px;
            &.add-footer {
                height: 40px;
                line-height: 40px;
                .footer-btn {
                    padding: 10px 0;
                }
            }
            .footer-btn {
                display: inline-block;
                //height: 100%;
                font-size: 14px;
                line-height: 20px;
                color: #666666;
                padding-top: 5px;
                padding-bottom: 5px;
                text-align: center;
                vertical-align: middle;
                i.cl {
                    font-size: 18px;
                    vertical-align: bottom;
                }
                &:after {
                    content: ' ';
                    width: 1px;
                    height: 20px;
                    float: right;
                    background: #EDEDED;
                }
                &:last-of-type {
                    &:after {
                        display: none;
                    }
                }
                &.disabled {
                    cursor: no-drop;
                    color: #999999;
                }

            }
        }

    }
    &-item-content {
        margin: 0 10px 20px;
        //height: 460px;
        background: #fff;
        box-shadow: 0px 2px 4px 0px rgba(233,235,237,1);
    }
    &-item-logo {
        width: 88px;
        height: 88px;
        padding: 0 12px;
        line-height: 88px;
        border: 1px solid #E4E4E4;
        border-radius: 12px;
        background: #ffffff;
        overflow: hidden;
        &>img {
            width: 100%;
            height: auto;
        }
    }
}

@media screen and ( min-width: 720px ){
    .sys-connector-item {
        width: 50%;
    }
}

@media screen and ( min-width: 980px ) {
    .sys-connector-item {
        width: 33.333%;
    }
}

@media screen and ( min-width: 1340px ){
    .sys-connector-item {
        width: 25%;
    }
}

@media screen and ( min-width: 1600px ){
    .sys-connector-item {
        width: 20%;
    }
}
