import { observable, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { autoServiceMessage } from "utils/service";
import * as ownerService from "./service";
import commonText from "constants/commonText";
import core from "core/index";
const initState = {
    tabs: [
        {
            key: "self",
            type: "added",
            name: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050593") /* "管理现有连接" */,
            dataSource: [],
            loaded: false,
            searchKey: "",
        },
        {
            key: "erp",
            type: "add",
            name: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050273") /* "添加ERP连接" */,
            dataSource: [],
            loaded: false,
            searchKey: "",
        },
        {
            key: "ec",
            type: "add",
            name: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050252") /* "添加电商连接" */,
            dataSource: [],
            loaded: false,
            searchKey: "",
            hide: true,
        },
    ],
    activeTabKey: "self",
    searchKey: "",

    restartErpTypes: [],
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    initTab = (tabKey = "self") => {
        let { tabs } = this.state;
        let tab = tabs.find((tab) => tab.key === tabKey);
        tab.dataSource = [];
        tab.loaded = false;
        tab.searchKey = "";
        this.state.tabs = tabs;
    };

    changeActiveTab = (tabKey) => {
        this.state.activeTabKey = tabKey;
        this.getDataSource(true);
    };

    // changeSearchKey = (_searchKey) => {
    //   console.log(_searchKey)
    //   let { activeTabKey, tabs } = this.state;
    //   let tab = tabs.find(tab => tab.key === activeTabKey);
    //   tab.searchKey = _searchKey;
    //   this.state.tabs = tabs;
    // }

    getDataSource = async (isChangeTab = false, searchValue) => {
        let { activeTabKey, tabs } = this.state;
        let tab = tabs.find((tab) => tab.key === activeTabKey);
        if (isChangeTab && tab.loaded) {
            this.state.activeDataSource = tab.dataSource;
            return false;
        }
        let service = null;
        let { searchKey } = tab;
        let _searchKey = typeof searchValue === "undefined" ? searchKey : searchValue;
        if (activeTabKey === "self") {
            service = ownerService.selfConnectorsService({ key: _searchKey });
        } else {
            service = ownerService.getConnectorsService({
                type: activeTabKey,
                key: _searchKey,
            });
        }
        let res = await autoServiceMessage({
            service: service,
        });
        if (res) {
            let resData = res.data || [];
            tab.dataSource = resData;
            tab.loaded = true;
            tab.searchKey = _searchKey;
            this.state.tabs = tabs;
        }
    };

    deleteConnector = async (connector) => {
        let {
            type,
            config: { orgcode, gatewayId, gatewayid },
        } = connector;
        let res = await autoServiceMessage({
            service: ownerService.deleteConnectorService({
                type,
                code: type === "u8" ? orgcode : gatewayId || gatewayid,
            }),
            success: window.lang.template(commonText.deleteSuccess),
        });
        if (res) {
            this.getDataSource(false);
        }
    };

    getRestartEryTypes = async () => {
        let res = await autoServiceMessage({
            service: ownerService.getRestartGwErpTypesService(),
        });
        if (res) {
            let resData = res.data || [];
            this.state.restartErpTypes = resData.map((item) => item.type);
        }
    };

    restartGateway = async (gatewayId) => {
        await autoServiceMessage({
            service: ownerService.restartGatewayIdService(gatewayId),
            success: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050781") /* "网关重启成功！" */,
        });
    };
}
/**
 * 根据具体模块修改 storeKey 为保证唯一性
 * storeKey 由 模块文件夹名称 + 页面名称 + 'Store' 小驼峰
 *
 * storeKey是作为当前 Store 的唯一键， storeKey与Store的实例会在 rootStore或者StoreMap中以 键值对的 形式存在
 * 1. 用于在模块的 storeMap（模块routes/store.js）中注册并初始化当前Store
 * 2. 用于在container的 inject 中关联当前Store的实例时，从模块的storeMap或者rootStore中获取对应store实例
 * */
export const storeKey = "systemConnectorHomeStore";
export const addStore = () => {
    core.addStore({
        storeKey: storeKey,
        store: new Store(),
    });
};
export default Store;
