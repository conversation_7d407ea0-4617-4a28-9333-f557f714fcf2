import { getInvokeService, getServicePath } from "utils/service";

/**
 * 或去我的连接器（已添加连接器）
 * @param {Object} data
 * @param {String} data.key -搜索条件
 * @return {Promise<unknown>}
 */
export const selfConnectorsService = function (data) {
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/ncconnect/getMyConnects",
        },
        data
    );
};

/**
 * 删除我的连接器
 * @param {Object} data
 * @param {Object} data.type -连接器类型 nc u8 等
 * @param {Object} data.code -删除所用连接器主键标识
    当type=u8时 code=config.orgcode
    当type=其他类型时候 code=config.gatewayId
 * @return {Promise<unknown>}
 */
export const deleteConnectorService = function (data) {
    let { type, code } = data;
    let path = "";
    if (type === "u8") {
        path = "/diwork/ncconnect/deleteU8Connect/";
    } else {
        path = "/diwork/ncconnect/deleteMyConnect/";
    }
    return getInvokeService(
        {
            method: "DELETE",
            path: path + code,
        },
        null
    );
};

/**
 * 获取连接器
 * @param {Object} data
 * @param {String} data.key -搜索条件
 * @param {String} data.type=[erp|ec] -erp erp拦截器 ec电商连接器
 * @return {Promise<unknown>}
 */
export const getConnectorsService = function (data) {
    let { key, type } = data;
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/ncconnect/connector/" + type,
        },
        { key }
    );
};

/**
 * 获取可重启网关erp类型
 * @returns {Promise | Promise<unknown>}
 */
export const getRestartGwErpTypesService = function () {
    return getInvokeService({
        method: "GET",
        path: "/mygwapp/ncconnect/erp/type/allowRestart",
        showLoading: false,
    });
};

/**
 * 重启网关
 * @param {String} gatewayId
 * @returns {Promise | Promise<unknown>}
 */
export const restartGatewayIdService = function (gatewayId) {
    return getInvokeService({
        method: "GET",
        path: `/mygwapp/gateway/${gatewayId}/restart`,
    });
};
