import React, { Component } from "react";
import IndexView from "./components/IndexView";
import { inject, observer } from "mobx-react";
import { storeKey as ownerStoreKey, addStore } from "./store";

addStore();

@inject((rootStore) => {
    let ownerStore = rootStore[ownerStoreKey];
    return {
        ownerStore: ownerStore,
        ownerState: ownerStore.toJS(),
    };
})
@observer
class Container extends Component {
    constructor(props) {
        super(props);
    }

    render() {
        return <IndexView {...this.props} />;
    }
}
export default Container;
