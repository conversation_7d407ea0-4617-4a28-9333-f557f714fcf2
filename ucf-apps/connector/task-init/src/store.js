import { observable, action, toJS, reaction, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import core from "core";
import { autoServiceMessage } from "utils/service";
import * as ownerService from "./service";
import { defaultPagination, selfPageChange } from "utils/pageListUtils";

const initState = {
    // 全部的列表数据
    allListData: [],
    // 显示的列表数据
    listData: [],
    // 页签列表
    tabList: [],
    // 当前选中的页签
    selectTabKey: null,
    // 是否只显示未生成任务的集成单元
    isOnlyShowNotGenerate: false,
    // 搜索框中的值
    searchValue: "",
    // 选中的列表
    selectedTaskList: [],
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }

    @observable state = initState;

    // 设置页签列表
    setTabList = (tabList) => {
        this.state.tabList = tabList;
        this.reflashTaskList();
    };

    // 切换选中页签
    changeSelectTab = (tabKey) => {
        this.state.selectTabKey = this.state.tabList.find((item) => {
            return item.pk_id === tabKey;
        });
        this.getDataSource();
    };

    setListData = (listData) => {
        this.state.listData = [...listData];
    };

    // 设置搜索框中的值
    setSearchValue = (value) => {
        this.state.searchValue = value;
        this.reflashTaskList();
    };

    // 设置是否只显示未生成任务的集成单元
    setIsOnlyShowNotGenerate = (value) => {
        this.state.isOnlyShowNotGenerate = value;
    };

    // 请求获取所有页签
    getTabList = async () => {
        let result = await ownerService.getTabsData();
        this.setTabList(result.data);
        this.state.selectTabKey = this.state.tabList[0];
    };

    // 设置选中的任务列表
    setSelectedTaskList = (list) => {
        this.state.selectedTaskList = list;
    };

    // 请求获取数据
    getDataSource = async () => {
        // 请求之前把选中的列表置空
        this.state.selectedTaskList = [];
        let result = await ownerService.getTaskListData(this.state.selectTabKey.code ? this.state.selectTabKey.code : "");

        // 重新请求后,要处理,如果已经初始化过的,就不可以再勾选
        this.state.allListData = result.data.map((item) => {
            return { ...item, _disabled: item.init, _checked: false };
        });

        this.reflashTaskList();
    };

    // 搜索或者勾选是否只显示未生成任务的集成单元后,刷新列表
    reflashTaskList = () => {
        let list = this.state.allListData;

        // 根据是否未生成任务集成单元过滤
        if (this.state.isOnlyShowNotGenerate) {
            // 移除所有init状态是true的
            list = list.filter((item) => {
                return !item.init;
            });
        }

        // 根据搜索条件过滤
        if (this.state.searchValue !== "") {
            list = list.filter((item) => {
                return item.tableview.indexOf(this.state.searchValue) !== -1;
            });
        }

        this.state.listData = list;
    };

    // 生成任务
    createTask = async () => {
        let postData = this.state.selectedTaskList.map((item) => {
            if (!item.selectConnect) {
                item.selectConnect = item.tenantConnects[0];
            }
            return {
                tableview: item.tableview,
                connectId: item.selectConnect.id,
            };
        });

        let result = await ownerService.creasteTask(postData);
        if (result.status === 1) {
            // 生成任务后，重新请求数据
            this.getDataSource();
        }
    };
}
/**
 * 根据具体模块修改 storeKey 为保证唯一性
 * storeKey 由 模块文件夹名称 + 页面名称 + 'Store' 小驼峰
 *
 * storeKey是作为当前 Store 的唯一键， storeKey与Store的实例会在 rootStore或者StoreMap中以 键值对的 形式存在
 * 1. 用于在模块的 storeMap（模块routes/store.js）中注册并初始化当前Store
 * 2. 用于在container的 inject 中关联当前Store的实例时，从模块的storeMap或者rootStore中获取对应store实例
 * */
export const storeKey = "systemConnectorHomeStore";
export const addStore = () => {
    core.addStore({
        storeKey: storeKey,
        store: new Store(),
    });
};
export default Store;
