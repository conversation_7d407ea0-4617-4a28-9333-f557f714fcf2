import { getInvokeService } from "utils/service";

/**
 * 任务分类下视图列表
 * @return {Promise<unknown>}
 */
export const getTaskListData = function (classCode) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwmanage/gwportal/diwork/erpdata/toBeInit",
        },
        { classCode: classCode }
    );
};

/**
 * 非预置任务分类
 * @return {Promise<unknown>}
 */
export const getTabsData = function () {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwmanage/gwportal/diwork/erpdata/nonPresetClasses",
        },
        null
    );
};

/**
 * 点击生成任务
 * @return {Promise<unknown>}
 */
export const creasteTask = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/gwmanage/gwportal/diwork/erpdata/task/initUnit",
        },
        data
    );
};
