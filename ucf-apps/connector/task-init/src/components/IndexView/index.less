.connector-task-init-header {
    width: 100%;
    height: 50px;
    padding-left: 40px;
    display: flex;
    align-items: center;

    // 头部样式
    .connector-task-init-tip {
        margin-left: 12px;
        font-size: 12px;
        font-weight: 400;
        color: #666666;
    }
}

// 底部样式
.connector-task-init-bottom-area {
    height: 50px;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    color: #666666;
    position: absolute;
    bottom: 0px;
    right: 0px;

    .connector-task-init-bottom-content {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-right: 18px;

        .connector-task-init-bottom-area-tip {
            margin-right: 50px;

            span{
                font-weight: bold; 
            }
        } 
    }
}

.task-init-tabs {
    .u-tabs-ink-bar {
        display: none !important;
    }

    .u-tabs .u-tabs-nav .u-tabs-tab-active:after {
        content: "";
        width: 0;
        height: 0;
        border-right: 4px solid transparent;
        border-left: 4px solid transparent;
        border-bottom: 3px solid #505766;
        display: block;
        margin: auto;
        transform: translate(0px, 8px);
    }
}