import React, { Fragment, useState, useEffect } from "react";
import { Radio, Button, Label, Icon, Switch, Tabs, Select, Message, Table } from "components/TinperBee";
import SearchInput from "components/TinperBee/SearchInput";
// import bigData from "tinper-bee/lib/bigData";
// 大数据处理
const { bigData } = Table;
const BigDataTable = bigData(Table);
import Highlight from "components/Highlight";
import "./index.less";
import { getScreenInnerHeight } from "utils/index";
// import * as ownerService from '../../service'

const ScreenInnerHeight = getScreenInnerHeight();

const TabPane = Tabs.TabPane;

const Option = Select.Option;

function IndexView(props) {
    const { ownerState, ownerStore } = props;

    const { tabList, listData, selectTabKey, isOnlyShowNotGenerate, selectedTaskList } = ownerState;

    // 打开/关闭只显示生成任务的集成单元
    const handleChangeIsOnlyShowNotGenerate = () => {
        ownerStore.setIsOnlyShowNotGenerate(!isOnlyShowNotGenerate);
    };

    // 切换header的页签
    const handleTabChange = (key) => {
        ownerStore.changeSelectTab(key);
    };

    // 普通文本列render
    const renderHighlight = (content) => {
        return <Highlight content={content} />;
    };

    // 任务生成状态
    const renderStatus = (taskstatus) => {
        let text = "",
            cls = "";
        if (!taskstatus) {
            text = lang.templateByUuid("UID:P_UBL-FE_20096040042803C8", "未生成") /* "未生成" */;
            cls = "task-status-doing";
        } else {
            text = lang.templateByUuid("UID:P_UBL-FE_20096040042803C9", "已生成") /* "已生成" */;
            cls = "task-status-success";
        }
        return <span className={`data-task-run-status ${cls}`}>{text}</span>;
    };

    // 选择连接
    const renderConnects = (connectList, listItem, index) => {
        if (!connectList || connectList.length == 0) {
            return null;
        }
        return (
            <Select
                fieldid="ublinker-task-init-src-components-IndexView-index-2567970-Select"
                onChange={(value) => handleConnectChange(value, listItem, index)}
                value={listItem.selectConnect ? listItem.selectConnect.alias : connectList[0].alias}
                disabled={listItem.init}
            >
                {connectList.map((item) => {
                    return (
                        <Option fieldid="UCG-FE-task-init-src-components-IndexView-index-6308396-Option" value={item.id}>
                            {item.alias}
                        </Option>
                    );
                })}
            </Select>
        );
    };

    // 更改连接
    const handleConnectChange = (selectConnectId, listItem, index) => {
        let selectConnect = listItem.tenantConnects.find((item) => item.id === selectConnectId);
        listItem.selectConnect = selectConnect;
        listData[index] = listItem;
        ownerStore.setListData(listData);
    };

    const columns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042803C1", "集成单元名称") /* "集成单元名称" */,
            dataIndex: "tableview",
            render: renderHighlight,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042803C3", "视图版本") /* "视图版本" */,
            dataIndex: "viewversion",
            render: renderHighlight,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042803C6", "任务生成状态") /* "任务生成状态" */,
            dataIndex: "init",
            width: 110,
            render: renderStatus,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042803C2", "选择连接") /* "选择连接" */,
            dataIndex: "tenantConnects",
            render: renderConnects,
        },
        {
            title: "",
            dataIndex: "",
            render: null,
        },
    ];

    // 搜索
    const handleSearch = (value) => {
        ownerStore.setSearchValue(value);
    };

    // 生成任务
    const handleCreateTask = async () => {
        // 判断是否选择了任务
        if (selectedTaskList.length > 0) {
            ownerStore.createTask();
        } else {
            // 提示
            Message.destroy();
            Message.create({
                content: lang.templateByUuid("UID:P_UBL-FE_20096040042803C5", "请至少选择一个集成单元。") /* "请至少选择一个集成单元。" */,
                color: "warning",
            });
        }
    };

    // 初始化请求tabs页签,仅初始化时请求一次
    useEffect(() => {
        const loadData = async () => {
            await ownerStore.getTabList();
            await ownerStore.getDataSource();
        };

        loadData();
    }, []);

    return (
        <Fragment>
            <div className="connector-task-init-header">
                <Switch
                    fieldid="ublinker-task-init-src-components-IndexView-index-5195059-Switch"
                    colors="blue"
                    checked={isOnlyShowNotGenerate}
                    onChange={handleChangeIsOnlyShowNotGenerate}
                />
                <span className="connector-task-init-tip">
                    {lang.templateByUuid("UID:P_UBL-FE_20096040042803C7", "仅显示未生成任务的集成单元") /* "仅显示未生成任务的集成单元" */}
                </span>
            </div>

            {selectTabKey ? (
                <div className="task-init-tabs">
                    <Tabs
                        fieldid="ublinker-task-init-src-components-IndexView-index-3882540-Tabs"
                        activeKey={selectTabKey.pk_id}
                        onChange={handleTabChange}
                        extraContent={
                            <div>
                                <SearchInput
                                    className="ucg-mar-r-20"
                                    size="lg"
                                    placeholder={lang.templateByUuid("UID:P_UBL-FE_20096040042803C4", "请输入集成单元名称") /* "请输入集成单元名称" */}
                                    showClear
                                    onSearch={handleSearch}
                                />
                            </div>
                        }
                    >
                        {tabList.map((item) => {
                            return <TabPane fieldid="UCG-FE-task-init-src-components-IndexView-index-6652037-TabPane" key={item.pk_id} tab={item.name} />;
                        })}
                    </Tabs>
                </div>
            ) : null}

            <BigDataTable
                rowKey={"id"}
                // tableHoc={BigDataTable}
                columns={columns}
                data={listData}
                multiSelect
                autoCheckedByClickRows={false}
                selectedList={selectedTaskList}
                getSelectedDataFunc={ownerStore.setSelectedTaskList}
                scroll={{ y: ScreenInnerHeight - 190 }}
            />
            <div className="connector-task-init-bottom-area">
                <div className="connector-task-init-bottom-content">
                    <div className="connector-task-init-bottom-area-tip"></div>
                    <Button
                        fieldid="ublinker-task-init-src-components-IndexView-index-4987787-Button"
                        colors="primary"
                        className="login"
                        onClick={handleCreateTask}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_20096040042803C0", "生成任务") /* "生成任务" */}
                    </Button>
                </div>
            </div>
        </Fragment>
    );
}

export default IndexView;
