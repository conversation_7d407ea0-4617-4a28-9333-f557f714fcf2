import React from "react";
import { RoutesRender } from "core";

import getConfigProvider from "components/ConfigProvider";
import config from "../config";
const ConfigProvider = getConfigProvider(config);

import AsyncComponent from "components/AsyncComponent";

const HomeComponent = AsyncComponent((cb) => {
    require.ensure(
        [],
        (require) => {
            let com = require("./home/<USER>");
            cb && cb(com.default);
        },
        "connector/common2/home/<USER>"
    );
});

const routes = [
    {
        path: "/",
        exact: true,
        component: HomeComponent,
    },
    {
        path: "/home",
        component: HomeComponent,
    },
];

const Routes = () => {
    return (
        <ConfigProvider fieldid="UCG-FE-connector-common-src-routes-index-4064598-ConfigProvider">
            <RoutesRender routes={routes} isRoot />
        </ConfigProvider>
    );
};
export default Routes;
