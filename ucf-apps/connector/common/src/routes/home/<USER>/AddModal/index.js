import React, { useState, useEffect } from "react";
import Modal from "components/TinperBee/Modal";
import { Button, FormList } from "components/TinperBee";
import { autoServiceMessage } from "utils/service";
import ConfigureConnectMessage from "../ConfigureConnectMessage";
import { getLinkerSetParam } from "../../service";
import { getAllVersionService } from "services/common";
import { createCommonLinkConfig, editCommonLinkConfig, commonLinkTest } from "../../service";
import { Success, Error } from "utils/feedback";

// 添加连接弹窗
const AddLinkerModal = (props) => {
    const [form] = FormList.useForm();
    const {
        show,
        modalCancel,
        type,
        addModalData,
        addModalData: { testable },
        editModalData,
        editModalData: { linker },
        gatewayList,
        systemList,
    } = props;

    const [isSureDisable, setIsSureDisable] = useState(testable || (linker && linker.testable)); //新增或编辑时此字段为true,需先测试再点确定，为false不判断

    const isAdd = type === "add";

    const [isLoad, setIsLoad] = useState(false);

    // 连接名称
    const [alias, setAlias] = useState("");
    // 连接编码
    const [connectCode, setConnectCode] = useState("");

    // 表单字段验证方法
    const { validateFields, getFieldProps, getFieldError } = form;

    // 弹窗标题
    const modalTitle = isAdd
        ? window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_2021891001") + addModalData.name
        : window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_2021891002");
    //"添加"   "编辑"

    // 当前选择版本
    const [selectVersion, setSelectVersion] = useState("");

    // 版本列表
    const [versionList, setVersionList] = useState([]);

    // 连接模版参数
    const [linkconfig, setLinkconfig] = useState([]);

    // 适配器模版参数
    const [linkerAdapterConfig, setLinkerAdapterConfig] = useState([]);

    // 当前选中网关ID
    const [selectGatewayId, setSelectGatewayId] = useState("");

    // 当前选中集成系统ID
    const [selectSystemId, setSelectSystemId] = useState("");
    // 当前选中集成系统name
    const [selectSystemName, setSelectSystemName] = useState("");

    // 取消
    const handleCancel = () => {
        modalCancel("cancel");
    };
    useEffect(() => {
        // props.ownerStore.getGatewayList();
    }, []);
    // 确认
    const handleCommit = () => {
        validateFields()
            .then(async (values) => {
                let res = null;
                if (isAdd) {
                    // 说明还未创建连接器,此时走新增接口
                    let postData = {
                        fromType: 1,
                        alias: alias,
                        connectCode: connectCode,
                        type: addModalData.code,
                        linkconfig: linkconfig,
                        linkerAdapterConfig: linkerAdapterConfig,
                        integratedSystemId: selectSystemId,
                        gatewayId: selectGatewayId,
                        erpVersion: selectVersion,
                    };
                    // res = await createCommonLinkConfig(postData);
                    res = await autoServiceMessage({
                        service: createCommonLinkConfig(postData),
                    });
                } else {
                    // 说明已经创建了连接器,此时走编辑修改接口
                    let postData = {
                        _id: editModalData.id,
                        alias: alias,
                        linkconfig: linkconfig,
                        linkerAdapterConfig: linkerAdapterConfig,
                        gatewayId: selectGatewayId,
                        integratedSystemId: selectSystemId,
                        connectCode: connectCode,
                    };
                    // res = await editCommonLinkConfig(postData);
                    res = await autoServiceMessage({
                        service: editCommonLinkConfig(postData),
                    });
                }
                modalCancel("commit");
                if (res !== null && res.data !== null) {
                    // 提示保存成功
                    Success(window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_2021891003") /* 保存成功 */);
                    setTestButtonDisable(false);
                } else {
                    // 提示保存失败
                    Error(window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_2021891004") /* 保存失败 */);
                }
            })
            .catch((errorInfo) => {
                console.log(errorInfo);
            });
    };

    // 点击测试按钮
    const handleTest = () => {
        validateFields()
            .then(async (values) => {
                let postData = {
                    fromType: 1,
                    alias: alias,
                    connectCode: connectCode,
                    type: addModalData.code || editModalData.type,
                    linkconfig: linkconfig,
                    linkerAdapterConfig: linkerAdapterConfig,
                    integratedSystemId: selectSystemId,
                    gatewayId: selectGatewayId,
                    erpVersion: selectVersion,
                };
                let res = await autoServiceMessage({
                    service: commonLinkTest(postData),
                    success: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_2021891005") /* 连接成功 */,
                });
                // if(res){
                //     setIsSureDisable(false)
                // }
            })
            .catch((errorInfo) => {
                console.log(errorInfo);
            });
    };

    // 定义测试按钮
    const getExtendBtn = () => {
        // if(isAdd) {
        //     return null;
        // }
        return (
            <Button fieldid="ublinker-routes-home-components-AddModal-index-497071-Button" colors="dark" className="ucg-mr-10" onClick={handleTest}>
                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_2021891006") /* 测试 */}
            </Button>
        );
    };

    // 新增初始化时获取连接模版参数（编辑时已经获取到参数类型和对应值，不需要再重复获取）
    useEffect(() => {
        let init = async () => {
            if (isAdd) {
                let resVersion = await getAllVersionService(addModalData.linkerId);
                setVersionList(resVersion.data);
                setSelectVersion(resVersion.data[0].code);
                let res = await getLinkerSetParam({
                    linkerId: addModalData.linkerId,
                    linkerVersionCode: resVersion.data[0].code,
                    adapterCode: addModalData.adapterCode,
                });
                setLinkconfig(res.data.linkerConfig);
                setLinkerAdapterConfig(res.data.linkerAdapterConfig);
                // 网关和集成系统默认选中第一个
                // if (gatewayList[0]) {
                //     setSelectGatewayId(gatewayList[0].gatewayID);
                // }
                // if (systemList[0]) {
                //     setSelectSystemId(systemList[0].id);
                // }
            } else {
                setAlias(editModalData.alias);
                setConnectCode(editModalData.connectCode);
                setSelectVersion(editModalData.selectVersion);
                setLinkconfig(editModalData.linkconfig);
                setLinkerAdapterConfig(editModalData.linkerAdapterConfig);
                // 网关和集成系统默认选中第一个
                setSelectGatewayId(editModalData.gatewayId);
                setSelectSystemId(editModalData.systemId);
                setSelectSystemName(editModalData.systemName);
            }
            setIsLoad(true);
        };

        init();
    }, []);

    return (
        <>
            <Modal
                fieldid="ublinker-routes-home-components-AddModal-index-2132256-Modal"
                title={modalTitle}
                show={isLoad}
                show={show}
                width="600px"
                onCancel={handleCancel}
                onOk={handleCommit}
                // okDisabled ={isSureDisable}
                extendBtn={getExtendBtn}
                extendBtnLocation="pre"
            >
                {/* 配置连接信息 */}
                <ConfigureConnectMessage
                    isAdd={isAdd}
                    form={form}
                    alias={alias}
                    setAlias={setAlias}
                    connectCode={connectCode}
                    setConnectCode={setConnectCode}
                    gatewayList={gatewayList}
                    systemList={systemList}
                    linkerName={isAdd ? addModalData.name : editModalData.type}
                    selectVersion={selectVersion}
                    setSelectVersion={setSelectVersion}
                    selectSystemId={selectSystemId}
                    setSelectSystemId={setSelectSystemId}
                    selectSystemName={selectSystemName}
                    setSelectSystemName={setSelectSystemName}
                    selectGatewayId={selectGatewayId}
                    setSelectGatewayId={setSelectGatewayId}
                    versionList={versionList}
                    linkconfig={linkconfig}
                    linkerAdapterConfig={linkerAdapterConfig}
                    changeLinkconfig={() => {
                        setLinkconfig([...linkconfig]);
                    }}
                    changeLinkerAdapterconfig={() => {
                        setLinkerAdapterConfig([...linkerAdapterConfig]);
                    }}
                    linkerId={addModalData.linkerId}
                    adapterCode={addModalData.adapterCode}
                    setLinkconfig={setLinkconfig}
                    setLinkerAdapterConfig={setLinkerAdapterConfig}
                    // alias={connectorName}
                    // connectId={linkerId}
                    // setLinkerId={setLinkerId}
                    // connectorType={connectorType}
                    // hasCreate={hasCreate}
                    // setHasCreate={setHasCreate}
                    // linkerParamInfo={_useLinkerParamInfo.linkerParamInfo}
                    // changeLinkerParamInfo={changeLinkerParamInfo}
                    // testable={queryParams.testable}
                    // erpVersion={erpVersion}
                />
            </Modal>
        </>
    );
};

export default AddLinkerModal;
