import React, { useState } from "react";
import "./index.less";
import commonText from "constants/commonText";
import { getLinkerSetParam } from "../../service";
import { FormControl, Select, Radio, Checkbox, Icon, FormList } from "components/TinperBee";
// import FormList from "components/TinperBee/Form";
import { ipReg, portReg, codeReg } from "utils/regExp";

const FormItem = FormList.Item;
// const labelCol = 150;
const Option = Select.Option;
const RadioGroup = Radio.Group;

// 动态类型组件
const DynamicDomByType = (props) => {
    const { type, data, changeInfo, setTestButtonDisable } = props;

    // 文本、整数、多行文本框、单选、下拉框变化
    const handleChange = (data, value) => {
        data.value = value;
        changeInfo();
        setTestButtonDisable(true);
    };

    // 下拉框变化
    const handleCheckChange = (item, value) => {
        item.defaultItem = value;
        changeInfo();
        setTestButtonDisable(true);
    };

    let dom = null;
    switch (type) {
        case "Text":
            // 文本输入框
            dom = (
                <FormItem
                    fieldid="ublinker-routes-home-components-ConfigureConnectMessage-index-7694937-FormItem"
                    label={data.name}
                    name={data.code}
                    initialValue={data.value}
                    validateTrigger="onChange"
                    rules={[
                        {
                            required: data.required,
                            message: `${window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050914") /* "请输入" */}${data.name}`,
                        },
                    ]}
                >
                    <FormControl
                        fieldid="ublinker-routes-home-components-ConfigureConnectMessage-index-1883188-FormControl"
                        className="ucg-mar-r-5"
                        style={{ width: 320 }}
                        type={data.code == "password" ? "password" : "text"}
                        onChange={(value) => {
                            handleChange(data, value);
                        }}
                    />
                </FormItem>
            );
            break;
        case "Integer":
            // 整数输入框
            dom = (
                <FormItem
                    fieldid="ublinker-routes-home-components-ConfigureConnectMessage-index-4182371-FormItem"
                    label={data.name}
                    initialValue={data.value}
                    name={data.code}
                    validateTrigger="onChange"
                    rules={[
                        {
                            required: data.required,
                            message: `${window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050914") /* "请输入" */}${data.name}`,
                        },
                    ]}
                >
                    <FormControl
                        fieldid="ublinker-routes-home-components-ConfigureConnectMessage-index-5332465-FormControl"
                        className="ucg-mar-r-5"
                        style={{ width: 320 }}
                        type="number"
                        onChange={(value) => {
                            handleChange(data, value);
                        }}
                    />
                </FormItem>
            );
            break;
        case "Textarea":
            // 多行文本输入框
            dom = (
                <FormItem
                    fieldid="ublinker-routes-home-components-ConfigureConnectMessage-index-251650-FormItem"
                    label={data.name}
                    initialValue={data.value}
                    name={data.code}
                    validateTrigger="onChange"
                    rules={[
                        {
                            required: data.required,
                            message: `${window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050914") /* "请输入" */}${data.name}`,
                        },
                    ]}
                >
                    <div className="muti-row-textarea">
                        <FormControl.TextArea
                            rows={4}
                            className="ucg-mar-r-5"
                            style={{ width: 320 }}
                            componentClass="textarea"
                            onChange={(value) => {
                                handleChange(data, value);
                            }}
                        />
                    </div>
                </FormItem>
            );
            break;
        case "Checkbox":
            // 复选框
            dom = (
                <FormItem
                    fieldid="ublinker-routes-home-components-ConfigureConnectMessage-index-6342177-FormItem"
                    label={data.name}
                    required={data.required}
                    name={data.code}
                    initialValue={data.value === null ? (data.itemList.find((item) => item.defaultItem) || "").realValue || "" : data.value}
                >
                    <div>
                        {data.itemList.map((item) => {
                            return (
                                <Checkbox
                                    fieldid="ublinker-routes-home-components-ConfigureConnectMessage-index-5982977-Checkbox"
                                    checked={item.defaultItem}
                                    // value={item.realValue}
                                    colors="dark"
                                    onChange={(value) => {
                                        handleCheckChange(item, value);
                                    }}
                                >
                                    {item.displayValue}
                                </Checkbox>
                            );
                        })}
                    </div>
                </FormItem>
            );
            break;
        case "Select":
            // 下拉列表
            dom = (
                <FormItem
                    fieldid="ublinker-routes-home-components-ConfigureConnectMessage-index-7623472-FormItem"
                    label={data.name}
                    required={data.required}
                    name={data.code}
                    initialValue={data.value === null ? (data.itemList.find((item) => item.defaultItem) || "").realValue || "" : data.value}
                >
                    <Select
                        fieldid="ublinker-routes-home-components-ConfigureConnectMessage-index-1134346-Select"
                        style={{ width: 320 }}
                        onChange={(value, e) => {
                            handleChange(data, value);
                        }}
                    >
                        {data.itemList.map((item) => {
                            return (
                                <Option fieldid="UCG-FE-routes-home-components-ConfigureConnectMessage-index-644352-Option" value={item.realValue}>
                                    {item.displayValue}
                                </Option>
                            );
                        })}
                    </Select>
                </FormItem>
            );
            break;
        case "Radio":
            // 单选
            dom = (
                <FormItem
                    fieldid="ublinker-routes-home-components-ConfigureConnectMessage-index-8153003-FormItem"
                    label={data.name}
                    required={data.required}
                    name={data.code}
                    initialValue={data.value === null ? (data.itemList.find((item) => item.defaultItem) || "").realValue || "" : data.value}
                >
                    <RadioGroup
                        // value={data.value === null ? data.itemList.find(item => item.defaultItem).realValue : data.value}
                        onChange={(value) => {
                            handleChange(data, value);
                        }}
                    >
                        {data.itemList.map((item) => {
                            return (
                                <Radio fieldid="ublinker-routes-home-components-ConfigureConnectMessage-index-429496-Radio" value={item.realValue}>
                                    {item.displayValue}
                                </Radio>
                            );
                        })}
                    </RadioGroup>
                </FormItem>
            );
            break;
        default:
            break;
    }
    return dom;
};

const ConfigureConnectMessage = (props) => {
    // hasCreate表明是否创建网关
    // const { linkerName, versionList, changeLinkerParamInfo, hasCreate, setHasCreate, alias, connectorType, form, testable, erpVersion, connectId, setLinkerId } = props;

    const {
        isAdd,
        alias,
        setAlias,
        connectCode,
        setConnectCode,
        gatewayList,
        systemList,
        linkerName,
        versionList,
        selectVersion,
        setSelectVersion,
        selectSystemId,
        setSelectSystemId,
        selectSystemName,
        setSelectSystemName,
        selectGatewayId,
        setSelectGatewayId,
        linkconfig,
        linkerAdapterConfig = [],
        changeLinkconfig,
        changeLinkerAdapterconfig,
        form,
        adapterCode,
        linkerId,
        setLinkconfig,
        setLinkerAdapterConfig,
    } = props;
    // 表单字段验证方法
    const { validateFields, getFieldProps, getFieldError } = form;

    // 测试连接按钮是否可点击状态
    const [testButtonDisable, setTestButtonDisable] = useState(true);

    // 连接名称变化
    const handleAliasChange = (value) => {
        console.log(value);
        setAlias(value);
    };

    // 连接编码变化
    const handleConnectCodeChange = (value) => {
        setConnectCode(value);
    };

    // 版本发生变化
    const handleVersionChange = async (value) => {
        setSelectVersion(value);
        let res = await getLinkerSetParam({
            linkerId: linkerId,
            linkerVersionCode: value,
            adapterCode: adapterCode,
        });
        setLinkconfig(res.data.linkerConfig);
        setLinkerAdapterConfig(res.data.linkerAdapterConfig);
    };

    // 集成系统选择
    const handleSystemChange = (value) => {
        setSelectSystemId(value);
    };

    // 网关选择
    const handleGateWayChange = (value) => {
        setSelectGatewayId(value);
    };
    const findSelectSystemName = (sysId) => {
        let item = systemList.find((item) => {
            return item.id === sysId;
        });
        console.log(item);
        return item && item.id ? item.systemName : "";
    };
    const formItemLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 12 },
    };
    return (
        <div className="connect-configure-content">
            <FormList
                fieldid="ublinker-routes-home-components-ConfigureConnectMessage-index-2596836-FormList"
                form={form}
                {...formItemLayout}
                className="config-action-form config-action-form-connect-message"
            >
                <FormItem
                    fieldid="ublinker-routes-home-components-ConfigureConnectMessage-index-9511190-FormItem"
                    label={lang.templateByUuid("UID:P_UBL-FE_2009604004280405", "安装补丁") /* "安装补丁" */}
                    className="formItem-title"
                ></FormItem>
                <FormItem
                    fieldid="ublinker-routes-home-components-ConfigureConnectMessage-index-9648850-FormItem"
                    label={lang.templateByUuid("UID:P_UBL-FE_2009604004280406", "基本信息") /* "基本信息" */}
                    className="formItem-title"
                ></FormItem>
                <FormItem
                    fieldid="ublinker-routes-home-components-ConfigureConnectMessage-index-5953969-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050443") /* "版本" */}
                    name="selectVersion"
                    validateTrigger="onChange"
                    rules={[{ required: true }]}
                    initialValue={selectVersion}
                >
                    <Select
                        fieldid="ublinker-routes-home-components-ConfigureConnectMessage-index-8241243-Select"
                        disabled={!isAdd}
                        onChange={(value, e) => {
                            handleVersionChange(value);
                        }}
                        style={{ width: 320 }}
                        value={selectVersion}
                    >
                        {versionList.map((item) => {
                            return (
                                <Option fieldid="UCG-FE-routes-home-components-ConfigureConnectMessage-index-6646394-Option" value={item.code}>
                                    {item.description}
                                </Option>
                            );
                        })}
                    </Select>
                </FormItem>
                <FormItem
                    fieldid="ublinker-routes-home-components-ConfigureConnectMessage-index-7495256-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050928") /* "连接名称" */}
                    required={true}
                    name="alias"
                    validateTrigger="onChange"
                    rules={[
                        {
                            required: true,
                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050933") /* "请输入连接名称" */,
                        },
                    ]}
                    initialValue={alias}
                >
                    <FormControl
                        fieldid="ublinker-routes-home-components-ConfigureConnectMessage-index-911275-FormControl"
                        className="ucg-mar-r-5"
                        style={{ width: 320 }}
                        onChange={(value) => {
                            handleAliasChange(value);
                        }}
                    />
                </FormItem>
                <FormItem
                    fieldid="ublinker-routes-home-components-ConfigureConnectMessage-index-5826454-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050932") /* "连接编码" */}
                    name="connectCode"
                    validateTrigger="onChange"
                    rules={[
                        {
                            required: true,
                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050925") /* "请输入连接编码" */,
                        },
                        {
                            pattern: codeReg,
                            message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050929") /* "连接编码由英文、数字、下划线组成" */,
                        },
                    ]}
                    initialValue={connectCode}
                >
                    <FormControl
                        fieldid="ublinker-routes-home-components-ConfigureConnectMessage-index-995387-FormControl"
                        disabled={!isAdd}
                        className="ucg-mar-r-5"
                        style={{ width: 320 }}
                        onChange={(value) => {
                            handleConnectCodeChange(value);
                        }}
                    />
                </FormItem>
                <FormItem
                    fieldid="ublinker-routes-home-components-ConfigureConnectMessage-index-472338-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050144") /* "网关" */}
                    name="selectGatewayId"
                    initialValue={selectGatewayId}
                >
                    <Select
                        fieldid="ublinker-routes-home-components-ConfigureConnectMessage-index-2458378-Select"
                        showSearch
                        optionFilterProp="children"
                        style={{ width: 320 }}
                        // value={selectGatewayId}
                        onChange={(value, e) => {
                            handleGateWayChange(value);
                        }}
                    >
                        {gatewayList.map((item) => {
                            return (
                                <Option fieldid="UCG-FE-routes-home-components-ConfigureConnectMessage-index-3460097-Option" value={item.gatewayID}>
                                    {item.name}
                                </Option>
                            );
                        })}
                    </Select>
                </FormItem>
                <FormItem
                    fieldid="ublinker-routes-home-components-ConfigureConnectMessage-index-4364849-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20221211627") /* "连接器" */}
                    name="linkerName"
                    validateTrigger="onChange"
                    rules={[{ required: true }]}
                    initialValue={linkerName}
                >
                    <FormControl
                        fieldid="ublinker-routes-home-components-ConfigureConnectMessage-index-6308654-FormControl"
                        className="ucg-mar-r-5"
                        disabled
                        style={{ width: 320 }}
                    />
                </FormItem>
                <FormItem
                    fieldid="ublinker-routes-home-components-ConfigureConnectMessage-index-7690001-FormItem"
                    label={lang.templateByUuid("UID:P_UBL-FE_2009604004280404", "连接配置") /* "连接配置" */}
                    className="formItem-title"
                ></FormItem>

                {/* {
                            linkconfig.map((item) => {
                                return <DynamicDomByType
                                    changeInfo={changeLinkconfig}
                                    setTestButtonDisable={setTestButtonDisable}
                                    type={item.type}
                                    data={item}
                                />
                            })
                        } */}

                {isAdd ? null : (
                    <FormItem
                        fieldid="ublinker-routes-home-components-ConfigureConnectMessage-index-7829512-FormItem"
                        label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050924") /* "集成系统" */}
                        name="selectSystemId"
                        initialValue={selectSystemId}
                    >
                        {selectSystemName}
                    </FormItem>
                )}
            </FormList>
        </div>
    );
};

export default ConfigureConnectMessage;
