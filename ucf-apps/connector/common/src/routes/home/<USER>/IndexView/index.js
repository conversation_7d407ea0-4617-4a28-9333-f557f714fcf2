import React, { Component, Fragment } from "react";
import { Modal, Select, Tooltip, Button, Icon, FormControl } from "components/TinperBee";
import { Content } from "components/PageView";
import withRouter from "decorator/withRouter";
import ConnectorList from "../ConnecterList";
import TablesList from "../TablesList/index";
import commonText from "constants/commonText";
import "./index.less";
import CreateModal from "../CreateModal";
import InitModal from "../InitModal";
import U9Modal from "../Modal/U9Modal";
import { Success, Error } from "utils/feedback";
import ConnSetConfigModal from "iuap-ip-commonui-fe/ConnSetConfigModal";
console.log(ConnSetConfigModal);
const Option = Select.Option;
@withRouter
class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            showConnSetConfigModal: false,
            Dcode: "",
            Dinfo: {},
            showConnectModal: false,
            showCreateModal: false,
            isFromCreateModal: false,
            showInitModal: false,

            ModalCode: "",
            ModalTitle: "",
            ConnectModalData: {},
            InitModalData: {},
            ModalTestable: false,
            ModalTemplates: "",
            ModalType: "", //edit  add

            connectorId: "",
            connectorAdapterCode: "",
            defaultSelectedVersion: "",

            // 添加连接时传递给弹窗的数据
            addModalData: {},
            // 编辑连接时传递给弹窗的数据
            editModalData: {},
            showType: "card", //card table,
        };
    }

    componentDidMount() {
        let { ownerStore, ownerState } = this.props;
        ownerStore.getAllConnector();
        ownerStore.getDataSource();
        ownerStore.getGatewayList();
        // ownerStore.getGatewayList();
        console.log("ooooo----, ", ownerState, ownerStore);
    }
    handleSearch = (searchValue) => {
        this.props.ownerStore.searchDataSource(searchValue);
    };

    handleDelete = (connector) => {
        let { ownerStore } = this.props;
        Modal.confirm({
            fieldid: "202306091518",
            title: window.lang.template(commonText.confirmDelete),
            content: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050468") /* "请慎重考虑，此操作不可逆！" */,
            onOk: ownerStore.deleteConnector.bind(null, connector),
        });
    };

    changeCreateModalShow = (fromCreateModalCancel) => {
        // alert(this.state.ModalType)
        this.setState({
            showCreateModal: !this.state.showCreateModal,
            isFromCreateModal: fromCreateModalCancel == "cancel" ? false : true, //1首次打开大的，记为true，2 fromCreateModalCancel有值 为大框直接取消，重置为false
            ModalType: fromCreateModalCancel == "cancel" ? "" : this.state.ModalType, //点大框取消ModalType==pre的情况，否则pre时候，再打开大框会回写状态 为了返回上一步回写信息的操作
        });
    };
    changeInitModalShow = (selectedItem) => {
        this.setState({
            showInitModal: !this.state.showInitModal,
            InitModalData: selectedItem,
        });
    };
    handleInitModalOk = () => {
        this.changeInitModalShow();
    };
    handleTestConnect = async (item) => {
        let res = await this.props.ownerStore.testConnect(item);
        return res;
    };
    handleSetDefault = async (data) => {
        this.props.ownerStore.setDefault(data);
    };
    handleStateChange = async (data) => {
        this.props.ownerStore.changeEnableState(data);
    };

    handleCreateModalOk = async (selectedItem, ModalType, isCreateAll) => {
        console.log(selectedItem);
        let { code: ModalCode, name: ModalTitle, id: connectorId, adapterCode: connectorAdapterCode, selfItem, testable, fromType } = selectedItem; //fromType==2调底座弹框
        let res = {},
            resTemplates = {},
            resVersion = {};
        if (ModalType == "add" && fromType != 2) {
            //新建时调  版本，，模板，，的接口
            if (!connectorId) {
                Error(window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202207251408") /* 请选择连接器 */);
                return;
            }
            this.props.ownerStore.getGatewayList();
            resVersion = await this.props.ownerStore.getAllVersion(connectorId);
            resTemplates = await this.props.ownerStore.getLinkerSetParam({
                linkerId: connectorId,
                adapterCode: connectorAdapterCode,
                linkerVersionCode: resVersion.data[0] && resVersion.data[0].code,
            });
        } else if (ModalType == "edit") {
            if (fromType != 2) {
                this.props.ownerStore.getGatewayList();
            } else {
                this.setState({
                    Dinfo: selfItem,
                });
            }
        } else if (ModalType == "submit") {
            this.props.ownerStore.getDataSource();
            this.setState({
                isFromCreateModal: false, //按流程依次打开大框，然后小框 ，成功提交，isFromCreateModal重置为false
            });
        } else if (ModalType == "cancel") {
            this.setState({
                isFromCreateModal: false, //按流程依次打开大框，然后小框 ，然后点叉，而不是上一步，isFromCreateModal重置为false
            });
        }
        if (fromType == 2) {
            this.setState(
                {
                    showConnSetConfigModal: true,
                    Dcode: selectedItem.code,
                },
                () => {
                    if (isCreateAll) {
                        this.changeCreateModalShow();
                    }
                }
            );
        } else {
            //打开新建单个连接器弹框
            this.setState(
                {
                    // [ModalCode]:!this.state[ModalCode],
                    showConnectModal: !this.state.showConnectModal,
                    ModalCode,
                    ModalTitle,
                    ModalTemplates: (resTemplates.data && resTemplates.data.linkerConfig) || (selfItem && selfItem.linkconfig),
                    ModalType,
                    ConnectModalData: selfItem,
                    ModalTestable: testable,

                    connectorId,
                    connectorAdapterCode,
                    defaultSelectedVersion: resVersion.data && resVersion.data[0].code,
                },
                () => {
                    //1 点击全部连接器的确定关掉全部弹框，不传值 让isFromCreateModal保持不变   2点小框上一步并且小框是从大框来的，则重新打开大框，不传值让isFromCreateModal保持不变
                    if (isCreateAll || (ModalType == "pre" && this.state.isFromCreateModal)) {
                        //放在回调里是为了点上一步能回写信息的操作
                        this.changeCreateModalShow();
                    }
                }
            );
        }
    };
    handleChangeView = (showType) => {
        this.setState({
            showType,
        });
    };
    changeVersionGetTemplates = async (value) => {
        let resTemplates = await this.props.ownerStore.getLinkerSetParam({
            linkerId: this.state.connectorId,
            adapterCode: this.state.connectorAdapterCode,
            linkerVersionCode: value,
        });
        this.setState(
            {
                ModalTemplates: [...resTemplates.data.linkerConfig],
            },
            () => {
                console.log(this.state.ModalTemplates);
            }
        );
    };
    handleSelectChange = (code) => {
        // if(code=='all'){ //如果选择全部，pageDataTemp为undefined，则显示pageData全部
        //   this.props.ownerStore.changeState({
        //     pageDataTemp:undefined
        //   })
        // }else{
        //   let {pageData}=this.props.ownerState
        //   let pageDataItem=pageData.filter((item)=>{
        //     return item.code==code
        //   })
        //   console.log(pageDataItem)
        //   this.props.ownerStore.changeState({
        //     pageDataTemp:pageDataItem
        //   })
        // }
        this.changeState({
            filterConnector: code,
        });
    };
    render() {
        let { ownerState, ownerStore } = this.props;
        let { gatewayList, versionList, systemList, allConnector, allConnectorSel, pageData = [], loaded, pageDataTemp, testConnectSuccess } = ownerState;
        let {
            showConnectModal,
            showCreateModal,
            isFromCreateModal,
            showInitModal,
            ModalType,
            addModalData,
            editModalData,
            showType,
            ModalTitle,
            ConnectModalData,
            InitModalData,
            ModalTestable,
            ModalCode,
            ModalTemplates,
            connectorId,
            connectorAdapterCode,
            defaultSelectedVersion,
            showConnSetConfigModal,
            Dcode,
            Dinfo,
        } = this.state;
        let completeModalTitle =
            ModalType == "add"
                ? window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281454", { ModalTitle: ModalTitle })
                : window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281455", { ModalTitle: ModalTitle }); //'新建/'编辑' + ModalTitle + '连接配置'
        return (
            <Content className="sys-connector-common-content common-config">
                <div className="header">
                    <div className="header-left">
                        <div className="header-left-title">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281450") /* 连接配置 */}</div>
                        <Tooltip
                            fieldid="ublinker-routes-home-components-IndexView-index-7765998-Tooltip"
                            inverse
                            placement="bottomLeft"
                            overlay={
                                <div className="header-tooltip-title">
                                    <div className="tooltip-title">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281450") /* 连接配置 */}</div>
                                    <div>
                                        {
                                            window.lang.template(
                                                "MIX_UBL_ALL_UBL_FE_LOC_20226281451"
                                            ) /* 配置具体集成系统的连接，基于连接器的协议，进行对第三方系统具体的连接实例配置。 */
                                        }
                                    </div>
                                </div>
                            }
                        >
                            <i fieldid="ublinker-routes-home-components-IndexView-index-784725-i" className="cl cl-Q ucg-pad-l-5 ucg-mar-t-4" />
                        </Tooltip>
                        {/* <Select fieldid="ublinker-routes-home-components-IndexView-index-9510708-Select" 
              style={{ width: 200, marginLeft: 16 }}
              onChange={this.handleSelectChange}
            >
              {
                allConnectorSel.map((item) => {
                  return (
                    <Option fieldid="UCG-FE-routes-home-components-IndexView-index-5028461-Option" value={item.code}>{item.name}</Option>
                  )
                })
              }
            </Select> */}
                    </div>
                    <div className="header-right">
                        {/* <FormControl fieldid="ublinker-routes-home-components-IndexView-index-7766605-FormControl" 
              className="ucg-mar-r-10 "
              style={{ width: '200px', boxSizing: 'border-box' }}
              onSearch={this.handleSearch}
              placeholder={'搜索连接配置名称/编码'}
              // onChange={this.onChange}
              // onClick={this.onClick}
              showClose
              maxLength={5}
              type='search'
            /> */}
                        <i
                            fieldid="ublinker-routes-home-components-IndexView-index-4700791-i"
                            className={`ipaas iPS-kapian header-right-icon ${showType == "card" ? "cur" : ""}`}
                            onClick={this.handleChangeView.bind(null, "card")}
                        ></i>
                        <i
                            fieldid="ublinker-routes-home-components-IndexView-index-7078826-i"
                            className={`ipaas iPS-liebiao header-right-icon ${showType == "table" ? "cur" : ""}`}
                            onClick={this.handleChangeView.bind(null, "table")}
                        ></i>
                        <Button
                            fieldid="ublinker-routes-home-components-IndexView-index-5870842-Button"
                            className="ucg-mar-l-10 "
                            colors="primary"
                            onClick={this.changeCreateModalShow}
                        >
                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281452") /* 新建连接 */}
                        </Button>
                    </div>
                </div>

                <div className="sys-connector-common-view-wrap">
                    {showType == "card" ? (
                        <ConnectorList
                            loaded={loaded}
                            dataSource={pageDataTemp || pageData}
                            handleCreateModalOk={this.handleCreateModalOk}
                            handleTestConnect={this.handleTestConnect}
                            handleSetDefault={this.handleSetDefault}
                            handleStateChange={this.handleStateChange}
                            changeInitModalShow={this.changeInitModalShow}
                            onDelete={this.handleDelete}
                            testConnectSuccess={testConnectSuccess}
                            changeCreateModalShow={this.changeCreateModalShow}
                        />
                    ) : (
                        <TablesList
                            dataSource={pageDataTemp || pageData}
                            handleCreateModalOk={this.handleCreateModalOk}
                            handleTestConnect={this.handleTestConnect}
                            handleSetDefault={this.handleSetDefault}
                            handleStateChange={this.handleStateChange}
                            onDelete={this.handleDelete}
                            testConnectSuccess={testConnectSuccess}
                            ownerState={ownerState}
                            ownerStore={ownerStore}
                        />
                    )}
                </div>
                {showCreateModal ? (
                    <CreateModal
                        show={showCreateModal}
                        ModalTitle={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281453") /* 选择连接器 */}
                        allConnector={allConnector}
                        ModalType={ModalType}
                        onCancel={this.changeCreateModalShow}
                        onOk={this.handleCreateModalOk}
                        ownerStore={ownerStore}
                        ownerState={ownerState}
                    />
                ) : null}
                {showInitModal ? (
                    <InitModal
                        show={showInitModal}
                        InitModalData={InitModalData}
                        onCancel={this.changeInitModalShow}
                        onOk={this.handleInitModalOk}
                        ownerStore={ownerStore}
                        ownerState={ownerState}
                    />
                ) : null}
                {showConnectModal ? (
                    <U9Modal
                        show={showConnectModal}
                        isFromCreateModal={isFromCreateModal}
                        completeModalTitle={completeModalTitle}
                        ModalTitle={ModalTitle}
                        ModalCode={ModalCode}
                        ModalTemplates={ModalTemplates}
                        ConnectModalData={ConnectModalData}
                        ModalTestable={ModalTestable}
                        changeModalTemplates={() => {
                            this.setState({ ModalTemplates: [...ModalTemplates] });
                        }}
                        changeVersionGetTemplates={this.changeVersionGetTemplates}
                        gatewayList={gatewayList}
                        versionList={versionList}
                        ModalType={ModalType}
                        systemList={systemList}
                        addModalData={addModalData}
                        editModalData={editModalData}
                        onCancel={this.handleCreateModalOk}
                        ownerStore={ownerStore}
                        ownerState={ownerState}
                        connectorId={connectorId}
                        connectorAdapterCode={connectorAdapterCode}
                        defaultSelectedVersion={defaultSelectedVersion}
                    />
                ) : null}
                {showConnSetConfigModal ? (
                    <ConnSetConfigModal
                        visible={showConnSetConfigModal}
                        handleClose={() => {
                            this.setState({ showConnSetConfigModal: false, Dinfo: {} });
                        }}
                        handleSuccess={() => {
                            this.setState({ showConnSetConfigModal: false, Dinfo: {} });
                            this.props.ownerStore.getDataSource();
                        }}
                        linkerCode={Dcode}
                        info={{
                            resName: Dinfo.resName,
                            resCode: Dinfo.resCode,
                            id: Dinfo.id,
                        }}
                        isTenant={true}
                        openType={Dinfo.resName ? "update" : "add"}
                    />
                ) : null}
            </Content>
        );
    }
}

export default IndexView;
