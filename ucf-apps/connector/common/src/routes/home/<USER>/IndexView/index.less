.sys-connector-common {
    &-content {
        background: #fff !important;
    }
    &-view-wrap {
        padding: 62px 16px;
        background: #fff;
    }
}
.sys-connector-common-tab .wui-tabs-bar{
    padding:10px 0;
}
.tabBarExtraContent{
    display: flex;
    align-items: center;
}
.common-config{

}
.common-config .header{
    position: fixed;
    top:0;
    left:0;
    width:100%;
    height: 46px;
    background: #FFFFFF;
box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
display: flex;
align-items: center;
justify-content: space-between;
padding:0 17px;
z-index: 1000;
}
.header-left,.header-right{
    display: flex;
align-items: center;
}
.header-left-title{
    font-size: 16px;
font-family: PingFang-SC-Heavy, PingFang-SC;
font-weight: 800;
color: #333333;
}
.header-right .mix-search-input.search-input-lg{
    width:200px;
}
.header-right-icon{
    font-size: 18px;
    background: #fff;
    width: 28px;
height: 28px;
border-radius: 2px;
padding-top: 2px!important;
cursor: pointer;
padding: 0 5px;
margin-left:10px;
}
.header-right-icon.cur{

background: #F2F3F3;

}
.data-task-run-status {
    display: inline-block;
    // min-width: 60px;
    height: 22px;
    font-size: 12px;
    line-height: 22px;
    text-align: center;
    // border: 1px solid;
    border-radius: 3px;
    padding: 0 10px;
}

.data-task-run-status.start {
    background: #EBF9F8;
    color: #00B39E;
}
.data-task-run-status.stop {
    
background: #F8F8F8;
   
color: #999999;
}
// .info-header-tag{
//     display: inline-block;
//     margin-left:12px;
//                         width: 49px;
//                         height: 22px;
//                         text-align: center;
//                         background: #EBF9F8;
//                         border-radius: 3px;
//                         border: 1px solid #00B39E;
//                         font-size: 12px;
//                         font-family: PingFang-SC-Medium, PingFang-SC;
//                         font-weight: 500;
//                         color: #00B39E;
//                         line-height: 20px;
// }
.wui-tooltip-inner{
   
}
.header-tooltip-title{
    padding:7px;
    font-size: 12px;
font-family: PingFangSC-Regular, PingFang SC;
font-weight: 400;
color: #666666;
line-height: 22px;
}
.wui-tooltip-inverse .wui-tooltip-inner{
}
.tooltip-title{
    font-size: 12px;
font-family: PingFang-SC-Heavy, PingFang-SC;
font-weight: 800;
color: #333333;
line-height: 17px;
margin-bottom:9px;
}
.cl-Q{
    color: #9e9e9e;
    margin-top:3px;
}
.cl-Q:hover{
    color:#505766;
}