import React, { Fragment, Component } from "react";
import classnames from "classnames";
import Empty from "components/EmptyPage";
import commonText from "constants/commonText";
import { Icon, Switch, Clipboard, Button, Table, Checkbox, Tooltip } from "components/TinperBee";
const { multiSelect } = Table;
const MultiSelectTable = multiSelect(Table, Checkbox);
import Grid from "components/TinperBee/Grid";
import Highlight from "components/Highlight";
import "./index.less";
import { updateConnectState } from "../../service";
import Logo from "components/Card/Logo";
import { getCompleteImg } from "utils";
const clientHeight = document.body.clientHeight;

class TablesList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            tableData: [],
            dataObj: {},
        };
    }
    columns16 = [
        {
            title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281473") /* 连接器名称 */,
            dataIndex: "name",
            width: 100,
        },
        // { title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281468") /* 连接器编码 */, dataIndex: "adapterCode",  width: 250 },
        { title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281490") /* 最新版本 */, dataIndex: "version", width: 100 },
        { title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281469") /* 发布时间 */, dataIndex: "releaseTime", width: 200 },
        { title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281471") /* 说明 */, dataIndex: "description", width: 200 },
    ];

    renderStatus = (value, record) => {
        console.log(value);
        return (
            <span className={`data-task-run-status ${value ? "start" : "stop"}`}>
                {
                    value
                        ? window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050927") /* 启用 */
                        : window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281487") /* 停用 */
                }
            </span>
        );
    };
    renderDefault = (value, record) => {
        console.log(value);
        return (
            <div>
                {value || "-"}
                {record.zhuzhangtao && <div className="table-default-icon-zhu">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281456") /* 主账套 */}</div>}
                {/* {
      record.linkerLevelDefault && <Tooltip fieldid="ublinker-routes-home-components-TablesList-index-338009-Tooltip"  inverse placement="bottomLeft" overlay={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281466") /* 当前连接器的默认连接配置 *}><span className='table-default-icon'>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281467") /* 默认 *}</span></Tooltip>
    } */}
                {record.isDefault && (
                    <Tooltip
                        fieldid="ublinker-routes-home-components-TablesList-index-5930902-Tooltip"
                        inverse
                        placement="bottomLeft"
                        overlay={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281457") /* 当前租户的默认连接配置 */}
                    >
                        <span className="table-default-icon-tennet">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281458") /* 租户默认 */}</span>
                    </Tooltip>
                )}
            </div>
        );
    };
    renderAlias = (value, record) => {
        console.log(value, record);
        return (
            <>
                <span>{value || "-"}</span>
                {record.zhuzhangtao && <span className="info-header-tag">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281456") /* 主账套 */}</span>}
            </>
        );
    };
    renderText = (value, record) => {
        return <span>{value || "-"}</span>;
    };

    columns17 = [
        { title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050932") /* 连接编码 */, dataIndex: "connectCode", render: this.renderDefault, width: "300" },
        { title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050928") /* 连接名称 */, dataIndex: "alias" },
        { title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281462") /* 连接器版本 */, dataIndex: "erpversion", render: this.renderText },
        // { title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281488") /* 启用状态 *, dataIndex: "linkerState",  key: 'linkerState',render: this.renderStatus },
        { title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281489") /* 网关 */, dataIndex: "gatewayId", render: this.renderText },
        { title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281463") /* 集成系统 */, dataIndex: "integratedSystemNames", render: this.renderText },
    ];
    handleStateChange = (item) => {
        this.props.handleStateChange({ id: item.id, state: item.linkerState == 1 ? 0 : 1 });
    };
    handleTestConnect = async (item) => {
        this.props.handleTestConnect(item);
    };
    handleSetDefault = async (item) => {
        this.props.handleSetDefault({ connectId: item.id, isDefault: !item.linkerLevelDefault });
    };
    handleEditConnect = (parentItem, selfItem) => {
        this.props.handleCreateModalOk({ ...parentItem, selfItem }, "edit");
    };
    handleNewConnect = (item) => {
        this.props.handleCreateModalOk(item, "add");
    };
    hoverContent = (parentItem, record) => {
        return (
            <Fragment>
                <Button
                    fieldid="ublinker-routes-home-components-TablesList-index-6985540-Button"
                    {...Grid.hoverButtonPorps}
                    onClick={this.handleEditConnect.bind(null, parentItem, record)}
                >
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050036") /* "编辑" */}
                </Button>
                {/* <Button fieldid="ublinker-routes-home-components-TablesList-index-4567698-Button" 
              {...Grid.hoverButtonPorps}
              onClick={this.handleStateChange.bind(null, record)}
              >{!record.linkerState?window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050927") /* 启用 * : window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281487") /* 停用 *}</Button> */}
                <Button
                    fieldid="ublinker-routes-home-components-TablesList-index-6426917-Button"
                    {...Grid.hoverButtonPorps}
                    onClick={this.handleTestConnect.bind(null, record)}
                >
                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281464") /* 测试连接 */}
                </Button>
                {/* <Button fieldid="ublinker-routes-home-components-TablesList-index-1510202-Button" 
              {...Grid.hoverButtonPorps}
              onClick={this.handleSetDefault.bind(null, record)}
              >{record.linkerLevelDefault?<Tooltip fieldid="ublinker-routes-home-components-TablesList-index-8447132-Tooltip" arrowPointAtCenter placement="bottomLeft" inverse overlay={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281459") /* 取消默认连接，可能会造成调用指定连接器的网关接口找不到默认连接而调用不成功的情况，请确认是否取消？ *}>
              {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281460") /* 取消默认 *}
          </Tooltip>:window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281461") /* 设为默认 *}</Button> */}
                {/* { (!record.linkerLevelDefault && !record.isDefault) && */}
                {!record.isDefault && (
                    <Button
                        fieldid="ublinker-routes-home-components-TablesList-index-9771262-Button"
                        {...Grid.hoverButtonPorps}
                        onClick={this.props.onDelete.bind(null, record)}
                    >
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050046") /* "删除" */}
                    </Button>
                )}
            </Fragment>
        );
    };
    gridhoverContent = (record, index) => {
        return (
            <Button
                fieldid="ublinker-routes-home-components-TablesList-index-4116838-Button"
                {...Grid.hoverButtonPorps}
                onClick={this.handleNewConnect.bind(null, record)}
            >
                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281452") /* 新建连接 */}
            </Button>
        );
    };
    expandedRowRender = (record, index, indent) => {
        const { ownerState } = this.props;
        return (
            <Grid
                fieldid="ublinker-routes-home-components-TablesList-index-4440771-Grid"
                rowKey={"id"}
                columns={this.columns17}
                data={record.taskList}
                hoverContent={this.hoverContent.bind(null, record)}
                // pagination={record.pagination}
            />
        );
    };
    getData = (expanded, record) => {
        // 当点击展开的时候才去请求数据
        let newObj = Object.assign({}, this.state.dataObj);
        let { dataSource } = this.props;
        if (expanded) {
            newObj[record.id] = dataSource.find((item) => {
                return item.id == record.id;
            }).items;
            this.setState({
                dataObj: { ...newObj },
            });
        }
    };

    render() {
        const { dataSource } = this.props;
        return (
            <Grid
                fieldid="ublinker-routes-home-components-TablesList-index-3738456-Grid"
                rowKey={"id"}
                // multiSelect
                // selectedList={[]}
                className="expanded-table"
                columns={this.columns16}
                data={dataSource}
                // defaultExpandAllRows={false}
                // data={dataSource}
                // scroll={{y: 350}}
                // onExpand={this.getData}
                // autoCheckedByClickRows={false}
                // getSelectedDataFunc={this.getSelectedDataFunc}
                expandedRowRender={this.expandedRowRender}
                hoverContent={this.gridhoverContent}
                // expandIconAsCell={true}
            />
        );
    }
}

export default TablesList;
