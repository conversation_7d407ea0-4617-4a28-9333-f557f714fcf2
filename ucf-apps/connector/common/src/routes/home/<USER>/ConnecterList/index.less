@import "~styles/textOverflow.less";
.cards-wrap-bottom .sys-connector-common {

    &-list {
        display: flex;
        // flex-direction: row;
        justify-content: space-between;
        flex-wrap: wrap;
        width:100%;
        height:auto;
        transition: 2s;
    }
    &-list.cur {
        // max-height:250px;
        // overflow: hidden;
    }

    &-item {
        // width: 300px;
        min-width:19%;
        margin-top:16px;
        // height: 182px;
        // margin-right: 16px;
        // margin-bottom:60px;

        &-added-content {
            background-color: #fff;
            // box-shadow: 0 5px 16px 0 #ebf1f7;
            // border: 1px solid #505766;
            border-radius: 9px;
            box-shadow: 0px 0px 16px 0px rgba(173, 180, 188, 0.2);
            padding-top:25px;
            border: 1px solid #fff;
            position: relative;
        }
        .default-icon{
            position: absolute;
            right:-1px;
            top:-1px;
            
            padding: 2px 8px;
            text-align: center;
border-radius: 0px 9px 0px 9px;

background: #505766;
font-size: 12px;
font-family: PingFangSC-Regular, PingFang SC;
font-weight: 400;
color: #FFFFFF;
        }
        .default-icon-tennet{
            margin-left:12px;

            background: #F1F1F3;                    border-radius: 3px;
                    
color: #505766;
border: 1px solid #505766;
                    font-size: 10px;
                    font-family: PingFang-SC-Medium, PingFang-SC;
                    font-weight: 500;
                    padding: 0 4px;
        }
        &-added-content:hover {    
            // box-shadow: 0 5px 30px 0 #d7dfe7;
            
box-shadow: 0px 0px 16px 0px rgba(173, 180, 188, 0.2);
            border: 1px solid #505766;
        }
        &-added-content:hover .item-added-header .info-right {    
            display: block;
        }

        // 已配置连接重新布局
        .item-added-header {
            height: 42px;
            width: 345px;
            background-size: 100% 100%;
            display: flex;
            flex-direction: row;
            align-items: flex-start;
            box-sizing: border-box;
            justify-content:flex-start;
            margin-bottom:16px;

          
            &-icon {
                width: 42px;
                height: 42px;
                background: #FFFFFF;
                border: 1px solid #D9D9D9;
                border-radius: 5px;
                // border: 1px solid #E4E4E4;
                margin-left: 21px;
                flex-shrink: 0;
                background-size: 100% 100%;
            }

            &-name {
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                // max-width: calc(~"100% - 210px");
                // font-size: 16px;
                // font-weight: 600;
                font-size: 16px;
font-family: PingFang-SC-Heavy, PingFang-SC;
font-weight: 800;
color: #000000;
            }

            .info-header {
                width: 100%;
                overflow: hidden;
                margin-left: 10px;
                // width: calc(100% - 90px);
                color: rgba(0, 0, 0, 0.8);
                height:42px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                // line-height: 16px;
                // height: 60px;

                // span {
                //     vertical-align: middle;
                // }
                &-name {
                    font-size: 20px;
                    line-height: 32px;
                    color: #333;
                    font-weight: 500;
                    span {
                        vertical-align: baseline;
                    }
                }
                &-state {
                    // display: inline-block;
                    min-width: 40px;
                    height: 18px;
                    padding: 0 8px;
                    border-radius: 4px;
                    background-color: #708091;
                    font-size: 12px;
                    color: #ffffff;
                    line-height: 18px;
                    text-align: center;
                    margin: 0 8px;
                    &.online {
                        background-color: #4CAF50;
                    }
                }
                &-tag {
                    margin-left:12px;
                    background: #EBF9F8;
                    border-radius: 3px;
                    border: 1px solid #00B39E;
                    font-size: 10px;
                    font-family: PingFang-SC-Medium, PingFang-SC;
                    font-weight: 500;
                    color: #00B39E;
                    padding: 0 4px;
                }
            }
            .info-right{
                display: none;
                font-size: 12px;
font-family: PingFangSC-Regular, PingFang SC;
font-weight: 400;
color: #4A8BF0;
cursor: pointer;
margin-left: auto;
margin-top:-3px;
padding-right:20px;
            }
            .info-right.cur{
                display: block;
            }
        }

        // 已添加卡片样式
        .item-added-content {
            // height: 45px;
            width: 345px;
            font-size: 12px;
           
font-weight: 400;
color: #333333;
            display: flex;
            flex-direction: column;
            padding-left: 20px;
            margin-bottom: 12px;
            p {
                margin-top: 3px;
                margin-bottom: 3px;
            }
            .item-added-edition{
                width:100%;
                overflow:hidden;
                text-overflow:ellipsis;
                white-space:nowrap;
            }
        }

        .item-added-footer {
border-top: 1px solid #E8E9EB;
            height: 46px;
            // line-height: 35px;
            display: flex;
            align-items: center;
            justify-content: space-around;
            font-size: 12px;
font-family: PingFangSC-Regular, PingFang SC;
font-weight: 400;
color: #4A8BF0;
            .item-added-footer-item{
                display: flex;
                cursor: pointer;
                align-items: center;
                .wui-button{
                    padding:0;
                    min-width: 0px;
                    margin-left: 3px;
                }
                i{
                    color:#03c;
                }
            }
            .item-added-footer-item:hover i{
                color: #06f;
            }
            .init-enabled{
                opacity: .5;
            }
            a {
                width: 40px;
                color: #4A8BF0;

            }

            &.add-footer {
                height: 40px;
                line-height: 40px;
                .footer-btn {
                    padding: 10px 0;
                }
            }
            .footer-btn {
                margin-left:2px;
                // display: inline-block;
                // font-size: 12px;
                // line-height: 20px;
                // color: #4A8BF0;

                // padding-top: 5px;
                // padding-bottom: 5px;
                // text-align: center;
                // vertical-align: middle;

                i.cl {
                    font-size: 18px;
                    vertical-align: bottom;
                }
                &:after {
                    content: ' ';
                    // width: 1px;
                    height: 20px;
                    float: right;
                    background: #EDEDED;
                }
                &:last-of-type {
                    &:after {
                        display: none;
                    }
                }
                &.disabled {
                    cursor: no-drop;
                    color: #999999;
                }

            }

            .footer-btn:hover {
                // color: #588CE9;
            }
        }

        .item-added-footer-empty {
            border-top: 0px;
        }

        .item-state-area {
            display: flex;
            // height: 60px;
            width: 100%;
            // justify-content: flex-start;
            align-items: center;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #333333;
            .item-state-area-switch{
                margin-top:-1px;
            }
        }
    }
}

.sys-connector-common-empty {
    img {
        width: 100px !important;
        height: 100px !important;
        margin-bottom: 0px !important;
    }
}

.temp-connect-header {
    border-radius: 8px;
    margin-left: 30px;
    flex-shrink: 0;
    height: 60px;
    width: 60px;
    font-size: 30px;
    text-align: center;
    line-height: 60px;
    background-color: rgb(195, 195, 255);
    color: #fff;
}

.temp-connect-header2 {
    width: 100px;
    height: 100px;
    border-radius: 8px;
    margin-left: 30px;
    flex-shrink: 0;
    font-size: 50px;
    text-align: center;
    line-height: 100px;
    background-color: rgb(195, 195, 255);
    color: #fff;
}
.cards-wrap-tem .cards-wrap{
    width: 100%;
// height: 345px;
padding:24px;
box-sizing: border-box;
background: #EFF1F7;
margin-bottom:16px;
}
.cards-wrap-top{
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
}
.cards-wrap-top .item-added-header-icon{
    // margin-right:0;
    width: 50px;
height: 50px;
// background: linear-gradient(180deg, #fff 0%, #fff 100%);
border: 1px solid #D9D9D9;
border-radius: 4px;
margin-right:8px;


}
.cards-wrap-top .item-added-header-icon.empty{
    img{
        width:40px;
        height:40px;
    }
}
.cards-wrap-top-all .wui-button{
// margin-left:auto
background: #EFF1F7;
    // .wui-button{
    //     // background: #EFF1F7;
    // }
}
.cards-wrap-top-info{
    display: flex;
    height:50px;
    flex-direction: column;
    justify-content: space-between;
}
.top-info-top{
    display: flex;
    align-items: center;
}
.top-info-top .name{
    font-size: 20px;
font-family: PingFang-SC-Heavy, PingFang-SC;
font-weight: 800;
color: #000000;
margin-right:10px;
// -webkit-background-clip: text;
// -webkit-text-fill-color: transparent;
}
.top-info-bottom{
    font-size: 12px;
font-family: PingFangSC-Regular, PingFang SC;
font-weight: 400;
color: #666666;
display: flex;
    align-items: center;
}
.bottom-list{
    margin-right:100px;
}
.bottom-list-label{
    margin-right:12px;
}
.cards-wrap-top-all{
    font-size: 12px;
font-family: PingFangSC-Regular, PingFang SC;
font-weight: 400;
color: #588CE9;
margin-left:auto
}
.cancelDefault-tip{
    word-break:break-all;
    width:213px;
}
// .wui-button-icon-cls-wrapper{
//     position: relative;
// }
.icon-position{
    position: absolute;
    top: -6px;
    left: -15px;
}
.cards-wrap-tem-empty{
    
background: #EFF1F7;
.cards-wrap{
    .cards-wrap-top{
        padding:16px 14px;
        border-bottom:1px solid #CCCFD3;
        .cards-wrap-top-info{
            .top-info-bottom{
                color: #999;

            }
            .top-info-top span{
                color: #999;
            }
        }
    }
    .cards-wrap-bottom{
        height:262px;
        .cards-wrap-bottom-content{
            padding-top:42px;
            width:500px;
            margin:0 auto;
            text-align: center;
            img{
                width:62px;
                height:62px;
                margin-bottom:12px;
            }
            .cards-wrap-bottom-content-word{
                margin-bottom:17px;
                font-size: 12px;
                font-family: MicrosoftYaHei;
                color: #333333;
            }

            .cards-wrap-bottom-content-btn{
                width: 132px;
                height: 28px;
                // background: #FFFFFF;
                border-radius: 4px;
                // border: 1px solid #505766;
            }
        }
    }
}
}
.item-added-name{
    width:94%;
    overflow: hidden;

text-overflow: ellipsis;

white-space: nowrap; 
}