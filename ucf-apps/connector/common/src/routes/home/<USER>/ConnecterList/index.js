import React, { Fragment, useState } from "react";
import classnames from "classnames";
import commonText from "constants/commonText";
import { Icon, Switch, Clipboard, Button, Tooltip, Popconfirm, Empty as EmptyTinper } from "components/TinperBee";
import Highlight from "components/Highlight";
import "./index.less";
import Logo from "components/Card/Logo";
import SvgIcon from "components/SvgIcon";
import { getCompleteImg } from "utils";
import { getRandomInt } from "utils/index";
import defaultAvatar from "./defaultAvatar.svg";
const clientHeight = document.body.clientHeight;
import ConnectorLogo from "components/ConnectorLogo";

const emptyStyle = {
    marginTop: clientHeight / 2 - 158 + "px",
};

const getCardList = (items, parentItem, props, cancelDefaultIndex, setCancelDefaultIndex) => {
    let { dataSource, type: cardType, searchKey, loaded, onDelete, onAdd, onEdit, navToAdd, testConnectSuccess } = props;
    const handleEditConnect = (parentItem, selfItem) => {
        props.handleCreateModalOk({ ...parentItem, selfItem }, "edit");
    };
    const handleTestConnect = async (item) => {
        props.handleTestConnect(item);
    };
    const handleCardHover = (a, b, c) => {
        console.log(a, b, c);
    };

    const handleInitConnect = async (parentItem, item) => {
        if (parentItem.testable) {
            //如果支持测试连接，先测试连接成功之后 再打开初始化弹框，如果不支持测试连接，直接打开初始化弹框
            let res = await props.handleTestConnect(item);
            res && props.changeInitModalShow(item);
        } else {
            props.changeInitModalShow(item);
        }
    };
    const handleSetDefault = (item) => {
        // debugger
        props.handleSetDefault({ connectId: item.id, isDefault: !item.linkerLevelDefault });
    };

    const handleStateChange = (item) => {
        props.handleStateChange({ id: item.id, state: item.linkerState == 1 ? 0 : 1 });
    };
    const LOGO_BG_COLORS = [
        "#FF6056",
        "#E83916",
        "#F59D1B",
        "#F4D21F",
        "#9EC922",
        "#C6DE95",
        "#6CBF84",
        "#70C7D5",
        "#318BCB",
        "#9294C8",
        "#68A691",
        "#ED95BE",
        "#F19494",
        "#B397C6",
        "#EF7755",
        "#FED693",
        "#FDB092",
        "#FF5B60",
        "#EBA492",
        "#C3C3FF",
        "#72DDF7",
        "#D44E5C",
        "#44AA8B",
        "#5EB2BF",
        "#139DA4",
        "#9680EF",
    ];

    function getLogoBgColor() {
        const ind = getRandomInt(0, LOGO_BG_COLORS.length - 1);
        return LOGO_BG_COLORS[ind];
    }

    return items.map((item, index) => {
        let {
            id,
            path,
            name,
            alias,
            type,
            state,
            intro,
            config,
            startLinker,
            endLinker,
            description,
            gatewayId,
            connectCode,
            linker,
            linkerState,
            canEdit,
            canDelete,
            fromType,
            testable,
            initView,
        } = item;
        if (linker === null || linker === undefined) {
            linker = {};
        }
        if (canEdit === undefined) {
            canEdit = true;
        }
        if (canDelete === undefined) {
            canDelete = true;
        }
        let { isdefault, orgcode } = config || {};
        let online = state === 1;
        let infoCode = orgcode;
        return (
            <li key={id} className="sys-connector-common-item" handleMouseEnter={handleCardHover}>
                <div className={`sys-connector-common-item-added-content`} handleMouseEnter={handleCardHover}>
                    <Fragment>
                        <div className="item-added-header">
                            <div
                                className={classnames("design-logo ", "item-added-header-icon")}
                                style={{
                                    background: getLogoBgColor(),
                                }}
                            >
                                <SvgIcon
                                    fieldid="UCG-FE-routes-home-components-ConnecterList-index-2123242-SvgIcon"
                                    icon={"connSetting"}
                                    style={{ fontSize: "24px" }}
                                />
                            </div>
                            {/* <Logo
                                    className='item-added-header-icon'
                                    logo={getCompleteImg(parentItem.logo)}
                                    title={(alias || name) ? (alias || name).substr(0, 2) : (alias || name)}
                                /> */}
                            <div className="info-header">
                                <Highlight
                                    className="item-added-header-name"
                                    // content={item.alias || name}
                                    title={item.alias || name}
                                    keyword={searchKey}
                                />
                                <div className="item-state-area">
                                    {/* <div>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050927") /* "启用" *}</div>&nbsp;&nbsp;
                                        <Switch fieldid="ublinker-routes-home-components-ConnecterList-index-6762062-Switch" 
                                            className='item-state-area-switch'
                                            checked={item.linkerState === 1}
                                            size="sm"
                                            colors='blue'
                                            onChange={() => { handleStateChange(item) }}
                                        /> */}
                                    {item.zhuzhangtao && (
                                        <div className="info-header-tag">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281456") /* 主账套 */}</div>
                                    )}
                                    {item.isDefault && (
                                        <Tooltip
                                            fieldid="ublinker-routes-home-components-ConnecterList-index-7508256-Tooltip"
                                            inverse
                                            placement="bottomLeft"
                                            overlay={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281457") /* 当前租户的默认连接配置 */}
                                        >
                                            <div className="default-icon-tennet" fieldid="ublinker-routes-home-components-ConnecterList-index-202306091355-Div">
                                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281458") /* 租户默认 */}
                                            </div>
                                        </Tooltip>
                                    )}
                                </div>
                            </div>
                            {/* <div className={`info-right`} onClick={handleSetDefault.bind(null, item)}>
                                    {item.linkerLevelDefault ?
                                        <Tooltip fieldid="ublinker-routes-home-components-ConnecterList-index-439949-Tooltip" arrowPointAtCenter placement="bottomLeft" inverse overlay={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281459") /* 取消默认连接，可能会造成调用指定连接器的网关接口找不到默认连接而调用不成功的情况，请确认是否取消？ *}>
                                            <Button fieldid="ublinker-routes-home-components-ConnecterList-index-7218388-Button" type="text" bordered >{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281460") /* 取消默认 *}</Button>
                                        </Tooltip>

                                        : <Button fieldid="ublinker-routes-home-components-ConnecterList-index-6565523-Button" type="text" bordered >{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281461") /* 设为默认 *}</Button>}

                                </div> */}
                            {/* <div className={`info-right ${index === cancelDefaultIndex ? 'cur' : ''}`}>
                                    {item.linkerLevelDefault ?
                                        //  '取消默认'  点击取消的时候出弹框，为了防止取消默认不消失，根据标志和位置让当前一直展示（失去Hover)意义，关闭弹框和点空白关闭的时候再去掉标志和位置恢复hover
                                        <Popconfirm fieldid="ublinker-routes-home-components-ConnecterList-index-4454929-Popconfirm" trigger="click" placement="bottom" close_btn={<Button fieldid="ublinker-routes-home-components-ConnecterList-index-2490177-Button" colors='primary'>取消默认</Button>} 
                                            content={<div className="cancelDefault-tip">取消默认连接，可能会造成调用指定连接器的网关接口找不到默认连接而调用不成功的情况，请确认是否取消？</div>} 
                                            onClick={() => { setCancelDefaultIndex(index) }} onClose={handleSetDefault.bind(null, item)} onCancel={() => { setCancelDefaultIndex('') }} onRootClose={() => { setCancelDefaultIndex('') }}>
                                            <div>取消默认</div>
                                        </Popconfirm>
                                        : <div onClick={handleSetDefault.bind(null, item)}>设为默认</div>}

                                </div> */}
                        </div>
                        <div className="item-added-content">
                            {/* 名称 */}
                            <p className="item-added-name" title={item.connectCode}>
                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050932") /* "连接编码：" */}：{item.connectCode}
                            </p>
                            <p className="item-added-name">
                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281462") /* 连接器版本： */}：{item.erpversion}
                            </p>
                            {/* 版本 */}
                            <p className="item-added-edition">
                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202262814891") /* "网关id：" */}：{item.gatewayId}
                            </p>
                            <p className="item-added-edition">
                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281463") /* 集成系统： */}：{item.integratedSystemNames}
                            </p>
                            {/* {
                                                (infoCode === undefined || infoCode === '') ?
                                                    null
                                                    :
                                                    <p className="info-code">
                                                        <span className="info-code-text">
                                                            {
                                                                window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050359")  "网关实例：" 
                                                            }
                                                            <Fragment>
                                                                {infoCode}
                                                                <span className="info-code-copy">
                                                                    <Clipboard fieldid="ublinker-routes-home-components-ConnecterList-index-7441004-Clipboard" 
                                                                        action="copy" text={infoCode}
                                                                        locale={{
                                                                        }}
                                                                    />
                                                                </span>
                                                            </Fragment>
                                                        </span>
                                                    </p>
                                            } */}
                        </div>
                        {fromType == 2 ? ( //底座的连接器
                            <>
                                <div className="item-added-footer">
                                    {canEdit && (
                                        <div className="item-added-footer-item">
                                            <i
                                                fieldid="ublinker-routes-home-components-ConnecterList-index-6697967-i"
                                                className={`ipaas iPS-edit-public-line`}
                                            ></i>
                                            <Button
                                                fieldid="ublinker-routes-home-components-ConnecterList-index-71304-Button"
                                                type="text"
                                                onClick={handleEditConnect.bind(null, parentItem, item)}
                                                bordered
                                            >
                                                {window.lang.template(commonText.edit)}
                                            </Button>
                                        </div>
                                    )}
                                    {testable && (
                                        <div className="item-added-footer-item">
                                            <i fieldid="ublinker-routes-home-components-ConnecterList-index-7229729-i" className={`ipaas iPS-refresh`}></i>
                                            <Button
                                                fieldid="ublinker-routes-home-components-ConnecterList-index-2949816-Button"
                                                type="text"
                                                onClick={handleTestConnect.bind(null, item)}
                                                bordered
                                            >
                                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281464") /* 测试连接 */}
                                            </Button>
                                        </div>
                                    )}
                                    {initView && (
                                        <div className={`item-added-footer-item  ${!testConnectSuccess ? "init-enabled" : ""}`}>
                                            <i fieldid="ublinker-routes-home-components-ConnecterList-index-8196794-i" className={`ipaas iPS-initial`}></i>
                                            <Button
                                                fieldid="ublinker-routes-home-components-ConnecterList-index-4793309-Button"
                                                type="text"
                                                onClick={handleInitConnect.bind(null, parentItem, item)}
                                                bordered
                                            >
                                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281465") /* 初始化 */}
                                            </Button>
                                        </div>
                                    )}
                                    {canDelete && (
                                        <div className="item-added-footer-item">
                                            <i
                                                fieldid="ublinker-routes-home-components-ConnecterList-index-1540696-i"
                                                className={`ipaas iPS-delete-public-line`}
                                            ></i>
                                            <Button
                                                fieldid="ublinker-routes-home-components-ConnecterList-index-2180484-Button"
                                                type="text"
                                                onClick={onDelete.bind(null, item)}
                                                bordered
                                            >
                                                {window.lang.template(commonText.deletion)}
                                            </Button>
                                        </div>
                                    )}
                                </div>
                            </>
                        ) : (
                            //友企连的连接器
                            <>
                                {canEdit || canDelete ? (
                                    <div className="item-added-footer">
                                        <div className="item-added-footer-item">
                                            <i
                                                fieldid="ublinker-routes-home-components-ConnecterList-index-6697967-i"
                                                className={`ipaas iPS-edit-public-line`}
                                            ></i>
                                            <Button
                                                fieldid="ublinker-routes-home-components-ConnecterList-index-71304-Button"
                                                type="text"
                                                onClick={handleEditConnect.bind(null, parentItem, item)}
                                                bordered
                                            >
                                                {window.lang.template(commonText.edit)}
                                            </Button>
                                        </div>
                                        {parentItem.testable && (
                                            <div className="item-added-footer-item">
                                                <i fieldid="ublinker-routes-home-components-ConnecterList-index-7229729-i" className={`ipaas iPS-refresh`}></i>
                                                <Button
                                                    fieldid="ublinker-routes-home-components-ConnecterList-index-2949816-Button"
                                                    type="text"
                                                    onClick={handleTestConnect.bind(null, item)}
                                                    bordered
                                                >
                                                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281464") /* 测试连接 */}
                                                </Button>
                                            </div>
                                        )}
                                        {parentItem.code != "YONBIP" && (
                                            <div className={`item-added-footer-item  ${!testConnectSuccess ? "init-enabled" : ""}`}>
                                                <i fieldid="ublinker-routes-home-components-ConnecterList-index-8196794-i" className={`ipaas iPS-initial`}></i>
                                                <Button
                                                    fieldid="ublinker-routes-home-components-ConnecterList-index-4793309-Button"
                                                    type="text"
                                                    onClick={handleInitConnect.bind(null, parentItem, item)}
                                                    bordered
                                                >
                                                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281465") /* 初始化 */}
                                                </Button>
                                            </div>
                                        )}
                                        {
                                            //连接级默认和租户级默认都不让删
                                            // (!item.linkerLevelDefault && !item.isDefault) &&
                                            !item.isDefault && (
                                                <div className="item-added-footer-item">
                                                    <i
                                                        fieldid="ublinker-routes-home-components-ConnecterList-index-1540696-i"
                                                        className={`ipaas iPS-delete-public-line`}
                                                    ></i>
                                                    <Button
                                                        fieldid="ublinker-routes-home-components-ConnecterList-index-2180484-Button"
                                                        type="text"
                                                        onClick={onDelete.bind(null, item)}
                                                        bordered
                                                    >
                                                        {window.lang.template(commonText.deletion)}
                                                    </Button>
                                                </div>
                                            )
                                        }
                                    </div>
                                ) : (
                                    <div className="item-added-footer item-added-footer-empty"></div>
                                )}
                            </>
                        )}
                    </Fragment>
                    {/* {
                            item.linkerLevelDefault && <Tooltip fieldid="ublinker-routes-home-components-ConnecterList-index-4670188-Tooltip" inverse placement="bottomLeft" overlay={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281466") /* 当前连接器的默认连接配置 *}>
                                <div className='default-icon'>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281467") /* 默认 *}</div>
                            </Tooltip>
                        } */}
                </div>
            </li>
        );
    });

    // <Empty fieldid="ublinker-routes-home-components-ConnecterList-index-4058050-Empty"
    //     className="sys-connector-common-empty"
    //     img={emptyImg}
    //     style={emptyStyle}
    //     info={
    //         <span>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050353") /* "暂无连接，" */}<a fieldid="ublinker-routes-home-components-ConnecterList-index-5247207-a" onClick={navToAdd.bind(null, 'erp')}>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050358") /* "添加连接" */}</a></span>
    //     }
    // />
};
const GetCardWrap = ({ item, dataSource, props, cancelDefaultIndex, setCancelDefaultIndex }) => {
    // let bb = [...item.items, ...item.items, ...item.items, ...item.items, ...item.items]
    // const [aa, setAa] = useState(bb)
    const [fold, setFold] = useState(true);

    const handleNewConnect = (item) => {
        props.handleCreateModalOk(item, "add");
    };
    const handleFold = (items) => {
        setFold(!fold);
        // if(fold){

        //     setAa(items.slice(0,5))
        // }else{
        //     setAa(bb)
        // }
    };
    return (
        <div className="cards-wrap">
            <div className="cards-wrap-top">
                {item.fromType == 1 ? (
                    <Logo
                        className="item-added-header-icon"
                        logo={getCompleteImg(item.logo)}
                        title={item.alias || item.name ? (item.alias || item.name).substr(0, 2) : item.alias || item.name}
                    />
                ) : (
                    <ConnectorLogo className="item-added-header-icon" logo={item.logo} title={item.name} code={item.code} />
                )}
                <div className="cards-wrap-top-info">
                    <div className="top-info-top">
                        <span className="name">{item.name}</span>
                        <Button
                            fieldid="ublinker-routes-home-components-ConnecterList-index-2560012-Button"
                            className="ucg-mar-l-10 "
                            onClick={() => {
                                handleNewConnect(item);
                            }}
                        >
                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281452") /* 新建连接 */}
                        </Button>
                    </div>
                    <div className="top-info-bottom">
                        {/* <span className='bottom-list'><span className='bottom-list-label'>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281468") /* 连接器编码 *}</span><span>{item.adapterCode}</span></span> */}
                        <span className="bottom-list">
                            <span className="bottom-list-label">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281490") /* 最新版本 */}</span>
                            <span>{item.version}</span>
                        </span>
                        <span className="bottom-list">
                            <span className="bottom-list-label">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281469") /* 发布时间 */}</span>
                            <span>{item.releaseTime}</span>
                        </span>
                        <span className="bottom-list">
                            <span className="bottom-list-label">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281471") /* 说明 */}</span>
                            <span>{item.description}</span>
                        </span>
                    </div>
                </div>
                <div className="cards-wrap-top-all">
                    <Button fieldid="ublinker-routes-home-components-ConnecterList-index-4922910-Button" type="text" onClick={handleFold}>
                        {
                            fold
                                ? window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281472") + "(" + item.items.length + ")" /* 全部显示 */
                                : window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202262814103") /*'折叠' */
                        }
                    </Button>
                </div>
            </div>
            <div className="cards-wrap-bottom">
                {
                    <ul className={`sys-connector-common-list ${true ? "cur" : ""}`}>
                        {getCardList(fold ? item.items.slice(0, 5) : item.items, item, props, cancelDefaultIndex, setCancelDefaultIndex)}
                        <li style={{ width: "19%" }}></li>
                        <li style={{ width: "19%" }}></li>
                        <li style={{ width: "19%" }}></li>
                        <li style={{ width: "19%" }}></li>
                        <li style={{ width: "19%" }}></li>
                    </ul>
                }
            </div>
        </div>
    );
};
const ConnectorList = (props) => {
    let { dataSource, type: cardType, searchKey, loaded, onDelete, onAdd, onEdit, navToAdd, changeCreateModalShow } = props;

    const [cancelDefaultIndex, setCancelDefaultIndex] = useState("");
    const handleStateChange = (item) => {
        // if (item.linkerState === 1) {
        //     item.linkerState = 0;
        // }
        // else {
        //     item.linkerState = 1;
        // }
        // updateConnectState({
        //     id: item.id,
        //     state: item.linkerState
        // });
        // props.updateTabDataSourceState(item.id, item.linkerState);
    };
    if (loaded) {
        if (dataSource && dataSource.length > 0) {
            return (
                <>
                    {dataSource.map((item, index) => {
                        return (
                            <div key={index} className="cards-wrap-tem">
                                <GetCardWrap
                                    item={item}
                                    dataSource={dataSource}
                                    props={props}
                                    cancelDefaultIndex={cancelDefaultIndex}
                                    setCancelDefaultIndex={setCancelDefaultIndex}
                                />
                                {/* {getCardWrap(item, dataSource, props, cancelDefaultIndex, setCancelDefaultIndex)} */}
                            </div>
                        );
                    })}
                </>
            );
        } else {
            return (
                <div className="cards-wrap-tem-empty">
                    <div className="cards-wrap">
                        <div className="cards-wrap-top">
                            <Logo className="item-added-header-icon empty" logo={defaultAvatar} />
                            {/* <div className='item-added-header-icon'>
                        <img fieldid="ublinker-routes-home-components-ConnecterList-index-6563462-img" src={defaultAvatar} alt=""/>
                        </div> */}
                            <div className="cards-wrap-top-info">
                                <div className="top-info-top">
                                    <span className="name">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281473") /* 连接器名称 */}</span>
                                </div>
                                <div className="top-info-bottom">
                                    <span className="bottom-list">
                                        <span className="bottom-list-label">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281468") /* 连接器编码 */}</span>
                                        <span>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281474") /* 暂无 */}</span>
                                    </span>
                                    <span className="bottom-list">
                                        <span className="bottom-list-label">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281490") /* 最新版本 */}</span>
                                        <span>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281474") /* 暂无 */}</span>
                                    </span>
                                    <span className="bottom-list">
                                        <span className="bottom-list-label">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281469") /* 发布时间 */}</span>
                                        <span>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281474") /* 暂无 */}</span>
                                    </span>
                                    <span className="bottom-list">
                                        <span className="bottom-list-label">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281471") /* 说明 */}</span>
                                        <span>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281474") /* 暂无 */}</span>
                                    </span>
                                </div>
                            </div>
                            <div className="cards-wrap-top-all"></div>
                        </div>
                        <div className="cards-wrap-bottom">
                            <div className="cards-wrap-bottom-content">
                                <EmptyTinper fieldid="demo_1" />
                                {/* <img fieldid="ublinker-routes-home-components-ConnecterList-index-4843874-img" src={emptyImg} alt="" />
                            <div className='cards-wrap-bottom-content-word'>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281475") /* 暂无连接配置 }</div> */}
                                <div>
                                    <Button
                                        fieldid="ublinker-routes-home-components-ConnecterList-index-6646850-Button"
                                        className="cards-wrap-bottom-content-btn"
                                        onClick={changeCreateModalShow}
                                    >
                                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281452") /* 新建连接 */}
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            );
        }
    } else {
        return null;
    }
};

export default ConnectorList;
