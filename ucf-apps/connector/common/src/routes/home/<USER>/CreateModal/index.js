import React, { useState, useEffect } from "react";
import Modal from "components/TinperBee/Modal";
import { Button, FormList } from "components/TinperBee";
import { autoServiceMessage } from "utils/service";
import ConfigureConnectMessage from "../ConfigureConnectMessage";
import { getLinkerSetParam } from "../../service";
import { getAllVersionService } from "services/common";
import { createCommonLinkConfig, editCommonLinkConfig, commonLinkTest } from "../../service";
import { Success, Error } from "utils/feedback";
import Highlight from "components/Highlight";
import { getCompleteImg } from "utils";
import Logo from "components/Card/Logo";
import ConnectorLogo from "components/ConnectorLogo";
import "./index.less";

// 添加连接弹窗
const AddLinkerModal = (props) => {
    const [form] = FormList.useForm();
    // debugger
    const {
        show,
        allConnector,
        onCancel,
        onOk,
        ModalTitle,
        ModalType,
        ownerStore,
        ownerState: { createModalSelectedIndex, createModalSelectedItem },
    } = props;

    const [selectedIndex, setSelectedIndex] = useState();
    const [connectorCode, setConnectorCode] = useState("");
    const [connectorName, setConnectorName] = useState("");
    const [connectorId, setConnectorId] = useState("");
    const [connectorAdapterCode, setConnectorAdapterCode] = useState("");
    const [selectedItem, setSelectedItem] = useState({});

    // 表单字段验证方法
    const { validateFields, getFieldProps, getFieldError } = form;

    // 取消
    const handleCancel = () => {
        onCancel("cancel");
    };
    useEffect(() => {
        //第二个框点上一步返回回写状态
        // if(ModalType=='pre'){
        //     setSelectedIndex(createModalSelectedIndex)
        //     setSelectedItem(createModalSelectedItem)
        // }
    }, [ModalType]);
    // 确认
    const handleCommit = () => {
        // onOk(connectorCode,connectorName,connectorId,connectorAdapterCode,'add','isCreateAll')
        onOk(selectedItem, "add", "isCreateAll");
    };
    const handleClickItem = (index, selectedItem) => {
        setSelectedIndex(index);
        setSelectedItem(selectedItem);
        ownerStore.rememberSelectedMsgInCreateModal(index, selectedItem);
    };
    return (
        <>
            <Modal
                fieldid="ublinker-routes-home-components-CreateModal-index-8823734-Modal"
                title={ModalTitle}
                show={show}
                height={600}
                onCancel={handleCancel}
                onOk={handleCommit}
                okText={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281476") /* 下一步 */}
                // okDisabled ={isSureDisable}
                // extendBtnLocation="pre"
            >
                <div className="allCommons">
                    <div className="allCommon-warp">
                        {allConnector.map((item, index) => {
                            return (
                                <div
                                    key={index}
                                    className={`allCommon-item ${selectedIndex == index ? "cur" : ""}`}
                                    onClick={() => {
                                        handleClickItem(index, item);
                                    }}
                                >
                                    {/* <Logo
                                            className='item-add-header-icon'
                                        logo={getCompleteImg(item.logo)}
                                        title={(item.alias || item.name) ? (item.alias || item.name).substr(0, 2) : (item.alias || item.name)}
                                        /> */}

                                    {item.fromType == 1 ? (
                                        <Logo
                                            className="item-add-header-icon"
                                            logo={getCompleteImg(item.logo)}
                                            title={item.alias || item.name ? (item.alias || item.name).substr(0, 2) : item.alias || item.name}
                                        />
                                    ) : (
                                        <ConnectorLogo className="item-add-header-icon" logo={item.logo} title={item.name} code={item.code} />
                                    )}

                                    <div className="item-add-header-content">
                                        <Highlight
                                            className="item-add-header-content-name"
                                            content={item.alias || item.name}
                                            // keyword={searchKey}
                                        />
                                        <div title={item.description} className="item-add-header-content-description">
                                            {item.description}
                                        </div>
                                    </div>
                                </div>
                            );
                        })}

                        <div style={{ width: "32%" }}></div>
                        <div style={{ width: "32%" }}></div>
                    </div>
                </div>
            </Modal>
        </>
    );
};

export default AddLinkerModal;
