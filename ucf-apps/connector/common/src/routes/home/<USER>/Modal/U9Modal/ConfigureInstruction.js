import React, { useState } from "react";
import { Popover, Steps, Icon, Button } from "components/TinperBee";
import { getLocalImg } from "utils/index";
const { Step } = Steps;

const Desc1 = (
    <div>
        <div>
            {
                window.lang.template(
                    "MIX_UBL_ALL_UBL_FE_LOC_20226281493"
                ) /* 安装客户端后，请使用*****************登录客户端。只有开放企业数据的用户、开发者的测试账户才能登录。 */
            }
        </div>
        <div>
            <img
                fieldid="ublinker-home-components-Modal-U9Modal-ConfigureInstruction-5670022-img"
                className="help-example1"
                src={getLocalImg("eclogo/clientPara2.png")}
            />
        </div>
    </div>
);
const Desc2 = (
    <div>
        <div>
            {
                window.lang.template(
                    "MIX_UBL_ALL_UBL_FE_LOC_20226281494"
                ) /* 如果应用用到了U8的API，必须配置系统类型为U8+的参数。配置U8+的参数，需要先配置好U8+的EAI。系统编码为：999 */
            }
        </div>
        <img
            fieldid="ublinker-home-components-Modal-U9Modal-ConfigureInstruction-4419556-img"
            className="help-example2"
            src={getLocalImg("eclogo/clientPara411.png")}
        />
        <img
            fieldid="ublinker-home-components-Modal-U9Modal-ConfigureInstruction-9584703-img"
            className="help-example2"
            src={getLocalImg("eclogo/clientPara412.png")}
        />
    </div>
);
const popContent = (setVisible) => (
    <>
        <div className="configure-pop-overlay-title">
            <span>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202283012") /* 连接配置说明 */}</span>
            <img
                fieldid="ublinker-home-components-Modal-U9Modal-ConfigureInstruction-8888229-img"
                src={getLocalImg("assetPack/pop-close.png")}
                className="configure-pop-overlay-close"
                onClick={() => setVisible(false)}
            />
        </div>
        <div className="configure-pop-overlay-title2">
            <span>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202283013") /* U8 OpenAPI 配置说明 */}</span>
        </div>
        {/* <div className='configure-pop-overlay-display'></div> */}
        {/* <header className='configure-pop-overlay-header'>
            <img fieldid="ublinker-home-components-Modal-U9Modal-ConfigureInstruction-9209704-img" src={getLocalImg('assetPack/help-hint.png')} />
            <span>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281496") /* 请按照下面步骤进行U8开放平台客户端的安装、配置 *}</span>
        </header> */}
        {/* <Steps fieldid="ublinker-home-components-Modal-U9Modal-ConfigureInstruction-3285384-Steps" direction="vertical" size="small" type="number" current={2}>
            <Step title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281497") /* 第一步：登录U8 OpenAPI客户端 *} icon={<img fieldid="ublinker-home-components-Modal-U9Modal-ConfigureInstruction-3054117-img" src={getLocalImg('assetPack/help4.svg')} />} description={Desc1} />
            <Step title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281498") /* 第二步：U8 OpenAPI 配置说明 *} icon={<img fieldid="ublinker-home-components-Modal-U9Modal-ConfigureInstruction-8982496-img" src={getLocalImg('assetPack/help3.svg')} />} description={Desc2} />
        </Steps> */}
        <div>
            <div>
                {
                    window.lang.template(
                        "MIX_UBL_ALL_UBL_FE_LOC_20226281494"
                    ) /* 如果应用用到了U8的API，必须配置系统类型为U8+的参数。配置U8+的参数，需要先配置好U8+的EAI。系统编码为：999 */
                }
            </div>
            <img
                fieldid="ublinker-home-components-Modal-U9Modal-ConfigureInstruction-4256603-img"
                className="help-example2"
                src={getLocalImg("eclogo/clientPara411.png")}
            />
            <img
                fieldid="ublinker-home-components-Modal-U9Modal-ConfigureInstruction-8033505-img"
                className="help-example3"
                src={getLocalImg("eclogo/clientPara412.png")}
            />
        </div>
    </>
);
const GatewayHelpModal = (props) => {
    const { placement, overlayMaxHeight } = props;
    const [visible, setVisible] = useState(false);
    return (
        <Popover
            fieldid="ublinker-home-components-Modal-U9Modal-ConfigureInstruction-1675518-Popover"
            overlayMaxHeight={true}
            arrowPointAtCenter={true}
            placement={placement || "rightTop"}
            overlayClassName="configure-pop-overlay"
            className="configure-pop"
            trigger="hover"
            show={visible} //
            autoAdjustOverflow={false}
            content={() => popContent(setVisible)}
            onHide={() => setVisible(false)}
        >
            <div
                style={{ marginLeft: 10 }}
                onMouseEnter={() => setVisible(true)}
                title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202283011") /* 配置说明 */}
            >
                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202283011") /* 配置说明 */}
            </div>
        </Popover>
    );
};

export default GatewayHelpModal;
