import React, { useState, useEffect, useCallback } from "react";
import Modal from "components/TinperBee/Modal";
import { <PERSON>ton, FormList, FormControl, Select, Col, Tooltip, Radio, Checkbox, Progress } from "components/TinperBee";
import Grid from "components/TinperBee/Grid";
import { autoServiceMessage } from "utils/service";
import { a_download } from "utils/index";
import {
    createCommonLinkConfig,
    editCommonLinkConfig,
    commonLinkTest,
    downloadAdapterService,
    downloadClientService,
    getcode,
    getOrgsService,
    getGwAppSecretService,
    downloadClient,
} from "../../../service";
import { Success, Error } from "utils/feedback";
import { ipReg, portReg, codeReg } from "utils/regExp";
import ConfigureInstruction from "./ConfigureInstruction";
import { saveAs } from "file-saver";
import { getInvokeService } from "utils/service";
import commonText from "constants/commonText";
import "./index.less";

const FormItem = FormList.Item;
const Option = Select.Option;
const RadioGroup = Radio.Group;
const columnObj = {
    ncAddr: [
        {
            title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050433") /* "账套编码" */,
            dataIndex: "accountcode",
        },
        {
            title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050283") /* "集团编码" */,
            dataIndex: "groupcode",
        },
    ],
    orgcode: [
        {
            title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20227131845") /* "组织编码" */,
            dataIndex: "code",
        },
        {
            title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20227131846") /* "名称" */,
            dataIndex: "name",
        },
        {
            title: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20227131847") /* "id" */,
            dataIndex: "id",
        },
    ],
};
// 动态类型组件
const DynamicDomByType = (props) => {
    const {
        type,
        data,
        changeInfo,
        setTestButtonDisable,
        ModalCode,
        ModalTemplates,
        index,
        getFieldValue,
        validateFields,
        setFieldsValue,
        setOldConfig,
        isAdd,
    } = props;
    // debugger
    // 文本、整数、多行文本框、单选、下拉框变化
    const handleChange = (data, value) => {
        data.value = value;
        // ModalTemplates[index].value=value
        changeInfo();
        // setTestButtonDisable(true);
    };

    // 下拉框变化
    const handleCheckChange = (item, value) => {
        item.defaultItem = value;
        changeInfo();
        // setTestButtonDisable(true);
    };
    const GroupClickNcAddr = async (ModalCode, data) => {
        let { code, value } = data;
        console.log(ModalCode, code, value, getFieldValue("gatewayId"), getFieldValue("erpversion"));
        let param = {
            ncurl1: value, //'http://172.20.47.43:18801' ||
            gatewayId: getFieldValue("gatewayId"), //'7Z_HHOENQd18z9AZWrRCkR' ||
            erpversion: getFieldValue("erpversion"),
        };
        let res = await autoServiceMessage({
            service: getcode(param),
        });
        if (res && res.data && res.data.length > 0) {
            setOldConfig({
                configList: res.data,
                selectedList: [],
                oldConfigModalShow: true,
                itemCode: "ncAddr",
                rowKey: "id",
            });
        }
    };
    const GroupClickOrgcode = async (ModalCode, data) => {
        let { code, value } = data;
        let res = await autoServiceMessage({
            service: getOrgsService(),
        });
        if (res && res.data) {
            setOldConfig({
                configList: res.data,
                selectedList: [],
                oldConfigModalShow: true,
                itemCode: "orgcode",
                rowKey: "id",
            });
        }
    };
    const GroupClickAppSecret = (ModalCode, data) => {
        // debugger
        validateFields(["appAddress"]).then(async (values) => {
            let res = await autoServiceMessage({
                service: getGwAppSecretService(values.appAddress),
            });
            if (res) {
                // setFieldsValue({ appSecret :res.gwsecret});
                data.value = res.gwsecret;
                changeInfo();
            } else {
                // debugger
                // setFieldsValue({ appSecret :123});
                // data.value=123
                // changeInfo();
            }
        });
    };

    // 按钮点击
    const handleClick = (data) => {
        if (data.code == "orgcode" && !isAdd) {
            return;
        }
        // debugger
        switch (ModalCode) {
            case "U8NativeSQL":
                switch (data.code) {
                    case "orgcode": //组织编码
                        GroupClickOrgcode(ModalCode, data);
                        break;
                    default:
                        null;
                }
                break;
            case "U8OpenApiHandler":
                break;
            case "YonBipMajorHandler":
            case "U8CHandler":
                switch (data.code) {
                    case "ncAddr": //erp版本  获取参数
                        GroupClickNcAddr(ModalCode, data);
                        break;
                    default:
                        null;
                }
                break;
            case "NCHandler":
            case "NCCHandler":
                switch (data.code) {
                    case "ncAddr": //erp版本  获取参数
                        GroupClickNcAddr(ModalCode, data);
                        break;
                    case "appSecret": //erp版本  获取混合云密钥
                        GroupClickAppSecret(ModalCode, data);
                        break;
                    default:
                        null;
                }
                break;
            case "U9PlatformHandler":
                break;
            default:
                null;
        }
    };

    let dom = null;
    switch (type) {
        case "Password":
        case "Text":
            // 文本输入框
            dom = (
                <FormItem
                    fieldid="ublinker-home-components-Modal-U9Modal-index-9198457-FormItem"
                    label={data.name}
                    name={data.code}
                    initialValue={data.value}
                    hidden={data.isShow == undefined ? false : !data.isShow}
                    validateTrigger="onChange"
                    rules={[
                        {
                            required: data.required,
                            message: `${window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050914") /* "请输入" */}${data.name}`,
                        },
                    ]}
                >
                    <FormControl
                        fieldid="ublinker-home-components-Modal-U9Modal-index-4279831-FormControl"
                        disabled={data.disable == undefined ? false : data.disable}
                        className="ucg-mar-r-5"
                        type={data.type == "Password" ? "password" : "text"}
                        onChange={(value) => {
                            handleChange(data, value);
                        }}
                    />
                </FormItem>
            );
            break;
        case "Integer":
            // 整数输入框
            dom = (
                <FormItem
                    fieldid="ublinker-home-components-Modal-U9Modal-index-5888064-FormItem"
                    label={data.name}
                    initialValue={data.value}
                    name={data.code}
                    validateTrigger="onChange"
                    rules={[
                        {
                            required: data.required,
                            message: `${window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050914") /* "请输入" */}${data.name}`,
                        },
                    ]}
                >
                    <FormControl
                        fieldid="ublinker-home-components-Modal-U9Modal-index-7415222-FormControl"
                        className="ucg-mar-r-5"
                        type="number"
                        onChange={(value) => {
                            handleChange(data, value);
                        }}
                    />
                </FormItem>
            );
            break;
        case "Textarea":
            // 多行文本输入框
            dom = (
                <FormItem
                    fieldid="ublinker-home-components-Modal-U9Modal-index-3468696-FormItem"
                    label={data.name}
                    initialValue={data.value}
                    name={data.code}
                    validateTrigger="onChange"
                    rules={[
                        {
                            required: data.required,
                            message: `${window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050914") /* "请输入" */}${data.name}`,
                        },
                    ]}
                >
                    <div className="muti-row-textarea">
                        <FormControl.TextArea
                            rows={4}
                            className="ucg-mar-r-5"
                            componentClass="textarea"
                            onChange={(value) => {
                                handleChange(data, value);
                            }}
                        />
                    </div>
                </FormItem>
            );
            break;
        case "Checkbox":
            // 复选框
            dom = (
                <FormItem
                    fieldid="ublinker-home-components-Modal-U9Modal-index-1753253-FormItem"
                    label={data.name}
                    required={data.required}
                    name={data.code}
                    initialValue={data.value === null ? (data.itemList.find((item) => item.defaultItem) || "").realValue || "" : data.value}
                >
                    <div>
                        {data.itemList &&
                            data.itemList.map((item) => {
                                return (
                                    <Checkbox
                                        fieldid="ublinker-home-components-Modal-U9Modal-index-306987-Checkbox"
                                        checked={item.defaultItem}
                                        // value={item.realValue}
                                        colors="dark"
                                        onChange={(value) => {
                                            handleCheckChange(item, value);
                                        }}
                                    >
                                        {item.displayValue}
                                    </Checkbox>
                                );
                            })}
                    </div>
                </FormItem>
            );
            break;
        case "Select":
            // 下拉列表
            dom = (
                <FormItem
                    fieldid="ublinker-home-components-Modal-U9Modal-index-3809120-FormItem"
                    label={data.name}
                    required={data.required}
                    name={data.code}
                    initialValue={data.value === null ? (data.itemList.find((item) => item.defaultItem) || "").realValue || "" : data.value}
                >
                    <Select
                        fieldid="ublinker-home-components-Modal-U9Modal-index-3777719-Select"
                        onChange={(value, e) => {
                            handleChange(data, value);
                        }}
                    >
                        {data.itemList &&
                            data.itemList.map((item) => {
                                return (
                                    <Option fieldid="UCG-FE-home-components-Modal-U9Modal-index-9155974-Option" value={item.realValue}>
                                        {item.displayValue}
                                    </Option>
                                );
                            })}
                    </Select>
                </FormItem>
            );
            break;
        case "Radio":
            // 单选
            dom = (
                <FormItem
                    fieldid="ublinker-home-components-Modal-U9Modal-index-5514163-FormItem"
                    label={data.name}
                    required={data.required}
                    name={data.code}
                    initialValue={data.value === null ? (data.itemList.find((item) => item.defaultItem) || "").realValue || "" : data.value}
                >
                    <RadioGroup
                        // value={data.value === null ? data.itemList.find(item => item.defaultItem).realValue : data.value}
                        onChange={(value) => {
                            handleChange(data, value);
                        }}
                    >
                        {data.itemList &&
                            data.itemList.map((item) => {
                                return (
                                    <Radio fieldid="ublinker-home-components-Modal-U9Modal-index-6330877-Radio" value={item.realValue}>
                                        {item.displayValue}
                                    </Radio>
                                );
                            })}
                    </RadioGroup>
                </FormItem>
            );
            break;
        case "Group":
            // 输入框组
            dom = (
                <FormItem
                    fieldid="ublinker-home-components-Modal-U9Modal-index-9863343-FormItem"
                    label={data.name}
                    required={data.required}
                    name={data.code}
                    initialValue={data.value}
                    // initialValue={data.value === null ? (data.itemList.find(item => item.defaultItem) || '').realValue || '' : data.value}
                >
                    <FormControl.Group>
                        <FormControl
                            fieldid="ublinker-home-components-Modal-U9Modal-index-3382623-FormControl"
                            type="text"
                            disabled={data.disable == undefined ? false : data.disable}
                            value={data.value}
                            onChange={(value) => {
                                handleChange(data, value);
                            }}
                        />
                        <FormControl.Group.Button
                            onClick={() => {
                                handleClick(data);
                            }}
                        >
                            <Button fieldid="UCG-FE-home-components-Modal-U9Modal-index-1946041-Button" disabled={data.code == "orgcode" && !isAdd}>
                                {data.btnName}
                            </Button>
                        </FormControl.Group.Button>
                    </FormControl.Group>
                </FormItem>
            );
            break;
        default:
            break;
    }
    return dom;
};

// 添加连接弹窗
const AddLinkerModal = (props) => {
    const [form] = FormList.useForm();
    let {
        show,
        isFromCreateModal,
        ModalTitle,
        completeModalTitle,
        ModalCode,
        ModalTemplates,
        ConnectModalData,
        ModalTestable,
        onCancel,
        onOk,
        ModalType,
        addModalData,
        addModalData: { testable },
        editModalData,
        editModalData: { linker },
        gatewayList,
        versionList,
        systemList,
        changeModalTemplates: changeLinkconfig,
        ownerStore,
        ownerState,
        connectorId,
        connectorAdapterCode,
        defaultSelectedVersion,
    } = props;
    // debugger
    // let {versionList}=ownerState
    const [isSureDisable, setIsSureDisable] = useState(testable || (linker && linker.testable)); //新增或编辑时此字段为true,需先测试再点确定，为false不判断

    const [clickedDownloadClient, setClickedDownloadClient] = useState(false);
    const [percent, setPercent] = useState(0);
    const isAdd = ModalType === "add";

    // 表单字段验证方法
    const { validateFields, setFieldsValue, getFieldsValue, getFieldValue } = form;
    // const [ModalTemplates, setModalTemplates] = useState([]);
    // 新增初始化时获取连接模版参数（编辑时已经获取到参数类型和对应值，不需要再重复获取）

    const [emailMsg, setEmailMsg] = useState({ data: {}, isupdate: false });
    useEffect(() => {
        let init = async () => {
            if (isAdd) {
                //    let resVersion= await ownerStore.getAllVersion(connectorId)
                //    let resTemplates =await ownerStore.getLinkerSetParam({linkerId:connectorId,adapterCode: connectorAdapterCode,linkerVersionCode: resVersion.data[0].code})
                //    //
                //    setModalTemplates(resTemplates.data.linkerConfig)
            } else {
                setFieldsValue({
                    erpversion: ConnectModalData.erpversion,
                    alias: ConnectModalData.alias,
                    connectCode: ConnectModalData.connectCode,
                    gatewayId: ConnectModalData.gatewayId,
                });
            }
            if (ModalCode == "U8NativeSQL") {
                let res = await ownerStore.initU8Email({ tenantId: (ConnectModalData && ConnectModalData.tenantId) || "" });
                //     res={"data":{"id":"62cbbf15bc6e1c526cc77315","createtime":"2022-07-11 14:11:33","tenantEmail":"","pk_tenantid":"0000L3KZSSS7D77IGV0000","openapicode":"25ed78a599aa4814b4caf63ea5b24273","userid":"25ed78a5-99aa-4814-b4ca-f63ea5b24273"},"accountType":false,"status":1
                //     // ,isupdate:2
                // }
                if (res.data.tenantEmail) {
                    setEmailMsg(res);
                } else if (res.status == 999) {
                    Error(res.data);
                } else {
                    Error(
                        window.lang.template(
                            "MIX_UBL_ALL_UBL_FE_LOC_202207181106"
                        ) /*'您的用户信息未配置邮箱信息，无法配置U8连接，请配置好邮箱后点击【获取邮箱账号】按钮'*/
                    );
                }
            }
        };
        init();
    }, []);
    let [oldConfig, setOldConfig] = useState({ configList: [], selectedList: [], oldConfigModalShow: false, itemCode: "", rowKey: "" });
    const handleOldConfigModalCancel = useCallback(() => {
        setOldConfig({
            configList: [],
            oldConfigModalShow: false,
            selectedList: [],
            itemCode: "",
            rowKey: "",
        });
    }, []);
    /** 参数配置列表窗台确认onOk */
    const handleOldConfigModalOk = useCallback(() => {
        let { selectedList } = oldConfig;
        let data = selectedList[0];

        switch (ModalCode) {
            case "U8NativeSQL":
                switch (oldConfig.itemCode) {
                    case "orgcode": //组织编码
                        setFieldsValue({
                            orgcode: data.code,
                            orgname: data.name,
                            pk_org: data.id,
                        });
                        ModalTemplates.map((item) => {
                            //这比较特殊，上面赋值之后并不会修改ModalTemplates里面的数据，所以手动修改一下
                            if (item.code == "orgcode") {
                                item.value = data.code;
                            }
                            if (item.code == "orgname") {
                                item.value = data.name;
                            }
                            if (item.code == "pk_org") {
                                item.value = data.id;
                            }
                        });
                        console.log(ModalTemplates);
                        break;
                    default:
                        null;
                }
                break;
            case "YonBipMajorHandler":
            case "NCHandler":
            case "NCCHandler":
            case "U8CHandler":
                switch (oldConfig.itemCode) {
                    case "ncAddr": //erp版本
                        setFieldsValue({
                            accountCode: data.accountcode,
                            groupCode: data.groupcode,
                        });
                        ModalTemplates.map((item) => {
                            //这比较特殊，上面赋值之后并不会修改ModalTemplates里面的数据，所以手动修改一下
                            if (item.code == "accountCode") {
                                item.value = data.accountcode;
                            }
                            if (item.code == "groupCode") {
                                item.value = data.groupcode;
                            }
                        });
                        console.log(ModalTemplates);
                        break;
                    default:
                        null;
                }
                break;
            default:
                null;
        }
        changeLinkconfig();
        handleOldConfigModalCancel();
    }, [oldConfig]);
    /** 参数配置列表选择回调 */
    const getOldConfigSelectedDataFunc = useCallback(
        (selectedList) => {
            setOldConfig({ ...oldConfig, selectedList: selectedList });
        },
        [oldConfig]
    );
    // 取消
    const handleCancel = (type) => {
        onCancel({ code: ModalCode }, type);
    };

    // 确认
    const handleCommit = () => {
        validateFields()
            .then(async (values) => {
                let res = null;
                if (isAdd) {
                    // 说明还未创建连接器,此时走新增接口
                    let postData = {
                        fromType: 1,
                        type: ModalCode,
                        // ...values,
                        erpversion: values.erpversion,
                        alias: values.alias,
                        connectCode: values.connectCode,
                        gatewayId: values.gatewayId,
                        linkconfig: ModalTemplates,
                        // integratedSystemId: selectSystemId,
                    };
                    res = await autoServiceMessage({
                        service: createCommonLinkConfig(postData),
                    });
                } else {
                    // 说明已经创建了连接器,此时走编辑修改接口
                    let postData = {
                        _id: ConnectModalData.id,
                        // erpversion: values.erpversion,
                        alias: values.alias,
                        connectCode: values.connectCode,
                        gatewayId: values.gatewayId,
                        linkconfig: ModalTemplates,
                    };
                    res = await autoServiceMessage({
                        service: editCommonLinkConfig(postData),
                    });
                }
                if (res !== null && res.data !== null) {
                    onCancel({ code: ModalCode }, "submit");
                    // 提示保存成功
                    Success(window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_2021891003") /* 保存成功 */);
                } else {
                    // 提示保存失败
                    Error(window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_2021891004") /* 保存失败 */);
                }
            })
            .catch((errorInfo) => {
                console.log(errorInfo);
            });
    };

    // 点击测试按钮
    const handleTest = () => {
        validateFields()
            .then(async (values) => {
                let postData = {
                    fromType: 1,
                    type: ModalCode,
                    erpversion: values.erpversion,
                    alias: values.alias,
                    connectCode: values.connectCode,
                    gatewayId: values.gatewayId,
                    linkconfig: ModalTemplates,
                    id: !isAdd ? ConnectModalData.id : "",
                    // ...values,
                    // integratedSystemId: selectSystemId,
                };
                let res = await autoServiceMessage({
                    service: commonLinkTest(postData),
                    success: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_2021891005") /* 连接成功 */,
                });
                // if(res){
                //     setIsSureDisable(false)
                // }
            })
            .catch((errorInfo) => {
                console.log(errorInfo);
            });
    };

    // 定义测试按钮
    const getExtendBtn = () => {
        // if (['U8CHandler', 'YonBipMajorHandler', 'NCHandler', 'NCCHandler'].indexOf(ModalCode) > -1) {
        return (
            <>
                {isFromCreateModal && (
                    <Button
                        fieldid="ublinker-home-components-Modal-U9Modal-index-4349412-Button"
                        colors="secondary"
                        className="ucg-mr-10"
                        onClick={() => {
                            handleCancel("pre");
                        }}
                    >
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281477") /* 上一步 */}
                    </Button>
                )}
                {ModalTestable && (
                    <Button fieldid="ublinker-home-components-Modal-U9Modal-index-2318498-Button" colors="dark" className="ucg-mr-10" onClick={handleTest}>
                        {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_2021891006") /* 测试 */}
                    </Button>
                )}
            </>
        );
    };

    const handleVersionChange = async (value) => {
        props.changeVersionGetTemplates(value);
    };
    const formItemLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 14 },
    };

    const setTestButtonDisable = () => {};
    const downloadNcAdapterClick = async () => {
        let erpversion = getFieldValue("erpversion");
        let res = await autoServiceMessage({
            service: downloadAdapterService(ModalCode, erpversion),
        });
        if (res) {
            const blob = res.data;
            const reader = new FileReader();
            reader.readAsDataURL(blob);
            reader.onload = (e) => {
                console.log(e);
                console.log(e.target.readyState, FileReader.DONE);
                if (e.target.readyState === FileReader.DONE) {
                    try {
                        let resultArr = e.target.result.split(",");
                        // console.log(window.atob(resultArr[1]))
                        const errorInfo = JSON.parse(decodeURIComponent(escape(window.atob(resultArr[1])))); //atob base64转ASCII码  decodeURIComponent  escape转义中文字符

                        Error(errorInfo.msg);
                    } catch (b) {
                        const a = document.createElement("a");
                        console.log(res.headers);
                        a.download = res.headers["content-disposition"].split("=")[1];
                        // a.download = `文件名称.zip`;
                        // 后端设置的文件名称在res.headers的 "content-disposition": "form-data; name=\"attachment\"; filename=\"20181211191944.zip\"",
                        a.href = e.target.result;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                    }
                }
            };
            // a_download(res.data.url)
        }
    };
    const handleDownloadClient = async (a, b) => {
        Success(window.lang.template(commonText.downing));
        downloadClient(1.8);

        // setClickedDownloadClient(true)
        // let res = await autoServiceMessage({
        //     service:
        //     // downloadClientService(1.8),
        //     getInvokeService({
        //         method: 'GET',
        //         path: '/mygwapp/ncconnect/openapiclient/download/ossurl/1.88888888',
        //         responseType:'blob',
        //         showLoading:()=>{
        //             Success(window.lang.template(commonText.downing))
        //         },
        //         timeout:300000,
        //         onDownloadProgress: progressEvent => {
        //             if(progressEvent.total<10000){ //如果小于此数,说明失败,失败则不计划百分比,不显示进度条
        //                 return
        //             }
        //             // console.log(progressEvent)
        //             let percentCompleted = Math.ceil((progressEvent.loaded * 100) / progressEvent.total);
        //             setPercent(percentCompleted)
        //             }
        //     })
        // });
        // if (res) {
        //     console.log(res)
        //     setPercent(0)
        //     setClickedDownloadClient(false)
        //     const blob1 = res.data;
        //         const reader = new FileReader();
        //         reader.readAsDataURL(blob1);
        //         reader.onload = (e) => {
        //             console.log(e)
        //             console.log(e.target.readyState,FileReader.DONE)
        //             if (e.target.readyState === FileReader.DONE) {
        //                 try {
        //                     let resultArr=e.target.result.split(',')
        //                     // console.log((resultArr[1]))
        //                     const errorInfo = JSON.parse(decodeURIComponent(escape(window.atob(resultArr[1]))))//atob base64转ASCII码  decodeURIComponent  escape转义中文字符
        //                     console.log(errorInfo)
        //                     Error(errorInfo.msg)
        //                 } catch (b) {
        //                     const blob = new Blob([blob1])
        //                     const url = window.URL.createObjectURL(blob)
        //                     console.log(url)
        //                     saveAs(url, res.headers['content-disposition'].split('=')[1])
        //                 }
        //             }
        //         };

        //         // a_download(res.data,true)
        //         // a_download(res.msg);
        // }else{
        //     setClickedDownloadClient(false)
        //     setPercent(0)
        // }
    };
    const handleGetEmail = async (a, b) => {
        if (emailMsg.data.tenantEmail) {
            //更换
            let res = await ownerStore.changeU8Email({ connectionId: ConnectModalData.id });
            if (res.data.tenantEmail) {
                setEmailMsg(res);
                Success(window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_2022821635") /* 更换成功 */);
            }
        } else {
            //获取
            let res = await ownerStore.getU8Email();
            if (res.data.tenantEmail) {
                setEmailMsg(res);
            } else {
                Error(
                    window.lang.template(
                        "MIX_UBL_ALL_UBL_FE_LOC_202207181106"
                    ) /*'您的用户信息未配置邮箱信息，无法配置U8连接，请配置好邮箱后点击【获取邮箱账号】按钮'*/
                );
            }
        }
    };
    const renderInstall = () => {
        // U8NativeSQL:false,
        // U8OpenApiHandler:false,
        // U9PlatformHandler:false,
        // U8CHandler:false,
        // HRPS: false,
        // YonBipMajorHandler:false,
        // NCHandler:false,
        // NCCHandler:false,
        // YONBIP:false,
        switch (ModalCode) {
            case "U8NativeSQL":
                return (
                    <>
                        <div className="formItem-title" style={{ marginLeft: 81 }}>
                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281478") /* U8开放平台客户端下载 */}
                            {/* <ConfigureInstruction /> */}
                            {/* <span className="formItem-title-two">U8开放平台客户端配置说明</span> */}
                        </div>
                        <div className="formItem-download">
                            <Button
                                fieldid="ublinker-home-components-Modal-U9Modal-index-5375146-Button"
                                className="ucg-mar-l-0 "
                                disabled={clickedDownloadClient}
                                onClick={handleDownloadClient}
                            >
                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281479") /* 下载客户端 */}
                            </Button>
                            {/* <span className='formItem-download-tip'>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281480")}</span> */}
                        </div>
                        <div className="formItem-download-box">
                            <div className="formItem-download">
                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202207181100") /* 1、若已安装，可跳过此步骤 */}
                            </div>
                            <div className="formItem-download">
                                {
                                    window.lang.template(
                                        "MIX_UBL_ALL_UBL_FE_LOC_20226281480"
                                    ) /* 2、下载U8开放平台客户端，并安装在可同时访问U8服务器与外网的Windows系统中 */
                                }
                            </div>
                            <div className="formItem-download">
                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202207181101") /* 3、启动并登录U8开放平台客户端：账号（如下图所示）、 */}
                                <span style={{ color: "#333333", fontWeight: "bold" }}>
                                    {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202207181102") /* 密码（邮箱对应用户在公有云的登录密码） */}
                                </span>
                            </div>
                        </div>
                        <FormItem
                            fieldid="ublinker-home-components-Modal-U9Modal-index-6700892-FormItem"
                            labelCol={{ span: 5 }}
                            wrapperCol={{ span: 19 }}
                            className="formItem-download-input"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202207181103") /* "客户端登录账号" */}
                            name="erpversion"
                            validateTrigger="onChange"
                            rules={[{ required: true }]}
                            initialValue={defaultSelectedVersion}
                            style={{ color: "#666666" }}
                        >
                            <input
                                className="formItem-download-input-input"
                                disabled
                                placeholder={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202207181107") /*'暂无账号'*/}
                                value={emailMsg.data.tenantEmail}
                            />
                            {(!emailMsg.data.tenantEmail || (emailMsg.isupdate && !isAdd)) && (
                                <Button
                                    fieldid="ublinker-home-components-Modal-U9Modal-index-7747746-Button"
                                    type="text"
                                    bordered
                                    style={{ float: "right", background: "#EFF1F7" }}
                                    onClick={handleGetEmail}
                                >
                                    {
                                        !emailMsg.data.tenantEmail
                                            ? window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202207181104") /* 获取当前账号*/
                                            : emailMsg.isupdate
                                              ? window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202207181105") /*更换为当前账号*/
                                              : lang.templateByUuid("UID:P_UBL-FE_200960400428018E", "不显示") /* "不显示" */
                                    }
                                </Button>
                            )}
                        </FormItem>
                    </>
                );
            case "U8CHandler":
            case "NCHandler":
            case "NCCHandler":
                return (
                    <>
                        <div className="formItem-title" style={{ marginLeft: 81 }}>
                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281483") /* 安装补丁 */}
                        </div>
                        <div className="formItem-download">
                            <Button
                                fieldid="ublinker-home-components-Modal-U9Modal-index-7436753-Button"
                                className="ucg-mar-l-0 "
                                onClick={downloadNcAdapterClick}
                            >
                                {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281481") /* 下载补丁 */}
                            </Button>
                            <span className="formItem-download-tip">
                                {
                                    window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281482", {
                                        ModalTitle: ModalTitle.slice(0, ModalTitle.length - 3),
                                    }) /* 把下载的补丁打到{ModalTitle}中，重启{ModalTitle} */
                                }
                            </span>
                        </div>
                    </>
                );
            case "U9PlatformHandler":
                break;
            default:
                null;
        }
    };
    return (
        <>
            <Modal
                fieldid="ublinker-home-components-Modal-U9Modal-index-7518987-Modal"
                title={completeModalTitle}
                show={show}
                height={600}
                onCancel={() => {
                    handleCancel("cancel");
                }}
                onOk={handleCommit}
                // okDisabled ={isSureDisable}
                extendBtn={getExtendBtn}
                extendBtnLocation="pre"
                cancelText={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281484") /* 取消 */}
                cancelHide={isFromCreateModal}
            >
                {ModalTemplates && (
                    <FormList
                        fieldid="ublinker-home-components-Modal-U9Modal-index-9526943-FormList"
                        form={form}
                        {...formItemLayout}
                        className="config-action-form config-action-form-connect-message"
                    >
                        {renderInstall()}
                        {/* <FormItem fieldid="ublinker-home-components-Modal-U9Modal-index-7717843-FormItem" 
                        label={'基本信息'}
                        className='formItem-title'
                    >
                    </FormItem> */}
                        <div className="formItem-title" style={{ marginLeft: 81 }}>
                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281485") /* 基本信息 */}
                        </div>

                        <FormItem
                            fieldid="ublinker-home-components-Modal-U9Modal-index-3065310-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050443") /* "版本" */}
                            name="erpversion"
                            validateTrigger="onChange"
                            rules={[{ required: true }]}
                            initialValue={defaultSelectedVersion}
                        >
                            <Select
                                fieldid="ublinker-home-components-Modal-U9Modal-index-190278-Select"
                                disabled={!isAdd}
                                onChange={(value, e) => {
                                    handleVersionChange(value);
                                }}
                                // value={selectVersion}
                            >
                                {versionList.map((item) => {
                                    return (
                                        <Option fieldid="UCG-FE-home-components-Modal-U9Modal-index-8735351-Option" value={item.code}>
                                            {item.description}
                                        </Option>
                                    );
                                })}
                            </Select>
                        </FormItem>

                        <FormItem
                            fieldid="ublinker-home-components-Modal-U9Modal-index-9117299-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050928") /* "连接名称" */}
                            required={true}
                            name="alias"
                            validateTrigger="onChange"
                            rules={[
                                {
                                    required: true,
                                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050933") /* "请输入连接名称" */,
                                },
                            ]}
                            // initialValue={alias}
                        >
                            <FormControl
                                fieldid="ublinker-home-components-Modal-U9Modal-index-7833033-FormControl"
                                className="ucg-mar-r-5"
                                // onChange={(value) => { handleAliasChange(value) }}
                            />
                        </FormItem>

                        <FormItem
                            fieldid="ublinker-home-components-Modal-U9Modal-index-7979114-FormItem"
                            label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050932") /* "连接编码" */}
                            name="connectCode"
                            validateTrigger="onChange"
                            rules={[
                                {
                                    required: true,
                                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050925") /* "请输入连接编码" */,
                                },
                                {
                                    pattern: codeReg,
                                    message: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050929") /* "连接编码由英文、数字、下划线组成" */,
                                },
                            ]}
                            // initialValue={connectCode}
                        >
                            <FormControl
                                fieldid="ublinker-home-components-Modal-U9Modal-index-3573044-FormControl"
                                disabled={!isAdd}
                                className="ucg-mar-r-5"
                                // onChange={(value) => { handleConnectCodeChange(value) }}
                            />
                        </FormItem>

                        {ModalCode != "U8NativeSQL" && (
                            <FormItem
                                fieldid="ublinker-home-components-Modal-U9Modal-index-2299975-FormItem" //u8的时候隐藏网关
                                label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050144") /* "网关" */}
                                name="gatewayId"
                                // initialValue={selectGatewayId}
                            >
                                <Select
                                    fieldid="ublinker-home-components-Modal-U9Modal-index-3985808-Select"
                                    disabled={!isAdd && (!ConnectModalData.fromType || ConnectModalData.fromType != 1)} //编辑 && fromType: 1 时可编辑
                                    showSearch
                                    optionFilterProp="children"
                                    // value={selectGatewayId}
                                    // onChange={(value, e) => { handleGateWayChange(value) }}
                                >
                                    {gatewayList.map((item, index) => {
                                        return (
                                            <Option fieldid="UCG-FE-home-components-Modal-U9Modal-index-5406104-Option" key={index} value={item.gatewayID}>
                                                {item.name}
                                            </Option>
                                        );
                                    })}
                                </Select>
                            </FormItem>
                        )}
                        <div className="formItem-title" style={{ marginLeft: 81 }}>
                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281486") /* 连接配置 */}
                            {ModalCode == "U8NativeSQL" && <ConfigureInstruction />}
                        </div>
                        {ModalTemplates &&
                            ModalTemplates.map((item, index) => {
                                return (
                                    <div className="groupadminBox" key={index}>
                                        <DynamicDomByType
                                            changeInfo={changeLinkconfig}
                                            setTestButtonDisable={setTestButtonDisable}
                                            type={item.type}
                                            data={item}
                                            ModalTemplates={ModalTemplates}
                                            index={index}
                                            ModalCode={ModalCode}
                                            getFieldValue={getFieldValue}
                                            validateFields={validateFields}
                                            setFieldsValue={setFieldsValue}
                                            setOldConfig={setOldConfig}
                                            isAdd={isAdd}
                                        />
                                        {item.description && (
                                            <div className={`groupadminBox-tip ${item.type == "Radio" ? "radio" : ""}`}>
                                                <Tooltip
                                                    fieldid="ublinker-home-components-Modal-U9Modal-index-7623767-Tooltip"
                                                    inverse
                                                    overlay={item.description}
                                                >
                                                    <i
                                                        fieldid="ublinker-home-components-Modal-U9Modal-index-3794223-i"
                                                        className="cl cl-Q ucg-pad-l-5"
                                                        style={{ color: "#505766" }}
                                                    />
                                                </Tooltip>
                                            </div>
                                        )}
                                    </div>
                                );
                            })}
                    </FormList>
                )}
                {percent ? (
                    <Progress
                        fieldid="UCG-FE-home-components-Modal-U9Modal-index-925768-Progress"
                        percent={percent}
                        style={{ position: "fixed", left: "1%", top: "7%" }}
                    />
                ) : null}
            </Modal>
            <Modal
                fieldid="ublinker-home-components-Modal-U9Modal-index-5020702-Modal"
                title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050621") /* "选择配置参数" */}
                show={oldConfig.oldConfigModalShow}
                onCancel={handleOldConfigModalCancel}
                onOk={handleOldConfigModalOk}
                size="md"
            >
                {oldConfig.oldConfigModalShow ? (
                    <Grid
                        fieldid="ublinker-home-components-Modal-U9Modal-index-6634781-Grid"
                        columns={columnObj[oldConfig.itemCode]}
                        radioSelect
                        rowKey={oldConfig.rowKey}
                        selectedList={oldConfig.selectedList}
                        getSelectedDataFunc={getOldConfigSelectedDataFunc}
                        data={oldConfig.configList}
                    />
                ) : null}
            </Modal>
        </>
    );
};

export default AddLinkerModal;
