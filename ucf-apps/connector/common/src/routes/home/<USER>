import { observable, action, toJS, reaction, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { defaultListMap } from "utils/pageListUtils";
import { autoServiceMessage } from "utils/service";
import { defaultPagination, selfPageChange } from "utils/pageListUtils";
import * as ownerService from "./service";
import { getAllVersionService } from "services/common";
import commonText from "constants/commonText";
import core from "core/index";
const initState = {
    tabs: [
        {
            key: "self",
            type: "added",
            name: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_202189959") /* "已配置连接" */, //'',
            dataSource: [],
            allDataSource: [],
            loaded: false,
            searchKey: "",
        },
        {
            key: "erp",
            type: "add",
            name: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_2021891000") /* "添加连接" */, //'','',
            dataSource: [],
            allDataSource: [],
            loaded: false,
            searchKey: "",
        },
    ],
    activeTabKey: "self",
    searchKey: "",
    // 网关列表
    gatewayList: [],
    versionList: [],
    ModalTemplates: {},
    // 集成方案列表
    systemList: [],
    allConnector: [],
    allConnectorSel: [],
    pageData: [],
    pageDataTemp: undefined,
    filterConnector: "",
    testConnectSuccess: true,
    navLinks: [],
    createModalSelectedIndex: "",
    createModalSelectedItem: {},
    loaded: false,
    pagination: {
        ...defaultPagination,
        dataNumSelect: [10, 20, 50, 100, 200],
    },
    taskList: [],
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    updateTabDataSourceState = (id, state) => {
        let temp1 = this.state.tabs[0].dataSource.find((item) => {
            return item.id === id;
        });
        temp1.linkerState = state;
        let temp2 = this.state.tabs[0].allDataSource.find((item) => {
            return item.id === id;
        });
        temp2.linkerState = state;
        console.log(this.toJS(this.state.tabs));
        this.state.tabs = [...this.toJS(this.state.tabs)];
    };

    initTab = (tabKey = "self") => {
        let { tabs } = this.state;
        let tab = tabs.find((tab) => tab.key === tabKey);
        tab.dataSource = [];
        tab.allDataSource = [];
        tab.loaded = false;
        tab.searchKey = "";
        this.state.tabs = tabs;
    };

    changeActiveTab = (tabKey) => {
        this.state.activeTabKey = tabKey;
        this.getDataSource(true);
    };

    // changeSearchKey = (_searchKey) => {
    //   console.log(_searchKey)
    //   let { activeTabKey, tabs } = this.state;
    //   let tab = tabs.find(tab => tab.key === activeTabKey);
    //   tab.searchKey = _searchKey;
    //   this.state.tabs = tabs;
    // }
    initU8Email = async (data) => {
        // this.state.filterConnector
        let res = await autoServiceMessage({
            service: ownerService.initU8EmailService(data),
        });
        // if (res) {

        // }
        return res;
    };
    getU8Email = async (gatewayId) => {
        // this.state.filterConnector
        let res = await autoServiceMessage({
            service: ownerService.getU8EmailService(gatewayId),
        });
        if (res) {
        }
        return res;
    };
    changeU8Email = async (gatewayId) => {
        // this.state.filterConnector
        let res = await autoServiceMessage({
            service: ownerService.changeU8EmailService(gatewayId),
        });
        if (res) {
        }
        return res;
    };
    // 把已配置连接的加载状态置为false
    changeTabOneState = () => {
        this.state.tabs[0].loaded = false;
    };
    getDataSource = async (gatewayId) => {
        // this.state.filterConnector
        let res = await autoServiceMessage({
            service: ownerService.getPageData(gatewayId),
        });
        if (res) {
            let data = this.onPageChange("", { pageNo: 1 }, res.data || [], "init");
            this.changeState({
                loaded: true,
                pageData: data,
            });
        }
    };
    getDataSource2 = async (isChangeTab = false, searchValue) => {
        let { activeTabKey, tabs } = this.state;
        let tab = tabs.find((tab) => tab.key === activeTabKey);
        if (isChangeTab && tab.loaded) {
            this.state.activeDataSource = tab.dataSource;
            return false;
        }
        let service = null;
        let { searchKey } = tab;
        let _searchKey = typeof searchValue === "undefined" ? searchKey : searchValue;
        if (activeTabKey === "self") {
            service = ownerService.selfConnectorsService();
        } else {
            // service = ownerService.getConnectorsService({
            //   type: activeTabKey, key: _searchKey
            // })

            // 因为没有分页,这里搜索改为前端搜索,那么需要添加一个变量记录返回的总数据
            service = ownerService.getCommonConnector();
        }
        let res = await autoServiceMessage({
            service: service,
        });
        if (res) {
            let resData = res.data || [];
            tab.allDataSource = resData;
            tab.dataSource = resData;
            tab.loaded = true;
            tab.searchKey = _searchKey;
            this.state.tabs = tabs;
        }
        this.getSystemList();
    };
    getAllConnector = async () => {
        let res = await autoServiceMessage({
            service: ownerService.getCommonConnector(),
        });
        if (res) {
            let allConnectorSel = JSON.parse(JSON.stringify(res.data));
            allConnectorSel.unshift({ name: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281491") /* 全部连接器 */, code: "all" });
            this.changeState({
                allConnector: res.data,
                allConnectorSel,
            });
        }
    };
    testConnect = async (item) => {
        let res = await autoServiceMessage({
            service: ownerService.testConnectService(item),
            success: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_20226281492") /* 测试连接成功 */,
        });
        if (res) {
            // this.changeState({
            //     testConnectSuccess:true
            // })
        }
        return res;
    };
    setDefault = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.setDefaultService(data),
        });
        if (res) {
            this.getDataSource();
            // this.changeState({
            //     allConnector:res.data
            // })
        }
    };
    changeEnableState = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.updateConnectState(data),
        });
        if (res) {
            this.getDataSource();
            // this.changeState({
            //     allConnector:res.data
            // })
        }
    };
    rememberSelectedMsgInCreateModal = (index, selectedItem) => {
        this.changeState({
            createModalSelectedIndex: index,
            createModalSelectedItem: selectedItem,
        });
    };

    // 前端搜索连接
    searchDataSource = (searchValue) => {
        let { activeTabKey, tabs } = this.state;
        let tab = tabs.find((tab) => tab.key === activeTabKey);
        let { searchKey } = tab;
        let _searchKey = typeof searchValue === "undefined" ? searchKey : searchValue;
        tab.searchKey = _searchKey;
        tab.dataSource = tab.allDataSource.filter((data) => {
            return (data.name || data.alias).indexOf(_searchKey) > -1;
        });
    };

    deleteConnector = async (connector) => {
        let { id } = connector;
        let res = await autoServiceMessage({
            service: ownerService.deleteConnectorService(id),
            success: window.lang.template(commonText.deleteSuccess),
        });
        if (res) {
            this.getDataSource(false);
        }
    };

    restartGateway = async (gatewayId) => {
        await autoServiceMessage({
            service: ownerService.restartGatewayIdService(gatewayId),
            success: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050781") /* "网关重启成功！" */,
        });
    };

    // 获取所有网关列表
    getGatewayList = async () => {
        let res = await autoServiceMessage({
            service: ownerService.getGatewayList(),
        });
        this.changeState({
            gatewayList: res.data,
        });
    };
    // 获取版本
    getAllVersion = async (linkerId) => {
        let res = await autoServiceMessage({
            service: getAllVersionService(linkerId),
        });
        this.changeState({
            versionList: res.data,
        });
        return res;
    };
    // 获取弹窗模板
    getLinkerSetParam = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.getLinkerSetParam(data),
        });
        this.changeState({
            ModalTemplates: res.data,
        });
        return res;
    };
    // 获取所有集成系统列表
    getSystemList = async () => {
        let res = await autoServiceMessage({
            service: ownerService.getSystemList(),
        });
        if (res) {
            res.data.unshift({ id: "", systemName: lang.templateByUuid("UID:P_UBL-FE_200960400428023B", "请选择") /* "请选择" */ });
            console.log(res.data);
            this.changeState({
                systemList: res.data,
            });
        }
    };
    setConfigName = (configName) => {
        this.state.configName = configName;
    };

    setNavLinks = (nav) => {
        this.state.navLinks.push(nav);
    };

    setQuestionShow = () => {
        this.state.questionShow = true;
    };
    onPageChange = (index, pageInfo, dataSource, type) => {
        if (type) {
            dataSource.forEach((item, index) => {
                let _dataSource = this.toJS(item.items);
                let pagination = this.toJS(this.state.pagination);

                let info = selfPageChange(pageInfo, _dataSource, {
                    ...pagination,
                    onPageChange: this.onPageChange,
                    index: index,
                });
                item.taskList = info.list;
                item.pagination = info.pagination;
            });
            console.log(dataSource);
            return dataSource;
        } else {
            let _dataSource = this.state.pageData[index].items;
            let pagination = this.toJS(this.state.pagination);

            let info = selfPageChange(pageInfo, _dataSource, {
                ...pagination,
                onPageChange: this.onPageChange,
                index: index,
            });
            this.state.pageData[index].taskList = info.list;
            this.state.pageData[index].pagination = info.pagination;
        }
    };
}
/**
 * 根据具体模块修改 storeKey 为保证唯一性
 * storeKey 由 模块文件夹名称 + 页面名称 + 'Store' 小驼峰
 *
 * storeKey是作为当前 Store 的唯一键， storeKey与Store的实例会在 rootStore或者StoreMap中以 键值对的 形式存在
 * 1. 用于在模块的 storeMap（模块routes/store.js）中注册并初始化当前Store
 * 2. 用于在container的 inject 中关联当前Store的实例时，从模块的storeMap或者rootStore中获取对应store实例
 * */
export const storeKey = "systemConnectorHomeStore";
export const addStore = () => {
    core.addStore({
        storeKey: storeKey,
        store: new Store(),
    });
};
export default Store;
