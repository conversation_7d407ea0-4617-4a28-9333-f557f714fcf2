import { getInvokeService, getServicePath } from "utils/service";
import { a_download } from "utils/index";
import _template from "lodash/template";
import { Success, Error } from "utils/feedback";
import commonText from "constants/commonText";

/**
 * 或去我的连接器（已添加连接器）
 * @param {Object} data
 * @param {String} data.key -搜索条件
 * @return {Promise<unknown>}
 */
export const selfConnectorsService = function (data) {
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/ncconnect/listMyConnects",
        },
        data
    );
};

/**
 * 删除连接
 * @param {Object} id

 * @return {Promise<unknown>}
 */
export const deleteConnectorService = function (id) {
    return getInvokeService({
        method: "POST",
        path: `/gwmanage/gwportal/diwork/connect/delete?id=${id}`,
    });
};

/**
 * 获取连接器
 * @param {Object} data
 * @param {String} data.key -搜索条件
 * @param {String} data.type=[erp|ec] -erp erp拦截器 ec电商连接器
 * @return {Promise<unknown>}
 */
export const getConnectorsService = function (data) {
    let { key, type } = data;
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/ncconnect/connector/" + type,
        },
        { key }
    );
};

/**
 * 获取可重启网关erp类型
 * @returns {Promise | Promise<unknown>}
 */
export const getRestartGwErpTypesService = function () {
    return getInvokeService({
        method: "GET",
        path: "/mygwapp/ncconnect/erp/type/allowRestart",
        showLoading: false,
    });
};

/**
 * 重启网关
 * @param {String} gatewayId
 * @returns {Promise | Promise<unknown>}
 */
export const restartGatewayIdService = function (gatewayId) {
    return getInvokeService({
        method: "GET",
        path: `/mygwapp/gateway/${gatewayId}/restart`,
    });
};

/**
 * 通用连接配置首页-获取连接器
 * @param {String} gatewayId
 * @returns {Promise | Promise<unknown>}
 */
export const getCommonConnector = function () {
    return getInvokeService({
        method: "GET",
        // _1604989107868
        path: "/gwmanage/gwportal/diwork/connect/listLinker",
        timeout: 30000000,
    });
};
export const testConnectService = function ({ id }) {
    return getInvokeService(
        {
            method: "GET",
            // _1604989107868
            path: "/gwmanage/gwportal/diwork/ncconnect/testConnection",
        },
        { tenantConnectId: id }
    );
};

export const setDefaultService = function (data) {
    return getInvokeService(
        {
            method: "PUT",
            path: "/gwmanage/gwportal/diwork/ncconnect/connect/setLinkerLevelDefault",
        },
        {},
        data
    );
};
export const getPageData = function (data) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwmanage/gwportal/diwork/ncconnect/listMyConnects/classifyByType",
            timeout: 3000000000,
        },
        data
    );
};
export const initU8EmailService = function (data) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwmanage/gwportal/diwork/queryOpenapiConfigNew",
        },
        data
    );
};
export const getU8EmailService = function (data) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwmanage/gwportal/diwork/getCurrentUser",
        },
        data
    );
};
export const changeU8EmailService = function (data) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwmanage/gwportal/diwork/changeUserApiConfig",
        },
        data
    );
};

/**
 * 新增通用连接配置
 * @param {Object} data
 * @param {String} data.gatewayId 网关ID
 * @param {Number} data.fromType
 * @param {String} data.entitryid
 * @param {String} data.alias 名称
 * @param {Array} data.linkconfig
 * @return {Promise<unknown>}
 */
export const createCommonLinkConfig = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: `/gwmanage/gwportal/diwork/ncconnect/createConnect/${data.type}`,
        },
        data
    );
};

/**
 * 编辑通用连接配置
 * @param {Object} data
 * @param {String} data._id 连接器ID
 * @param {Array} data.linkconfig 连接配置列表
 * @return {Promise<unknown>}
 */
export const editCommonLinkConfig = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: `/gwmanage/gwportal/diwork/ncconnect/updateConnectLinkConfig`,
        },
        data
    );
};

/**
 * 通用连接配置-测试连接接口
 * @param {String} tenantConnectId 连接器ID
 * @return {Promise<unknown>}
 */
export const commonLinkTest = function (data) {
    return getInvokeService(
        {
            method: "POST",
            // path: `/gwmanage/gwportal/diwork/ncconnect/testConnection`
            path: `/gwmanage/gwportal/diwork/ncconnect/v2/testConnection`,
        },
        data
    );
};

/**
 * 获取连接器下对应所有用户设置参数
 * @param {Object} data
 * @param {String} data.linkerId
 * @param {String} data.linkerVersionCode
 * @return {Promise<unknown>}
 */
export const getLinkerSetParam = function (data) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwmanage/gwportal/diwork/connect/listUserLinkerParamByLinkerIdAndVersionCode",
        },
        data
    );
};

// 获取所有网关列表
export const getGatewayList = function () {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwmanage/gwportal/mygwapp/gateway/list",
        },
        {
            order: "asc",
            isAll: true,
        }
    );
};

// 获取所有集成系统列表
export const getSystemList = function () {
    return getInvokeService({
        method: "GET",
        path: "/gwmanage/gwportal/mygwapp/integrated/system/findAll",
    });
};

// 更新连接状态
export const updateConnectState = function (data) {
    return getInvokeService({
        method: "PUT",
        path: "/gwmanage/gwportal/diwork/ncconnect/state/update" + "?id=" + data.id + "&state=" + data.state,
    });
};
/**
 * 获取nc|ncc|u8c补丁下载地址
 * @param erpVersion
 * @returns {*}
 */
export const downloadAdapterService = function (ModalCode, erpVersion) {
    let download_path = "";
    if (["NCHandler", "NCCHandler"].indexOf(ModalCode) > -1) {
        download_path = "ncpath";
    } else if (["U8CHandler"].indexOf(ModalCode) > -1) {
        download_path = "u8cpath";
    }
    return getInvokeService({
        method: "GET",
        path: `/mygwapp/ncconnect/${download_path}/download/ossurl/${erpVersion}`,
        responseType: "blob",
        timeout: 300000,
    });
};
export const downloadClient = function (version) {
    let url = window.location.origin + "/iuap-ipaas-dataintegration" + getServicePath("/diwork/ncconnect/openapiclient/download/ossurl/<%= version %>");
    console.log(url);
    // https://bip-test.yyuap.com/iuap-ipaas-dataintegration/gwmanage/gwportal/diwork/ncconnect/openapiclient/download/ossurl/1.8
    a_download(_template(url)({ version }), true);
    // return getInvokeService({
    //     method: 'GET',
    //     path: `/mygwapp/ncconnect/openapiclient/download/ossurl/${version}`,
    //     responseType:'blob',
    //     timeout:300000
    // });
};

export const downloadClientService = function (version) {
    return getInvokeService({
        method: "GET",
        path: `/mygwapp/ncconnect/openapiclient/download/ossurl/${version}`,
        responseType: "blob",
        timeout: 300000,
        showLoading: () => {
            Success(window.lang.template(commonText.downing));
        },
    });
};

export const getcode = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: `/gwmanage/gwportal/diwork/ncconnect/getcode`,
        },
        data
    );
};
//initTask
//   getDataViewService, initDataViewService, updateSyncTaskService, getInitMessageService
export const getDataViewService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/erpdata/initTask/dataview",
        },
        data
    );
};
export const initDataViewService = function (data, useMainTenantGateway) {
    let url = `/diwork/erpdata/task/init/selectedTask/${data.gatewayId}`;

    if (useMainTenantGateway !== "" && useMainTenantGateway !== undefined) {
        url += "?useMainTenantGateway=" + useMainTenantGateway;
    }
    return getInvokeService(
        {
            method: "POST",
            path: url,
        },
        data
    );
};
export const updateSyncTaskService = function (data) {
    let { taskId, ..._data } = data;
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/erpdata/task/update/" + taskId,
        },
        _data
    );
};
export const getInitMessageService = function (data) {
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/erpdata/task/getInitMessage",
        },
        data
    );
};

//initDoc
//   initDocStatusService, getDefDocService, editDefDocService, pushDefDocService
export const initDocStatusService = function (erpVersion) {
    return getInvokeService({
        method: "GET",
        path: "/diwork/defDoc/datamapping/initState/" + erpVersion,
        showLoading: false,
    });
};
export const getDefDocService = function (data) {
    let { erpVersion, ..._data } = data;
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/defDoc/datamapping/get/" + erpVersion,
            timeout: 20000,
        },
        _data
    );
};
export const editDefDocService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/defDoc/datamapping/set/",
        },
        data
    );
};
export const pushDefDocService = function (data) {
    let { erpVersion, erpPks } = data;
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/defDoc/datamapping/push/" + erpVersion,
            showLoading: false,
        },
        erpPks
    );
};
//u8 inittask
export const getU8DataViewService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/erpdata/initTask/u8dataview/get",
        },
        {},
        data
    );
};
export const initU8DataViewService = function (data) {
    let { orgCode, tableViews } = data;
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/erpdata/task/initu8/selectedView/" + orgCode,
        },
        tableViews
    );
};
export const getConfigDataService = function (configId) {
    return getInvokeService({
        method: "GET",
        path: "/diwork/ncconnect/queryU8ConfigById/" + configId,
    });
};
export const getDefaultConfigInfoService = function (data) {
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/queryOpenapiConfig",
        },
        data
    );
};
//u8
export const getOrgsService = function () {
    return getInvokeService({
        method: "GET",
        path: "/diwork/queryOrgvos",
    });
};
export const getGwAppSecretService = function (gwaddr) {
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/querySecret",
        },
        { gwaddr }
    );
};
