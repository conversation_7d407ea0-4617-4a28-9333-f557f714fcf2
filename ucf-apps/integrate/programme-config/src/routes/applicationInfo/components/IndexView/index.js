import React, { Component, Fragment } from "react";
import { Header, Content, Footer } from "components/PageView";
import query from "query-string";
import { Button, Message, Modal } from "components/TinperBee";
import { Steps } from "@tinper/next-ui";
import AppInfoRoutes from "../../routes";
import styles from "./index.modules.css";
import { getPageParams } from "decorator/index";
import globalStyles from "../../../global.modules.css";
import { Error } from "utils/feedback";
import withRouter from "decorator/withRouter";

const { Step } = Steps;

@withRouter
@getPageParams
class IndexView extends Component {
    constructor(props) {
        super(props);
        let {
            location: { type },
        } = this.props;

        this.state = {
            pageType: type,
            pageTitle: this.getPageTitle(type),
            step: "",
            applicationCode: "",
        };
    }
    mappingEditRef = React.createRef();
    filterConditionRef = React.createRef(); // 数据过滤条件表单
    targetFilterConditionRef = React.createRef(); // 数据过滤条件表单
    sourceInfoRef = React.createRef(); // 来源数据处理设置表单
    basicInfoRef = React.createRef(); // 方案基本信息表单
    pageParam = this.props.getPageParams(this.props);
    componentWillUnmount() {
        setTimeout(() => {
            // 方案编辑路由被切换需要清空用户对过滤条件的编辑
            const { setFilterConditions, setFilterConditionElement } = this.props.ownerStore;
            setFilterConditions(null);
            setFilterConditionElement({});
            this.filterConditionRef = null;
            this.targetFilterConditionRef = null;
        }, 0);
    }
    async componentDidMount() {
        const type = this.pageParam?.queryParams?.type;
        if (type) {
            this.setState({ pageType: type, pageTitle: this.getPageTitle(type) });
        }

        const { getDataType, initStore } = this.props.ownerStore;

        const { getYmsMicro } = this.props.IntegrateApplicationStore;
        initStore();
        let pageParam = this.props.getPageParams(this.props);
        let applicationInfo = this.props.applicationInfoState;
        const { apiId, sourceConnectorCode, sourceObjectId } = pageParam.queryParams;
        if (apiId) {
            //来自开放平台   多显示一个集成应用名称，选择其中一个应用，回填来源连接器，配置，目标连接器，配置，来源对象分类，来源对象，其他的还按原来的自己选
            this.props.ownerStore.changeState({
                fromOpenApi: true,
                openApiSourceConnectorCode: sourceConnectorCode,
                openApiSourceObjectId: sourceObjectId,
                openApiApiId: apiId,
            });
            //获取来源分类
            if (sourceObjectId && sourceConnectorCode) {
                this.props.ownerStore.getOpenApiSouceObjectCategory({
                    linkerCode: sourceConnectorCode,
                    objectId: sourceObjectId,
                    apiId: apiId,
                });
            }
        } else {
            //来自同步任务 / 本身的新增和编辑逻辑
            let { id: itemId, from } = pageParam.queryParams;
            if (from === "sync-task") {
                //从同步任务跳过来没有applicationInfo，所以需要调用
                applicationInfo = await this.props.ownerStore.getApplyByIntegreID(itemId);
            }
            this.setState({
                applicationCode: pageParam.queryParams.applicationCode || applicationInfo?.code,
            });
            this.props.IntegrateApplicationStore.changeApplicationInfo(applicationInfo); // 更新applicationInfo，回退的时候给方案列表页使用
            //应用过来的连接器，连接配置 手动 格式化
            this.props.ownerStore.changeState({
                connectorsArr: [
                    { value: applicationInfo.connectTypeCode, title: applicationInfo.connectTypeName, type: "one" },
                    { value: applicationInfo.connectTypeCodeTwo, title: applicationInfo.connectTypeNameTwo, type: "two" },
                ],
                connectConfigObj: {
                    one: [{ value: applicationInfo.connectId, title: applicationInfo.connectName, type: "one" }],
                    two: [{ value: applicationInfo.connectIdTwo, title: applicationInfo.connectNameTwo, type: "two" }],
                },
                connectConfigArr: [
                    { value: applicationInfo.connectId, title: applicationInfo.connectName, versionName: applicationInfo.versionName },
                    { value: applicationInfo.connectIdTwo, title: applicationInfo.connectNameTwo, versionName: applicationInfo.versionNameTwo },
                ],
                isSameOfTwoConnectors: applicationInfo.connectTypeCode == applicationInfo.connectTypeCodeTwo,
            });
            if (itemId) {
                (async () => {
                    let info = await this.props.ownerStore.getDataSource(itemId);
                    this.props.ownerStore.changeState({
                        editId: itemId,
                    });
                    //得调用分类，再调用对象
                    //获取分类列表
                    this.props.ownerStore.getSourceCategoryList({
                        linkerCode: info?.sourceConnectorCode || "",
                        connectConfigId: info?.sourceConnectConfigId || "",
                    }); //u8open  ownerState.applicationInfo.connectTypeCode
                    this.props.ownerStore.getTargetCategoryList({
                        linkerCode: info?.targetConnectorCode || "",
                        connectConfigId: info?.targetConnectConfigId || "",
                    }); //u8open  ownerState.applicationInfo.connectTypeCode
                    //获取要显示的分类,不用方案上自带的了
                    let sourceCategory = await this.props.ownerStore.getCategoryId({
                        linkerCode: "" || info.sourceConnectorCode,
                        objectCode: "" || info.sourceObjectCode,
                        categoryId: "" || info.sourceCategoryId,
                        connectConfigId: info?.sourceConnectConfigId,
                    });
                    let targetCategory = await this.props.ownerStore.getCategoryId2({
                        linkerCode: "" || info.targetConnectorCode,
                        objectCode: "" || info.targetObjectCode,
                        categoryId: "" || info.targetCategoryId,
                        connectConfigId: info?.targetConnectConfigId,
                    });
                    //上面得到要显示的分类，再用其id去查下面的对象
                    this.props.ownerStore.getSourceObjectListByCategoryId({
                        Id: "" || (sourceCategory && sourceCategory.id),
                        linkerCode: "" || info.sourceConnectorCode,
                        connectConfigId: info.sourceConnectConfigId || "",
                    });
                    this.props.ownerStore.getTargetObjectListByCategoryId({
                        Id: "" || (targetCategory && targetCategory.id),
                        linkerCode: "" || info.targetConnectorCode,
                        connectConfigId: info.targetConnectConfigId || "",
                    });
                })();
            }
        }

        getDataType();
        getYmsMicro(); // 获取yms微服务列表
    }

    getPageTitle = (pageType) => {
        let title = "";
        switch (pageType) {
            case "edit":
                title = lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418009F", "编辑集成方案") /* "编辑集成方案" */;
                break;
            case "add":
            default:
                title = lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041800A3", "新增集成方案") /* "新增集成方案" */;
                break;
        }
        return title;
    };
    formatOrderConditions = (orderConditions) => {
        let array = orderConditions.filter((item, index) => {
            return item.ascending != "" && item.fieldParam != "";
        });
        return array;
    };
    handleSubmit = async (isUpdateRelation) => {
        let { filterConditions, targetFilterConditions } = this.props;
        let {
            info,
            apiInfo,
            targetApiInfo,
            dataExchangeDTO,
            tmpId,
            orderConditions,
            advanceCondition,
            dataExchangeReg: dataExchangeReg2,
            fromOpenApi,
        } = this.props.ownerState;
        let { addSchemeList, updateScheme, setTmpId } = this.props.ownerStore;
        let params = {};
        let dataExchangeReg = {};
        let pkIntegratedId = null;
        let isEdit = sessionStorage.getItem("returnIntegrate");
        info.parseReturnValue = info.parseReturnValue ? "1" : "0";
        let newOrderConditions = this.formatOrderConditions(orderConditions);
        let queryCriteria = info.queryCriteria;
        let saveCriteria = info.saveCriteria;

        // 区分api类型是不是rest，给queryCriteria拼装不同的数据结构
        if (this.filterConditionRef.current) {
            filterConditions = await this.filterConditionRef.current.collectData();
            // 数据过滤条件校验不通过
            if (!filterConditions) return;
        }
        if (filterConditions) {
            if (apiInfo?.paramDTOS?.[0]?.executionType === "rest") {
                queryCriteria = { coreRequest: { ...filterConditions } };
            } else {
                queryCriteria = Object.keys(filterConditions).reduce((acc, key) => {
                    return { ...acc, ...(filterConditions[key] || {}) };
                }, {});
            }
        }
        if (this.targetFilterConditionRef.current) {
            targetFilterConditions = await this.targetFilterConditionRef.current.collectData();
            // 数据过滤条件校验不通过
            if (!targetFilterConditions) return;
        }
        if (targetFilterConditions) {
            if (targetApiInfo?.paramDTOS?.[0]?.executionType === "rest") {
                saveCriteria = { coreRequest: { ...targetFilterConditions } };
            } else {
                saveCriteria = Object.keys(targetFilterConditions).reduce((acc, key) => {
                    return { ...acc, ...(targetFilterConditions[key] || {}) };
                }, {});
            }
        }

        if (this.sourceInfoRef.current) {
            try {
                const sourceInfo = await this.sourceInfoRef.current.validateFields();
                info = { ...info, ...sourceInfo };
            } catch (error) {
                Error(lang.templateByUuid("UID:P_UBL-FE_1D76CF5804F0000E", "来源数据处理设置校验失败") /* "来源数据处理设置校验失败" */);
                return;
            }
        }
        dataExchangeReg.sourceCode = info.sourceObjectCode;
        dataExchangeReg.targetCode = info.targetObjectCode;
        if (tmpId) {
            info._id = tmpId;
            params.integrateScheme = info;
            params.integrateScheme.orderConditions = newOrderConditions;
            params.integrateScheme.advanceCondition = advanceCondition;
            params.integrateScheme.queryCriteria = queryCriteria;
            params.integrateScheme.saveCriteria = saveCriteria;
        } else {
            params.integrateScheme = info;
            params.integrateScheme.orderConditions = newOrderConditions;
            params.integrateScheme.advanceCondition = advanceCondition;
            params.integrateScheme.queryCriteria = queryCriteria;
            params.integrateScheme.saveCriteria = saveCriteria;
            params.dataExchangeReg = dataExchangeReg;
        }
        params.dataExchangeItems = dataExchangeDTO;
        if ("id" in params.integrateScheme || "pkid" in params.dataExchangeItems || isEdit === "1") {
            let sessionIntegratedId = sessionStorage.getItem("pkIntegratedId");
            //let dataExchangeRegObj = await this.props.ownerStore.getDataSource(params.integrateScheme.id ? params.integrateScheme.id : sessionIntegratedId);
            // dataExchangeReg._id = dataExchangeRegObj.id
            dataExchangeReg._id = dataExchangeReg2?.id;
            info._id = params.integrateScheme.id || sessionIntegratedId;
            if (isEdit === "1") {
                info.id = sessionIntegratedId;
            }
            params.integrateScheme = info;
            params.dataExchangeReg = dataExchangeReg;
            pkIntegratedId = await updateScheme(params);
            if (!pkIntegratedId) {
                return false;
            }
            sessionStorage.removeItem("returnIntegrate");
            sessionStorage.removeItem("pkIntegratedId");
        } else {
            pkIntegratedId = await addSchemeList(params);
            if (!pkIntegratedId) {
                return false;
            }
            setTmpId(pkIntegratedId);
            sessionStorage.setItem("pkIntegratedId", pkIntegratedId);
        }
        //双良   暂时注释
        // if (pkIntegratedId && isUpdateRelation) {
        //     this.props.ownerStore.updateDataTypeBindRelation();
        // }
        if (fromOpenApi) {
            setTimeout(() => {
                window.close();
            }, 1500);
        } else {
            this.props.navigate({
                pathname: "/applicationList",
                search:
                    "?" +
                    query.stringify({
                        applicationCode: this.state.applicationCode,
                        back: "back",
                    }),
                type: "back",
            });
        }
    };
    handleSave = async () => {
        this.handleSubmit();
    };

    handleNext = async (step, prevStep) => {
        // 基础信息校验
        if (prevStep === 0) {
            try {
                await this.basicInfoRef.current.validateFields();
            } catch (err) {
                Message.error(lang.templateByUuid("UID:P_UBL-FE_1B7F4D900548000D", "数据校验不通过"));
                return;
            }
        }
        // 转换规则编辑态校验
        if (prevStep === 1) {
            try {
                await this.mappingEditRef.current.validateFields();
            } catch (err) {
                Message.error(lang.templateByUuid("UID:P_UBL-FE_1B7F4D900548000D", "数据校验不通过"));
                return;
            }
            //去第三步的时候清空映射的搜索style
            this.clearDataExchangeDTOStyle();
        }

        if (step === 1) {
            this.props.navigate({
                pathname: "/applicationInfo/select",
            });
        } else if (step === 2) {
            this.props.navigate({
                pathname: "/applicationInfo/set",
            });
        } else if (step === 0) {
            // 点击步骤条1
            this.props.navigate({
                pathname: "/applicationInfo",
                type: this.state.pageType,
            });
        }
    };

    handleCancel = () => {
        Modal.confirm({
            fieldid: "************",
            title: lang.templateByUuid(
                "UID:P_UBL-FE_18D8CEF6041800A8",
                "直接返回会丢失未保存的数据,是否确认返回？" //@notranslate
            ) /* "直接返回会丢失未保存的数据,是否确认返回？" */,
            onOk: () => {
                this.props.navigate({
                    pathname: "/applicationList",
                    search:
                        "?" +
                        query.stringify({
                            applicationCode: this.state.applicationCode || this.state.applicationInfo?.code,
                            back: "back",
                        }),
                    type: "back",
                });
            },
        });
    };
    clearDataExchangeDTOStyle = () => {
        let {
            ownerState: { dataExchangeDTO },
            ownerStore,
        } = this.props;
        dataExchangeDTO.length != 0 &&
            dataExchangeDTO.forEach(function (item, index) {
                delete item.style;
            });
        ownerStore.changeState({
            dataExchangeDTO,
        });
    };
    checkrDataExchangeDTOSRepeat = () => {
        let {
            ownerState: { dataExchangeDTO },
            ownerStore,
        } = this.props;

        let filterDataExchangeDTO = dataExchangeDTO
            .filter((item) => item.sfieldCode || item.tfieldCode)
            .map((items) => {
                return { sfieldCode: items.sfieldCode, tfieldCode: items.tfieldCode };
            });
        console.log(filterDataExchangeDTO);
        let duplicateItems = this.findDuplicateObjectIndices(filterDataExchangeDTO);
        console.log(duplicateItems);
        if (duplicateItems.length != 0) {
            Modal.warning({
                title: lang.templateByUuid("UID:P_UBL-FE_1B2308940520000E", "提示", undefined, {
                    returnStr: true,
                }) /* "提示" */,
                content: this.getMessageDom(duplicateItems),
            });
            return false;
        }
        return true;
    };
    getMessageDom = (duplicateItems) => {
        return (
            <div className={styles["applicationInfo-warning"]}>
                {duplicateItems.map((item) => {
                    return (
                        <div>
                            <span style={{ fontWeight: "bold" }}>
                                {
                                    lang.templateByUuid("UID:P_UBL-FE_1B2308940520000C", "序号", undefined, {
                                        returnStr: true,
                                    }) /* "序号" */
                                }
                            </span>
                            &nbsp;
                            {item.join(" , ")} :&nbsp;
                            <span>
                                {
                                    lang.templateByUuid("UID:P_UBL-FE_1B2308940520000D", "来源、目标字段重复,请修改", undefined, {
                                        returnStr: true,
                                    }) /* "来源、目标字段重复,请修改" */
                                }
                            </span>
                        </div>
                    );
                })}
            </div>
        );
    };
    findDuplicateObjectIndices = (arr) => {
        const indices = {}; // 映射对象到它们首次出现的索引
        const duplicates = []; // 存储重复对象的索引数组

        for (let i = 0; i < arr.length; i++) {
            const item = arr[i];
            const itemString = JSON.stringify(item); // 将对象转换为字符串以进行比较

            if (indices.hasOwnProperty(itemString)) {
                // 如果已经遇到过这个对象，记录其索引
                const previousIndices = duplicates[indices[itemString]];
                // if (!previousIndices.includes(i)) {
                // 避免重复添加相同的索引
                previousIndices.push(i + 1);
                // }
            } else {
                // 如果是新的对象，记录其索引并添加到映射中
                indices[itemString] = duplicates.length;
                duplicates.push([i + 1]);
            }
        }

        // 返回重复对象的索引数组
        return duplicates.filter((indicesArray) => indicesArray.length > 1);
    };
    handlePrev = async (step, prevStep) => {
        if (prevStep == 1) {
            try {
                await this.mappingEditRef.current.validateFields();
            } catch (err) {
                Message.error(lang.templateByUuid("UID:P_UBL-FE_1B7F4D900548000D", "数据校验不通过"));
                return;
            }
            //回第一步的时候清空映射的搜索style
            this.clearDataExchangeDTOStyle();
        }
        this.props.navigate(-1);
    };

    getStep = () => {
        let {
            location: { pathname },
        } = this.props;
        let step = 0;
        if (pathname.includes("start")) {
            step = 3;
        } else if (pathname.includes("set")) {
            step = 2;
        } else if (pathname.includes("select")) {
            step = 1;
        }
        return step;
    };

    transErrorType = (nums) => {
        let text = "";
        switch (nums) {
            case 1:
                text = lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041800AC", "错误时停止") /* "错误时停止" */;
                break;
            case 2:
                text = lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041800AD", "错误时忽略") /* "错误时忽略" */;
                break;
            default:
                text = "";
                break;
        }
        return text;
    };

    translateText = (nums) => {
        let text = "";
        switch (nums) {
            case 0:
                text = lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041800B5", "无") /* "无" */;
                break;
            case 1:
                text = lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041800A0", "编码相同") /* "编码相同" */;
                break;
            case 2:
                text = lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041800A2", "名称相同") /* "名称相同" */;
                break;
        }
        return text;
    };

    translateSavelog = (nums) => {
        let text = "";
        switch (nums) {
            case 0:
                text = "0";
                break;
            case 1:
                text = "1";
                break;
            default:
                text = "";
                break;
        }
        return text;
    };

    steps = [
        lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041800B1", "基础信息") /* "基础信息" */,
        lang.templateByUuid("UID:P_UBL-FE_1C39D42404F80009", "转换规则") /* "转换规则" */,
        lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041800B3", "其他设置") /* "其他设置" */,
        // , "启动方案"
    ];

    render() {
        let { pageTitle } = this.state;
        let {
            ownerState: { fromOpenApi },
        } = this.props;
        let step = this.getStep();
        return (
            <div className={globalStyles["application-info"]}>
                {fromOpenApi ? null : (
                    <Header fixed={false} back={this.handleCancel} title={pageTitle}>
                        <Steps
                            fieldid="1e255b2e-4717-46c9-b9c6-91f26732416d"
                            onChange={(current) => this.handleNext(current, step)}
                            current={step}
                            type="arrow"
                            size="small"
                            // className={styles["assetPack-header-step"]}
                        >
                            {this.steps.map((item, index) => (
                                <Step key={index} title={item} description={item} />
                            ))}
                        </Steps>
                    </Header>
                )}

                <div className={styles["step-content"]} style={step !== 0 ? { paddingTop: "0" } : { paddingTop: "20px" }}>
                    <AppInfoRoutes
                        mappingEditRef={this.mappingEditRef}
                        filterConditionRef={this.filterConditionRef}
                        targetFilterConditionRef={this.targetFilterConditionRef}
                        basicInfoRef={this.basicInfoRef}
                        sourceInfoRef={this.sourceInfoRef}
                    />
                </div>
                {step < 3 ? (
                    <div className={styles["application-info-footer"]}>
                        {step === 0 ? (
                            fromOpenApi ? null : (
                                <Button
                                    fieldid="ublinker-routes-info-components-IndexView-index-5580731-Button"
                                    className="ucg-mar-r-sm"
                                    onClick={this.handleCancel}
                                    colors="secondary"
                                >
                                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041800AA", "取消") /* "取消" */}
                                </Button>
                            )
                        ) : null}
                        {0 < step ? (
                            <Button
                                fieldid="ublinker-routes-info-components-IndexView-index-2451309-Button"
                                className="ucg-mr-10"
                                colors="dark"
                                onClick={this.handlePrev.bind(null, step - 1, step)}
                            >
                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041800A9", "上一步") /* "上一步" */}
                            </Button>
                        ) : null}
                        {step < 2 ? (
                            <Button
                                fieldid="ublinker-routes-info-components-IndexView-index-9249715-Button"
                                className="ucg-mr-10"
                                colors="primary"
                                onClick={this.handleNext.bind(null, step + 1, step)}
                            >
                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041800AE", "下一步") /* "下一步" */}
                            </Button>
                        ) : null}
                        {step === 2 ? (
                            <Button
                                fieldid="ublinker-routes-info-components-IndexView-index-5054296-Button"
                                className="ucg-mr-10"
                                colors="primary"
                                onClick={this.handleSubmit.bind()}
                            >
                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041800B0", "提交") /* "提交" */}
                            </Button>
                        ) : null}
                    </div>
                ) : null}
            </div>
        );
    }
}

export default IndexView;
