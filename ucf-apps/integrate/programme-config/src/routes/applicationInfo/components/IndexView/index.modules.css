.iuapIpaasDataintegrationFe-integrate {
    .config-action-form {
        padding-left: 10%;
    }
}

.header {
    height: 44px;
    background: #ffffff;
    font-size: 14px;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 9;
    padding: 0 10px;
    display: flex;
    align-items: center;
}
.assetPack-header-back {
    cursor: pointer;
    min-width: 72px;
    text-align: center;
    color: #505766;
    > img {
        width: 17px;
        height: 15px;
    }
}
.assetPack-header-divider {
    width: 1px;
    height: 21px;
    border: 1px solid #e8e9eb;
}
.assetPack-header-content {
    padding: 0 16px;
    margin-top: 2px;
}
.assetPack-header-oper {
    padding: 0 14px;
}
.assetPack-header-step {
    /* position: absolute; */
    /* margin-left: 50%;
    transform: translate(-50%); */
    /* right: 6px; */
    width: 690px;
}
.applicationInfo-warning {
    font-size: 16px;
    line-height: 26px;
}
.step-content {
    background-color: #f7f9fd;
    /* height: calc(100% - 111px); */
    flex: 1;
    overflow: auto;
}
.application-info-footer {
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 10px;
    background-color: #ffffff;
}
