import React, { Fragment, Component } from "react";
import Modal from "components/TinperBee/Modal";
import { FormControl, Button, Layout, Tabs, FormList, Upload, Icon, ButtonGroup, Dropdown, Menu } from "components/TinperBee";
import SearchInput from "components/TinperBee/SearchInput";
import { Modal as ModalConfirm, Empty, Message, Select } from "@tinper/next-ui";
import Grid from "components/TinperBee/Grid";
import { GridActions, GridAction } from "components/TinperBee/Grid/GridActions";
import { getScreenInnerHeight } from "utils";
import { ipReg, portReg, codeReg } from "utils/regExp";
import "./index.less";
import styles from "./index.modules.css";
const { TabPane } = Tabs;

const ScreenInnerHeight = getScreenInnerHeight();
const { Header, Sider, Content } = Layout;
const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 14 },
};
const FormItem = FormList.Item;

class AddConstantModal extends Component {
    newTabIndex;
    constructor(props) {
        super(props);
        this.newTabIndex = 0;
        const panes = [
            { title: "Tab 1", content: "Content of Tab 1", key: "1" },
            { title: "Tab 2", content: "Content of Tab 2", key: "2" },
            {
                title: "Tab 3",
                content: "Content of Tab 3",
                key: "3",
                closable: false,
            },
        ];
        this.state = {
            selectedList: [],
            activeKey: "",
            panes,
            showNewTab: false,
            file: "",
            editingIndex: -1,
            belongsMicroService: "", //所属微服务
        };
        this.form = React.createRef();
        this.searchInputRef = React.createRef();
        let _this = this;
        this.uploadData = {
            name: "file",
            // action: '/upload.do',
            headers: {
                authorization: "authorization-text",
            },
            beforeUpload(zipFile, list) {
                console.log(zipFile, list);
                _this.setState({ file: zipFile });
                _this.props.ownerStore.importExcelConstant({ file: zipFile, classifyCode: _this.state.activeKey });
                return false; // 禁止上传
            },
        };
    }

    async componentDidMount() {
        const { ownerStore, nowRecord } = this.props;
        let res = await this.props.ownerStore.getContantsTabsData();
        // let echoSelectedList = nowRecord.conversionRuleValue.map((item) => {
        //   return { id: item }
        // })
        console.log(nowRecord.conversionRuleValue.length == 0 ? res.data[0].code : nowRecord.conversionRuleValue[0]);
        this.setState(
            {
                // selectedList: echoSelectedList,
                activeKey: nowRecord.conversionRuleValue.length == 0 ? res.data[0].code : nowRecord.conversionRuleValue[0], //如果conversionRuleContantsActiveTab有值，则选中该值所代表的页签，否则显示第一个
            },
            () => {
                const { activeKey } = this.state;
                const classifyNode = res.data.find((item) => item.code === activeKey);
                if (classifyNode) {
                    // 这里可以更新 activeKey 或其他状态
                    this.setState({ belongsMicroService: classifyNode.microServiceCode });
                }
            }
        );
        ownerStore.changeState({
            conversionRuleValueIds: nowRecord.conversionRuleValue.length == 0 ? [res.data[0].code] : nowRecord.conversionRuleValue,
            conversionRuleValueNames: nowRecord.conversionRuleName.length == 0 ? [res.data[0].name] : nowRecord.conversionRuleName,
            // constantRuleClassify: nowRecord.constantRuleClassify,
            activeKey: nowRecord.conversionRuleValue.length == 0 ? res.data[0].code : nowRecord.conversionRuleValue[0],
        });

        this.props.ownerStore.getConstantAllData2({
            classifyCode: nowRecord.conversionRuleValue.length == 0 ? res.data[0].code : nowRecord.conversionRuleValue[0],
        });
    }

    handleChange = (index, field, value) => {
        this.props.ownerStore.changeConstantValue(index, field, value);
    };

    onOk = () => {
        // let stringSelectedList = JSON.stringify(this.state.selectedList)
        // sessionStorage.setItem("selectedList", stringSelectedList)
        this.props.ownerStore.changeState({ searchClassifyValue: "" });
        this.props.onOk();
    };
    onCancel = () => {
        this.props.ownerStore.changeState({ searchClassifyValue: "" });
        this.props.onCancel();
    };
    handleDelete = async (id) => {
        const res = await this.props.ownerStore.deleteConstantData(id);
        if (res) {
            this.setState({ editingIndex: -1 });
        }
    };

    handleAdd = async () => {
        let record = this.props.ownerState.constantData.list[0];
        let params = {
            name: record.name,
            valueSrc: record.valueSrc,
            valueDes: record.valueDes,
            classifyCode: this.state.activeKey,
            microServiceCode: this.state.belongsMicroService,
        };
        const res = await this.props.ownerStore.addConstantlist(params);
        if (res) {
            this.setState({ editingIndex: -1 });
        }
    };

    handleSearch = (value) => {
        this.props.ownerStore.changeState({ searchValue: value });
        this.props.ownerStore.getConstantAllData2({ searchValue: value, pageNo: 1 });
    };
    handleSearchClassify = (value) => {
        this.props.ownerStore.changeState({ searchClassifyValue: value });
        this.props.ownerStore.getContantsTabsData({ searchClassifyValue: value });
        // this.props.ownerStore.getConstantAllData2({ searchValue: value, pageNo: 1 });
    };
    selectedOption = (selectedList, record) => {
        let selectedItems = [];
        let selectedItemsNames = [];
        let { setTranslator } = this.props.ownerStore;
        selectedList.forEach((item, index) => {
            selectedItems.push(item.id);
            selectedItemsNames.push(item.name);
        });
        setTranslator(selectedItems, selectedItemsNames, this.state.activeKey);
        this.setState({
            selectedList,
        });
    };

    renderGridText = (field) => {
        return (value, record, index) => {
            if (record.status && record.status == 1) {
                return (
                    <FormControl
                        fieldid="ublinker-routes-MappingRelation-components-AddModal-index-3784885-FormControl"
                        value={value}
                        showClose
                        size={"sm"}
                        onChange={this.handleChange.bind(null, index, field)}
                        style={{ width: 180 }}
                    />
                );
            }
            return value;
        };
    };

    columns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803BD", "常量规则名称") /* "常量规则名称" */,
            dataIndex: "name",
            render: this.renderGridText("name"),
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803BE", "转换前") /* "转换前" */,
            dataIndex: "valueSrc",
            render: this.renderGridText("valueSrc"),
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803C0", "转换后") /* "转换后" */,
            dataIndex: "valueDes",
            render: this.renderGridText("valueDes"),
        },
    ];

    hoverContent = (record) => {
        return (
            <Fragment>
                {record.status && record.status == 1 ? (
                    <Button
                        fieldid="UCG-FE-routes-MappingRelation-components-AddModal-index-8906701-Button"
                        {...Grid.hoverButtonPorps}
                        onClick={() => {
                            this.handleAdd(record);
                        }}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_18D7622804180242", "保存") /* "保存" */}
                    </Button>
                ) : null}
                <Button
                    fieldid="UCG-FE-routes-MappingRelation-components-AddModal-index-2734942-Button"
                    {...Grid.hoverButtonPorps}
                    onClick={() => {
                        this.handleDelete(record.id);
                    }}
                >
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803B8", "删除") /* "删除" */}
                </Button>
            </Fragment>
        );
    };

    renderActions = (value, record) => {
        return (
            <GridActions>
                {record.status && record.status == 1 ? (
                    <GridAction
                        fieldid="UCG-FE-routes-MappingRelation-components-AddModal-index-3537720-GridAction"
                        onClick={() => {
                            this.handleAdd(record);
                        }}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_1CCD7D9A05F00002", "确定") /* "确定" */}
                    </GridAction>
                ) : null}
                <GridAction
                    fieldid="UCG-FE-routes-MappingRelation-components-AddModal-index-747880-GridAction"
                    onClick={() => {
                        this.handleDelete(record.id);
                    }}
                >
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803B8", "删除") /* "删除" */}
                </GridAction>
            </GridActions>
        );
    };

    onTabChange = (activeKey, option) => {
        //切换需清空选中的
        this.searchInputRef.current.setValue("");
        this.setState({
            activeKey,
            belongsMicroService: option.microServiceCode,
        });
        let activeName = this.props.ownerState.tabsData.find((item) => {
            return item.code == activeKey;
        }).name;
        this.props.ownerStore.changeState({
            activeKey,
            conversionRuleValueIds: [activeKey],
            conversionRuleValueNames: [activeName],
            constantRuleClassify: "",
            searchValue: "",
        });
        this.props.ownerStore.getConstantAllData2({ classifyCode: activeKey, pageNo: 1 });
    };
    remove = async (e, targetKey) => {
        console.log(e, targetKey);
        e.stopPropagation();
        ModalConfirm.confirm({
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803C4", "确认要删除吗？") /* "确认要删除吗？" */,
            onOk: async () => {
                let res = await this.props.ownerStore.delNewTab(targetKey);
                if (res) {
                    this.setState({
                        activeKey: this.props.ownerState.tabsData[0].code,
                    });
                    this.props.ownerStore.changeState({ activeKey: this.props.ownerState.tabsData[0].code });
                    this.onTabChange(this.props.ownerState.tabsData[0].code);
                }
            },
        });
        // res && this.add()
        // let { activeKey } = this.state;
        // let lastIndex = 0;
        // this.state.panes.forEach((pane, i) => {
        //   if (pane.key === targetKey) {
        //     lastIndex = i - 1;
        //   }
        // });
        // const panes = this.state.panes.filter(pane => pane.key !== targetKey);
        // if (panes.length && activeKey === targetKey) {
        //   if (lastIndex >= 0) {
        //     activeKey = panes[lastIndex].key;
        //   }
        //   else {
        //     activeKey = panes[0].key;
        //   }
        // }
        // this.setState({ panes, activeKey });
    };
    saveNewTab = () => {
        this.form.current.validateFields().then(async (values) => {
            let res = await this.props.ownerStore.saveNewTab(values);
            res && this.add();
        });
    };
    add = () => {
        console.log("add");
        // const { panes } = this.state;
        // const activeKey = `newTab${this.newTabIndex++}`;
        // panes.push({ title: 'New Tab', content: 'Content of new Tab', key: activeKey });
        this.setState({
            showNewTab: !this.state.showNewTab,
        });
    };
    onEdit = (targetKey, action, a, b, c) => {
        console.log("onEdit", targetKey, action, a, b, c);
        this[action](targetKey);
    };
    importExcel = async (key) => {
        await this.props.ownerStore.importExcelConstant({ file: file, classifyCode: this.state.activeKey });
    };
    handleScroll = (res) => {
        console.log("333,", res);
        this.setState({
            testvalue: res,
        });
    };
    menuOnCard = () => {
        let { ownerStore } = this.props;
        return (
            <Menu fieldid="ublinker-routes-list-components-GateWayCard-index-25778611-Menu" onClick={this.handleDownload} className="dataMapModal-download">
                <Menu.Item fieldid="UCG-FE-routes-list-components-GateWayCard-index-293655211-Menu.Item" key={"1"}>
                    <Upload fieldid="UCG-FE-routes-MappingRelation-components-IndexView-index-71849541-Upload" {...this.uploadData}>
                        <Button fieldid="ublinker-routes-MappingRelation-components-IndexView-index-39262351-Button" type="text" style={{ color: "#111" }}>
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418050D", "导入excel") /* "导入excel" */}
                        </Button>
                    </Upload>
                </Menu.Item>
                <Menu.Item fieldid="UCG-FE-routes-list-components-GateWayCard-index-2936552112-Menu.Item" key={"2"}>
                    <Button
                        fieldid="ublinker-routes-MappingRelation-components-IndexView-index-39262352-Button"
                        type="text"
                        style={{ color: "#111" }}
                        onClick={ownerStore.downloadExcel}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_1B12E78A05400005", "下载Excel模板") /* "下载Excel模板" */}
                    </Button>
                </Menu.Item>
            </Menu>
        );
    };
    menuOnCard2 = () => {
        let { ownerStore } = this.props;
        let { activeKey } = this.state;
        return (
            <Menu fieldid="ublinker-routes-list-components-GateWayCard-index-257786111-Menu" onClick={this.handleDownload} className="dataMapModal-download">
                <Menu.Item fieldid="UCG-FE-routes-list-components-GateWayCard-index-2936552111-Menu.Item" key={"1"}>
                    <Button
                        fieldid="ublinker-routes-MappingRelation-components-IndexView-index-39262351-Button"
                        type="text"
                        style={{ color: "#111" }}
                        onClick={() => {
                            ownerStore.exportContants(activeKey);
                        }}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_1C2A899805700010", "导出excel") /* "导出excel" */}
                    </Button>
                </Menu.Item>
            </Menu>
        );
    };
    render() {
        let { show, onCancel, ownerState, ownerStore, IntegrateApplicationState } = this.props;
        let { constantData, tabsData, pagination, conversionRuleValueNames } = ownerState;
        let { selectedList, showNewTab, activeKey } = this.state;
        return (
            <>
                <Modal
                    fieldid="ublinker-routes-MappingRelation-components-AddModal-index-1338799-Modal"
                    show={show}
                    cancelText={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803C6", "取消") /* "取消" */}
                    title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803C7", "常量", undefined, {
                        returnStr: true,
                    })}
                    onOk={this.onOk}
                    onCancel={this.onCancel}
                    className="contanst-modal"
                    width={"1200px"}
                    height={"600px"}
                >
                    <Layout fieldid="UCG-FE-routes-MappingRelation-components-AddModal-index-4430569-Layout" style={{ height: "100%", overflow: "hidden" }}>
                        <Sider
                            className={styles.slider}
                            width={"240px"}
                            style={{
                                height: "100%",
                                borderRight: "1px solid #D3D3D3",
                                padding: "16px",
                                textAlign: "center",
                                overflow: "hidden",
                                display: "flex",
                            }}
                        >
                            <SearchInput
                                // ref={this.searchInputRef}
                                className=""
                                // size="md"
                                style={{ width: "100%" }}
                                placeholder={lang.templateByUuid("UID:P_UBL-FE_1C27C2C80438000D", "请输入编码/名称", undefined, {
                                    returnStr: true,
                                })}
                                showClear
                                onSearch={this.handleSearchClassify}
                            />
                            {tabsData && tabsData.length == 0 ? (
                                <div>
                                    <div style={{ margin: "50px 0 20px 0", fontSize: "24px" }}>
                                        <Empty fieldid="demo_1" />
                                    </div>
                                    <Button fieldid="UCG-FE-routes-MappingRelation-components-AddModal-index-7416738-Button" onClick={this.add}>
                                        {lang.templateByUuid("UID:P_UBL-FE_1CCD5A3805F0000A", "新增") /* "新增" */}
                                    </Button>
                                </div>
                            ) : (
                                <>
                                    <div className="contanst-modal-add">
                                        <Icon fieldid="UCG-FE-routes-MappingRelation-components-AddModal-index-4040997-Icon" type="uf-plus" />
                                        <Button fieldid="UCG-FE-routes-MappingRelation-components-AddModal-index-7698840-Button" type="text" onClick={this.add}>
                                            {lang.templateByUuid("UID:P_UBL-FE_1CCD5A3805F0000A", "新增") /* "新增" */}
                                        </Button>
                                    </div>
                                    <div fieldid="UCG-FE-routes-MappingRelation-components-AddModal-index-9983081-Tabs" className="AddModal-tabs">
                                        {tabsData &&
                                            tabsData.map((pane, index) => (
                                                <div
                                                    className={`AddModal-tabs-item ${this.state.activeKey == pane.code && "tabcurrentIndex"}`}
                                                    fieldid="UCG-FE-routes-MappingRelation-components-AddModal-index-8132553-TabPane"
                                                    key={pane.code}
                                                    id={pane.id}
                                                    title={`${pane.name}(${pane.code})`}
                                                    // closable={pane.code != "defclassifycode" && true}
                                                    onClick={this.onTabChange.bind(null, pane.code, pane)}
                                                >
                                                    <span className="contanst-tab">{`${pane.name}(${pane.code})`}</span>
                                                    <Icon
                                                        fieldid="UCG-FE-routes-MappingRelation-components-AddModal-index-716896-Icon"
                                                        type="uf-close"
                                                        className="contanst-tab-close"
                                                        onClick={(e) => {
                                                            this.remove(e, pane.code);
                                                        }}
                                                    />
                                                </div>
                                            ))}
                                    </div>
                                </>
                            )}
                        </Sider>
                        {tabsData && tabsData.length == 0 ? (
                            <div style={{ margin: "100px auto", fontSize: "30px" }}>
                                <Empty fieldid="demo_1" />
                            </div>
                        ) : (
                            <Content md={10} className="scheme-main" style={{ overflow: "auto" }}>
                                <Header style={{ height: 48, minHeight: 48, paddingTop: 10, paddingLeft: 10 }} className="addmodal-header">
                                    <span className="addmodal-header-title" title={conversionRuleValueNames[0]}>
                                        {conversionRuleValueNames[0]}
                                    </span>
                                    <Button
                                        fieldid="ublinker-routes-MappingRelation-components-AddModal-index-5095443-Button"
                                        style={{ float: "right", marginRight: 10 }}
                                        // colors="primary"
                                        onClick={() => {
                                            if (this.state.editingIndex !== -1) {
                                                Message.destroy();
                                                Message.create({
                                                    content: lang.templateByUuid("UID:P_UBL-FE_1D964EF805E00009", "请先保存当前行") /* "请先保存当前行" */,
                                                    color: "warning",
                                                });
                                                return;
                                            }
                                            ownerStore.addConstant();
                                            this.setState({ editingIndex: 0 });
                                        }}
                                    >
                                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803BF", "新增") /* "新增" */}
                                    </Button>

                                    <ButtonGroup style={{ float: "right", marginRight: 10 }}>
                                        <Dropdown
                                            fieldid="ublinker-routes-list-components-GateWayCard-index-15218481-Dropdown"
                                            overlay={this.menuOnCard()}
                                            placement="bottom"
                                        >
                                            <Button
                                                fieldid="ublinker-routes-list-components-DataMapModal-index-164555511-Button"
                                                className=""
                                                colors=""
                                                type="uf-anglearrowdown"
                                            >
                                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418051B", "导入") /* "导入" */}
                                                <Icon fieldid="d35afdfb-f6ce-4c67-919f-4951c76e128c" style={{ marginLeft: 5 }} type="uf-anglearrowdown" />
                                            </Button>
                                        </Dropdown>
                                        <Dropdown
                                            fieldid="ublinker-routes-list-components-GateWayCard-index-152184811-Dropdown"
                                            overlay={this.menuOnCard2()}
                                            placement="bottom"
                                        >
                                            <Button fieldid="ublinker-routes-list-components-DataMapModal-index-1645555111-Button" className="" colors="">
                                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418051E", "导出") /* "导出" */}
                                                <Icon fieldid="5e0b0be9-2b4d-41ce-b2c9-49bdad9b2e62" style={{ marginLeft: 5 }} type="uf-anglearrowdown" />
                                            </Button>
                                        </Dropdown>
                                    </ButtonGroup>

                                    <SearchInput
                                        ref={this.searchInputRef}
                                        className="ucg-mar-r-20 ucg-float-r"
                                        size="md"
                                        // style={{ float: 'right', marginRight: 10 }}
                                        placeholder={lang.templateByUuid("UID:P_UBL-FE_1C27C2C80438000D", "请输入编码/名称", undefined, {
                                            returnStr: true,
                                        })}
                                        showClear
                                        onSearch={this.handleSearch}
                                    />
                                </Header>
                                <Grid
                                    fieldid="ublinker-routes-MappingRelation-components-AddModal-index-680785-Grid"
                                    rowKey={"id"}
                                    autoCheckedByClickRows={false}
                                    columns={this.columns}
                                    data={constantData.list}
                                    // multiSelect
                                    selectedRowIndex={0}
                                    // selectedList={selectedList}
                                    // getSelectedDataFunc={this.selectedOption}
                                    pagination={pagination}
                                    hoverContent={this.hoverContent}
                                    // style={{ height: '100px'}}
                                    scroll={{ y: 450 }}
                                />
                            </Content>
                        )}
                    </Layout>
                </Modal>
                {showNewTab && (
                    <Modal
                        fieldid="ublinker-routes-MappingRelation-components-referModal-index-6859259-Modal"
                        show={showNewTab}
                        backdropClosable={false}
                        className="ucg-ma-modal modal-add"
                        title={lang.templateByUuid("UID:P_UBL-FE_1CCD5A3805F0000A", "新增", undefined, {
                            returnStr: true,
                        })}
                        onCancel={this.add}
                        onOk={this.saveNewTab}
                        size="md"
                        height={250}
                    >
                        <FormList
                            fieldid="ublinker-routes-MappingRelation-components-referModal-index-************-FormList"
                            // className="coxnfig-action-form"
                            ref={this.form}
                            name="form122"
                            {...formItemLayout}
                        >
                            <FormItem
                                fieldid="UCG-FE-routes-MappingRelation-components-AddModal-index-7344595-FormItem"
                                name="name"
                                rules={[
                                    {
                                        required: true,
                                        message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803C1", "请输入分组名称") /* "请输入分组名称" */,
                                    },
                                ]}
                                fieldid="ublinker-routes-MappingRelation-components-referModal-index-3282702-FormItem"
                                label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803C2", "分组名称") /* "分组名称" */}
                            >
                                <FormControl fieldid="ublinker-routes-MappingRelation-components-referModal-index-672413-TreeSelect" showSearch />
                            </FormItem>
                            <FormItem
                                fieldid="UCG-FE-routes-MappingRelation-components-AddModal-index-6258309-FormItem"
                                name="code"
                                rules={[
                                    {
                                        required: true,
                                        message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803C5", "请输入分组编码") /* "请输入分组编码" */,
                                    },
                                    {
                                        pattern: codeReg,
                                        message: lang.templateByUuid(
                                            "UID:P_UBL-FE_1C2A899805700011",
                                            "分组编码由英文、数字、下划线组成"//@notranslate
                                        ) /* "分组编码由英文、数字、下划线组成" */,
                                    },
                                ]}
                                fieldid="ublinker-routes-MappingRelation-components-referModal-index-3282702-FormItem"
                                label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803CA", "分组编码") /* "分组编码" */}
                            >
                                <FormControl fieldid="ublinker-routes-MappingRelation-components-referModal-index-672413-TreeSelect" showSearch />
                            </FormItem>
                            {JSON.parse(localStorage.getItem("ublinkerEnv") || "{}").env === "test" ? (
                                <FormItem
                                    label={lang.templateByUuid("UID:P_UBL-FE_2007730004F8000E", "YMS微服务") /* "YMS微服务" */}
                                    name="microServiceCode"
                                    validateTrigger="onChange"
                                >
                                    <Select
                                        showSearch
                                        allowClear
                                        optionFilterProp="children"
                                        placeholder={lang.templateByUuid("UID:P_UBL-FE_2007730004F8000F", "请选择") /* "请选择" */}
                                    >
                                        {Object.entries(IntegrateApplicationState?.ymsMicro || {}).map(([key, value]) => (
                                            <option key={value} value={value}>
                                                {key}
                                            </option>
                                        ))}
                                    </Select>
                                </FormItem>
                            ) : null}
                        </FormList>
                    </Modal>
                )}
            </>
        );
    }
}
export default AddConstantModal;
