.u-row {
    padding-top: 15px;
}

.u-form-control.rc-textarea {
    min-height: 100px !important;
}
.addmodal-header .wui-upload-wrapper {
    float: right;
}
.contanst-modal .wui-modal-content .wui-modal-body {
    padding: 0;
}

.contanst-modal .wui-tabs-tab {
    width: 200px !important;
    text-align: left;
}
.contanst-modal .wui-tabs {
    width: auto;
}
.contanst-modal-add {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-bottom: 10px;
    button {
        margin-left: -5px;
        margin-top: 2px;
        padding: 0;
    }
}
.contanst-tab {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    flex: 1;
}
.contanst-tab-close {
    display: none;
    font-size: 12px;
    cursor: pointer;
}
.contanst-modal .wui-tabs-tab:hover .contanst-tab-close {
    display: inline-block;
}
.addmodal-header-title {
    font-size: 12px;
    font-family:
        PingFangSC-Regular,
        PingFang SC;
    font-weight: 400;
    color: #111111;
    line-height: 28px;
    display: inline-block;
    width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.contanst-modal .wui-tabs-ink-bar-animated {
    display: none !important;
}
.AddModal-tabs {
    overflow-y: auto;
    text-align: left;
}
.AddModal-tabs-item {
    display: flex;
    align-items: center;
    overflow: hidden;
    padding: 6px 5px;
    cursor: pointer;
}
.AddModal-tabs-item:hover {
    background: #f7f7f7;
    color: red;
}
.AddModal-tabs-item:hover .contanst-tab-close {
    display: inline-block;
}
.tabcurrentIndex {
    font-weight: bold;
}
