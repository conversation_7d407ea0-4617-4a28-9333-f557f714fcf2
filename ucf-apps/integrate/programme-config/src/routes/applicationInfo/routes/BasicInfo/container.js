import React, { Component } from "react";
import IndexView from "./components/IndexView";
import { inject, observer } from "mobx-react";
import Store, { storeKey } from "../../store";

import { toJS } from "mobx";
import { storeKey as integrateApplicationKey } from "../../../application/store";

@inject((rootStore) => {
    let ownerStore = rootStore[storeKey];
    let integrateApplicationStore = rootStore[integrateApplicationKey];
    return {
        ownerState: ownerStore.toJS(),
        applicationInfoState: toJS(integrateApplicationStore?.applicationInfo),
        IntegrateApplicationStore: integrateApplicationStore,
        IntegrateApplicationState: toJS(integrateApplicationStore?.state),
        ownerStore: ownerStore,
    };
})
@observer
class Container extends Component {
    constructor(props) {
        super(props);
    }
    render() {
        return <IndexView {...this.props} />;
    }
}

export default Container;
