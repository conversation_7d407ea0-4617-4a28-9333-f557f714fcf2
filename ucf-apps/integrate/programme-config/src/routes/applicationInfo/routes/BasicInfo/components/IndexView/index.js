import React, { Component, Fragment } from "react";
import { FormControl, Select, Radio, FormList, Tooltip, Row, Col, InputNumber } from "components/TinperBee";
import "./index.less";
import { TreeSelect } from "@tinper/next-ui";
import styles from "./index.modules.css";
import { codeReg } from "utils/regExp";
import SelectApplicationModal from "../SelectApplicationModal";
import Paragraph from "components/Paragraph";
import { getPageParams } from "decorator/index";

import CustomSelect from "./CustomSelect";

const FormItem = FormList.Item;
const Option = Select.Option;
const formItemLayout = {
    labelCol: { span: 5 },
    wrapperCol: { span: 19 },
};

@getPageParams
class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            showModal: false,
            tokenPluginInfo: null,
            newArch: false,
            showApplicationListModal: false,
            isFromOpenApi: false,
        };
    }
    async componentDidMount() {
        if (window.name === "_WORKBENCH_") {
            window.jDiwork.getContext((data) => {
                this.setState({
                    newArch: data.ext.newArch,
                });
            });
        }
        const { connectorsArr, categoryInfo, categoryInfo2, info } = this.props.ownerState;
        const { applicationInfoState: applicationInfo } = this.props; // inject store
        const [transRules, patterns] = await Promise.all([this.props.ownerStore.getTransRuleList(), this.props.ownerStore.getPattern()]);
        this.props.basicInfoRef.current?.setFieldsValue({
            ...{
                transformationRule: transRules[0].value,
                errorType: patterns[0].value,
                ...this.props.ownerState.info,
            },
            sourceConnectorCode: connectorsArr?.find((item) => {
                return item.value === this.props.ownerState?.info?.sourceConnectorCode;
            })?.type,
            sourceObjectCode: info?.sourceObjectCode || undefined,
            targetObjectCode: info?.targetObjectCode || undefined,
            applicationCode: applicationInfo.code,
            applicationName: applicationInfo.name,
            //回退到第一步，如果是编辑用categoryInfo，如果是新建用info.sourceCategoryId
            sourceCategoryId: (categoryInfo && categoryInfo.id) || "" || this.props.ownerState.info.sourceCategoryId,
            targetCategoryId: (categoryInfo2 && categoryInfo2.id) || "" || this.props.ownerState.info.targetCategoryId,
        });
        this.props.ownerStore.changeState({
            ///把应用名称和编码放到Info中
            info: {
                transformationRule: transRules[0].value,
                errorType: patterns[0].value,
                ...this.props.ownerState.info,
                applicationCode: applicationInfo.code,
                applicationName: applicationInfo.name,
                fromType: 2,
            },
        });

        const {
            queryParams: { apiId },
        } = this.props.getPageParams(this.props);
        this.setState({ isFromOpenApi: !!apiId });
    }

    componentDidUpdate(prevProps) {
        if (
            this.props.ownerState.info.schemeCode !== prevProps.ownerState.info.schemeCode ||
            this.props.ownerState.categoryInfo.id !== prevProps.ownerState.categoryInfo.id ||
            this.props.ownerState.categoryInfo2.id !== prevProps.ownerState.categoryInfo2.id
        ) {
            this.props.basicInfoRef.current?.setFieldsValue({
                ...this.props.ownerState.info,
                ...this.props.applicationInfo,
                sourceConnectorCode:
                    this.props.ownerState?.connectorsArr?.find((item) => {
                        return item.value === this.props.ownerState?.info?.sourceConnectorCode;
                    })?.type || "",
            });
            if (this.props.ownerState.editId) {
                //如果是编辑
                this.props.basicInfoRef.current?.setFieldsValue({
                    sourceCategoryId: (this.props.ownerState.categoryInfo && this.props.ownerState.categoryInfo.id) || "",
                    targetCategoryId: (this.props.ownerState.categoryInfo2 && this.props.ownerState.categoryInfo2.id) || "",
                });
            }
        }
    }
    showSelectedApplicationInfo = (item = {}) => {
        const { connectorsArr, connectConfigObj, openApiSourceConnectorCode, openApiSourceObjectId, systemList, info, integrateObjList } =
            this.props.ownerState;
        let sourceShowValue = connectorsArr.find((item) => {
            return item.value == openApiSourceConnectorCode;
        })?.type;
        let sourceConnectorCode, sourceConnectConfigId, targetConnectorCode, targetConnectConfigId, sourceObjectCode, sourceObjectName;
        if (sourceShowValue) {
            sourceConnectorCode = sourceShowValue;
            sourceConnectConfigId = connectConfigObj[sourceShowValue]?.[0]?.value;
            targetConnectorCode = connectorsArr.find((item) => {
                return item.type != sourceShowValue;
            })?.value;
            targetConnectConfigId =
                connectConfigObj[
                    Object.keys(connectConfigObj).find((item) => {
                        return item != sourceShowValue;
                    })
                ]?.[0]?.value;
            ({ code: sourceObjectCode, name: sourceObjectName } =
                integrateObjList.length != 0 &&
                (integrateObjList.find((item) => item.id == openApiSourceObjectId) ? integrateObjList.find((item) => item.id == openApiSourceObjectId) : ""));

            //调用目标分类
            this.props.ownerStore.getTargetCategoryList({
                linkerCode:
                    "" ||
                    connectorsArr.find((item) => {
                        return item.type != sourceShowValue;
                    })?.value,
                connectConfigId:
                    connectConfigObj[
                        Object.keys(connectConfigObj).find((item) => {
                            return item != sourceShowValue;
                        })
                    ]?.[0]?.value,
            });
        }
        this.props.basicInfoRef.current?.setFieldsValue({
            applicationName: item.name,
            applicationCode: item.code,
            sourceConnectorCode,
            sourceConnectConfigId,
            targetConnectorCode,
            targetConnectConfigId,
            sourceCategoryId: systemList && systemList[0] && systemList[0].value,
            sourceObjectCode,
        });

        //手动赋值这8项，其他的还按原来的操作并赋值
        this.props.ownerStore.changeInfo("applicationName", item.name);
        this.props.ownerStore.changeInfo("applicationCode", item.code);
        this.props.ownerStore.changeInfo("sourceConnectorCode", sourceConnectorCode);
        this.props.ownerStore.changeInfo("targetConnectorCode", targetConnectorCode);
        this.props.ownerStore.changeInfo("sourceConnectConfigId", sourceConnectConfigId);
        this.props.ownerStore.changeInfo("targetConnectConfigId", targetConnectConfigId);
        this.props.ownerStore.changeInfo("sourceCategoryId", systemList[0] && systemList[0].value);
        this.props.ownerStore.changeInfo("sourceObjectCode", sourceObjectCode);
        this.props.ownerStore.changeInfo("sourceObjectName", sourceObjectName);

        //开放平台暂时只能被动，所以这里设置为0
        this.props.ownerStore.changeInfo("getDataWay", "0");
    };
    transErrorType = (nums) => {
        let text = "";
        switch (nums) {
            case 1:
                text = lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804C4", "错误时停止") /* "错误时停止" */;
                break;
            case 2:
                text = lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804C7", "错误时忽略") /* "错误时忽略" */;
                break;
            default:
                text = "";
                break;
        }
        return text;
    };

    translateText = (nums) => {
        let text = "";
        switch (nums) {
            case 0:
                text = lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804A9", "无") /* "无" */;
                break;
            case 1:
                text = lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804AD", "编码相同") /* "编码相同" */;
                break;
            case 2:
                text = lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804AF", "名称相同") /* "名称相同" */;
                break;
        }
        return text;
    };

    handleSourceChange = (field, value, a, b, c) => {
        let { info, systemList, categoryInfo } = this.props.ownerState;
        // let getcode = systemList.find(ele => ele.id == value);
        this.props.ownerStore.changeInfo("sourceCategoryId", value);
        this.props.ownerStore.changeInfo("sourceCategoryName", a[0]);

        this.props.ownerStore.changeInfo("sourceObjectCode", "");
        this.props.ownerStore.changeInfo("sourceObjectName", "");
        this.props.basicInfoRef.current?.setFieldsValue({ sourceObjectCode: "" });
        this.props.ownerStore.getSourceObjectListByCategoryId({
            Id: "" || value,
            linkerCode: "" || info.sourceConnectorCode,
            connectConfigId: info.sourceConnectConfigId || "",
        }); //1536006594267971588
        //因为分类是从接口获取，而非方案上，所以如果修改后，同步到这里，再进到该页面是修改后的
        this.props.ownerStore.changeState({ categoryInfo: { ...categoryInfo, name: a[0], id: value } });
    };

    handleTargetChange = (field, value, a, b, c) => {
        let { info, systemList, openApiApiId, categoryInfo2 } = this.props.ownerState;
        // let getcode = systemList.find(ele => ele.id == value);
        this.props.ownerStore.changeInfo("targetCategoryId", value);
        this.props.ownerStore.changeInfo("targetCategoryName", a[0]);

        this.props.ownerStore.changeInfo("targetObjectCode", "");
        this.props.ownerStore.changeInfo("targetObjectName", "");
        this.props.basicInfoRef.current?.setFieldsValue({ targetObjectCode: "" });
        this.props.ownerStore.getTargetObjectListByCategoryId({
            Id: "" || value,
            linkerCode: "" || info.targetConnectorCode,
            connectConfigId: info.targetConnectConfigId || "",
        }); //openApiApiId 开放平台调过来传apiId apiId:openApiApiId,
        this.props.ownerStore.changeState({ categoryInfo2: { ...categoryInfo2, name: a[0], id: value } });
    };

    handleInput = (field, value, self, b, c) => {
        let {
            ownerState,
            ownerState: { connectConfigObj, connectorsArr, isSameOfTwoConnectors },
        } = this.props;
        this.props.ownerStore.changeInfo(field, value);
        if (field == "sourceConnectorCode") {
            const sourceConnectConfigId = connectConfigObj[value][0].value;
            const targetConnectConfigId =
                connectConfigObj[
                    Object.keys(connectConfigObj).find((item) => {
                        return item != value;
                    })
                ][0].value;
            // this.props.ownerStore.changeState({   //选择来源连接器后给来源连接配置赋值 连接配置不让选 ，这个就没用了
            //     connectConfigItem:this.props.ownerState.connectConfigObj[type]
            // })
            //选择来源连接器后给  目标连接器赋值，来源连接配置，目标连接配置  赋值，给来源目标分类、对象和目标分类、对象全置空,下面原来是用value（u8open）判断，但
            //是两个连接器有可能都是 u8open，所以遍历结构的时候加个唯一值type,用type判断
            this.props.ownerStore.changeInfo("sourceConnectorCode", self.realValue);
            this.props.ownerStore.changeInfo(
                "targetConnectorCode",
                connectorsArr.length != 0 &&
                    connectorsArr.find((item) => {
                        return item.type != value;
                    }).value
            );
            this.props.ownerStore.changeInfo("sourceConnectConfigId", connectConfigObj[value][0].value);
            this.props.ownerStore.changeInfo(
                "targetConnectConfigId",
                connectConfigObj[
                    Object.keys(connectConfigObj).find((item) => {
                        return item != value;
                    })
                ][0].value
            );

            this.props.ownerStore.changeInfo("sourceCategoryId", "");
            this.props.ownerStore.changeInfo("sourceCategoryName", "");
            this.props.ownerStore.changeInfo("sourceObjectCode", "");
            this.props.ownerStore.changeInfo("sourceObjectName", "");
            this.props.ownerStore.changeInfo("targetCategoryId", "");
            this.props.ownerStore.changeInfo("targetCategoryName", "");
            this.props.ownerStore.changeInfo("targetObjectCode", "");
            this.props.ownerStore.changeInfo("targetObjectName", "");
            this.props.basicInfoRef.current?.setFieldsValue({
                sourceCategoryId: "",
                sourceObjectCode: "",
                targetCategoryId: "",
                targetObjectCode: "",
                targetConnectorCode:
                    connectorsArr.length != 0 &&
                    connectorsArr.find((item) => {
                        return item.type != value;
                    }).value, //手动给form赋值
                sourceConnectConfigId: connectConfigObj[value][0].value,
                targetConnectConfigId:
                    connectConfigObj[
                        Object.keys(connectConfigObj).find((item) => {
                            return item != value;
                        })
                    ][0].value,
            });

            this.props.ownerStore.getSourceCategoryList({ linkerCode: "" || self.realValue, connectConfigId: sourceConnectConfigId }); //调用来源分类
            this.props.ownerStore.getTargetCategoryList({
                linkerCode:
                    "" ||
                    (connectorsArr.length != 0 &&
                        connectorsArr.find((item) => {
                            return item.type != value;
                        }).value),
                connectConfigId: targetConnectConfigId,
            }); //调用目标分类

            //如果有一个连接器编码为 api网关连接器，就设置为被动
            if (connectorsArr.some((obj) => obj.value == "api-gateway-connector")) {
                this.props.ownerStore.changeInfo("getDataWay", "0");
            }
        } else if (field == "targetConnectorCode") {
            // this.props.ownerStore.changeState({   //选择来源连接器后给来源连接配置赋值 连接配置不让选 ，这个就没用了
            //     connectConfigItem:this.props.ownerState.connectConfigObj[type]
            // })
            //选择来源连接器后给  目标连接器赋值，来源连接配置，目标连接配置  赋值，给来源目标分类、对象和目标分类、对象全置空,下面原来是用value（u8open）判断，但
            //是两个连接器有可能都是 u8open，所以遍历结构的时候加个唯一值type,用type判断
            this.props.ownerStore.changeInfo(
                "sourceConnectorCode",
                connectorsArr.length != 0 &&
                    connectorsArr.find((item) => {
                        return item.value != value;
                    }).value
            );
            this.props.ownerStore.changeInfo("targetConnectorCode", value);
            this.props.ownerStore.changeInfo(
                "sourceConnectConfigId",
                connectConfigObj[
                    connectorsArr.find((item) => {
                        return item.value != value;
                    }).type
                ][0].value
            );
            this.props.ownerStore.changeInfo(
                "targetConnectConfigId",
                connectConfigObj[
                    connectorsArr.find((item) => {
                        return item.value == value;
                    }).type
                ][0].value
            );

            this.props.ownerStore.changeInfo("sourceCategoryId", "");
            this.props.ownerStore.changeInfo("sourceCategoryName", "");
            this.props.ownerStore.changeInfo("sourceObjectCode", "");
            this.props.ownerStore.changeInfo("sourceObjectName", "");
            this.props.ownerStore.changeInfo("targetCategoryId", "");
            this.props.ownerStore.changeInfo("targetCategoryName", "");
            this.props.ownerStore.changeInfo("targetObjectCode", "");
            this.props.ownerStore.changeInfo("targetObjectName", "");
            this.props.basicInfoRef.current?.setFieldsValue({
                sourceCategoryId: "",
                sourceObjectCode: "",
                targetCategoryId: "",
                targetObjectCode: "",
                sourceConnectorCode:
                    connectorsArr.length != 0 &&
                    connectorsArr.find((item) => {
                        return item.value != value;
                    }).type,
                sourceConnectConfigId:
                    connectConfigObj[
                        connectorsArr.find((item) => {
                            return item.value != value;
                        }).type
                    ][0].value,
                targetConnectConfigId:
                    connectConfigObj[
                        connectorsArr.find((item) => {
                            return item.value == value;
                        }).type
                    ][0].value,
            });

            this.props.ownerStore.getSourceCategoryList({
                linkerCode:
                    "" ||
                    (connectorsArr.length != 0 &&
                        connectorsArr.find((item) => {
                            return item.value != value;
                        }).value),
                connectConfigId:
                    connectConfigObj[
                        connectorsArr.find((item) => {
                            return item.value != value;
                        }).type
                    ][0].value,
            }); //调用来源分类
            this.props.ownerStore.getTargetCategoryList({
                linkerCode: "" || value,
                connectConfigId:
                    connectConfigObj[
                        connectorsArr.find((item) => {
                            return item.value == value;
                        }).type
                    ][0].value,
            }); //调用目标分类

            //如果有一个连接器编码为 api网关连接器，就设置为被动
            if (connectorsArr.some((obj) => obj.value == "api-gateway-connector")) {
                this.props.ownerStore.changeInfo("getDataWay", "0");
            }
        } else if (field == "longTSFlag" && value) {
            this.props.ownerStore.changeInfo("pageSize", "1");
            // this.props.ownerStore.changeInfo('pageType',3);
        }
    };
    onSelect = (value) => {
        this.props.ownerStore.getSelectedOptionFunc(value);
    };

    dealLog = (log) => {
        if (log === "") {
            return "";
        }
        if (!isNaN(log)) {
            return log.toString();
        }
    };

    checkCode = (value) => {
        const {
            ownerStore,
            ownerState: { editId },
        } = this.props;
        ownerStore.checkCode(value, editId);
    };
    handleBtnClick = (type, value) => {
        console.log(type, value);
    };
    toThousands(num) {
        if (!num && num !== 0) return "";
        let result = "",
            counter = 0;
        num = (num || 0).toString();
        const numArr = num.split(".");
        num = numArr[0];
        for (let i = num.length - 1; i >= 0; i--) {
            counter++;
            result = num.charAt(i) + result;
            if (!(counter % 3) && i != 0) {
                result = "," + result;
            }
        }
        return numArr.length === 1 ? result : result + "." + numArr[1];
    }
    handlePop = (record, index) => {
        const { ownerStore } = this.props;
        this.setState({ showApplicationListModal: true });
        ownerStore.changeState({});
    };
    handlePopClose = (record, index) => {
        this.setState({ showApplicationListModal: false });
    };
    render() {
        let { ownerState, ownerStore, IntegrateApplicationStore } = this.props;
        let { showApplicationListModal } = this.state;
        let {
            info: { targetObjectName },
            ruleList,
            pattern,
            systemList,
            systemList2,
            integrateObjList,
            integrateObjList2,
            correspondDataType,
            editId,
            connectorsArr,
            connectConfigObj,
            connectConfigItem,
            connectConfigArr,
            isSameOfTwoConnectors,
            fromOpenApi,
        } = ownerState;
        // debugger

        // 允许方案编码前后有空格的正则表达式
        const schemeCodeReg = /^\s*[0-9a-zA-Z_*]+\s*$/;

        return (
            <Fragment>
                <div className={styles["source-container"]}>
                    <div className={styles["source-container-left"]}>
                        <FormList
                            fieldid="ublinker-routes-BasicInfo-components-IndexView-index-791995-FormList"
                            ref={this.props.basicInfoRef}
                            // labelWrap
                            {...formItemLayout}
                        >
                            <Paragraph
                                title={lang.templateByUuid("UID:P_UBL-FE_1C0264D605D80030", "基本设置") /* "基本设置" */}
                                children={
                                    <>
                                        {fromOpenApi ? (
                                            <FormItem
                                                fieldid="ublinker-routes-BasicInfo-components-IndexView-index-608047-FormItem"
                                                label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804AB", "集成应用名称") /* "集成应用名称" */}
                                                name="applicationName"
                                                rules={[
                                                    {
                                                        required: true,
                                                    },
                                                ]}
                                            >
                                                <FormControl
                                                    fieldid="ublinker-routes-MappingRelation-components-IndexView-index-3713291-FormControl"
                                                    className="clicked-input"
                                                    readOnly
                                                    size={"sm"}
                                                    suffix={
                                                        <i
                                                            fieldid="ublinker-routes-MappingRelation-components-IndexView-index-692592-i"
                                                            className="plugin-icon-select cl cl-list"
                                                        />
                                                    }
                                                    autoSize={{ minRows: 0, maxRows: 0 }}
                                                    onClick={() => {
                                                        this.handlePop(1, 1);
                                                    }}
                                                />
                                            </FormItem>
                                        ) : null}

                                        <FormItem
                                            fieldid="ublinker-routes-BasicInfo-components-IndexView-index-608047-FormItem"
                                            label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804B5", "方案编码") /* "方案编码" */}
                                            name="schemeCode"
                                            rules={[
                                                {
                                                    required: true,
                                                    message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804B8", "请输入方案编码") /* "请输入方案编码" */,
                                                },
                                                {
                                                    pattern: schemeCodeReg,
                                                    message: lang.templateByUuid(
                                                        "UID:P_UBL-FE_18D8CEF6041804BA",
                                                        "方案编码由英文、数字、下划线组成" //@notranslate
                                                    ) /* "方案编码由英文、数字、下划线组成" */,
                                                },
                                            ]}
                                        >
                                            <FormControl
                                                fieldid="ublinker-routes-BasicInfo-components-IndexView-index-5470986-FormControl"
                                                // disabled={editId}
                                                maxLength={128}
                                                onChange={(value) => {
                                                    this.handleInput("schemeCode", value);
                                                }}
                                                onBlur={this.checkCode}
                                                placeholder={lang.templateByUuid("UID:P_UBL-FE_1D17E1CA0598000A", "请输入英文、数字、下划线", undefined, {
                                                    returnStr: true,
                                                })}
                                            />
                                        </FormItem>

                                        <FormItem
                                            fieldid="ublinker-routes-BasicInfo-components-IndexView-index-777771-FormItem"
                                            label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804BE", "方案名称") /* "方案名称" */}
                                            name="schemeName"
                                            rules={[
                                                {
                                                    required: true,
                                                    message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804C0", "请输入方案名称") /* "请输入方案名称" */,
                                                },
                                            ]}
                                        >
                                            <FormControl
                                                placeholder={lang.templateByUuid("UID:P_UBL-FE_1CE536BE0558000F", "请输入") /* "请输入" */}
                                                fieldid="ublinker-routes-BasicInfo-components-IndexView-index-5483535-FormControl"
                                                maxLength={128}
                                                onChange={(value) => {
                                                    this.handleInput("schemeName", value);
                                                }}
                                            />
                                        </FormItem>

                                        <FormItem
                                            fieldid="ublinker-home-components-Modal-U9Modal-index-3065310-FormItem"
                                            label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804C9", "来源连接器") /* "来源连接器" */}
                                            required
                                        >
                                            <div className={styles["nested-form-item"]}>
                                                <FormItem
                                                    fieldid="ublinker-routes-BasicInfo-components-IndexView-index-608047-FormItem"
                                                    noStyle
                                                    name="sourceConnectorCode"
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message: lang.templateByUuid(
                                                                "UID:P_UBL-FE_18D8CEF6041804AE",
                                                                "请选择来源连接器名称" //@notranslate
                                                            ) /* "请选择来源连接器名称" */,
                                                        },
                                                    ]}
                                                >
                                                    <Select
                                                        placeholder={lang.templateByUuid("UID:P_UBL-FE_1CE536BE0558000C", "请选择") /* "请选择" */}
                                                        fieldid="ublinker-routes-BasicInfo-components-IndexView-index-8915159-Select"
                                                        style={{ flex: 1, minWidth: 0 }}
                                                        onChange={(value, a, b, c) => {
                                                            this.handleInput("sourceConnectorCode", value, a, b, c);
                                                        }}
                                                    >
                                                        {connectorsArr.map((item) => {
                                                            return (
                                                                <Option
                                                                    fieldid="UCG-FE-routes-BasicInfo-components-IndexView-index-134150-Option"
                                                                    key={item.type}
                                                                    value={item.type}
                                                                    realValue={item.value}
                                                                >
                                                                    {item.title}
                                                                </Option>
                                                            );
                                                        })}
                                                    </Select>
                                                </FormItem>
                                                <FormItem
                                                    fieldid="ublinker-routes-BasicInfo-components-IndexView-index-608047-FormItem"
                                                    noStyle
                                                    name="sourceConnectConfigId"
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message: lang.templateByUuid(
                                                                "UID:P_UBL-FE_18D8CEF6041804B9",
                                                                "请选择来源连接配置名称" //@notranslate
                                                            ) /* "请选择来源连接配置名称" */,
                                                        },
                                                    ]}
                                                >
                                                    <Select
                                                        placeholder={lang.templateByUuid("UID:P_UBL-FE_1CE536BE0558000C", "请选择") /* "请选择" */}
                                                        style={{ marginLeft: "10px", flex: 1, minWidth: 0 }}
                                                        fieldid="ublinker-routes-BasicInfo-components-IndexView-index-8915159-Select"
                                                        disabled
                                                        onChange={(value, a, b, c) => {
                                                            this.handleInput("sourceConnectConfigId", value, a, b);
                                                        }}
                                                    >
                                                        {connectConfigArr.map((item) => {
                                                            return (
                                                                <Option
                                                                    fieldid="UCG-FE-routes-BasicInfo-components-IndexView-index-8578696-Option"
                                                                    key={item.value}
                                                                    value={item.value}
                                                                >
                                                                    {`${item.title}${item.versionName ? "-" + item.versionName : ""}`}
                                                                </Option>
                                                            );
                                                        })}
                                                    </Select>
                                                </FormItem>
                                            </div>
                                        </FormItem>
                                        <FormItem
                                            fieldid="ublinker-home-components-Modal-U9Modal-index-3065310-FormItem"
                                            label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804C1", "目标连接器") /* "目标连接器" */}
                                            required
                                        >
                                            <div className={styles["nested-form-item"]}>
                                                <FormItem
                                                    fieldid="ublinker-routes-BasicInfo-components-IndexView-index-608047-FormItem"
                                                    noStyle
                                                    name="targetConnectorCode"
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message: lang.templateByUuid(
                                                                "UID:P_UBL-FE_18D8CEF6041804CB",
                                                                "请选择目标连接器名称" //@notranslate
                                                            ),
                                                        },
                                                    ]}
                                                >
                                                    <Select
                                                        placeholder={lang.templateByUuid("UID:P_UBL-FE_1CE536BE0558000C", "请选择") /* "请选择" */}
                                                        fieldid="ublinker-routes-BasicInfo-components-IndexView-index-8915159-Select"
                                                        // disabled
                                                        style={{ flex: 1, minWidth: 0 }}
                                                        onChange={(value) => {
                                                            this.handleInput("targetConnectorCode", value);
                                                        }}
                                                    >
                                                        {connectorsArr.map((item) => {
                                                            return (
                                                                <Option
                                                                    fieldid="UCG-FE-routes-BasicInfo-components-IndexView-index-1595199-Option"
                                                                    key={item.value}
                                                                    value={item.value}
                                                                >
                                                                    {item.title}
                                                                </Option>
                                                            );
                                                        })}
                                                    </Select>
                                                </FormItem>

                                                <FormItem
                                                    fieldid="ublinker-routes-BasicInfo-components-IndexView-index-608047-FormItem"
                                                    noStyle
                                                    name="targetConnectConfigId"
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message: lang.templateByUuid(
                                                                "UID:P_UBL-FE_18D8CEF6041804B2",
                                                                "请选择目标连接配置名称" //@notranslate
                                                            ) /* "请选择目标连接配置名称" */,
                                                        },
                                                    ]}
                                                >
                                                    <Select
                                                        placeholder={lang.templateByUuid("UID:P_UBL-FE_1CE536BE0558000C", "请选择") /* "请选择" */}
                                                        style={{ marginLeft: "10px", flex: 1, minWidth: 0 }}
                                                        fieldid="ublinker-routes-BasicInfo-components-IndexView-index-8915159-Select"
                                                        disabled
                                                        onChange={(value, a, b, c) => {
                                                            this.handleInput("targetConnectConfigId", value, a, b);
                                                        }}
                                                    >
                                                        {connectConfigArr.map((item) => {
                                                            return (
                                                                <Option
                                                                    fieldid="UCG-FE-routes-BasicInfo-components-IndexView-index-4310675-Option"
                                                                    key={item.value}
                                                                    value={item.value}
                                                                >
                                                                    {`${item.title}${item.versionName ? "-" + item.versionName : ""}`}
                                                                </Option>
                                                            );
                                                        })}
                                                    </Select>
                                                </FormItem>
                                            </div>
                                        </FormItem>

                                        <FormItem
                                            fieldid="ublinker-routes-BasicInfo-components-IndexView-index-5429168-FormItem"
                                            label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804BB", "来源对象分类") /* "来源对象分类" */}
                                            validateTrigger="onChange"
                                            required={!this.state.isFromOpenApi} // 开放平台放开对象限制
                                        >
                                            <div className={styles["nested-form-item"]}>
                                                <FormItem
                                                    fieldid="ublinker-routes-BasicInfo-components-IndexView-index-5429168-FormItem"
                                                    noStyle
                                                    name="sourceCategoryId"
                                                    rules={[
                                                        {
                                                            required: !this.state.isFromOpenApi,
                                                            message: lang.templateByUuid(
                                                                "UID:P_UBL-FE_18D8CEF6041804C5",
                                                                "请选择来源对象分类" //@notranslate
                                                            ) /* "请选择来源对象分类" */,
                                                        },
                                                    ]}
                                                >
                                                    <TreeSelect
                                                        placeholder={lang.templateByUuid("UID:P_UBL-FE_1CE536BE0558000C", "请选择") /* "请选择" */}
                                                        fieldid="ublinker-routes-BasicInfo-components-IndexView-index-5595485-TreeSelect"
                                                        style={{ flex: 1, minWidth: 0 }}
                                                        showSearch
                                                        treeData={systemList}
                                                        treeNodeFilterProp={"name"}
                                                        fieldNames={{ label: "name", value: "id", children: "children", disabled: "disabled" }}
                                                        size={"small"}
                                                        onChange={(value, a, b, c) => {
                                                            this.handleSourceChange("sourceCategoryId", value, a, b, c);
                                                        }}
                                                        notFoundContent={lang.templateByUuid("UID:P_UBL-FE_18D9147C05300012", "无匹配结果") /* "无匹配结果" */}
                                                        treeDefaultExpandAll
                                                    />
                                                </FormItem>

                                                <FormItem
                                                    fieldid="ublinker-routes-BasicInfo-components-IndexView-index-3795180-FormItem"
                                                    noStyle
                                                    name="sourceObjectCode"
                                                    rules={[
                                                        {
                                                            required: !this.state.isFromOpenApi,
                                                            message: lang.templateByUuid(
                                                                "UID:P_UBL-FE_1CE536BE0558000D",
                                                                "请选择来源对象" //@notranslate
                                                            ) /* "请选择来源对象" */,
                                                        },
                                                    ]}
                                                >
                                                    <CustomSelect
                                                        style={{ marginLeft: "10px", flex: 1, minWidth: 0 }}
                                                        options={integrateObjList}
                                                        connectVersionCode={this.props.ownerState.info?.sourceConnectCodeVersion}
                                                        onEdit={(value) => {
                                                            this.handleInput("sourceObjectCode", value);
                                                        }}
                                                    />
                                                </FormItem>
                                            </div>
                                        </FormItem>
                                        <FormItem
                                            fieldid="ublinker-routes-BasicInfo-components-IndexView-index-8624496-FormItem"
                                            label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804B7", "目标对象分类") /* "目标对象分类" */}
                                            validateTrigger="onChange"
                                            required={!this.state.isFromOpenApi}
                                        >
                                            <div className={styles["nested-form-item"]}>
                                                <FormItem
                                                    fieldid="ublinker-routes-BasicInfo-components-IndexView-index-8624496-FormItem"
                                                    noStyle
                                                    name="targetCategoryId"
                                                    rules={[
                                                        {
                                                            required: !this.state.isFromOpenApi,
                                                            message: lang.templateByUuid(
                                                                "UID:P_UBL-FE_18D8CEF6041804BD",
                                                                "请选择目标对象分类" //@notranslate
                                                            ) /* "请选择目标对象分类" */,
                                                        },
                                                    ]}
                                                >
                                                    <TreeSelect
                                                        placeholder={lang.templateByUuid("UID:P_UBL-FE_1CE536BE0558000C", "请选择") /* "请选择" */}
                                                        fieldid="ublinker-routes-BasicInfo-components-IndexView-index-5595485-TreeSelect"
                                                        showSearch
                                                        style={{ flex: 1, minWidth: 0 }}
                                                        treeData={systemList2}
                                                        treeNodeFilterProp={"name"}
                                                        fieldNames={{ label: "name", value: "id", children: "children", disabled: "disabled" }}
                                                        size={"small"}
                                                        onChange={(value, a, b, c) => {
                                                            this.handleTargetChange("targetCategoryId", value, a, b, c);
                                                        }}
                                                        notFoundContent={lang.templateByUuid("UID:P_UBL-FE_18D9147C05300012", "无匹配结果") /* "无匹配结果" */}
                                                        treeDefaultExpandAll
                                                    />
                                                </FormItem>

                                                <FormItem
                                                    fieldid="ublinker-routes-BasicInfo-components-IndexView-index-6677190-FormItem"
                                                    noStyle
                                                    name="targetObjectCode"
                                                    rules={[
                                                        {
                                                            required: !this.state.isFromOpenApi,
                                                            message: lang.templateByUuid(
                                                                "UID:P_UBL-FE_1CE536BE0558000E",
                                                                "请选择目标对象" //@notranslate
                                                            ) /* "请选择目标对象" */,
                                                        },
                                                    ]}
                                                >
                                                    <CustomSelect
                                                        style={{ marginLeft: "10px", flex: 1, minWidth: 0 }}
                                                        options={integrateObjList2}
                                                        connectVersionCode={this.props.ownerState.info?.targetConnectCodeVersionTwo}
                                                        onEdit={(value) => {
                                                            this.handleInput("targetObjectCode", value);
                                                            // this.handleSourceInput("", value);
                                                        }}
                                                    />
                                                </FormItem>
                                            </div>
                                        </FormItem>
                                    </>
                                }
                            />

                            <Paragraph
                                title={lang.templateByUuid("UID:P_UBL-FE_1C0264D605D8002D", "高级设置") /* "高级设置" */}
                                isOpen={true}
                                children={
                                    <>
                                        <FormItem label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804B0", "错误处理") /* "错误处理" */} required={true}>
                                            <div className={styles["nested-form-item"]}>
                                                <FormItem
                                                    noStyle
                                                    fieldid="ublinker-routes-BasicInfo-components-IndexView-index-7552854-FormItem"
                                                    name="errorType"
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message: lang.templateByUuid(
                                                                "UID:P_UBL-FE_18D8CEF6041804B1",
                                                                "请选择错误处理" //@notranslate
                                                            ) /* "请选择错误处理" */,
                                                        },
                                                    ]}
                                                >
                                                    <Select
                                                        placeholder={lang.templateByUuid("UID:P_UBL-FE_1CE536BE0558000C", "请选择") /* "请选择" */}
                                                        fieldid="ublinker-routes-BasicInfo-components-IndexView-index-283657-Select"
                                                        onChange={(value) => {
                                                            this.handleInput("errorType", value);
                                                        }}
                                                    >
                                                        {pattern.map((item) => {
                                                            return (
                                                                <Option key={item.value} value={item.value}>
                                                                    {item.typeName}
                                                                </Option>
                                                            );
                                                        })}
                                                    </Select>
                                                </FormItem>
                                                <Tooltip
                                                    fieldid="eb49d822-363f-498d-8c2a-6a3475f6d2e5"
                                                    inverse
                                                    overlay={
                                                        lang.templateByUuid(
                                                            "UID:P_UBL-FE_1C0264D605D80031",
                                                            "数据翻译失败时处理方式，忽略将抛弃数据继续同步，停止将停止任务并抛出错误" //@notranslate
                                                        ) /* "数据翻译失败时处理方式，忽略将抛弃数据继续同步，停止将停止任务并抛出错误" */
                                                    }
                                                >
                                                    <i fieldid="5cc1cb98-38b7-499e-a74b-62d7f2f9e6f0" className="cl cl-Q row-tip" />
                                                </Tooltip>
                                            </div>
                                        </FormItem>
                                        <FormItem
                                            label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804B4", "自动映射规则") /* "自动映射规则" */}
                                            required={true}
                                        >
                                            <div className={styles["nested-form-item"]}>
                                                <FormItem
                                                    noStyle
                                                    fieldid="ublinker-routes-BasicInfo-components-IndexView-index-9416887-FormItem"
                                                    name="transformationRule"
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message: lang.templateByUuid(
                                                                "UID:P_UBL-FE_18D8CEF6041804B6",
                                                                "请选择自动映射规则" //@notranslate
                                                            ) /* "请选择自动映射规则" */,
                                                        },
                                                    ]}
                                                >
                                                    <Select
                                                        placeholder={lang.templateByUuid("UID:P_UBL-FE_1CE536BE0558000C", "请选择") /* "请选择" */}
                                                        fieldid="ublinker-routes-BasicInfo-components-IndexView-index-585224-Select"
                                                        onChange={(value) => {
                                                            this.handleInput("transformationRule", value);
                                                        }}
                                                    >
                                                        {ruleList.map((item) => {
                                                            return (
                                                                <Option key={item.value} value={item.value}>
                                                                    {item.typeName}
                                                                </Option>
                                                            );
                                                        })}
                                                    </Select>
                                                </FormItem>
                                                <Tooltip
                                                    fieldid="76ebc1a8-7693-4e29-8f96-6620fbcc3865"
                                                    inverse
                                                    overlay={
                                                        lang.templateByUuid(
                                                            "UID:P_UBL-FE_1C0264D605D80032",
                                                            "来源与目标数据快速映射方式，支持名称或编码映射" //@notranslate
                                                        ) /* "来源与目标数据快速映射方式，支持名称或编码映射" */
                                                    }
                                                >
                                                    <i fieldid="84c3f93f-ba89-46f8-8c12-f183fea6d272" className="cl cl-Q row-tip" />
                                                </Tooltip>
                                            </div>
                                        </FormItem>
                                        <FormItem
                                            label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804C8", "是否树形档案") /* "是否树形档案" */}
                                            required={true}
                                        >
                                            <div className={styles["nested-form-item"]}>
                                                <FormItem
                                                    noStyle
                                                    fieldid="ublinker-routes-BasicInfo-components-IndexView-index-7739803-FormItem"
                                                    name="treeDoc"
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message: lang.templateByUuid(
                                                                "UID:P_UBL-FE_18D8CEF6041804CA",
                                                                "请选择是否为树形档案" //@notranslate
                                                            ) /* "请选择是否为树形档案" */,
                                                        },
                                                    ]}
                                                >
                                                    <Radio.Group
                                                        onChange={(value) => {
                                                            this.handleInput("treeDoc", value);
                                                        }}
                                                    >
                                                        <Radio fieldid="ublinker-routes-BasicInfo-components-IndexView-index-6048118-Radio" value="1">
                                                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804C2", "是") /* "是" */}
                                                        </Radio>
                                                        <Radio fieldid="ublinker-routes-BasicInfo-components-IndexView-index-352763-Radio" value="0">
                                                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804C3", "否") /* "否" */}
                                                        </Radio>
                                                    </Radio.Group>
                                                </FormItem>
                                                <Tooltip
                                                    fieldid="2689e9ad-4acf-4544-ab57-34cd5f7d5a9b"
                                                    inverse
                                                    overlay={
                                                        lang.templateByUuid(
                                                            "UID:P_UBL-FE_1C0265E605D80008",
                                                            "数据为树形档案,将会推送两次，需要保证数据的层级结构正确和父级ID正确，例如组织档案的上下级关系" //@notranslate
                                                        ) /* "数据为树形档案,将会推送两次，需要保证数据的层级结构正确和父级ID正确，例如组织档案的上下级关系" */
                                                    }
                                                >
                                                    <i fieldid="a04fe6da-3386-44b6-9da6-754f8f6b09b6" className="cl cl-Q row-tip" />
                                                </Tooltip>
                                            </div>
                                        </FormItem>
                                        <FormItem
                                            label={
                                                lang.templateByUuid(
                                                    "UID:P_UBL-FE_2007730004F8000A",
                                                    "部分成功是否更新增量时间戳" //@notranslate
                                                ) /* "部分成功是否更新增量时间戳" */
                                            }
                                            required={true}
                                        >
                                            <div className={styles["nested-form-item"]}>
                                                <FormItem
                                                    noStyle
                                                    name="partialSuccessUpdateTime"
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message: lang.templateByUuid("UID:P_UBL-FE_1CE536BE0558000C", "请选择") /* "请选择" */,
                                                        },
                                                    ]}
                                                >
                                                    <Radio.Group
                                                        onChange={(value) => {
                                                            this.handleInput("partialSuccessUpdateTime", value);
                                                        }}
                                                    >
                                                        <Radio value="1">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804C2", "是") /* "是" */}</Radio>
                                                        <Radio value="0">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804C3", "否") /* "否" */}</Radio>
                                                    </Radio.Group>
                                                </FormItem>
                                            </div>
                                        </FormItem>
                                        <FormItem label={lang.templateByUuid("UID:P_UBL-FE_1C0264D605D8002E", "存储模型编码") /* "存储模型编码" */}>
                                            <div className={styles["nested-form-item"]}>
                                                <FormItem
                                                    fieldid="ublinker-routes-BasicInfo-components-IndexView-index-5817038-FormItem"
                                                    name="correspondDataType"
                                                    noStyle
                                                >
                                                    <Select
                                                        placeholder={lang.templateByUuid("UID:P_UBL-FE_1CE536BE0558000C", "请选择") /* "请选择" */}
                                                        style={{ flex: 1 }}
                                                        fieldid="ublinker-routes-BasicInfo-components-IndexView-index-6073802-Select"
                                                        showSearch={true}
                                                        allowClear={true}
                                                        optionFilterProp="children"
                                                        onChange={(value) => {
                                                            this.handleInput("correspondDataType", value);
                                                        }}
                                                    >
                                                        {correspondDataType.map((item) => {
                                                            return (
                                                                <Option
                                                                    fieldid="UCG-FE-routes-BasicInfo-components-IndexView-index-6044229-Option"
                                                                    key={item.code}
                                                                    value={item.code}
                                                                >
                                                                    {item.name}
                                                                </Option>
                                                            );
                                                        })}
                                                    </Select>
                                                </FormItem>
                                                <Tooltip
                                                    fieldid="d96cedb6-4c29-4c6b-bf7c-e420ed6de8da"
                                                    inverse
                                                    overlay={
                                                        lang.templateByUuid(
                                                            "UID:P_UBL-FE_1C0264D605D80035",
                                                            "选择存储模型后，多个集成方案产生的数据映射关系将存储在一个模型中被翻译器使用" //@notranslate
                                                        ) /* "选择存储模型后，多个集成方案产生的数据映射关系将存储在一个模型中被翻译器使用" */
                                                    }
                                                >
                                                    <i fieldid="01c543c7-99dd-4ebd-9f32-01bdab37d755" className="cl cl-Q row-tip" />
                                                </Tooltip>
                                            </div>
                                        </FormItem>
                                        <FormItem label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804B3", "每秒执行次数") /* "每秒执行次数" */}>
                                            <div className={styles["nested-form-item"]}>
                                                <FormItem
                                                    noStyle
                                                    fieldid="ublinker-routes-BasicInfo-components-IndexView-index-5817038-FormItem"
                                                    name="pageExecRate"
                                                >
                                                    <InputNumber
                                                        placeholder={lang.templateByUuid("UID:P_UBL-FE_1CE536BE0558000F", "请输入") /* "请输入" */}
                                                        fieldid="UCG-FE-routes-BasicInfo-components-IndexView-index-3941603-InputNumber"
                                                        id="demo1-id"
                                                        // onPressEnter={() => alert('123')}
                                                        iconStyle="one"
                                                        min={60}
                                                        max={100}
                                                        autoFix
                                                        toNumber
                                                        //  value={this.state.value}
                                                        format={(value) => this.toThousands(value)}
                                                        //   onFocus={(value) => { this.setState({ value});   }}
                                                        onChange={(value) => {
                                                            this.handleInput("pageExecRate", value);
                                                        }}
                                                        handleBtnClick={this.handleBtnClick}
                                                        toThousands
                                                    />
                                                </FormItem>
                                                <Tooltip
                                                    fieldid="52880b8b-f619-4763-b9e2-e306bcf5d7ad"
                                                    inverse
                                                    overlay={
                                                        lang.templateByUuid(
                                                            "UID:P_UBL-FE_1C0264D605D80034",
                                                            "为保护下游服务，可以设置执行频率" //@notranslate
                                                        ) /* "为保护下游服务，可以设置执行频率" */
                                                    }
                                                >
                                                    <i fieldid="8d4fdb12-0cb6-47b7-9b72-0ff31deb4706" className="cl cl-Q row-tip" />
                                                </Tooltip>
                                            </div>
                                        </FormItem>
                                        <FormItem label={lang.templateByUuid("UID:P_UBL-FE_191DEC3004500040", "附件传输方案") /* "附件传输方案" */}>
                                            <div className={styles["nested-form-item"]}>
                                                <FormItem
                                                    noStyle
                                                    fieldid="ublinker-routes-BasicInfo-components-IndexView-index-77398031025-FormItem"
                                                    name="longTSFlag"
                                                >
                                                    <Radio.Group
                                                        onChange={(value) => {
                                                            this.handleInput("longTSFlag", value);
                                                        }}
                                                    >
                                                        <Radio fieldid="ublinker-routes-BasicInfo-components-IndexView-index-60481181025-Radio" value={true}>
                                                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804C2", "是") /* "是" */}
                                                        </Radio>
                                                        <Radio fieldid="ublinker-routes-BasicInfo-components-IndexView-index-3527631025-Radio" value={false}>
                                                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804C3", "否") /* "否" */}
                                                        </Radio>
                                                    </Radio.Group>
                                                </FormItem>
                                                <Tooltip
                                                    fieldid="0867391b-392a-4913-8ac2-825df5f63f95"
                                                    inverse
                                                    overlay={
                                                        lang.templateByUuid(
                                                            "UID:P_UBL-FE_1C0264D605D80033",
                                                            "开启后，本方案可以支持附件传递" //@notranslate
                                                        ) /* "开启后，本方案可以支持附件传递" */
                                                    }
                                                >
                                                    <i fieldid="e5543c0d-5729-44d3-86bb-6a70278f662c" className="cl cl-Q row-tip" />
                                                </Tooltip>
                                            </div>
                                        </FormItem>

                                        {true && (
                                            <FormItem label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804BC", "支持特征转换") /* "支持特征转换" */}>
                                                <div className={styles["nested-form-item"]}>
                                                    <FormItem
                                                        noStyle
                                                        fieldid="ublinker-routes-BasicInfo-components-IndexView-index-77398031025-FormItem"
                                                        name="supportFeatures"
                                                    >
                                                        <Radio.Group
                                                            onChange={(value) => {
                                                                this.handleInput("supportFeatures", value);
                                                            }}
                                                        >
                                                            <Radio fieldid="ublinker-routes-BasicInfo-components-IndexView-index-60481181025-Radio" value="1">
                                                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804C2", "是") /* "是" */}
                                                            </Radio>
                                                            <Radio fieldid="ublinker-routes-BasicInfo-components-IndexView-index-3527631025-Radio" value="0">
                                                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804C3", "否") /* "否" */}
                                                            </Radio>
                                                        </Radio.Group>
                                                    </FormItem>
                                                    <Tooltip
                                                        fieldid="1a481905-c260-4bcf-9bdc-1a776c2e4a02"
                                                        inverse
                                                        overlay={
                                                            lang.templateByUuid(
                                                                "UID:P_UBL-FE_18D8CEF6041804C6",
                                                                "是否根据特征同步结果自动处理数据" //@notranslate
                                                            ) /* "是否根据特征同步结果自动处理数据" */
                                                        }
                                                    >
                                                        <i fieldid="cc66934b-8e04-483d-8f8e-58037b56e61f" className="cl cl-Q row-tip" />
                                                    </Tooltip>
                                                </div>
                                            </FormItem>
                                        )}
                                        {JSON.parse(localStorage.getItem("ublinkerEnv") || "{}").env === "test" ? (
                                            <FormItem
                                                label={lang.templateByUuid("UID:P_UBL-FE_2007730004F8000B", "YMS微服务") /* "YMS微服务" */}
                                                name="microServiceCode"
                                                validateTrigger="onChange"
                                            >
                                                <Select
                                                    showSearch
                                                    allowClear
                                                    optionFilterProp="children"
                                                    placeholder={lang.templateByUuid("UID:P_UBL-FE_1CE536BE0558000C", "请选择") /* "请选择" */}
                                                    onChange={(value) => {
                                                        this.handleInput("microServiceCode", value);
                                                    }}
                                                >
                                                    {Object.entries(this.props.IntegrateApplicationState?.ymsMicro || {}).map(([key, value]) => (
                                                        <option key={value} value={value}>
                                                            {key}
                                                        </option>
                                                    ))}
                                                </Select>
                                            </FormItem>
                                        ) : null}
                                    </>
                                }
                            />
                        </FormList>
                    </div>
                    <div className={styles["source-container-center"]} />
                    <div className={styles["source-container-right"]}></div>
                </div>

                {showApplicationListModal ? (
                    <SelectApplicationModal
                        show={showApplicationListModal}
                        onCancel={this.handlePopClose}
                        onOk={this.showSelectedApplicationInfo}
                        ownerStore={ownerStore}
                        ownerState={ownerState}
                        IntegrateApplicationStore={IntegrateApplicationStore}
                    ></SelectApplicationModal>
                ) : null}
            </Fragment>
        );
    }
}

export default IndexView;
