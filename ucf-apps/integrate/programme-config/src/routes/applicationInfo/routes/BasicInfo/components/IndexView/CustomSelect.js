import React, { useEffect, useRef, useState } from "react";
import { Select } from "@tinper/next-ui";
const CustomSelect = (props) => {
    const { connectVersionCode, options, onEdit, onChange, ...rest } = props;
    // const [composCode, setComposCode] = useState(value);
    const isChanged = useRef(false);

    // 兼容集成方案中老的操作和对象数据去匹配来自开放平台的新下拉数据
    useEffect(() => {
        if (!isChanged.current && connectVersionCode && rest.value && options?.length) {
            isChanged.current = true;
            const objCode = `${rest.value}_${connectVersionCode}`;
            if (options.some((item) => item.code === objCode)) {
                handleChange(objCode);
            } else {
                handleChange(rest.value);
            }
        }
    }, [rest.value, options]);
    const handleChange = (data) => {
        onEdit(data);
        onChange(data);
    };
    return (
        <>
            <Select showSearch optionFilterProp="children" onChange={handleChange} {...rest}>
                {options?.map((item) => (
                    <Select.Option key={item.id} value={item.code} item={item}>
                        {item.name}
                    </Select.Option>
                ))}
            </Select>
        </>
    );
};
export default CustomSelect;
