import React, { useState, useEffect, useCallback } from "react";
import Modal from "components/TinperBee/Modal";
import { <PERSON>ton, FormList, FormControl, Select, Tabs } from "components/TinperBee";
import Grid from "components/TinperBee/Grid";
import { Success, Error } from "utils/feedback";
import "./index.less";
const columnObj = [
    {
        title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180379", "应用编码") /* "应用编码" */,
        dataIndex: "code",
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418037A", "应用名称") /* "应用名称" */,
        dataIndex: "name",
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418037B", "连接器1") /* "连接器1" */,
        dataIndex: "connectTypeName",
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418037C", "连接器2") /* "连接器2" */,
        dataIndex: "connectTypeNameTwo",
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180376", "描述") /* "描述" */,
        dataIndex: "memo",
    },
];

// 添加连接弹窗
const SelectApplicationModal = (props) => {
    let {
        show,
        info = {},
        ownerState: { pagination, applicationList },
        ownerStore: { getApplicationList, setSelectAppSearch },
        onCancel,
        onOk,
    } = props;

    const [selectedList, setSelectedList] = useState([]);

    useEffect(() => {
        getApplicationList();
        return () => {
            setSelectAppSearch(undefined);
        };
    }, []);

    // 确认
    const handleCommit = () => {
        if (selectedList.length == 0) {
            Error(
                lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180378", "请选择应用", undefined, {
                    returnStr: true,
                }) /* "请选择应用" */
            );
            return;
        }
        onOk(selectedList[0]);
        onCancel();
    };

    const getSelectedDataFunc = (selectedList, data) => {
        setSelectedList(selectedList);
        let applicationInfo = selectedList[0];
        props.IntegrateApplicationStore.changeApplicationInfo(applicationInfo);
        props.ownerStore.changeState({
            connectorsArr: [
                { value: applicationInfo.connectTypeCode, title: applicationInfo.connectTypeName, type: "one" },
                { value: applicationInfo.connectTypeCodeTwo, title: applicationInfo.connectTypeNameTwo, type: "two" },
            ],
            connectConfigObj: {
                one: [{ value: applicationInfo.connectId, title: applicationInfo.connectName, type: "one" }],
                two: [{ value: applicationInfo.connectIdTwo, title: applicationInfo.connectNameTwo, type: "two" }],
            },
            connectConfigArr: [
                { value: applicationInfo.connectId, title: applicationInfo.connectName, versionName: applicationInfo.versionName },
                { value: applicationInfo.connectIdTwo, title: applicationInfo.connectNameTwo, versionName: applicationInfo.versionNameTwo },
            ],
        });
    };
    const handleSearch = (searchValue) => {
        setSelectAppSearch(searchValue);
        getApplicationList({
            pageNo: 1,
        });
        // props.ownerStore.getOpenApiApplication({
        //     pageNo: 1,
        //     searchValue,
        // });
    };
    return (
        <>
            <Modal
                fieldid="ublinker-home-components-Modal-U9Modal-index-7518987-Modal"
                title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180377", "选择应用", undefined, {
                    returnStr: true,
                })}
                show={show}
                height={600}
                width={1000}
                onCancel={onCancel}
                onOk={handleCommit}
                className="initApplication-modal"
            >
                <div className="modal-search-box" style={{ display: "flex", justifyContent: "flex-end" }}>
                    <FormControl
                        fieldid="ublinker-routes-home-components-IndexView-index-************-FormControl"
                        className=""
                        style={{ width: "240px", margin: "10px" }}
                        onSearch={(value) => {
                            if (/[\/\\.\{}\[\]\s]/.test(value)) {
                                Error(
                                    lang.templateByUuid("UID:P_UBL-FE_1AFE6A0C04280007", "搜索内容不能包含特殊字符：/,\\ , .或空格", undefined, {
                                        returnStr: true,
                                    }) /* "搜索内容不能包含特殊字符：/,\\ , .或空格" */
                                );
                                return;
                            }
                            handleSearch(value);
                        }}
                        placeholder={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418004F", "搜索名称/编码", undefined, {
                            returnStr: true,
                        })}
                        showClose
                        type="search"
                    />
                </div>
                <Grid
                    fieldid="ublinker-routes-home-components-TablesList-index-3738456-Grid"
                    rowKey={"id"}
                    lockable={false}
                    radioSelect
                    selectedList={selectedList}
                    className="expanded-table"
                    columns={columnObj}
                    data={applicationList.list}
                    pagination={pagination}
                    getSelectedDataFunc={getSelectedDataFunc}
                />
            </Modal>
        </>
    );
};

export default SelectApplicationModal;
