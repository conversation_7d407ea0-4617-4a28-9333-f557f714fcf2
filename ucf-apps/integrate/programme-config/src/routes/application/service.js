import { getInvokeService, getServicePath } from "utils/service";

/**
 * 或去我的连接器（已添加连接器）
 * @param {Object} data
 * @param {String} data.key -搜索条件
 * @return {Promise<unknown>}
 */
export const selfConnectorsService = function (data, header) {
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/ncconnect/listMyConnects",
            header,
        },
        data
    );
};

/**
 * 删除连接
 * @param {Object} id

 * @return {Promise<unknown>}
 */
export const deleteConnectorService = function (id, header) {
    return getInvokeService({
        method: "POST",
        path: `/gwmanage/gwportal/diwork/connect/delete?id=${id}`,
        header,
    });
};

/**
 * 获取连接器
 * @param {Object} data
 * @param {String} data.key -搜索条件
 * @param {String} data.type=[erp|ec] -erp erp拦截器 ec电商连接器
 * @return {Promise<unknown>}
 */
export const getConnectorsService = function (data, header) {
    let { key, type } = data;
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/ncconnect/connector/" + type,
            header,
        },
        { key }
    );
};

/**
 * 获取可重启网关erp类型
 * @returns {Promise | Promise<unknown>}
 */
export const getRestartGwErpTypesService = function (header) {
    return getInvokeService({
        method: "GET",
        path: "/mygwapp/ncconnect/erp/type/allowRestart",
        showLoading: false,
        header,
    });
};

/**
 * 重启网关
 * @param {String} gatewayId
 * @returns {Promise | Promise<unknown>}
 */
export const restartGatewayIdService = function (gatewayId, header) {
    return getInvokeService({
        method: "GET",
        path: `/mygwapp/gateway/${gatewayId}/restart`,
        header,
    });
};

/**
 * 通用连接配置首页-获取连接器
 * @param {String} gatewayId
 * @returns {Promise | Promise<unknown>}
 */
export const getCommonConnector = function (header) {
    return getInvokeService({
        method: "GET",
        // _1604989107868
        path: "/gwmanage/gwportal/diwork/connect/listLinker",
        header,
    });
};
export const testConnectService = function ({ id }, header) {
    return getInvokeService(
        {
            method: "GET",
            // _1604989107868
            path: "/gwmanage/gwportal/diwork/ncconnect/testConnection",
            header,
        },
        { tenantConnectId: id }
    );
};

export const setDefaultService = function (data, header) {
    return getInvokeService(
        {
            method: "PUT",
            path: "/gwmanage/gwportal/diwork/ncconnect/connect/setLinkerLevelDefault",
            header,
        },
        {},
        data
    );
};
export const getPageData = function (data, header) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwmanage/gwportal/diwork/ncconnect/listMyConnects/classifyByType",
            header,
        },
        data
    );
};
export const initU8EmailService = function (data, header) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwmanage/gwportal/diwork/queryOpenapiConfigNew",
            header,
        },
        data
    );
};
export const getU8EmailService = function (data, header) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwmanage/gwportal/diwork/getCurrentUser",
            header,
        },
        data
    );
};
export const changeU8EmailService = function (data, header) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwmanage/gwportal/diwork/changeUserApiConfig",
            header,
        },
        data
    );
};

/**
 * 新增通用连接配置
 * @param {Object} data
 * @param {String} data.gatewayId 网关ID
 * @param {Number} data.fromType
 * @param {String} data.entitryid
 * @param {String} data.alias 名称
 * @param {Array} data.linkconfig
 * @return {Promise<unknown>}
 */
export const createCommonLinkConfig = function (data, header) {
    return getInvokeService(
        {
            method: "POST",
            path: `/gwmanage/gwportal/diwork/ncconnect/createConnect/${data.type}`,
            header,
        },
        data
    );
};

/**
 * 编辑通用连接配置
 * @param {Object} data
 * @param {String} data._id 连接器ID
 * @param {Array} data.linkconfig 连接配置列表
 * @return {Promise<unknown>}
 */
export const editCommonLinkConfig = function (data, header) {
    return getInvokeService(
        {
            method: "POST",
            path: `/gwmanage/gwportal/diwork/ncconnect/updateConnectLinkConfig`,
            header,
        },
        data
    );
};

/**
 * 通用连接配置-测试连接接口
 * @param {String} tenantConnectId 连接器ID
 * @return {Promise<unknown>}
 */
export const commonLinkTest = function (data, header) {
    return getInvokeService(
        {
            method: "POST",
            // path: `/gwmanage/gwportal/diwork/ncconnect/testConnection`
            path: `/gwmanage/gwportal/diwork/ncconnect/v2/testConnection`,
            header,
        },
        data
    );
};

/**
 * 获取连接器下对应所有用户设置参数
 * @param {Object} data
 * @param {String} data.linkerId
 * @param {String} data.linkerVersionCode
 * @return {Promise<unknown>}
 */
export const getLinkerSetParam = function (data, header) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwmanage/gwportal/diwork/connect/listUserLinkerParamByLinkerIdAndVersionCode",
            header,
        },
        data
    );
};

// 获取所有网关列表
export const getGatewayList = function (header) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwmanage/gwportal/mygwapp/gateway/list",
            header,
        },
        {
            order: "asc",
            isAll: true,
        }
    );
};
// 获取yms微服务
export const getYmsMicroService = function (header) {
    return getInvokeService({
        method: "GET",
        path: "/gwmanage/gwportal/diwork/msCode/listWithNoDomainPrefix",
        header,
    });
};

// 获取所有集成系统列表
export const getSystemList = function (header) {
    return getInvokeService({
        method: "GET",
        path: "/gwmanage/gwportal/mygwapp/integrated/system/findAll",
        header,
    });
};

// 更新连接状态
export const updateConnectState = function (data, header) {
    return getInvokeService({
        method: "PUT",
        path: "/gwmanage/gwportal/diwork/ncconnect/state/update" + "?id=" + data.id + "&state=" + data.state,
        header,
    });
};
/**
 * 获取nc|ncc|u8c补丁下载地址
 * @param erpVersion
 * @returns {*}
 */
export const downloadAdapterService = function (ModalCode, erpVersion, header) {
    let download_path = "";
    if (["NCHandler", "NCCHandler"].indexOf(ModalCode) > -1) {
        download_path = "ncpath";
    } else if (["U8CHandler"].indexOf(ModalCode) > -1) {
        download_path = "u8cpath";
    }
    return getInvokeService({
        method: "GET",
        path: `/mygwapp/ncconnect/${download_path}/download/ossurl/${erpVersion}`,
        header,
    });
};
export const downloadClientService = function (version, header) {
    return getInvokeService({
        method: "GET",
        path: `/mygwapp/ncconnect/openapiclient/download/ossurl/${version}`,
        header,
    });
};

export const getcode = function (data, header) {
    return getInvokeService(
        {
            method: "POST",
            path: `/gwmanage/gwportal/diwork/ncconnect/getcode`,
            header,
        },
        data
    );
};
//initTask
//   getDataViewService, initDataViewService, updateSyncTaskService, getInitMessageService
export const getDataViewService = function (data, header) {
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/erpdata/initTask/dataview",
            header,
        },
        data
    );
};
export const initDataViewService = function (data, useMainTenantGateway, header) {
    let url = `/diwork/erpdata/task/init/selectedTask/${data.gatewayId}`;

    if (useMainTenantGateway !== "" && useMainTenantGateway !== undefined) {
        url += "?useMainTenantGateway=" + useMainTenantGateway;
    }
    return getInvokeService(
        {
            method: "POST",
            path: url,
            header,
        },
        data
    );
};
export const updateSyncTaskService = function (data, header) {
    let { taskId, ..._data } = data;
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/erpdata/task/update/" + taskId,
            header,
        },
        _data
    );
};
export const getInitMessageService = function (data, header) {
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/erpdata/task/getInitMessage",
            header,
        },
        data
    );
};

//initDoc
//   initDocStatusService, getDefDocService, editDefDocService, pushDefDocService
export const initDocStatusService = function (erpVersion, header) {
    return getInvokeService({
        method: "GET",
        path: "/diwork/defDoc/datamapping/initState/" + erpVersion,
        showLoading: false,
        header,
    });
};
export const getDefDocService = function (data, header) {
    let { erpVersion, ..._data } = data;
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/defDoc/datamapping/get/" + erpVersion,
            timeout: 20000,
            header,
        },
        _data
    );
};
export const editDefDocService = function (data, header) {
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/defDoc/datamapping/set/",
            header,
        },
        data
    );
};
export const pushDefDocService = function (data, header) {
    let { erpVersion, erpPks } = data;
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/defDoc/datamapping/push/" + erpVersion,
            showLoading: false,
            header,
        },
        erpPks
    );
};
//u8 inittask
export const getU8DataViewService = function (data, header) {
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/erpdata/initTask/u8dataview/get",
            header,
        },
        {},
        data
    );
};
export const initU8DataViewService = function (data, header) {
    let { orgCode, tableViews } = data;
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/erpdata/task/initu8/selectedView/" + orgCode,
            header,
        },
        tableViews
    );
};
export const getConfigDataService = function (configId, header) {
    return getInvokeService({
        method: "GET",
        path: "/diwork/ncconnect/queryU8ConfigById/" + configId,
        header,
    });
};
export const getDefaultConfigInfoService = function (data, header) {
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/queryOpenapiConfig",
            header,
        },
        data
    );
};
//u8
export const getOrgsService = function (header) {
    return getInvokeService({
        method: "GET",
        path: "/diwork/queryOrgvos",
        header,
    });
};
export const getGwAppSecretService = function (gwaddr, header) {
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/querySecret",
            header,
        },
        { gwaddr }
    );
};

//集成应用
export const getApplicationListAllService = function (data, header) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwmanage/gwportal/diwork/application/listAll",
            header,
        },
        data
    );
};
export const SaveApplicationService = function (data, header) {
    return getInvokeService(
        {
            method: "POST",
            path: "/gwmanage/gwportal/diwork/application/save",
            header,
        },
        data
    );
};

export const ImportApplicationService = function (data, header) {
    /*接口以formatData形式接收*/
    const formdata = new FormData();
    formdata.append("file", data.file);
    formdata.append("name", data.name);
    formdata.append("memo", data.memo);
    formdata.append("overWriteExistScheme", data.overWriteExistScheme);

    return getInvokeService(
        {
            method: "POST",
            path: "/gwmanage/gwportal/diwork/integscheme/importApplicationSetting",
            timeout: 120000,
            header: { "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundary6Jdq1PXI3LkQLVe0", ...header },
        },
        formdata
    );
};
//初始化集成应用列表

export const getAllApplicationService = function (data, header) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwmanage/gwportal/diwork/integscheme/getAllApplication",
            header,
        },
        data
    );
};
//初始化集成应用

export const InitApplicationService = function (data, header) {
    return getInvokeService(
        {
            method: "POST",
            path: "/gwmanage/gwportal/diwork/integscheme/initApplicationSchemes",
            header,
        },
        data
    );
};

export const ExportApplyService = function (data, header) {
    return getInvokeService(
        {
            method: "POST",
            path: "/gwmanage/gwportal/diwork/integscheme/exportApplicationSetting",
            responseType: "blob", //下载流文件需要在这声明
            timeout: 30000,
            header,
        },
        data
    );
};
export const CopyApplyService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: `/gwmanage/gwportal/diwork/integscheme/copyApplicationInfo`,
        },
        data
    );
};
export const DelApplicationService = function (id, header) {
    return getInvokeService({
        method: "GET",
        path: "/gwmanage/gwportal/diwork/application/deleteById?id=" + id,
        header,
    });
};
export const getSchemeByAppCodeService = function (header) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwmanage/gwportal/diwork/integscheme/listSchemeByAppCode",
            header,
        },
        { applicationCode: null }
    );
};

//   分页获取连接器
export const getListBaseLinkerService = function (header) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwmanage/gwportal/diwork/connect/listBaseLinker",
            timeout: 30000,
            header,
        },
        {}
    );
};
//  获取连接器下连接配置
export const getListBaseLinkerInfosService = function (data, header) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwmanage/gwportal/diwork/connect/listBaseLinkerInfos",
            header,
        },
        data
    );
};

//  删除集成应用
export const deleteByIdService = function (data, header) {
    return getInvokeService({
        method: "POST",
        path: `/gwmanage/gwportal/diwork/application/deleteById?id=${data.id}`,
        header,
    });
};

export const createDefaultConnectService = function (data, header) {
    return getInvokeService({
        method: "POST",
        path: `/gwmanage/gwportal/diwork/integscheme/createDefaultConnect`,
        header,
    });
};
/**
 * 获取连接器版本
 * @param {*} data
 * @param {*} header
 * @returns
 */
export const fetchConnVersionsService = function (linkerCode) {
    return getInvokeService({
        method: "GET",
        path: `/gwmanage/gwportal/diwork/application/queryVersionInfo?linkerCode=${linkerCode}`,
    });
};
