import { observable, action, toJS, reaction, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { defaultListMap } from "utils/pageListUtils";
import { autoServiceMessage } from "utils/service";
import { defaultPagination, selfPageChange } from "utils/pageListUtils";
import * as ownerService from "./service";
import { getAllVersionService } from "services/common";
import { saveAs } from "file-saver";

import { Success, Error } from "utils/feedback";
const initState = {
    listBaseLinker: [],
    listBaseLinkerInfos1: [],
    listBaseLinkerInfos2: [],

    tabs: [
        {
            key: "self",
            type: "added",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180552", "已配置连接") /* "已配置连接" */,
            dataSource: [],
            allDataSource: [],
            loaded: false,
            searchKey: "",
        },
        {
            key: "erp",
            type: "add",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180555", "添加连接") /* "添加连接" */,
            dataSource: [],
            allDataSource: [],
            loaded: false,
            searchKey: "",
        },
    ],
    activeTabKey: "self",
    searchKey: "",
    // 网关列表
    gatewayList: [],
    versionList: [],
    ModalTemplates: {},
    // 集成方案列表
    systemList: [],
    allConnector: [],
    allConnectorSel: [],
    pageData: [],
    pageDataTemp: undefined,
    filterConnector: "",
    testConnectSuccess: true,
    navLinks: [],
    createModalSelectedIndex: "",
    createModalSelectedItem: {},
    loaded: false,
    pagination: {
        ...defaultPagination,
        dataNumSelect: [10, 20, 50, 100, 200],
    },
    pagination2: {
        ...defaultPagination,
        dataNumSelect: [10, 20, 50, 100, 200],
    },
    taskList: [],
    dataSource: {
        ...defaultListMap,
        pageSize: 20,
    },
    dataSource2: {
        ...defaultListMap,
        pageSize: 20,
    },
    freezeImportBtn: false,
    initApplicationSelected: [],
    searchInfo: {},
    serviceCodeDiwork: "kflj_jjfa",
    appContextSmallApp: {},
    ymsMicro: {},
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;
    @observable applicationInfo = {};
    @observable loading = true;
    @observable initSearchInfo = {};
    @observable showType = "card";
    @observable cardList = [];

    // setAppContext = (str) => {
    //     console.log("@@@@@@@@@@@@@@@")
    //     console.log(str)
    //     this.state.appContextSmallApp = str;
    // }
    init = () => {
        this.state = initState;
    };
    updateTabDataSourceState = (id, state) => {
        let temp1 = this.state.tabs[0].dataSource.find((item) => {
            return item.id === id;
        });
        temp1.linkerState = state;
        let temp2 = this.state.tabs[0].allDataSource.find((item) => {
            return item.id === id;
        });
        temp2.linkerState = state;
        console.log(this.toJS(this.state.tabs));
        this.state.tabs = [...this.toJS(this.state.tabs)];
    };

    initTab = (tabKey = "self") => {
        let { tabs } = this.state;
        let tab = tabs.find((tab) => tab.key === tabKey);
        tab.dataSource = [];
        tab.allDataSource = [];
        tab.loaded = false;
        tab.searchKey = "";
        this.state.tabs = tabs;
    };

    changeActiveTab = (tabKey) => {
        this.state.activeTabKey = tabKey;
        this.getDataSource({
            pageNo: 1,
        });
    };

    // changeSearchKey = (_searchKey) => {
    //   console.log(_searchKey)
    //   let { activeTabKey, tabs } = this.state;
    //   let tab = tabs.find(tab => tab.key === activeTabKey);
    //   tab.searchKey = _searchKey;
    //   this.state.tabs = tabs;
    // }
    initU8Email = async (data) => {
        // this.state.filterConnector
        let res = await autoServiceMessage({
            service: ownerService.initU8EmailService(data, { serviceCode: this.state.serviceCodeDiwork }),
        });
        // if (res) {

        // }
        return res;
    };
    getU8Email = async (gatewayId) => {
        // this.state.filterConnector
        let res = await autoServiceMessage({
            service: ownerService.getU8EmailService(gatewayId, { serviceCode: this.state.serviceCodeDiwork }),
        });
        return res;
    };
    changeU8Email = async (gatewayId) => {
        // this.state.filterConnector
        let res = await autoServiceMessage({
            service: ownerService.changeU8EmailService(gatewayId, { serviceCode: this.state.serviceCodeDiwork }),
        });
        return res;
    };
    // 把已配置连接的加载状态置为false
    changeTabOneState = () => {
        this.state.tabs[0].loaded = false;
    };
    @action
    setShowType = (data) => {
        this.showType = data;
        this.state.dataSource = initState.dataSource;
        this.cardList = [];
        this.getDataSource({
            pageNo: 1,
        });
    };

    @action
    getDataSource = async (data) => {
        let requestData = {
            ...this.state.searchInfo,
            ...data,
        };
        this.loading = true;
        await this.getPagesListFunc({
            service: ownerService.getApplicationListAllService,
            requestData,
            dataKey: "dataSource",
            onPageChange: this.getDataSource,
            header: { serviceCode: this.state.serviceCodeDiwork },
            loadEndFunc: () => (this.loading = false),
        });
        if (this.showType === "card") {
            if (this.state.dataSource?.pageIndex === 1) {
                this.cardList = this.state.dataSource?.list || [];
                return;
            }
            this.cardList.replace(this.cardList.concat(this.state.dataSource.list));
        }
    };
    changeInitSearchState = (data) => {
        this.initSearchInfo = data;
    };
    getAllApplicationService = async (requestData) => {
        this.getPagesListFunc({
            service: ownerService.getAllApplicationService,
            requestData: { ...this.initSearchInfo, ...requestData },
            dataKey: "dataSource2",
            paginationKey: "pagination2",
            onPageChange: this.getAllApplicationService,
            header: { serviceCode: this.state.serviceCodeDiwork },
        });
    };
    getListBaseLinker = async () => {
        // this.state.filterConnector
        let res = await autoServiceMessage({
            service: ownerService.getListBaseLinkerService({ serviceCode: this.state.serviceCodeDiwork }),
        });
        if (res) {
            // res.data.list.unshift({name:"全部连接器",code:'',id:''})
            this.changeState({
                listBaseLinker: res.data,
            });
        }
    };
    createDefaultConnect = async () => {
        let res = await autoServiceMessage({
            service: ownerService.createDefaultConnectService("", { serviceCode: this.state.serviceCodeDiwork }),
        });
    };
    getListBaseLinkerInfos1 = async (data) => {
        // this.state.filterConnector
        let res = await autoServiceMessage({
            service: ownerService.getListBaseLinkerInfosService(data, { serviceCode: this.state.serviceCodeDiwork }),
        });
        if (res) {
            this.changeState({
                listBaseLinkerInfos1: (res.data && res.data[0] && res.data[0].resources) || [],
            });
            return (res.data && res.data[0] && res.data[0].resources) || [];
        }
    };
    getListBaseLinkerInfos2 = async (data) => {
        // this.state.filterConnector
        let res = await autoServiceMessage({
            service: ownerService.getListBaseLinkerInfosService(data, { serviceCode: this.state.serviceCodeDiwork }),
        });
        if (res) {
            this.changeState({
                listBaseLinkerInfos2: (res.data && res.data[0] && res.data[0].resources) || [],
            });
            return (res.data && res.data[0] && res.data[0].resources) || [];
        }
    };
    SaveApplication = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.SaveApplicationService(data, { serviceCode: this.state.serviceCodeDiwork }),
        });
        if (res) {
            this.getDataSource({
                pageNo: 1,
            });
        }
        return res;
    };
    ImportApplication = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.ImportApplicationService(data, { serviceCode: this.state.serviceCodeDiwork }),
        });
        if (res) {
            Success(
                lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180551", "导入成功，请编辑该应用的连接配置运行任务", undefined, {
                    returnStr: true,
                }) /* "导入成功，请编辑该应用的连接配置运行任务" */
            );
            this.getDataSource({
                pageNo: 1,
            });
        }
        return res;
    };
    InitApplication = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.InitApplicationService(data, { serviceCode: this.state.serviceCodeDiwork }),
        });
        if (res) {
            Success(
                lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180556", "初始化成功", undefined, {
                    returnStr: true,
                }) /* "初始化成功" */
            );
            this.getDataSource({
                pageNo: 1,
            });
        }
        return res;
    };

    ExportApply = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.ExportApplyService(data, { serviceCode: this.state.serviceCodeDiwork }),
        });
        if (res) {
            const blob1 = res.data;
            const reader = new FileReader();
            reader.readAsDataURL(blob1);
            reader.onload = (e) => {
                if (e.target.readyState === FileReader.DONE) {
                    try {
                        let resultArr = e.target.result.split(",");
                        const errorInfo = JSON.parse(decodeURIComponent(escape(window.atob(resultArr[1])))); //atob base64转ASCII码  decodeURIComponent  escape转义中文字符
                        console.log(errorInfo);
                        Error(errorInfo.message);
                    } catch (b) {
                        const blob = new Blob([blob1]);
                        const url = window.URL.createObjectURL(blob);
                        console.log(url);
                        saveAs(url, res.headers["content-disposition"].split("=")[1]);
                    }
                }
            };
        }
        return res;
    };
    CopyApply = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.CopyApplyService(data),
        });
        if (res) {
            Success(
                lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180558", "复制成功", undefined, {
                    returnStr: true,
                }) /* "复制成功" */
            );
            this.getDataSource({
                pageNo: 1,
            });
        }
        return res;
    };
    DeleteById = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.deleteByIdService(data, { serviceCode: this.state.serviceCodeDiwork }),
        });
        if (res) {
            this.getDataSource({
                pageNo: 1,
            });
        }
        return res;
    };
    changeApplicationInfo = async (obj) => {
        this.applicationInfo = { ...this.applicationInfo, ...obj };
    };
    getDataSource2 = async (isChangeTab = false, searchValue) => {
        let { activeTabKey, tabs } = this.state;
        let tab = tabs.find((tab) => tab.key === activeTabKey);
        if (isChangeTab && tab.loaded) {
            this.state.activeDataSource = tab.dataSource;
            return false;
        }
        let service = null;
        let { searchKey } = tab;
        let _searchKey = typeof searchValue === "undefined" ? searchKey : searchValue;
        if (activeTabKey === "self") {
            service = ownerService.selfConnectorsService({ serviceCode: this.state.serviceCodeDiwork });
        } else {
            // service = ownerService.getConnectorsService({
            //   type: activeTabKey, key: _searchKey
            // })

            // 因为没有分页,这里搜索改为前端搜索,那么需要添加一个变量记录返回的总数据
            service = ownerService.getCommonConnector({ serviceCode: this.state.serviceCodeDiwork });
        }
        let res = await autoServiceMessage({
            service: service,
        });
        if (res) {
            let resData = res.data || [];
            tab.allDataSource = resData;
            tab.dataSource = resData;
            tab.loaded = true;
            tab.searchKey = _searchKey;
            this.state.tabs = tabs;
        }
        this.getSystemList();
    };
    getAllConnector = async () => {
        let res = await autoServiceMessage({
            service: ownerService.getCommonConnector({ serviceCode: this.state.serviceCodeDiwork }),
        });
        if (res) {
            let allConnectorSel = JSON.parse(JSON.stringify(res.data));
            allConnectorSel.unshift({ name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180557", "全部连接器") /* "全部连接器" */, code: "all" });
            this.changeState({
                allConnector: res.data,
                allConnectorSel,
            });
        }
    };
    testConnect = async (item) => {
        let res = await autoServiceMessage({
            service: ownerService.testConnectService(item, { serviceCode: this.state.serviceCodeDiwork }),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180553", "测试连接成功", undefined, {
                returnStr: true,
            }) /* "测试连接成功" */,
        });
        if (res) {
            // this.changeState({
            //     testConnectSuccess:true
            // })
        }
        return res;
    };
    setDefault = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.setDefaultService(data, { serviceCode: this.state.serviceCodeDiwork }),
        });
        if (res) {
            this.getDataSource({
                pageNo: 1,
            });
            // this.changeState({
            //     allConnector:res.data
            // })
        }
    };
    changeEnableState = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.updateConnectState(data, { serviceCode: this.state.serviceCodeDiwork }),
        });
        if (res) {
            this.getDataSource({
                pageNo: 1,
            });
            // this.changeState({
            //     allConnector:res.data
            // })
        }
    };
    rememberSelectedMsgInCreateModal = (index, selectedItem) => {
        this.changeState({
            createModalSelectedIndex: index,
            createModalSelectedItem: selectedItem,
        });
    };

    // 前端搜索连接
    searchDataSource = (searchValue) => {
        let { activeTabKey, tabs } = this.state;
        let tab = tabs.find((tab) => tab.key === activeTabKey);
        let { searchKey } = tab;
        let _searchKey = typeof searchValue === "undefined" ? searchKey : searchValue;
        tab.searchKey = _searchKey;
        tab.dataSource = tab.allDataSource.filter((data) => {
            return (data.name || data.alias).indexOf(_searchKey) > -1;
        });
    };

    deleteConnector = async (connector) => {
        let { id } = connector;
        let res = await autoServiceMessage({
            service: ownerService.deleteConnectorService(id, { serviceCode: this.state.serviceCodeDiwork }),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180559", "删除成功！", undefined, {
                returnStr: true,
            }) /* "删除成功！" */,
        });
        if (res) {
            this.getDataSource({
                pageNo: 1,
            });
        }
    };

    restartGateway = async (gatewayId) => {
        await autoServiceMessage({
            service: ownerService.restartGatewayIdService(gatewayId, { serviceCode: this.state.serviceCodeDiwork }),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180554", "网关重启成功！", undefined, {
                returnStr: true,
            }) /* "网关重启成功！" */,
        });
    };

    // 获取所有网关列表
    getGatewayList = async () => {
        let res = await autoServiceMessage({
            service: ownerService.getGatewayList({ serviceCode: this.state.serviceCodeDiwork }),
        });
        this.changeState({
            gatewayList: res.data,
        });
    };
    // 获取YMS微服务列表
    getYmsMicro = async () => {
        let res = await autoServiceMessage({
            service: ownerService.getYmsMicroService(),
        });
        this.changeState({
            ymsMicro: res.data,
        });
    };
    // 获取版本
    getAllVersion = async (linkerId) => {
        let res = await autoServiceMessage({
            service: getAllVersionService(linkerId),
        });
        this.changeState({
            versionList: res.data,
        });
        return res;
    };
    // 获取弹窗模板
    getLinkerSetParam = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.getLinkerSetParam(data, { serviceCode: this.state.serviceCodeDiwork }),
        });
        this.changeState({
            ModalTemplates: res.data,
        });
        return res;
    };
    // 获取所有集成系统列表
    getSystemList = async () => {
        let res = await autoServiceMessage({
            service: ownerService.getSystemList({ serviceCode: this.state.serviceCodeDiwork }),
        });
        if (res) {
            res.data.unshift({ id: "", systemName: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180550", "请选择") /* "请选择" */ });
            console.log(res.data);
            this.changeState({
                systemList: res.data,
            });
        }
    };
    setConfigName = (configName) => {
        this.state.configName = configName;
    };

    setNavLinks = (nav) => {
        this.state.navLinks.push(nav);
    };

    setQuestionShow = () => {
        this.state.questionShow = true;
    };
    onPageChange = (index, pageInfo, dataSource, type) => {
        if (type) {
            dataSource.forEach((item, index) => {
                let _dataSource = this.toJS(item.items);
                let pagination = this.toJS(this.state.pagination);

                let info = selfPageChange(pageInfo, _dataSource, {
                    ...pagination,
                    onPageChange: this.onPageChange,
                    index: index,
                });
                item.taskList = info.list;
                item.pagination = info.pagination;
            });
            console.log(dataSource);
            return dataSource;
        } else {
            let _dataSource = this.state.pageData[index].items;
            let pagination = this.toJS(this.state.pagination);

            let info = selfPageChange(pageInfo, _dataSource, {
                ...pagination,
                onPageChange: this.onPageChange,
                index: index,
            });
            this.state.pageData[index].taskList = info.list;
            this.state.pageData[index].pagination = info.pagination;
        }
    };
}
/**
 * 根据具体模块修改 storeKey 为保证唯一性
 * storeKey 由 模块文件夹名称 + 页面名称 + 'Store' 小驼峰
 *
 * storeKey是作为当前 Store 的唯一键， storeKey与Store的实例会在 rootStore或者StoreMap中以 键值对的 形式存在
 * 1. 用于在模块的 storeMap（模块routes/store.js）中注册并初始化当前Store
 * 2. 用于在container的 inject 中关联当前Store的实例时，从模块的storeMap或者rootStore中获取对应store实例
 * */
export const storeKey = "integrateApplicationKey";
export default Store;
