import React, { Component, Fragment } from "react";
import { Modal, Select, Tooltip, Button, Icon, FormControl } from "components/TinperBee";
import { Content } from "components/PageView";
import withRouter from "decorator/withRouter";
import "./index.less";
import GuideCard from "../GuideCard";
import { Success, Error } from "utils/feedback";
const Option = Select.Option;
@withRouter
class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            showConnectModal: false,
            showCreateModal: false,
            isFromCreateModal: false,
            showInitModal: false,

            ModalCode: "",
            ModalTitle: "",
            ConnectModalData: {},
            InitModalData: {},
            ModalTestable: false,
            ModalTemplates: "",
            ModalType: "", //edit  add

            connectorId: "",
            connectorAdapterCode: "",
            defaultSelectedVersion: "",

            // 添加连接时传递给弹窗的数据
            addModalData: {},
            // 编辑连接时传递给弹窗的数据
            editModalData: {},
            showType: "card", //card table,
        };
    }

    componentDidMount() {
        let { ownerStore, ownerState } = this.props;
        // ownerStore.getAllConnector()
        // ownerStore.getDataSource();
        // ownerStore.getGatewayList()
        console.log("ooooo----, ", ownerState, ownerStore);
    }

    render() {
        const { handleNewApplication, show } = this.props;
        return (
            <>
                <div className={`guide-box`}>
                    <div className="guide-box-title">
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180403", "创建您的第一个集成应用") /* "创建您的第一个集成应用" */}
                    </div>
                    <div className="guide-box-desc">
                        {
                            lang.templateByUuid(
                                "UID:P_UBL-FE_18D8CEF604180404",
                                "数据集成提供可视化无代码工具制作跨系统间的数据集成方案"//@notranslate
                            ) /* "数据集成提供可视化无代码工具制作跨系统间的数据集成方案" */
                        }
                    </div>
                    <GuideCard />
                    <Button
                        fieldid="ublinker-routes-home-components-IndexView-index-5870842-Button"
                        className="guide-btn"
                        colors="primary"
                        onClick={() => {
                            handleNewApplication("add", "", "new");
                        }}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180543", "新增") /* "新增" */}
                    </Button>
                    {/* <div className='guide-select'>
            <span className="guide-select-title">{"选择其他方式" }</span>
            <Button fieldid="UCG-FE-routes-application-components-Guide-index-5085507-Button"  type="text" bordered >{"导入应用"}</Button>|
            <Button fieldid="UCG-FE-routes-application-components-Guide-index-1347259-Button"  type="text" bordered >{"初始化应用"}</Button>
          </div> */}
                </div>
            </>
        );
    }
}

export default IndexView;
