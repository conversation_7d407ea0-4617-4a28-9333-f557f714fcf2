import React, { Component, Fragment } from "react";
import { Modal, Select, Tooltip, Button, Icon, FormControl, Steps } from "components/TinperBee";
import { Content } from "components/PageView";
import withRouter from "decorator/withRouter";
import ConnectorList from "../ConnecterList";
import TablesList from "../TablesList/index";
import commonText from "constants/commonText";
import "./index.less";
import CreateModal from "../CreateModal";
import InitModal from "../InitModal";
import { Success, Error } from "utils/feedback";
const Option = Select.Option;
const { Step } = Steps;
import { getLocalImg } from "utils/index";
@withRouter
class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            showType: "card", //card table,
        };
    }

    componentDidMount() {}
    gotoPage = (serviceCode) => {
        jDiwork.ready(() => {
            jDiwork.openService(serviceCode);
        });
    };
    render() {
        const { hideConfig = false } = this.props;
        return (
            <div>
                <div className="guide-card-box">
                    <div className="guide-card">
                        <div className="guide-card-logo">
                            <div className="guide-card-logo1">
                                <img className="logo1-img" src={getLocalImg("assetPack/application-lianjie.svg")} />
                            </div>
                            <div className="guide-card-logo2">
                                <img className="logo2-img" src={getLocalImg("assetPack/application-zhuanhuan.svg")} />
                            </div>
                            <div className="guide-card-logo3">
                                <img className="logo1-img" src={getLocalImg("assetPack/application-lianjie.svg")} />
                            </div>
                        </div>
                        <div className="guide-card-title">
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041800EB", "新建一个集成应用") /* "新建一个集成应用" */}
                        </div>
                        <div
                            className="guide-card-desc"
                            title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041800EC", "选择2个连接器的配置、填写应用信息", undefined, {
                                returnStr: true,
                            })}
                        >
                            {
                                lang.templateByUuid(
                                    "UID:P_UBL-FE_18D8CEF6041800EC",
                                    "选择2个连接器的配置、填写应用信息"//@notranslate
                                ) /* "选择2个连接器的配置、填写应用信息" */
                            }
                        </div>
                        <div className="guide-card-tip">
                            {
                                lang.templateByUuid(
                                    "UID:P_UBL-FE_18D8CEF6041800ED",
                                    "此步骤依赖连接配置,请提前去【连接配置】节点配置"//@notranslate
                                ) /* "此步骤依赖连接配置,请提前去【连接配置】节点配置" */
                            }
                        </div>
                        {/* {
              !hideConfig &&  <Button fieldid="ublinker-routes-home-components-ConnecterList-index-7218388-Button" type="text" bordered onClick={this.gotoPage.bind(null, 'kflj_ljpz')}>{"去配置"}</Button>
            } */}
                    </div>
                    <div className="guid-line-box">
                        <div className="guid-line"></div>
                        <div className="guid-line-block"></div>
                    </div>
                    <div className="guide-card">
                        <div className="guide-card-logo-wenjian">
                            <div className="wenjian-guide-card-logo1">
                                <img className="wenjian-logo1-img" src={getLocalImg("assetPack/application-wenjian.svg")} />
                            </div>
                        </div>
                        <div className="guide-card-title">
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041800EE", "新建1个集成方案") /* "新建1个集成方案" */}
                        </div>
                        <div
                            className="guide-card-desc"
                            title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041800EF", "选择方案的来源和目标、 需要移动的数据", undefined, {
                                returnStr: true,
                            })}
                        >
                            {
                                lang.templateByUuid(
                                    "UID:P_UBL-FE_18D8CEF6041800EF",
                                    "选择方案的来源和目标、 需要移动的数据"//@notranslate
                                ) /* "选择方案的来源和目标、 需要移动的数据" */
                            }
                        </div>
                        <div className="guide-card-tip">
                            {
                                lang.templateByUuid(
                                    "UID:P_UBL-FE_18D8CEF6041800F0",
                                    "此步骤依赖数据， 请提前去【集成对象】节点配置"//@notranslate
                                ) /* "此步骤依赖数据， 请提前去【集成对象】节点配置" */
                            }
                        </div>

                        {/* {
              !hideConfig &&  <Button fieldid="ublinker-routes-home-components-ConnecterList-index-7218388-Button" type="text" bordered onClick={this.gotoPage.bind(null, 'kflj_jjdx')}>{"去配置"}</Button>
            } */}
                    </div>
                    <div className="guid-line-box">
                        <div className="guid-line"></div>
                        <div className="guid-line-block"></div>
                    </div>
                    <div className="guide-card">
                        <div className="guide-card-logo">
                            <div className="guide-card-logo1">
                                <img className="logo1-img" src={getLocalImg("assetPack/application-lianjie.svg")} />
                            </div>
                            <div className="guide-card-logo2">
                                <img className="logo2-img" src={getLocalImg("assetPack/application-chuanshu.svg")} />
                            </div>
                            <div className="guide-card-logo3">
                                <img className="logo1-img" src={getLocalImg("assetPack/application-lianjie.svg")} />
                                <div className="guide-card-logo-fabu">
                                    <img className="logo3-img" src={getLocalImg("assetPack/application-fabu.svg")} />
                                </div>
                            </div>
                        </div>
                        <div className="guide-card-title">
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041800E8", "设计并发布集成方案") /* "设计并发布集成方案" */}
                        </div>
                        <div
                            className="guide-card-desc"
                            title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041800E9", "配置数据转换规则及相关属性，完成并发布集成方案", undefined, {
                                returnStr: true,
                            })}
                        >
                            {
                                lang.templateByUuid(
                                    "UID:P_UBL-FE_18D8CEF6041800E9",
                                    "配置数据转换规则及相关属性，完成并发布集成方案"//@notranslate
                                ) /* "配置数据转换规则及相关属性，完成并发布集成方案" */
                            }
                        </div>
                        <div className="guide-card-tip">
                            {
                                lang.templateByUuid(
                                    "UID:P_UBL-FE_18D8CEF6041800EA",
                                    "此步骤依赖字段信息，请提前去【集成对象】节点配置"//@notranslate
                                ) /* "此步骤依赖字段信息，请提前去【集成对象】节点配置" */
                            }
                        </div>

                        {/* {
              !hideConfig &&  <Button fieldid="ublinker-routes-home-components-ConnecterList-index-7218388-Button" type="text" bordered onClick={this.gotoPage.bind(null, 'kflj_jjdx')}>{"去配置"}</Button>
            } */}
                    </div>
                </div>
                {/* <div className="guide-step">

         <Steps fieldid="UCG-FE-routes-application-components-GuideCard-index-5011745-Steps">
            <Step icon={<img className="guide-step-icon" src={getLocalImg('assetPack/help1.svg')}/>}/>
            <Step icon={<img className="guide-step-icon" src={getLocalImg('assetPack/help2.svg')}/>}/>
            <Step icon={<img className="guide-step-icon" src={getLocalImg('assetPack/help3.svg')}/>}/>
        </Steps> 
        </div> */}
            </div>
        );
    }
}

export default IndexView;
