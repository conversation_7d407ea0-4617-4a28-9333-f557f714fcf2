import React, { useState, useEffect, useRef } from "react";
import { Table } from "@tinper/next-ui";
import Modal from "components/TinperBee/Modal";
import { <PERSON>ton, FormList, FormControl, Select, Tabs } from "components/TinperBee";
import Grid from "components/TinperBee/Grid";
import { Error } from "utils/feedback";
import ArrowSvg from "../../ArrowSvg";
import "./index.less";
import Pagination from "components/TinperBee/Pagination";

// 添加连接弹窗
const AddLinkerModal = (props) => {
    let {
        show,

        ownerState: { pagination2, dataSource2 },
        onCancel,
        onOk,
    } = props;

    const [activeKey, setActiveKey] = useState("all");
    const [selectedList, setSelectedList] = useState([]);
    const [scrollHeight, setScrollHeight] = useState(0);

    useEffect(() => {
        let init = async () => {
            props.ownerStore.getAllApplicationService({ pageNo: 1 });
        };
        init();
        return () => {
            props.ownerStore.changeInitSearchState({ searchVal: undefined });
        };
    }, []);

    const handleRenderDirection = (text, record) => {
        return <ArrowSvg style={{ width: "16px", height: "16px" }} isDouble={text !== "single"} />;
    };
    // 确认
    const handleCommit = () => {
        if (selectedList.length == 0) {
            Error(
                lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180121", "请选择应用", undefined, {
                    returnStr: true,
                }) /* "请选择应用" */
            );
            return;
        }

        onOk("add", selectedList[0], "init");
    };

    const handleTabChange = (activeKey) => {
        setActiveKey(activeKey);
    };
    const handleSearch = (search) => {
        props.ownerStore.changeInitSearchState({ searchVal: search });
        props.ownerStore.getAllApplicationService({
            pageNo: 1,
        });
    };
    const columnObj = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180122", "应用编码") /* "应用编码" */,
            dataIndex: "code",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180124", "应用名称") /* "应用名称" */,
            dataIndex: "name",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_1CD2995805480001", "来源连接器"),
            dataIndex: "connectTypeName",
        },
        {
            title: "",
            dataIndex: "schemesDirection",
            width: 40,
            render: handleRenderDirection,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_1CD2995805480002", "目标连接器"),
            dataIndex: "connectTypeNameTwo",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418011E", "描述") /* "描述" */,
            dataIndex: "memo",
        },
    ];
    let rowSelection = {
        type: "radio",
        checkStrictly: false,
        selectedRowKeys: selectedList.map((item) => item.id),
        onChange: (selectedRowKeys, selectedRows) => {
            setSelectedList(selectedRows);
        },
    };
    return (
        <>
            <Modal
                fieldid="ublinker-home-components-Modal-U9Modal-index-7518987-Modal"
                title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180120", "初始化应用", undefined, {
                    returnStr: true,
                })}
                show={show}
                height={650}
                width={1200}
                onCancel={onCancel}
                onOk={handleCommit}
                okText={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180125", "下一步") /* "下一步" */}
                // okDisabled ={isSureDisable}
                // cancelText={ "取消"}
                // style={{ display: 'flex' }}
                className="initApplication-modal"
                destroyOnClose
            >
                <div style={{ display: "flex", flexDirection: "column", height: "100%" }}>
                    <Tabs
                        fieldid="ublinker-routes-sync-log-components-IndexView-index-2570325-Tabs"
                        activeKey={activeKey}
                        className="initApplication-tabs"
                        extraContent={
                            <FormControl
                                fieldid="ublinker-routes-home-components-IndexView-index-************-FormControl"
                                className=""
                                style={{ width: "240px", boxSizing: "border-box", marginTop: "-4px" }}
                                onSearch={(value) => {
                                    handleSearch(value);
                                }}
                                placeholder={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180123", "搜索名称/编码", undefined, {
                                    returnStr: true,
                                })}
                                showClose
                                type="search"
                            />
                        }
                        onChange={handleTabChange}
                    >
                        <Tabs.TabPane
                            fieldid="UCG-FE-application-components-Modal-InitApplicationModal-index-372732-Tabs.TabPane"
                            key="all"
                            tab={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418011F", "预置应用") /* "预置应用" */}
                        />
                        {/* <Tabs.TabPane fieldid="UCG-FE-application-components-Modal-InitApplicationModal-index-8754555-Tabs.TabPane" key='fail' tab={"sql应用"} /> */}
                    </Tabs>
                    <div style={{ display: "flex", flexDirection: "column", flex: 1 }}>
                        <Table
                            fieldid="ublinker-routes-home-components-TablesList-index-3738456-Grid"
                            rowKey="id"
                            columns={columnObj}
                            data={dataSource2.list}
                            className="expanded-table"
                            pagination={false}
                            rowSelection={rowSelection}
                            scroll={{ y: "449px" }}
                            bodyStyle={{ flex: 1 }}
                        />
                        {pagination2 && (
                            <Pagination
                                current={pagination2.activePage}
                                onChange={(a, b) => pagination2?.onPageChange({ pageSize: b, pageNo: a })}
                                onPageSizeChange={(a, b) => pagination2?.onPageChange({ pageSize: b, pageNo: 1 })}
                                showSizeChanger
                                total={pagination2.total}
                                pageSize={pagination2.pageSize}
                                pageSizeOptions={[...Pagination.dataNumSelect["page"]]}
                                // style={{ backgroundColor: "#fff", position: "fixed", bottom: 0, right: 0, width: "100%" }}
                            />
                        )}

                        {/* <Pagination pagination={pagination2} /> */}
                    </div>
                </div>
            </Modal>
        </>
    );
};

export default AddLinkerModal;
