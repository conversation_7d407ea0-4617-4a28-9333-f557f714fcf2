import React, { useState } from "react";
import { Popover, Steps, Icon, Button } from "components/TinperBee";
import { getLocalImg } from "utils/index";
const { Step } = Steps;
import GuideCard from "../../GuideCard";

const popContent = (setVisible) => (
    <>
        <div className="help-title">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801B2", "帮助说明") /* "帮助说明" */}</div>
        <GuideCard hideConfig />
    </>
);
const GatewayHelpModal = (props) => {
    const { placement, overlayMaxHeight } = props;
    const [visible, setVisible] = useState(false);
    return (
        <Popover
            fieldid="ublinker-home-components-Modal-U9Modal-ConfigureInstruction-1675518-Popover"
            overlayMaxHeight={true}
            arrowPointAtCenter={true}
            placement={placement || "leftTop"}
            overlayClassName="configure-pop-overlay"
            className="configure-pop"
            trigger="hover"
            show={visible} //
            autoAdjustOverflow={false}
            content={() => popContent(setVisible)}
            onHide={() => setVisible(false)}
        >
            <div style={{ marginLeft: 10 }} onMouseEnter={() => setVisible(true)}>
                <i fieldid="ublinker-routes-home-components-IndexView-index-784725-i" className="cl cl-Q ucg-pad-l-5 ucg-mar-t-4" />
                <Button
                    fieldid="UCG-FE-application-components-Modal-ApplicationModal-ConfigureInstruction-3589889-Button"
                    type="text"
                    bordered
                    style={{ padding: 0 }}
                >
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801B2", "帮助说明") /* "帮助说明" */}
                </Button>
            </div>
        </Popover>
    );
};

export default GatewayHelpModal;
