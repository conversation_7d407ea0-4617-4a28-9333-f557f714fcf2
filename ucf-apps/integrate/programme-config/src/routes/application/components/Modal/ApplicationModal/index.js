import { <PERSON><PERSON>, <PERSON>, Tooltip, Checkbox, Row, Upload, Icon } from "components/TinperBee";
import React, { useState, useEffect, useRef } from "react";
import { autoServiceMessage } from "utils/service";
import Modal from "components/TinperBee/Modal";
import { Form as FormList, Input as FormControl, Select, Radio, Space } from "@tinper/next-ui";
import { codeReg } from "utils/regExp";
import ConfigureInstruction from "./ConfigureInstruction";
import { getLocalImg, getCompleteImg } from "utils/index";
import Logo from "components/Card/Logo";
import ConnectorLogo from "components/ConnectorLogo";
import ArrowSvg from "../../ArrowSvg";
import { fetchConnVersionsService } from "../../../service";
import "./index.less";
import styles from "./index.modules.css";
const Dragger = Upload.Dragger;

const FormItem = FormList.Item;
const Option = Select.Option;
const OptGroup = Select.OptGroup;

// edit  handleNewApplication("edit", selfItem, "new");
// 新增 handleNewApplication("add", "", "new");
// 初始化 handleNewApplication("add", selectedList[0], "init");
// 导入 handleNewApplication( "add", "", "import")

// 添加连接弹窗
const ApplicationModal = (props) => {
    const [form] = FormList.useForm();
    let {
        show,
        info = {},
        listBaseLinker,
        ownerState: { listBaseLinkerInfos1 = [], listBaseLinkerInfos2 = [], applicationInfo },
        isFromCreateModal,
        completeModalTitle,
        onCancel,
        ModalType,
        changeModalTemplates: changeLinkconfig,
        ownerStore,
        createType = "import",
    } = props;

    const isAdd = ModalType === "add";
    const { validateFields } = form;
    let [isFormChange, setFormChange] = useState(0);
    const applicationInfoRef = useRef({});
    const [linkerList, setLinkerList] = useState([]);
    const [linkerList2, setLinkerList2] = useState([]);
    const [connVersions, setConnVersions] = useState({ source: [], target: [] });

    // 新增初始化时获取连接模版参数（编辑时已经获取到参数类型和对应值，不需要再重复获取）

    const [file, setFile] = useState();
    const [overWriteExistScheme, setOverWriteExistScheme] = useState(false);
    const [fileList, setFileList] = useState();

    const demo6props = {
        name: "file",
        // action: '#',
        accept: ".zip",
        fileList,
        beforeUpload(zipFile, list) {
            console.log(zipFile, list);
            setFile(zipFile);
            setFileList(list);
            return false; // 禁止上传
        },
        onRemove() {
            setFileList([]);
            setFile(null);
            return true;
        },

        enterDragger() {
            console.log("拖拽进入");
        },
        leaveDragger() {
            console.log("拖拽离开");
        },
    };
    const handleSpecialConnector = async (info, oneChange, twoChange) => {
        if (oneChange && info?.connectTypeCode) {
            let [LinkerInfos1, connVersionList] = await Promise.all([
                props.ownerStore.getListBaseLinkerInfos1({ linkerCode: info.connectTypeCode }),
                // handleFetchConnVersions(info?.connectTypeCode),
            ]);
            setLinkerList(LinkerInfos1 || []);
            // setConnVersions({ ...connVersions, source: connVersionList });
            // 编辑和初始化
            // 导入的，点编辑进来 ，是没有连接配置的，if(第一个连接器编码或第二个为用友YonBIP旗舰版连接器时，给其对应的连接配置赋值默认值)
            if ((info.connectTypeCode == "yonyou-erp-yonbip" || info.connectTypeCode == "api-gateway-connector") && !info.connectId) {
                //连接器为 用友YonBIP旗舰版连接器 时，设置个固定值
                let defaultLinkerInfo = LinkerInfos1.filter((item) => {
                    return item.resCode == (info.connectTypeCode == "yonyou-erp-yonbip" ? "CURRENTYONBIP" : "CURRENTAPIGATEWAY");
                });
                info = { ...info, connectId: defaultLinkerInfo[0]?.id /* sourceConnectCodeVersion: defaultLinkerInfo[0]?.connVersion */ };
            }
        }

        if (twoChange && info?.connectTypeCodeTwo) {
            let [LinkerInfos2, connVersionList] = await Promise.all([
                props.ownerStore.getListBaseLinkerInfos2({ linkerCode: info.connectTypeCodeTwo }),
                // handleFetchConnVersions(info?.connectTypeCodeTwo),
            ]);
            setLinkerList2(LinkerInfos2 || []);
            // setConnVersions({ ...connVersions, target: connVersionList });
            if ((info.connectTypeCodeTwo == "yonyou-erp-yonbip" || info.connectTypeCodeTwo == "api-gateway-connector") && !info.connectIdTwo) {
                //连接器为 用友YonBIP旗舰版连接器 时，设置个固定值
                let defaultLinkerInfo = LinkerInfos2.filter((item) => {
                    return item.resCode == (info.connectTypeCodeTwo == "yonyou-erp-yonbip" ? "CURRENTYONBIP" : "CURRENTAPIGATEWAY");
                });
                info = {
                    ...info,
                    connectIdTwo: defaultLinkerInfo[0]?.id,
                    // targetConnectCodeVersionTwo: defaultLinkerInfo[0]?.connVersion,
                };
            }
        }

        applicationInfoRef.current = info;
        setFormChange(++isFormChange);
    };
    useEffect(() => {
        if (info) {
            handleSpecialConnector(info, true, true);
        }
        if (createType === "new") {
            props.ownerStore.getYmsMicro();
        }
    }, [info, createType]);

    useEffect(() => form.setFieldsValue(applicationInfoRef.current), [isFormChange]);

    // 确认
    const handleCommit = () => {
        // SaveApplication
        validateFields()
            .then(async (values) => {
                let postData = {};
                let res;
                if (createType == "new") {
                    if (isAdd) {
                        postData = {
                            ...values,
                            applicationType: 1,
                        };
                    } else {
                        postData = {
                            _id: applicationInfoRef.current?.id,
                            ...values,
                            applicationType: 1,
                        };
                    }

                    res = await ownerStore.SaveApplication(postData);
                } else if (createType == "import") {
                    postData = {
                        file,
                        overWriteExistScheme,
                    };
                    res = await ownerStore.ImportApplication(postData);
                } else if (createType == "init") {
                    postData = {
                        ...values,
                        overWriteExistScheme,
                        applicationType: 1,
                    };
                    res = await ownerStore.InitApplication(postData);
                }
                if (res) {
                    if (createType == "import") {
                        ownerStore.changeState({ freezeImportBtn: true });
                        setTimeout(() => {
                            ownerStore.changeState({ freezeImportBtn: false });
                        }, 30000);
                    }
                    onCancel();
                }
            })
            .catch((errorInfo) => {
                console.log(errorInfo);
            });
    };

    const formItemLayout = {
        labelCol: { span: 5 },
        wrapperCol: { span: 16 },
    };
    const gotoPage = (serviceCode) => {
        jDiwork.ready(() => {
            jDiwork.openService(serviceCode);
        });
    };
    const isApiGatewayConnector = (connectorCode) => {
        //API网关连接器
        return connectorCode == "api-gateway-connector";
    };
    const isYonyouErpYonbip = (connectorCode) => {
        //用友YonBIP旗舰版连接器
        return connectorCode == "yonyou-erp-yonbip";
    };
    const handleFetchConnVersions = async (linkerCode) => {
        let res = await autoServiceMessage({
            service: fetchConnVersionsService(linkerCode),
        });
        return res?.data;
    };
    const ublinkerEnv = JSON.parse(localStorage.getItem("ublinkerEnv") || "{}");
    return (
        <>
            <Modal
                fieldid="ublinker-home-components-Modal-U9Modal-index-7518987-Modal"
                title={completeModalTitle}
                show={show}
                height={600}
                width={1000}
                onCancel={onCancel}
                onOk={handleCommit}
                // okDisabled ={isSureDisable}
                // extendBtn={getExtendBtn}
                extendBtnLocation="pre"
                // cancelText={"取消"}
                cancelHide={isFromCreateModal}
                style={{ display: "flex" }}
                className="application-modal"
            >
                <div className="modal-left">
                    <div className="modal-left-title">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418036A", "集成应用设计") /* "集成应用设计" */}</div>
                    <div className="modal-left-desc">
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418036B", "1、选择连接器连接异构系统链路") /* "1、选择连接器连接异构系统链路" */}
                    </div>
                    <div className="modal-left-desc">
                        {
                            lang.templateByUuid(
                                "UID:P_UBL-FE_18D8CEF60418036C",
                                "2、制作异构系统间的档案/单据映射关系" //@notranslate
                            ) /* "2、制作异构系统间的档案/单据映射关系" */
                        }
                    </div>
                    <div className="modal-left-desc">
                        {
                            lang.templateByUuid(
                                "UID:P_UBL-FE_18D8CEF60418036D",
                                "3、多种调度任务执行该应用进行数据同步" //@notranslate
                            ) /* "3、多种调度任务执行该应用进行数据同步" */
                        }
                    </div>
                    <div className="modal-left-preview">
                        <div className="preview-half">
                            <div className="preview-half-imgbox">
                                {getCompleteImg(applicationInfoRef.current?.logo1) != true ? (
                                    <Logo
                                        style={{ width: "100%", height: "100%" }}
                                        className="item-added-header-icon"
                                        logo={getCompleteImg(applicationInfoRef.current?.logo1)}
                                        title={applicationInfoRef.current?.name}
                                    />
                                ) : (
                                    <ConnectorLogo
                                        className="item-added-header-icon"
                                        style={{ width: "100%", height: "100%" }}
                                        iconfontsize={{ fontSize: "60px" }}
                                        logo={applicationInfoRef.current?.logo1}
                                        title={applicationInfoRef.current?.connectName}
                                        code={applicationInfoRef.current?.connectTypeCode}
                                    />
                                )}
                            </div>
                            <div className="preview-half-name">{lang.templateByUuid("UID:P_UBL-FE_1CD2995805480001", "来源连接器") /* "来源连接器" */}</div>
                        </div>
                        <ArrowSvg
                            style={{ marginTop: "27px" }}
                            className="modal-logo2-img"
                            isDouble={applicationInfoRef.current?.schemesDirection !== "single"}
                        />
                        <div className="preview-half">
                            <div className="preview-half-imgbox">
                                {getCompleteImg(applicationInfoRef.current?.logo2) != true ? (
                                    <Logo
                                        style={{ width: "100%", height: "100%" }}
                                        className="item-added-header-icon"
                                        logo={getCompleteImg(applicationInfoRef.current?.logo2)}
                                        title={applicationInfoRef.current?.connectNameTwo}
                                    />
                                ) : (
                                    <ConnectorLogo
                                        className="item-added-header-icon"
                                        style={{ width: "100%", height: "100%" }}
                                        iconfontsize={{ fontSize: "60px" }}
                                        logo={applicationInfoRef.current?.logo2}
                                        title={applicationInfoRef.current?.connectNameTwo}
                                        code={applicationInfoRef.current?.connectTypeCodeTwo}
                                    />
                                )}
                            </div>
                            <div className="preview-half-name">{lang.templateByUuid("UID:P_UBL-FE_1CD2995805480002", "目标连接器") /* "目标连接器" */}</div>
                        </div>
                    </div>
                </div>
                <div className="modal-right">
                    <div className="modal-right-help">
                        <ConfigureInstruction />
                    </div>
                    <FormList fieldid="ublinker-home-components-Modal-U9Modal-index-9526943-FormList" form={form} {...formItemLayout}>
                        {createType == "new" || createType == "init" ? (
                            <>
                                <FormItem
                                    fieldid="fc41b871-9fe0-4aa1-96ea-6ddedbed3f97"
                                    label={lang.templateByUuid("UID:P_UBL-FE_1CD2995805480001", "来源连接器") /* "来源连接器" */}
                                    validateTrigger="onChange"
                                    rules={[{ required: true }]}
                                >
                                    <div className={styles["multi-form-item"]}>
                                        <FormItem
                                            fieldid="c9b6cf9c-84f5-48b2-a526-f65ac1d7171e"
                                            noStyle
                                            name="connectTypeCode"
                                            validateTrigger="onChange"
                                            rules={[
                                                {
                                                    required: true,
                                                    message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180370", "请选择连接器") /* "请选择连接器" */,
                                                },
                                            ]}
                                        >
                                            <Select
                                                fieldid="6b5dc509-0aa6-43a8-9097-9211a73624ad"
                                                placeholder={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180370", "请选择连接器", undefined, {
                                                    returnStr: true,
                                                })}
                                                showSearch={true}
                                                allowClear={true}
                                                optionFilterProp="children"
                                                onChange={(value, option) => {
                                                    const newInfo = {
                                                        ...applicationInfoRef.current,
                                                        connectName: option?.item.name,
                                                        connectTypeCode: value,
                                                        connectId: "",
                                                        // sourceConnectCodeVersion: "",
                                                        logo1: option?.item.logo,
                                                    };
                                                    handleSpecialConnector(newInfo, true, false);
                                                }}
                                                style={{ width: "200px" }}
                                            >
                                                {listBaseLinker?.map((cate) => {
                                                    return (
                                                        <OptGroup label={cate.name} key={cate.code}>
                                                            {(cate.children || []).map((item) => (
                                                                <Option key={item.id} item={item} value={item.code}>
                                                                    {item.name}
                                                                </Option>
                                                            ))}
                                                        </OptGroup>
                                                    );
                                                })}
                                            </Select>
                                        </FormItem>
                                        <FormItem
                                            fieldid="8c78dcfa-dca3-4d78-a8fd-ca4e8de3df5c"
                                            noStyle
                                            name="connectId"
                                            validateTrigger="onChange"
                                            rules={[
                                                {
                                                    // required: !isApiGatewayConnector(applicationInfoRef.current.connectTypeCode),
                                                    required: true,
                                                    message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180364", "请选择连接配置") /* "请选择连接配置" */,
                                                },
                                            ]}
                                        >
                                            <Select
                                                fieldid="c94181fa-d0c1-42f2-abae-be3324bc6adf"
                                                showSearch={true}
                                                allowClear={true}
                                                optionFilterProp="filterValue"
                                                placeholder={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180364", "请选择连接配置", undefined, {
                                                    returnStr: true,
                                                })}
                                                onChange={(value, option) => {
                                                    const newInfo = {
                                                        ...applicationInfoRef.current,
                                                        connectId: option?.item?.id,
                                                        // sourceConnectCodeVersion: option?.item?.connVersion,
                                                    };

                                                    setFormChange(++isFormChange);
                                                    applicationInfoRef.current = newInfo;
                                                }}
                                                style={{ width: "200px" }}
                                                dropdownClassName="dropdownClassName"
                                                disabled={isApiGatewayConnector(applicationInfoRef.current.connectTypeCode)}
                                            >
                                                {linkerList.map((item = {}) => {
                                                    return (
                                                        <Option
                                                            key={item.id}
                                                            item={item}
                                                            value={item.id}
                                                            filterValue={`${item.resName}${item.versionName ?? ""}`}
                                                        >
                                                            {`${item.resName}${item.versionName ? "-" + item.versionName : ""}`}
                                                        </Option>
                                                    );
                                                })}
                                            </Select>
                                        </FormItem>
                                        <Button
                                            fieldid="824a431b-8ab0-4a9e-bba1-1b75439ae217"
                                            className={styles["form-item-add"]}
                                            type="text"
                                            bordered
                                            onClick={() => {
                                                gotoPage("kflj_ljpz");
                                            }}
                                            disabled={isApiGatewayConnector(applicationInfoRef.current.connectTypeCode)}
                                        >
                                            {lang.templateByUuid("UID:P_UBL-FE_1CCD5A3805F0000A", "新增") /* "新增" */}
                                        </Button>
                                    </div>
                                </FormItem>

                                <FormItem
                                    fieldid="b3d42916-6503-4d51-98cd-ccb3b60fc17b"
                                    label={lang.templateByUuid("UID:P_UBL-FE_1CD2995805480002", "目标连接器") /* "目标连接器" */}
                                    validateTrigger="onChange"
                                    rules={[{ required: true }]}
                                >
                                    <div className={styles["multi-form-item"]}>
                                        <FormItem
                                            fieldid="f0217076-041b-4298-ad5c-5d4b545960fd"
                                            noStyle
                                            name="connectTypeCodeTwo"
                                            validateTrigger="onChange"
                                            rules={[
                                                {
                                                    required: true,
                                                    message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180370", "请选择连接器") /* "请选择连接器" */,
                                                },
                                            ]}
                                        >
                                            <Select
                                                fieldid="a56571a5-b13c-412b-9e8d-3b21bb55c0be"
                                                showSearch={true}
                                                allowClear={true}
                                                optionFilterProp="children"
                                                placeholder={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180370", "请选择连接器", undefined, {
                                                    returnStr: true,
                                                })}
                                                onChange={(value, option) => {
                                                    const newInfo = {
                                                        ...applicationInfoRef.current,
                                                        connectNameTwo: option?.item.name,
                                                        connectTypeCodeTwo: option?.item.code,
                                                        connectIdTwo: "",
                                                        // targetConnectCodeVersionTwo: "",
                                                        logo2: option?.item.logo,
                                                    };
                                                    handleSpecialConnector(newInfo, false, true);
                                                }}
                                                style={{ width: "200px" }}
                                            >
                                                {listBaseLinker?.map((cate) => {
                                                    return (
                                                        <OptGroup label={cate.name} key={cate.code}>
                                                            {(cate.children || []).map((item) => (
                                                                <Option key={item.id} item={item} value={item.code}>
                                                                    {item.name}
                                                                </Option>
                                                            ))}
                                                        </OptGroup>
                                                    );
                                                })}
                                            </Select>
                                        </FormItem>
                                        <FormItem
                                            fieldid="3ffce233-a917-4d90-b3e5-b354c1e59663"
                                            noStyle
                                            name="connectIdTwo"
                                            validateTrigger="onChange"
                                            rules={[
                                                {
                                                    required: true,
                                                    message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180364", "请选择连接配置") /* "请选择连接配置" */,
                                                },
                                            ]}
                                        >
                                            <Select
                                                fieldid="be0d96dc-37c6-4a18-9bce-0bbac8a12a67"
                                                placeholder={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180364", "请选择连接配置", undefined, {
                                                    returnStr: true,
                                                })}
                                                showSearch={true}
                                                allowClear={true}
                                                optionFilterProp="filterValue"
                                                onChange={(value, option) => {
                                                    const newInfo = {
                                                        ...applicationInfoRef.current,
                                                        connectIdTwo: option?.item?.id,
                                                        // targetConnectCodeVersionTwo: option?.item?.connVersion,
                                                    };
                                                    setFormChange(++isFormChange);
                                                    applicationInfoRef.current = newInfo;
                                                }}
                                                style={{ width: "200px" }}
                                                dropdownClassName="dropdownClassName"
                                                disabled={isApiGatewayConnector(applicationInfoRef.current.connectTypeCodeTwo)}
                                            >
                                                {linkerList2.map((item = {}) => {
                                                    return (
                                                        <Option
                                                            key={item.id}
                                                            item={item}
                                                            value={item.id}
                                                            filterValue={`${item.resName}${item.versionName ?? ""}`}
                                                        >
                                                            {`${item.resName}${item.versionName ? "-" + item.versionName : ""}`}
                                                        </Option>
                                                    );
                                                })}
                                            </Select>
                                        </FormItem>
                                        {/* <FormItem noStyle name="targetConnectCodeVersionTwo">
                                            <Select disabled style={{ flex: 1 }} optionFilterProp="children">
                                                {(connVersions.target || []).map((item = {}) => {
                                                    return (
                                                        <Option key={item.versionCode} value={item.versionCode}>
                                                            {item.versionName}
                                                        </Option>
                                                    );
                                                })}
                                            </Select>
                                        </FormItem> */}
                                        <Button
                                            fieldid="b023bfdf-b3a3-45e5-9386-5af80b3d0b04"
                                            className={styles["form-item-add"]}
                                            type="text"
                                            bordered
                                            onClick={() => {
                                                gotoPage("kflj_ljpz");
                                            }}
                                            disabled={isApiGatewayConnector(applicationInfoRef.current.connectTypeCodeTwo)}
                                        >
                                            {lang.templateByUuid("UID:P_UBL-FE_1CCD5A3805F0000A", "新增") /* "新增" */}
                                        </Button>
                                    </div>
                                </FormItem>

                                <FormItem
                                    fieldid="ublinker-home-components-Modal-U9Modal-index-9117299-FormItem"
                                    label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180373", "应用编码") /* "应用编码" */}
                                    required={true}
                                    name="code"
                                    validateTrigger="onChange"
                                    rules={[
                                        {
                                            required: true,
                                            message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180361", "请输入应用编码") /* "请输入应用编码" */,
                                        },
                                        {
                                            pattern: codeReg,
                                            message: lang.templateByUuid(
                                                "UID:P_UBL-FE_18D8CEF604180363",
                                                "应用编码由英文、数字、下划线组成" //@notranslate
                                            ) /* "应用编码由英文、数字、下划线组成" */,
                                        },
                                    ]}
                                >
                                    <FormControl
                                        fieldid="ublinker-home-components-Modal-U9Modal-index-7833033-FormControl"
                                        disabled={!isAdd || createType == "init"}
                                        onChange={(value, e) => {}}
                                        className="ucg-mar-r-5"
                                        maxLength="128"
                                    />
                                </FormItem>

                                <FormItem
                                    fieldid="ublinker-home-components-Modal-U9Modal-index-7979114-FormItem"
                                    label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180368", "应用名称") /* "应用名称" */}
                                    name="name"
                                    validateTrigger="onChange"
                                    rules={[
                                        {
                                            required: true,
                                            message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418036F", "请输入应用名称") /* "请输入应用名称" */,
                                        },
                                    ]}
                                >
                                    <FormControl
                                        fieldid="ublinker-home-components-Modal-U9Modal-index-3573044-FormControl"
                                        onChange={(value, e) => {}}
                                        className="ucg-mar-r-5"
                                        maxLength="128"
                                    />
                                </FormItem>

                                <FormItem
                                    fieldid="ublinker-home-components-Modal-U9Modal-index-7979114-FormItem"
                                    label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180372", "描述") /* "描述" */}
                                    name="memo"
                                    validateTrigger="onChange"
                                >
                                    <FormControl
                                        fieldid="UCG-FE-application-components-Modal-ApplicationModal-index-1638458-FormControl"
                                        type="textarea"
                                        fieldid="ublinker-home-components-Modal-U9Modal-index-3573044-FormControl"
                                        onChange={(value, e) => {}}
                                        className="ucg-mar-r-5"
                                    />
                                </FormItem>
                                {createType == "init" ? (
                                    <FormItem
                                        fieldid="ublinker-home-components-Modal-U9Modal-index-91172991-FormItem"
                                        label={lang.templateByUuid("UID:P_UBL-FE_1A25367E05280016", "已存在方案处理") /* "已存在方案处理" */}
                                        required={true}
                                    >
                                        <div className={styles["multi-form-item"]}>
                                            <FormItem
                                                fieldid="ublinker-home-components-Modal-U9Modal-index-91172991-FormItem"
                                                noStyle
                                                required={true}
                                                name="overWriteExistScheme"
                                                validateTrigger="onChange"
                                                wrapperCol={{ span: 24 }}
                                                initialValue={false}
                                            >
                                                <Radio.Group
                                                    onChange={(value) => {
                                                        setOverWriteExistScheme(value);
                                                    }}
                                                >
                                                    <Radio fieldid={`ublinker-home-components-InitModal-InitTask-index-555959311-Checkbox`} value={false}>
                                                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418052F", "跳过") /* "跳过" */}
                                                    </Radio>
                                                    <Radio fieldid={`ublinker-home-components-InitModal-InitTask-index-555959321-Checkbox`} value={true}>
                                                        {lang.templateByUuid("UID:P_UBL-FE_1A25367E05280015", "覆盖") /* "覆盖" */}
                                                    </Radio>
                                                </Radio.Group>
                                            </FormItem>
                                            <Tooltip
                                                fieldid="ublinker-home-components-InitModal-InitTask-index-45620411-Tooltip"
                                                inverse
                                                overlay={
                                                    <div>
                                                        {
                                                            lang.templateByUuid(
                                                                "UID:P_UBL-FE_1A25367E05280014",
                                                                "覆盖将删除已有方案中的所有调整信息，仅保留同步数据，请谨慎操作" //@notranslate
                                                            ) /* "覆盖将删除已有方案中的所有调整信息，仅保留同步数据，请谨慎操作" */
                                                        }
                                                    </div>
                                                }
                                            >
                                                <i
                                                    fieldid="ublinker-routes-home-components-IndexView-index-7847251-i"
                                                    className="cl cl-Q"
                                                    style={{ cursor: "pointer" }}
                                                />
                                            </Tooltip>
                                        </div>
                                    </FormItem>
                                ) : (
                                    ublinkerEnv.env === "test" &&
                                    ublinkerEnv.isPremises === "false" && (
                                        <FormItem label={lang.templateByUuid("UID:P_UBL-FE_2043FFC605E00006","YMS微服务") /* "YMS微服务" */} name="microServiceCode" validateTrigger="onChange">
                                            <Select
                                                showSearch
                                                allowClear
                                                optionFilterProp="children"
                                                placeholder={lang.templateByUuid("UID:P_UBL-FE_2007730004F8000D", "请选择") /* "请选择" */}
                                            >
                                                {Object.entries(props.ownerState?.ymsMicro || {}).map(([key, value]) => (
                                                    <option key={value} value={value}>
                                                        {key}
                                                    </option>
                                                ))}
                                            </Select>
                                        </FormItem>
                                    )
                                )}
                            </>
                        ) : (
                            <>
                                <FormItem
                                    fieldid="ublinker-home-components-Modal-U9Modal-index-9117299-FormItem"
                                    label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180362", "导入应用") /* "导入应用" */}
                                    required={true}
                                    name="file"
                                    validateTrigger="onChange"
                                >
                                    <Dragger {...demo6props}>
                                        <div style={{ padding: "10px 0" }}>
                                            <p className="u-upload-drag-icon">
                                                <Icon
                                                    fieldid="UCG-FE-application-components-Modal-ApplicationModal-index-4554093-Icon"
                                                    type="inbox"
                                                    className="uf-upload"
                                                />
                                            </p>
                                            <p className="u-upload-text">
                                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180367", "拖拽或") /* "拖拽或" */}
                                                <span style={{ color: "#0033CC" }}>
                                                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180366", "点击上传应用") /* "点击上传应用" */}
                                                </span>
                                            </p>
                                        </div>
                                        {/* <p className="u-upload-hint">支持单个或批量上传。严禁上传公司数据或其他 band 文件。</p> */}
                                    </Dragger>
                                </FormItem>
                                <FormItem
                                    fieldid="ublinker-home-components-Modal-U9Modal-index-91172992-FormItem"
                                    label={lang.templateByUuid("UID:P_UBL-FE_1A25367E05280016", "已存在方案处理") /* "已存在方案处理" */}
                                    required={true}
                                >
                                    <div className={styles["multi-form-item"]}>
                                        <FormItem
                                            fieldid="ublinker-home-components-Modal-U9Modal-index-91172992-FormItem"
                                            noStyle
                                            required={true}
                                            name="overWriteExistScheme"
                                            validateTrigger="onChange"
                                            initialValue={false}
                                        >
                                            <Radio.Group
                                                onChange={(value) => {
                                                    setOverWriteExistScheme(value);
                                                }}
                                            >
                                                <Radio fieldid={`ublinker-home-components-InitModal-InitTask-index-555959312-Checkbox`} value={false}>
                                                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418052F", "跳过") /* "跳过" */}
                                                </Radio>
                                                <Radio fieldid={`ublinker-home-components-InitModal-InitTask-index-555959322-Checkbox`} value={true}>
                                                    {lang.templateByUuid("UID:P_UBL-FE_1A25367E05280015", "覆盖") /* "覆盖" */}
                                                </Radio>
                                            </Radio.Group>
                                        </FormItem>
                                        <Tooltip
                                            fieldid="ublinker-home-components-InitModal-InitTask-index-45620411-Tooltip"
                                            inverse
                                            overlay={
                                                <div>
                                                    {
                                                        lang.templateByUuid(
                                                            "UID:P_UBL-FE_1A25367E05280014",
                                                            "覆盖将删除已有方案中的所有调整信息，仅保留同步数据，请谨慎操作" //@notranslate
                                                        ) /* "覆盖将删除已有方案中的所有调整信息，仅保留同步数据，请谨慎操作" */
                                                    }
                                                </div>
                                            }
                                        >
                                            <i
                                                fieldid="ublinker-routes-home-components-IndexView-index-7847251-i"
                                                className="cl cl-Q"
                                                style={{ cursor: "pointer" }}
                                            />
                                        </Tooltip>
                                    </div>
                                </FormItem>
                            </>
                        )}
                    </FormList>
                </div>
            </Modal>
            {/* <Modal fieldid="ublinker-home-components-Modal-U9Modal-index-5020702-Modal"
        title={"选择配置参数"}
        // show={oldConfig.oldConfigModalShow}
        // onCancel={handleOldConfigModalCancel}
        // onOk={handleOldConfigModalOk}
        size="md"
    >

    </Modal> */}
        </>
    );
};

export default ApplicationModal;
