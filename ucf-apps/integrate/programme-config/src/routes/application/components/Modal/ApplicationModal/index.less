.connect-config-ip {
    // width: 100%;
    position: relative;
}

.connect-config-ip-address {
    position: absolute;
    left: 71%;
    top: 0px;
    cursor: pointer;
    height: 28px;
    width: 69px;
    text-align: center;
    line-height: 26px;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
    flex-shrink: 0;
    margin-left: -1px;
}
.groupadminBox {
    position: relative;
}
.groupadminBox .groupadminBox-tip {
    position: absolute;
    top: 5px;
    left: 84%;
}
.groupadminBox .groupadminBox-tip.radio {
    position: absolute;
    top: 4px;
    left: 38%;
}
.formItem-title {
    margin-bottom: 8px;
    display: flex;
    align-items: flex-end;
}
.formItem-title-two {
    display: inline-block;
    margin-left: 10px;
    margin-top: 5px;
    font-size: 12px;
    font-family:
        PingFangSC-Regular,
        PingFang SC;
    font-weight: 400;
    color: #588ce9;
}
.formItem-download-box {
    margin-top: 8px;
    margin-bottom: 8px;
}
.formItem-download {
    margin-left: 81px;
    // margin-top:8px;
    margin-bottom: 2px;
    display: flex;
    align-items: center;
    font-size: 12px;
    font-family:
        PingFangSC-Regular,
        PingFang SC;
    font-weight: 400;
    color: #999999;
}
.formItem-download-input {
    margin-left: 81px;
    // margin-top:8px;
    margin-bottom: 17px;
    display: flex;
    align-items: center;
    font-size: 12px;
    font-family:
        PingFangSC-Regular,
        PingFang SC;
    font-weight: 400;
    color: #999999;
    background-color: #eff1f7;
    width: 72%;
}
.formItem-download-input-input {
    border: none;
    border-style: none;
    outline: none;
    background: #eff1f7 !important;
    height: 28px;
    color: #666666;
    width: 65%;
}
.formItem-download-tip {
    margin-left: 10px;
    font-size: 12px;
    font-family:
        PingFangSC-Regular,
        PingFang SC;
    font-weight: 400;
    color: #999999;
}

// 网关帮助文档
.configure-pop {
    max-width: 150px;
    overflow: hidden;
    margin-right: 19px;
    font-size: 12px;
    font-family:
        PingFangSC-Regular,
        PingFang SC;
    font-weight: 400;
    color: #3a8df8;
    line-height: 17px;
    cursor: pointer;
    display: flex;
    align-items: center;
}
.configure-pop-overlay {
    // left: 75px !important;
    .wui-popover-content {
        width: 950px;
        background: #ffffff;
        box-shadow: 0px 2px 3px 0px rgba(0, 0, 0, 0.1);
        border: 1px solid #d9d9d9;
    }
    .wui-popover-inner {
        // padding: 0 16px 20px 20px;
        // overflow: auto;
    }
    &-title {
        width: 600px;
        height: 50px;
        position: absolute;
        line-height: 17px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        background-color: white;
        z-index: 100;
        margin-bottom: 50px;
        font-size: 14px;
        font-family: PingFang-SC-Heavy, PingFang-SC;
        font-weight: 800;
        color: #505766;
    }
    &-title2 {
        padding-top: 50px;
        font-size: 14px;
        font-family: PingFang-SC-Heavy, PingFang-SC;
        font-weight: 800;
        color: #505766;
    }
    &-display {
        height: 50px;
    }
    &-close {
        width: 26px;
        height: 26px;
        cursor: pointer;
    }
    &-header {
        width: 605px;
        background: #fff7e7;
        border-radius: 4px;
        display: flex;
        flex-direction: row;
        padding: 3px 10px;

        margin-bottom: 9px;
        display: flex;
        align-items: center;
        img {
            display: inline-block;
            width: 17px;
            margin-right: 6px;
            height: 17px;
        }
        span {
            flex: 1;
            font-size: 12px;
            font-family:
                PingFangSC-Regular,
                PingFang SC;
            font-weight: 400;
            color: #ee2223;
            white-space: wrap;
            line-height: 22px;
        }
    }
    // 网关帮助步骤条自定义图标
    .wui-steps-icon {
        img {
            width: 22px;
            height: 22px;
        }
    }
    .wui-steps-item-content {
        overflow: hidden;
        margin-left: 36px;
    }
    .wui-steps-item-title {
        font-size: 12px;
        font-family:
            PingFangSC-Regular,
            PingFang SC;
        font-weight: 400;
        color: #111111;
        line-height: 17px;
    }
    .wui-steps-item-description {
        font-size: 12px;
        font-family:
            PingFangSC-Regular,
            PingFang SC;
        font-weight: 400;
        color: #666666;
        line-height: 22px;
    }
    .help-example1 {
        width: 183px;
        height: 199px;
        display: block;
        margin: 7px 0;
    }
    .help-example2 {
        width: 330px;
        height: 280px;
        display: block;
        margin: 7px 0;
    }
    .help-example3 {
        width: 330px;
        height: 166px;
        display: block;
        margin: 7px 0;
    }
}

.application-modal .wui-modal-content .wui-modal-body {
    display: flex;
    padding: 0;
}
.modal-left {
    width: 34%;
    border-right: 1px solid #d3d3d3;
    padding: 17px 36px;
    box-sizing: border-box;

    background: #f7f9fd;
}
.modal-left-title {
    font-size: 14px;
    font-family: PingFang-SC-Heavy, PingFang-SC;
    font-weight: 800;
    color: #505766;
    line-height: 20px;
}
.modal-left-desc {
    font-size: 12px;
    font-family:
        PingFangSC-Regular,
        PingFang SC;
    font-weight: 400;
    color: #505766;
    line-height: 24px;
}
.modal-left-preview {
    margin: 0 auto;
    margin-top: 78px;
    // width: 254px;
    // height: 172px;
    background: #ffffff;
    box-shadow: 0px 0px 16px 0px rgba(173, 180, 188, 0.4);
    border-radius: 10px;
    padding: 33px 24px;
    box-sizing: border-box;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
}
.modal-logo2-img {
    width: 26px;
    height: 26px;
}
.preview-half {
}
.preview-half-imgbox {
    width: 80px;
    height: 80px;
    background: #ffffff;
    border-radius: 3px;
    border: 2px dashed #dcdde0;
    position: relative;
}
.preview-logo1-img,
.preview-logo2-img {
    width: 100%;
    height: 100%;
}
.preview-half-name {
    margin-top: 10px;
    text-align: center;
    font-size: 12px;
    font-family: PingFang-SC-Heavy, PingFang-SC;
    font-weight: 800;
    color: #505766;
    line-height: 17px;
}

.modal-right {
    flex: 1;
    padding-top: 136px;
}
// .wui-select-item-option-content{
//   position: relative;
// }
.modal-right-help {
    position: absolute;
    top: 10px;
    right: 0;
}
.help-title {
    font-size: 12px;
    font-family: PingFang-SC-Heavy, PingFang-SC;
    font-weight: 800;
    color: #333333;
    line-height: 17px;
    margin-bottom: 10px;
}
.modal-right .wui-form .wui-form-item {
    margin-bottom: 4px;
}
.dropdownClassName {
    width: auto !important;
    max-width: 300px;
}
