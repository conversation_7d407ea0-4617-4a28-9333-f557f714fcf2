import { Checkbox, Popconfirm, Table } from "@tinper/next-ui";
import React, { Component } from "react";
import Grid from "components/TinperBee/Grid";

const { multiSelect } = Table;

const columns16 = [
    {
        title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418026E", "操作") /* "操作" */,
        dataIndex: "d",
        width: 100,
        render(text, record, index) {
            return (
                <Popconfirm
                    fieldid="ublinker-routes-home-components-TablesList-index2-7573440-Popconfirm"
                    trigger="click"
                    placement="right"
                    content={
                        lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180273", "这是第", undefined, {
                            returnStr: true,
                        }) +
                        (index + 1) +
                        lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180272", "行", undefined, {
                            returnStr: true,
                        }) /* "行" */
                    }
                >
                    <a fieldid="ublinker-routes-home-components-TablesList-index2-7507784-a" href="javascript:void(0);" tooltip={text}>
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180274", "一些操作") /* "一些操作" */}
                    </a>
                </Popconfirm>
            );
        },
    },
    { title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180275", "订单编号") /* "订单编号" */, dataIndex: "a", width: 250 },
    { title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180276", "单据日期") /* "单据日期" */, dataIndex: "b", width: 100 },
    { title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180277", "供应商") /* "供应商" */, dataIndex: "c", width: 200 },
];
const columns17 = [
    { title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180275", "订单编号") /* "订单编号" */, dataIndex: "A", width: 100 },
    { title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180276", "单据日期") /* "单据日期" */, dataIndex: "B", width: 100 },
    { title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180277", "供应商") /* "供应商" */, dataIndex: "C", width: 200 },
];

const data16 = [
    {
        a: "NU0391001",
        b: "2019-03-01",
        c: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418026F", "xx供应商") /* "xx供应商" */,
        d: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418026E", "操作") /* "操作" */,
        id: "1",
    },
    {
        a: "NU0391002",
        b: "2018-11-02",
        c: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180270", "yy供应商") /* "yy供应商" */,
        d: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418026E", "操作") /* "操作" */,
        id: "2",
    },
    {
        a: "NU0391003",
        b: "2019-05-03",
        c: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180271", "zz供应商") /* "zz供应商" */,
        d: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418026E", "操作") /* "操作" */,
        id: "3",
    },
];
//
const MultiSelectTable = multiSelect(Table, Checkbox);

class Demo16 extends Component {
    constructor(props) {
        super(props);
        this.state = {
            tableData: data16,
            dataObj: {},
        };
    }

    expandedRowRender = (record, index, indent) => {
        return <Table fieldid="ublinker-routes-home-components-TablesList-index2-1861536-Table" columns={columns17} data={this.state.dataObj[record.id]} />;
    };
    getData = (expanded, record) => {
        // 当点击展开的时候才去请求数据
        let newObj = Object.assign({}, this.state.dataObj);
        if (expanded) {
            // if (record.key === '1') {
            newObj[record.id] = [
                { A: "NU0391056", b: "2019-03-01", c: "gys1", d: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418026E", "操作") /* "操作" */, key: "1" },
                { A: "NU0391057", b: "2018-11-02", c: "gys2", d: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418026E", "操作") /* "操作" */, key: "2" },
            ];
            this.setState({
                dataObj: newObj,
            });
            // }
            // else {
            //   newObj[record.key] = [
            //     { a: "NU0391079", b: "2019-04-17", c: "gys5", d: "操作", key: "3" },
            //   ]
            //   this.setState({
            //     dataObj: newObj
            //   })
            // }
        }
    };

    render() {
        const { dataSource } = this.props;
        console.log(dataSource);
        return (
            // <MultiSelectTable
            //   className="expanded-table"
            //   columns={columns16}
            //   data={this.state.tableData}
            //   scroll={{ y: 350 }}
            //   onExpand={this.getData}
            //   autoCheckedByClickRows={false}
            //   getSelectedDataFunc={this.getSelectedDataFunc}
            //   expandedRowRender={this.expandedRowRender}
            //   expandIconAsCell={true}
            // />
            <Table
                fieldid="ublinker-routes-home-components-TablesList-index2-7737694-Table"
                rowKey={"id"}
                // multiSelect
                // selectedList={[]}
                className="expanded-table"
                columns={columns16}
                // data={this.state.tableData}
                data={dataSource}
                scroll={{ y: 350 }}
                onExpand={this.getData}
                // autoCheckedByClickRows={false}
                // getSelectedDataFunc={this.getSelectedDataFunc}
                expandedRowRender={this.expandedRowRender}
                expandIconAsCell={true}
            />
        );
    }
}

export default Demo16;
