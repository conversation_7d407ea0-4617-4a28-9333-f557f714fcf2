import React, { Component } from "react";
import { Button } from "components/TinperBee";
import ArrowSvg from "../ArrowSvg";
import Grid from "components/TinperBee/Grid";
import "./index.less";
import ConnectorLogo from "components/ConnectorLogo";
import Logo from "components/Card/Logo";
import { getCompleteImg } from "utils";
import Pagination from "components/TinperBee/Pagination";
import { Pagination as PaginationTinper } from "@tinper/next-ui";
import ResizeObserver from "resize-observer-polyfill";

class TablesList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            tableData: [],
            dataObj: {},
            size: { width: 0, height: 0 },
        };
    }
    bodyRef = React.createRef();
    serviceInfo = React.createRef();

    componentDidMount() {
        this.resizeObserver = new ResizeObserver((entries) => {
            entries.forEach((entry) => {
                const { clientWidth, clientHeight } = entry.target;
                this.setState({ size: { width: clientWidth, height: clientHeight } });
            });
        });
        this.resizeObserver.observe(this.bodyRef.current);
    }

    componentWillUnmount() {
        this.resizeObserver?.disconnect();
    }

    renderAlias = (value, record) => {
        return (
            <span onClick={this.handleGoScheme.bind(null, record.code, record)} style={{ color: "#06f", cursor: "pointer" }}>
                {value}
            </span>
        );
    };
    handleGoScheme = (value, record) => {
        this.props.handleGoScheme(value, record);
    };
    renderImg = (value, item) => {
        return (
            <div className="grid-col">
                <div className="item-header">
                    {getCompleteImg(item.logo1) != true ? (
                        <Logo className="item-header-icon" logo={getCompleteImg(item.logo1)} title={item.connectTypeName} />
                    ) : (
                        <ConnectorLogo
                            className="item-header-icon"
                            iconfontsize={{ fontSize: "20px" }}
                            logo={item.logo1}
                            title={item.connectTypeName}
                            code={item.connectTypeCode}
                        />
                    )}
                    <ArrowSvg isDouble={item.schemesDirection !== "single"} className="logo2-img" />
                    {
                        // item.fromType2==1
                        getCompleteImg(item.logo2) != true ? (
                            <Logo className="item-header-icon" logo={getCompleteImg(item.logo2)} title={item.connectTypeNameTwo} />
                        ) : (
                            <ConnectorLogo
                                className="item-header-icon"
                                iconfontsize={{ fontSize: "20px" }}
                                logo={item.logo2}
                                title={item.connectTypeNameTwo}
                                code={item.connectTypeCodeTwo}
                            />
                        )
                    }
                    {/* <Logo
            className='item-header-icon'
            logo={getCompleteImg(item.logo2)}
            title={(item.alias || item.name) ? (item.alias || item.name).substr(0, 2) : (item.alias || item.name)}
          /> */}
                </div>
            </div>
        );
    };

    columns16 = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180130", "序号") /* "序号" */,
            dataIndex: "$$index",
            width: 50,
            render: (value, record, index) => {
                return index + 1;
            },
        },
        { title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180133", "连接器") /* "连接器" */, dataIndex: "", render: this.renderImg, width: 100 },
        { title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180134", "应用编码") /* "应用编码" */, dataIndex: "code" },
        { title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180128", "应用名称") /* "应用名称" */, dataIndex: "name", render: this.renderAlias },
        { title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418012A", "方案数量") /* "方案数量" */, dataIndex: "integrateSchemeSize", width: 80 },
        { title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418012B", "创建人") /* "创建人" */, dataIndex: "ccreator" },
        { title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418012C", "创建时间") /* "创建时间" */, dataIndex: "ccreationtime" },
        { title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418012D", "描述") /* "描述" */, dataIndex: "memo" },
    ];

    renderText = (value, record) => {
        return <span>{value || "-"}</span>;
    };
    gridhoverContent = (record, index) => {
        return (
            <>
                <Button
                    fieldid="ublinker-routes-home-components-TablesList-index-69855401-Button"
                    {...Grid.hoverButtonPorps}
                    onClick={this.props.handleGoScheme.bind(null, record.code, record)}
                >
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418012F", "设计") /* "设计" */}
                </Button>
                <Button
                    fieldid="ublinker-routes-home-components-TablesList-index-6985540-Button"
                    {...Grid.hoverButtonPorps}
                    onClick={this.props.handleNewApplication.bind(null, "edit", record, "new")}
                >
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180131", "编辑") /* "编辑" */}
                </Button>
                <Button
                    fieldid="ublinker-routes-home-components-TablesList-index-69855401-Button"
                    {...Grid.hoverButtonPorps}
                    onClick={this.props.handleCopyApply.bind(null, record?.id)}
                >
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180132", "复制") /* "复制" */}
                </Button>
                <Button
                    fieldid="ublinker-routes-home-components-TablesList-index-69855402-Button"
                    {...Grid.hoverButtonPorps}
                    onClick={this.props.handleExportApply.bind(null, record)}
                >
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180129", "导出") /* "导出" */}
                </Button>
                <Button
                    fieldid="ublinker-routes-home-components-TablesList-index-9771262-Button"
                    {...Grid.hoverButtonPorps}
                    onClick={this.props.onDelete.bind(null, record)}
                >
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418012E", "删除") /* "删除" */}
                </Button>
                {window.name === "_WORKBENCH_" &&
                    window.cb?.context.getCommonOptionValue("enableMigration") &&
                    "compare" === window.jDiwork?.diworkContext()?.migrationVersion && (
                        <Button fieldid="4a34ef4d-28e5-471b-8413-d1085a865d1c" {...Grid.hoverButtonPorps} onClick={() => this.handleAddToMigrationList(record)}>
                            {lang.templateByUuid("UID:P_IPB-FE_1CB90D8E04380007", "加入迁移清单")}
                        </Button>
                    )}
            </>
        );
    };
    getServiceInfo = async () => {
        const genServiceInfo = (data) => {
            const lang = window.lang.lang;
            return {
                labelCode: data.labelCode,
                labelName: data.labelMultiLangText[lang] || data.labelMultiLangText.zh_CN,
                appCode: data.applicationCode,
                appName: data.applicationMultiLangText[lang] || data.applicationMultiLangText.zh_CN,
                thirdCode: data.serviceCode,
                thirdType: "service",
                thirdName: data.serviceNameMultiLangText[lang] || data.serviceNameMultiLangText.zh_CN,
            };
        };
        if (this.serviceInfo.current) return this.serviceInfo.current;
        return new Promise((resolve) => {
            window.jDiwork.getState((e) => {
                this.serviceInfo.current = genServiceInfo(e.serviceInfo);
                resolve(this.serviceInfo.current);
            });
        });
    };
    handleAddToMigrationList = async (record) => {
        const serviceInfoData = await this.getServiceInfo();
        window.addToSandboxList({
            ...serviceInfoData,
            domainCode: "",
            dataPk: record.id,
            name: record.name,
            type: "integrated_app",
            code: record.code,
            extData: {},
        });
        // 触发清单数量刷新
        window.cb?.events?.execute("showSandboxBadge");
    };
    render() {
        const {
            dataSource,
            ownerState: { pagination },
            scroll,
        } = this.props;
        return (
            <>
                <div style={{ flex: 1, overflow: "hidden" }} ref={this.bodyRef}>
                    <Grid
                        fieldid="ublinker-routes-home-components-TablesList-index-3738456-Grid"
                        rowKey={"id"}
                        lockable={false}
                        className="expanded-table"
                        columns={this.columns16}
                        data={dataSource}
                        pagination={false}
                        hoverContent={this.gridhoverContent}
                        scroll={{ y: this.state.size.height - 32 }}
                        bodyStyle={{ minHeight: this.state.size.height - 32 }}
                    />
                </div>
                <PaginationTinper
                    disabled={pagination.disabled}
                    current={pagination.activePage}
                    onChange={(a, b) => pagination?.onPageChange({ pageSize: b, pageNo: a })}
                    onPageSizeChange={(a, b) => pagination?.onPageChange({ pageSize: b, pageNo: 1 })}
                    showSizeChanger
                    total={pagination.total}
                    pageSize={pagination.pageSize}
                    pageSizeOptions={[...Pagination.dataNumSelect["page"]]}
                    style={{ backgroundColor: "#fff" }}
                />
            </>
        );
    }
}

export default TablesList;
