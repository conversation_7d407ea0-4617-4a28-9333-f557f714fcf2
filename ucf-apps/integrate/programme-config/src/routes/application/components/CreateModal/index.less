.allCommons{

    // padding:10px;
}
.allCommon-warp{
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 100%;
}
.allCommon-item{
    margin-bottom: 12px;
    width: 32%;
    padding:16px 18px;
    box-shadow: 0px 0px 16px 0px rgba(173, 180, 188, 0.2);
    border: 1px solid #E8E9EB;
    border-radius: 6px;
    display: flex;
    align-items: center;
    cursor: pointer;
}
.allCommon-item:hover{
    box-shadow: 0px 0px 16px 0px rgba(173, 180, 188, 0.2);
border: 1px solid #505766;
}
.allCommon-item.cur{
    box-shadow: 0px 0px 16px 0px rgba(173, 180, 188, 0.2);
border: 1px solid #505766;
}
.allCommons .item-add-header-icon {
    // background: linear-gradient(180deg, #E53935 0%, #F46B65 100%);
    
border: 1px solid #D9D9D9;
border-radius: 4px;
    height: 40px;
    width: 40px;
    margin-right: 12px;
}

.allCommons .item-add-header-content-name{
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #000000;
    line-height: 20px;
}
.allCommons .item-add-header-content-description{
    font-size: 12px;
font-family: PingFangSC-Regular, PingFang SC;
font-weight: 400;
color: #666666;
line-height: 22px;
}