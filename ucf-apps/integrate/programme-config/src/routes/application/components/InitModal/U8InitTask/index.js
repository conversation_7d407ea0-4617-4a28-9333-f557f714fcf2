import React, { useState, useEffect, useCallback, useMemo, Fragment } from "react";
import classnames from "classnames";
import { Tooltip } from "components/TinperBee";
import { Button, Icon, Modal, Checkbox, Radio, FormList } from "components/TinperBee";
import Grid from "components/TinperBee/Grid";
import { autoServiceMessage } from "utils/service";
import { batchSyncTaskService } from "services/taskServices";
import { GridAction, GridActions } from "components/TinperBee/Grid/GridActions";
import commonText from "constants/commonText";
import { QUERY_PARAMS } from "utils/util";

import {
    getDataViewService,
    initDataViewService,
    updateSyncTaskService,
    getInitMessageService,
    getU8DataViewService,
    initU8DataViewService,
    getConfigDataService,
    getDefaultConfigInfoService,
} from "../../../service";

// import { ConfigInfoItem } from "../../../../../../../erp/src/routes/config/routes/components/ConfigInfo";

// import SqlWhereModal from "../SqlWhereModal";
// import FormList from "components/TinperBee/Form";
import "./index.less";

const u8DataViewColumns = [
    {
        title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418045A", "数据视图") /* "数据视图" */,
        dataIndex: "datatype.typename",
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418045C", "视图所属应用") /* "视图所属应用" */,
        dataIndex: "appcode",
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418045E", "视图版本") /* "视图版本" */,
        dataIndex: "tableview",
    },
];
function useInitU8DataViewHook(_useConfigData) {
    //初始化任务

    let [u8DataViews, setU8DataViews] = useState([]);
    let [u8SyncTasks, setU8SyncTasks] = useState([]);
    let [updateTaskModalShow, setUpdateTaskModalShow] = useState(false);
    let [editTaskInfo, setEditTaskInfo] = useState(false);
    let [syncTasksBatchEnd, setSyncTaskBatchEnd] = useState(false); //视图任务是否已执行同步
    // let {isInit} = _useConfigData;
    // useEffect(() => {
    //   console.log(5)
    //   setSyncTaskBatchEnd(isInit);
    // }, [isInit]);

    const getU8DataView = useCallback(async (data) => {
        let res = await autoServiceMessage({
            service: getU8DataViewService(data),
        });
        if (res) {
            setU8DataViews(res.data || []);
        }
    }, []);
    const getU8SyncTasks = useCallback(
        async (orgcode) => {
            let res = await autoServiceMessage({
                service: initU8DataViewService({
                    orgCode: orgcode,
                    tableViews: u8DataViews.map((item) => item.tableview),
                }),
            });
            if (res) {
                setU8SyncTasks(res.data || []);
            }
        },
        [u8DataViews]
    );

    const updateSyncTask = useCallback(
        async (data, index) => {
            let res = await autoServiceMessage({
                service: updateSyncTaskService(data),
                success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180454", "修改成功！", undefined, {
                    returnStr: true,
                }) /* "修改成功！" */,
            });
            if (res) {
                let _task = u8SyncTasks.find((item) => item.tableview === editTaskInfo.tableview);
                _task.sqlwhere = data.sqlwhere;
                setU8SyncTasks([...u8SyncTasks]);
                setUpdateTaskModalShow(false);
                setEditTaskInfo(null);
            }
        },
        [u8SyncTasks, editTaskInfo]
    );

    // const handleEdit = useCallback((taskInfo) => {
    //   setUpdateTaskModalShow(true);
    //   setEditTaskInfo(taskInfo);
    // }, []);

    //同步任务列信息
    const u8SyncTaskColumns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180459", "视图名称") /* "视图名称" */,
            dataIndex: "dataview.datatype.typename",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418045B", "是否增量") /* "是否增量" */,
            dataIndex: "incrementValue",
            width: 100,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418045D", "编辑数据条件") /* "编辑数据条件" */,
            dataIndex: "sqlwhere",
        },
    ];

    //执行同步任务
    const batchSyncTask = useCallback(async () => {
        let taskIds = u8SyncTasks.map((item) => item.pk_id);
        let res = await autoServiceMessage({
            service: batchSyncTaskService(taskIds),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180455", "操作成功！", undefined, {
                returnStr: true,
            }) /* "操作成功！" */,
        });
        if (res) {
            // setSyncTaskBatchEnd(true);
        }
    }, [u8SyncTasks]);

    return {
        u8DataViews,
        getU8DataView,
        u8SyncTasks,
        getU8SyncTasks,
        batchSyncTask,
        u8SyncTaskColumns,
        updateTaskModalShow,
        editTaskInfo,
        updateSyncTask,
        setUpdateTaskModalShow,
        syncTasksBatchEnd,
    };
}
const InitTaskView = (props) => {
    // debugger
    const { InitModalData, labelCol } = props;
    const [form] = FormList.useForm();
    const formItemLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 12 },
    };
    const { getFieldProps, getFieldError, validateFields, setFieldsValue } = form;
    let { id: configId, appId, tenantId } = InitModalData; //当前条数据的id
    let [isInit, setIsInit] = useState(true);
    let [configData, setConfigData] = useState({});
    let [defaultConfigInfo, setDefaultConfigInfo] = useState({
        tenantEmail: "",
        pk_tenantid: "",
        openapicode: "",
    });
    let [defaultConfigError, setDefaultConfigError] = useState("");
    const {
        u8DataViews,
        getU8DataView,
        u8SyncTasks,
        getU8SyncTasks,
        batchSyncTask,
        u8SyncTaskColumns,
        updateTaskModalShow,
        editTaskInfo,
        updateSyncTask,
        setUpdateTaskModalShow,
        syncTasksBatchEnd,
    } = useInitU8DataViewHook({ isInit: false });

    // defaultConfig 后天返回错误信息
    useEffect(() => {
        const getConfig = async (configId) => {
            // if (!configId) {
            //   setIsInit(false);
            //   return;
            // }
            let res = await autoServiceMessage({
                service: getConfigDataService(configId),
            });
            if (res) {
                let _configData = res.data;
                // setIsInit(JSON.parse(res.isInit));
                setConfigData(_configData);
            }
        };
        getConfig(configId); //获取表单内容

        const getDefaultConfig = async (tenantId) => {
            let res = await autoServiceMessage({
                service: getDefaultConfigInfoService({ tenantId }),
            });
            if (res) {
                let { status, data, accountType } = res;
                //当statue=1 data返回 {tenantEmail: "", pk_tenantid: "", openapicode: "",}等有用信息
                //否则 data 返回 一个报错信息
                if (status === 1) {
                    setDefaultConfigInfo(data);
                    // setAccountType(accountType);
                } else {
                    setDefaultConfigError(data);
                }
            }
        };
        getDefaultConfig(tenantId);
    }, []);
    useEffect(() => {
        if (configData.identifies === "SYSDB" || configData.identifies === "DBSYS") {
            //&& !isInit
            getU8DataView({
                tenantId: defaultConfigInfo.pk_tenantid,
                appid: InitModalData.appId,
                erpversion: configData.u8version,
                configid: configData.id,
            });
        }
    }, [configData, defaultConfigInfo]);

    let canInitTask = useMemo(() => configData.identifies === "SYSDB" || configData.identifies === "DBSYS", [configData]);
    return (
        <div className="u8InitTask">
            <p className="">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418045A", "数据视图") /* "数据视图" */}</p>
            <Grid
                fieldid="ublinker-home-components-InitModal-U8InitTask-index-7830486-Grid"
                empty={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180457", "无数据") /* "无数据" */}
                columns={u8DataViewColumns}
                data={u8DataViews}
                rowKey="tableview"
                footer={
                    <Button
                        fieldid="ublinker-home-components-InitModal-U8InitTask-index-1514858-Button"
                        disabled={!canInitTask || u8DataViews.length <= 0}
                        colors="primary"
                        onClick={getU8SyncTasks.bind(null, configData.orgcode)}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180453", "初始化任务") /* "初始化任务" */}
                    </Button>
                }
            />
            <div className="config-img"></div>
            <p className="">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180456", "同步任务") /* "同步任务" */}</p>
            <Grid
                fieldid="ublinker-home-components-InitModal-U8InitTask-index-8445165-Grid"
                empty={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180457", "无数据") /* "无数据" */}
                columns={u8SyncTaskColumns}
                data={u8SyncTasks}
                rowKey="pk_id"
                footer={
                    // syncTasksBatchEnd ? null : (
                    <Button
                        fieldid="ublinker-home-components-InitModal-U8InitTask-index-1668693-Button"
                        disabled={!canInitTask || u8SyncTasks.length <= 0}
                        colors="primary"
                        onClick={batchSyncTask}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180458", "执行同步任务") /* "执行同步任务" */}
                    </Button>
                    // )
                }
            />
        </div>
    );
};

export default InitTaskView;
