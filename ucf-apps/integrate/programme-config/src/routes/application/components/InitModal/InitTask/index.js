import React, { useState, useEffect, useCallback, useMemo, Fragment } from "react";
import classnames from "classnames";
import { Tooltip } from "components/TinperBee";
import { Button, Icon, Modal, Checkbox, Radio, FormList } from "components/TinperBee";
import Grid from "components/TinperBee/Grid";
import { autoServiceMessage } from "utils/service";
import { batchSyncTaskService } from "services/taskServices";
import { GridAction, GridActions } from "components/TinperBee/Grid/GridActions";
import commonText from "constants/commonText";
import { QUERY_PARAMS } from "utils/util";

import { getDataViewService, initDataViewService, updateSyncTaskService, getInitMessageService } from "../../../service";

// import {ConfigInfoItem} from "../../../../../../../erp/src/routes/config/routes/components/ConfigInfo";

import SqlWhereModal from "../SqlWhereModal";
// import FormList from "components/TinperBee/Form";
import "./index.less";

const FormItem = FormList.Item;
const CheckboxGroup = Checkbox.Group;
const dataViewColumns = [
    {
        title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180162", "数据视图") /* "数据视图" */,
        dataIndex: "datatype.typename",
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180163", "视图所属应用") /* "视图所属应用" */,
        dataIndex: "appcode",
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180164", "视图版本") /* "视图版本" */,
        dataIndex: "tableview",
    },
];

export function useInitTask(_useSetConnector, _useInitDoc, connectorType, connectorConfig) {
    const { instanceInfo, erpVersion } = _useSetConnector;
    const [isInitTaskView, setIsInitTaskView] = useState(true); //视图任务是否初始化
    const [isInitConfigView, setIsIniConfigView] = useState(false); //初始化设置项是否显示
    const [isSetConfigView, setIsSetConfigView] = useState(false); //初始化ERP任务是否已保存设置
    const [dataViews, setDataViews] = useState([]); //数据视图列表
    const [enableHR, setEnableHR] = useState(false); //是否是hr版
    const [rankStandard, setRankStandard] = useState([]); //职级体系标准
    const [classesSystem, setClassesSystem] = useState([]); //系统级任务分类
    const [classesTenant, setClassesTenant] = useState([]); //租户级任务分类
    const [rankStandardSelect, setRankStandardSelect] = useState(""); //职级体系标准 选择select
    const [classesSystemSelect, setClassesSystemSelect] = useState([]); //系统级任务分类 选择select
    const [classesTenantSelect, setClassesTenantSelect] = useState([]); //租户级任务分类 选择select
    const [syncTasks, setSyncTasks] = useState([]); //同步任务列表
    const [updateTaskModalShow, setUpdateTaskModalShow] = useState(false); //编辑任务视图条件Modal显示隐藏
    const [editTaskInfo, setEditTaskInfo] = useState(false); //当前编辑视图任务数据
    const [syncTasksBatchEnd, setSyncTaskBatchEnd] = useState(false); //视图任务是否已执行同步
    const [isClickBatchSyncTask, setIsClickBatchSyncTask] = useState(false); //视图任务是否已执行同步
    const [isInitTask, setIsInitTask] = useState(false); //是否已初始化，若已初始化不能再点击初始化按钮

    // //获取视图任务是否已经初始化
    // const getIsViewInitService = useCallback(async (gatewayId) => {
    //   let res = await autoServiceMessage({
    //     service: isViewInitService(gatewayId)
    //   })
    //   if (res) {
    //     //let { isViewInit } = res;
    //     setIsInitTaskView(true);
    //     setSyncTaskBatchEnd(true)
    //   }
    // }, [])

    // //gatewayId更新后，自动获取 视图任务更新与否状态
    // useEffect(() => {
    //   if (instanceInfo.gatewayId) {
    //     getIsViewInitService(instanceInfo.gatewayId)
    //   }
    // }, [instanceInfo.gatewayId])

    const initDisabled = useCallback(() => {
        if (erpVersion && instanceInfo.gatewayId) {
            return false;
        } else {
            return true;
        }
    }, [erpVersion, instanceInfo]);

    //查询初始化信息
    const getInitConfigViewService = useCallback(async () => {
        let param = {
            erpType: connectorType,
            detailType: erpVersion,
            connectId: instanceInfo.connectId,
        };
        let res = await autoServiceMessage({
            service: getInitMessageService(param),
        });
        if (res) {
            let { isHr, classes, rank = [] } = res.data;
            let { System, Tenant = [] } = classes;
            let classesSystemSelect = System.filter((item) => item.select).map((item) => item.code);
            let classesTenantSelect = Tenant.filter((item) => item.select).map((item) => item.code);
            let rankStandardSelect = "";
            rank.forEach((item) => {
                if (item.select) {
                    rankStandardSelect = item.code;
                }
            });
            setEnableHR(isHr);
            setRankStandard(rank);
            setClassesSystem(System);
            setClassesTenant(Tenant);
            setClassesSystemSelect(classesSystemSelect);
            setClassesTenantSelect(classesTenantSelect);
            setRankStandardSelect(rankStandardSelect);
        }
    }, [isInitConfigView]);

    //点击初始化任务，显示初始化任务选项
    useEffect(() => {
        if (isInitConfigView) {
            getInitConfigViewService();
        }
    }, [isInitConfigView]);

    /**
     * 获取数据视图, 需要确认hr视图档案类型，再次调用此接口时传入doc参数
     * @param {String=} doc=[category|sequence]
     */
    const getDataViews = useCallback(
        async (doc) => {
            const requestData = {
                erpversion: erpVersion,
                connectId: instanceInfo.connectId,
                classes: classesSystemSelect.concat(classesTenantSelect),
                isHr: enableHR,
                doc,
            };
            let res = await autoServiceMessage({
                service: getDataViewService(requestData),
            });
            if (res) {
                //res.enableHR=true 如果需要确认hr视图档案类型，再次调用此接口
                const { data = [], enableHR } = res;
                //setEnableHR(!!enableHR);
                setDataViews(data);
                setIsIniConfigView(false); //控制显示隐藏
                setIsSetConfigView(true);
            }
        },
        [erpVersion, instanceInfo.connectId, classesSystemSelect, classesTenantSelect, enableHR]
    );

    // //当没有初始化任务视图 并且初始化档案已结束
    // useEffect(() => {
    //   if (!isInitTaskView && instanceInfo.configId && _useInitDoc.stepDone) {
    //     getDataViews()
    //   }
    // }, [isInitTaskView, _useInitDoc.stepDone, erpVersion, instanceInfo.configId])

    //根据视图获取同步任务
    const getSyncTasks = useCallback(async () => {
        let res = await autoServiceMessage({
            service: initDataViewService(
                {
                    gatewayId: instanceInfo.connectId,
                    // connectId:
                    tableViewNames: dataViews.map((item) => item.tableview),
                    isHr: enableHR,
                    rankType: rankStandardSelect,
                    erpType: connectorType,
                    detailType: erpVersion,
                    classes: {
                        System: classesSystemSelect,
                        Tenant: classesTenantSelect,
                    },
                },
                instanceInfo.useMainTenantGateway
            ),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180168", "保存成功", undefined, {
                returnStr: true,
            }) /* "保存成功" */,
        });
        if (res) {
            setSyncTaskBatchEnd(false);
            setIsInitTask(true);
            setSyncTasks(res.data || []);
        }
    }, [instanceInfo, dataViews, enableHR, rankStandardSelect, connectorType, erpVersion, classesSystemSelect, classesTenantSelect]);

    //编辑视图条件
    const updateSyncTask = useCallback(async (data, index) => {
        data.sqlwhere = btoa(encodeURIComponent(data.sqlwhere));
        let res = await autoServiceMessage({
            service: updateSyncTaskService(data),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418016B", "修改成功！", undefined, {
                returnStr: true,
            }) /* "修改成功！" */,
        });
        if (res) {
            let _task = syncTasks.find((item) => item.pk_id === editTaskInfo.pk_id);
            _task.sqlwhere = data.sqlwhere;
            setSyncTasks([...syncTasks]);
            setUpdateTaskModalShow(false);
            setEditTaskInfo(null);
        }
    });

    const handleEdit = useCallback((taskInfo) => {
        setUpdateTaskModalShow(true);
        setEditTaskInfo(taskInfo);
    });

    //同步任务列信息
    const syncTaskColumns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180156", "视图名称") /* "视图名称" */,
            dataIndex: "dataview.datatype.typename",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180158", "是否增量") /* "是否增量" */,
            dataIndex: "incrementValue",
            width: 100,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418015A", "编辑数据条件") /* "编辑数据条件" */,
            dataIndex: "sqlwhere",
        },
    ];

    //执行同步任务
    const batchSyncTask = useCallback(async () => {
        setIsClickBatchSyncTask(true);
        let taskIds = syncTasks.map((item) => item.pk_id);
        let res = await autoServiceMessage({
            service: batchSyncTaskService(taskIds),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180161", "操作成功！", undefined, {
                returnStr: true,
            }) /* "操作成功！" */,
        });
        if (res) {
            setSyncTaskBatchEnd(true);
            setIsClickBatchSyncTask(false);
        } else {
            setIsClickBatchSyncTask(false);
        }
    });

    return {
        dataViews,
        getDataViews,
        enableHR,
        syncTaskColumns,
        syncTasks,
        getSyncTasks,
        batchSyncTask,
        updateTaskModalShow,
        editTaskInfo,
        updateSyncTask,
        setUpdateTaskModalShow,
        isInitTaskView,
        syncTasksBatchEnd,
        isInitConfigView,
        setIsIniConfigView,
        classesSystem,
        classesTenant,
        rankStandard,
        classesSystemSelect,
        setClassesSystemSelect,
        classesTenantSelect,
        setClassesTenantSelect,
        rankStandardSelect,
        setRankStandardSelect,
        isSetConfigView,
        setIsSetConfigView,
        initDisabled,
        isInitTask,
        isClickBatchSyncTask,
        setIsClickBatchSyncTask,
    };
}

// const syncTaskHtml = process.env.CONTEXT + '/data/sync-task/index.html' + window.location.search;

const navToSyncTask = () => {
    if (QUERY_PARAMS.from === "diwork") {
        window.jDiwork.openWin({
            id: "ublinker-data-sync-task",
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418016C", "数据同步任务") /* "数据同步任务" */,
            // url: window.location.origin + syncTaskHtml
        });
    }
    // else {
    //   window.location.href = syncTaskHtml;
    // }
};

const SelectTag = ({ children, onClick, active = false }) => {
    const [selfActive, setSelfActive] = useState(active);

    const handleClick = useCallback(() => {
        setSelfActive(!selfActive);
        onClick && onClick();
    }, [selfActive]);

    const cls = useMemo(() => {
        return classnames("ucg-select-tag-card", {
            active: selfActive,
        });
    }, [selfActive]);

    return (
        <li className={cls} onClick={handleClick}>
            <span className="tag-card-check">
                <Icon fieldid="ublinker-home-components-InitModal-InitTask-index-9080298-Icon" type="uf-correct-2" />
            </span>
            {children}
        </li>
    );
};

const InitTaskView = (props) => {
    const { _useInitTask, labelCol } = props;
    const [form] = FormList.useForm();
    const formItemLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 12 },
    };
    const { getFieldProps, getFieldError, validateFields } = form;
    const {
        dataViews,
        getDataViews,
        enableHR,
        syncTaskColumns,
        syncTasks,
        getSyncTasks,
        batchSyncTask,
        updateTaskModalShow,
        editTaskInfo,
        updateSyncTask,
        setUpdateTaskModalShow,
        isInitTaskView,
        isInitConfigView,
        setIsIniConfigView,
        syncTasksBatchEnd,
        classesSystem,
        classesTenant,
        rankStandard,
        classesSystemSelect,
        setClassesSystemSelect,
        classesTenantSelect,
        setClassesTenantSelect,
        rankStandardSelect,
        setRankStandardSelect,
        isSetConfigView,
        initDisabled,
        isInitTask,
        isClickBatchSyncTask,
        setIsClickBatchSyncTask,
    } = _useInitTask;

    useEffect(() => {
        //给checkbox.group赋值，因为initialValue只执行一次，第一次为空，所以赋值不上，所以这样赋值
        form.setFieldsValue({ classesSystem: classesSystemSelect, classesTenant: classesTenantSelect, rankStandard: rankStandardSelect });
    }, [classesSystemSelect, classesTenantSelect, rankStandardSelect]);
    let rankDisabled = rankStandard.some((item) => item.select);
    /** 点击保存按钮 */
    const handleSave = () => {
        validateFields().then((values) => {
            getDataViews(rankStandardSelect);
        });
    };
    return (
        <Fragment>
            <div className="initTask-box">
                <div className="box-header-title ucg-mar-r-10">
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418016A", "初始化ERP任务") /* "初始化ERP任务" */}
                </div>
                {!isInitConfigView && !isSetConfigView ? (
                    <Button
                        fieldid="ublinker-home-components-InitModal-InitTask-index-4364349-Button"
                        className=" ucg-mar-t-10"
                        colors="primary"
                        onClick={setIsIniConfigView.bind(null, true)}
                        disabled={initDisabled()}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418016E", "初始化任务") /* "初始化任务" */}
                    </Button>
                ) : null}
                {isInitConfigView ? (
                    <FormList
                        fieldid="ublinker-home-components-InitModal-InitTask-index-7117352-FormList"
                        className="init-task-form"
                        form={form}
                        name="form122"
                        labelAlign="right"
                        {...formItemLayout}
                    >
                        {classesSystem.length ? (
                            <FormItem
                                fieldid="ublinker-home-components-InitModal-InitTask-index-711131-FormItem"
                                label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180157", "系统级任务分类") /* "系统级任务分类" */}
                                name="classesSystem"
                                initialValue={classesSystemSelect}
                                validateTrigger="onChange"
                                rules={[
                                    {
                                        required: true,
                                        message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418015B", "请选择系统级任务分类") /* "请选择系统级任务分类" */,
                                    },
                                ]}
                            >
                                <CheckboxGroup
                                    onChange={(value) => {
                                        setClassesSystemSelect(value);
                                    }}
                                >
                                    {classesSystem.map((item) => {
                                        return (
                                            <Checkbox
                                                fieldid="ublinker-home-components-InitModal-InitTask-index-5559593-Checkbox"
                                                key={item.code}
                                                value={item.code}
                                                disabled={item.select}
                                            >
                                                {item.name}
                                            </Checkbox>
                                        );
                                    })}
                                </CheckboxGroup>
                            </FormItem>
                        ) : null}
                        {classesTenant.length ? (
                            <FormItem
                                fieldid="ublinker-home-components-InitModal-InitTask-index-1952780-FormItem"
                                label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180165", "租户级任务分类") /* "租户级任务分类" */}
                                name="classesTenant"
                                initialValue={classesTenantSelect}
                                validateTrigger="onChange"
                                rules={[
                                    {
                                        required: true,
                                        message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180166", "请选择租户级任务分类") /* "请选择租户级任务分类" */,
                                    },
                                ]}
                            >
                                <CheckboxGroup
                                    value={classesTenantSelect}
                                    onChange={(value) => {
                                        setClassesTenantSelect(value);
                                    }}
                                >
                                    {classesTenant.map((item) => {
                                        return (
                                            <Checkbox
                                                fieldid="ublinker-home-components-InitModal-InitTask-index-552458-Checkbox"
                                                key={item.code}
                                                value={item.code}
                                                disabled={item.select}
                                            >
                                                {item.name}
                                            </Checkbox>
                                        );
                                    })}
                                </CheckboxGroup>
                            </FormItem>
                        ) : null}
                        {enableHR && rankStandard.length > 0 ? (
                            <div className="rankBox">
                                <FormItem
                                    fieldid="ublinker-home-components-InitModal-InitTask-index-8915050-FormItem"
                                    label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418016D", "职级体系标准") /* "职级体系标准" */}
                                    name="rankStandard"
                                    initialValue={rankStandardSelect}
                                    validateTrigger="onChange"
                                    rules={[
                                        {
                                            required: true,
                                            message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418016F", "请选择职级体系标准") /* "请选择职级体系标准" */,
                                        },
                                    ]}
                                >
                                    <Radio.Group
                                        selectedValue={rankStandardSelect}
                                        onChange={(value) => {
                                            setRankStandardSelect(value);
                                        }}
                                    >
                                        {rankStandard.map((item) => {
                                            return (
                                                <Radio
                                                    fieldid="ublinker-home-components-InitModal-InitTask-index-8686666-Radio"
                                                    key={item.code}
                                                    value={item.code}
                                                    disabled={rankDisabled}
                                                >
                                                    {item.name}
                                                </Radio>
                                            );
                                        })}
                                    </Radio.Group>
                                </FormItem>
                                <div className="rankBox-tip">
                                    <Tooltip
                                        fieldid="ublinker-home-components-InitModal-InitTask-index-456204-Tooltip"
                                        inverse
                                        overlay={
                                            <div>
                                                <p>
                                                    {
                                                        lang.templateByUuid(
                                                            "UID:P_UBL-FE_18D8CEF60418015C",
                                                            "ERP需要通过网关访问云服务，因此要确保网关IP和端口对ERP服务器开放。"//@notranslate
                                                        ) /* "ERP需要通过网关访问云服务，因此要确保网关IP和端口对ERP服务器开放。" */
                                                    }
                                                </p>
                                                <p>
                                                    {
                                                        lang.templateByUuid(
                                                            "UID:P_UBL-FE_18D8CEF60418015E",
                                                            "所以请务必选择（或手动输入）部署网关的服务器的正确IP地址和端口。"//@notranslate
                                                        ) /* "所以请务必选择（或手动输入）部署网关的服务器的正确IP地址和端口。" */
                                                    }
                                                </p>
                                                <p>
                                                    {
                                                        lang.templateByUuid(
                                                            "UID:P_UBL-FE_18D8CEF60418015F",
                                                            "IP和端口仅用于在内网中ERP与网关通信，不需要对外开放，安全性可保障。"//@notranslate
                                                        ) /* "IP和端口仅用于在内网中ERP与网关通信，不需要对外开放，安全性可保障。" */
                                                    }
                                                </p>
                                                <p>
                                                    {
                                                        lang.templateByUuid(
                                                            "UID:P_UBL-FE_18D8CEF604180160",
                                                            "如果测试网关连接异常，将只影响ERP访问云端的业务，基础档案数据同步不会受到影响。"//@notranslate
                                                        ) /* "如果测试网关连接异常，将只影响ERP访问云端的业务，基础档案数据同步不会受到影响。" */
                                                    }
                                                </p>
                                            </div>
                                        }
                                    >
                                        <i
                                            fieldid="ublinker-home-components-InitModal-InitTask-index-9635980-i"
                                            className="cl cl-Q ucg-pad-l-5"
                                            style={{ color: "#505766" }}
                                        />
                                    </Tooltip>
                                </div>
                            </div>
                        ) : null}

                        {classesSystem.length ? (
                            <FormItem
                                fieldid="ublinker-home-components-InitModal-InitTask-index-2126071-FormItem"
                                // labelCol={labelCol}
                                label=" "
                            >
                                <Button
                                    fieldid="ublinker-home-components-InitModal-InitTask-index-2186994-Button"
                                    className="ucg-mar-r-10"
                                    colors="primary"
                                    onClick={handleSave}
                                >
                                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180167", "保存") /* "保存" */}
                                </Button>
                                <Button
                                    fieldid="ublinker-home-components-InitModal-InitTask-index-1309041-Button"
                                    border
                                    onClick={setIsIniConfigView.bind(null, false)}
                                >
                                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180169", "取消") /* "取消" */}
                                </Button>
                            </FormItem>
                        ) : null}
                    </FormList>
                ) : null}
                {isSetConfigView ? (
                    <Fragment>
                        <div className="dataview-header">
                            <p className="">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180162", "数据视图") /* "数据视图" */}</p>
                            <Button
                                fieldid="ublinker-home-components-InitModal-InitTask-index-9320538-Button"
                                disabled={dataViews.length <= 0 || isInitTask}
                                colors="primary"
                                onClick={getSyncTasks.bind(null)}
                            >
                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418016A", "初始化ERP任务") /* "初始化ERP任务" */}
                            </Button>
                        </div>
                        <Grid
                            fieldid="ublinker-home-components-InitModal-InitTask-index-9889563-Grid"
                            empty={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418015D", "无数据") /* "无数据" */}
                            columns={dataViewColumns}
                            data={dataViews}
                            rowKey="tableview"
                            scroll={{ y: 300 }}
                        />
                        <div className="config-img"></div>
                        <div className="dataview-header">
                            <p className="">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180155", "同步任务") /* "同步任务" */}</p>
                            {syncTasksBatchEnd ? null : (
                                <Button
                                    fieldid="ublinker-home-components-InitModal-InitTask-index-4499753-Button"
                                    disabled={syncTasks.length <= 0 || isClickBatchSyncTask}
                                    colors="primary"
                                    onClick={batchSyncTask}
                                >
                                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180159", "执行同步任务") /* "执行同步任务" */}
                                </Button>
                            )}
                        </div>
                        <Grid
                            fieldid="ublinker-home-components-InitModal-InitTask-index-3120520-Grid"
                            empty={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418015D", "无数据") /* "无数据" */}
                            columns={syncTaskColumns}
                            data={syncTasks}
                            rowKey="pk_id"
                            scroll={{ y: 300 }}
                        />
                    </Fragment>
                ) : null}
                {syncTasksBatchEnd ? (
                    <>
                        {
                            //   <Button fieldid="ublinker-home-components-InitModal-InitTask-index-7542226-Button"
                            //   bordered
                            //   className='ucg-mar-t-5'
                            //   onClick={navToSyncTask}
                            // ><a>{"查看任务"}</a></Button>
                        }
                    </>
                ) : null}
                <SqlWhereModal show={updateTaskModalShow} taskInfo={editTaskInfo} onCancel={setUpdateTaskModalShow.bind(null, false)} onOk={updateSyncTask} />
            </div>
        </Fragment>
    );
};
const FormInitTaskView = InitTaskView;

export default FormInitTaskView;
