.init-task-form {
    padding-left: 80px;
}


.ucg-select-tag-card {
    position: relative;
    display: inline-block;
    margin: 0 8px;
    width: 100px;
    height: 36px;
    background: #FFFFFF;
    border: 1px solid #D9D9D9;
    border-radius: 2px;
    text-align: center;
    line-height: 36px;
    font-size: 12px;
    color: #333333;
    cursor: pointer;
    &.active {
        .tag-card-check {
            border-top-color: #EE2223;
        }
    }
    .tag-card-check {
        width: 0;
        height: 0;
        border-top: 18px solid #D9D9D9;
        border-right: 18px solid transparent;
        position: absolute;
        top: 0;
        left: 0;
        i.uf {
            position: absolute;
            left: 0;
            top: -18px;
            color: #FFFFFF;
            height: 12px;
            width: 12px;
            padding: 0;
            line-height: 12px;
            font-size: 12px;
        }
    }
}
.rankBox{
    position: relative;
}
.rankBox .rankBox-tip{
    position: absolute;
    top:5px;
    left:76%
}
.dataview-header{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin:9px 0;
}