import React, { Component } from "react";
import Modal from "components/TinperBee/Modal";
import { Checkbox, FormControl } from "components/TinperBee";

class SqlWhereModal extends Component {
    constructor() {
        super();
    }

    handleOk = () => {
        let { taskInfo } = this.props;
        let data = {
            sqlwhere: btoa(encodeURIComponent(this.sqlwhereInput.state.value)),
            dataversion: taskInfo.dataversion,
            increment: taskInfo.increment,
            taskId: taskInfo.pk_id,
        };
        this.props.onOk(data);
    };

    render() {
        let { show, onCancel, taskInfo } = this.props;

        return (
            <Modal
                fieldid="ublinker-home-components-InitModal-SqlWhereModal-index-1510487-Modal"
                show={show}
                title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041800BA", "视图任务编辑", undefined, {
                    returnStr: true,
                })}
                onCancel={onCancel}
                onOk={this.handleOk}
            >
                {show && taskInfo ? (
                    <div className="ucg-pad-20">
                        <p>{lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041800B7", "SQL条件") /* "SQL条件" */}</p>
                        <FormControl.TextArea
                            rows={4}
                            // componentClass="textarea"
                            defaultValue={taskInfo.sqlwhere || ""}
                            // placeholder={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041800B9","查询条件，无需输入where关键词，示例：code\u003d\'2212\' and ts\u003e@lastupdatetime[@lastupdatetime为最后一次成功同步时间") /* "查询条件，无需输入where关键词，示例：code\u003d\'2212\' and ts\u003e@lastupdatetime[@lastupdatetime为最后一次成功同步时间" */}
                            ref={(node) => (this.sqlwhereInput = node)}
                            style={{ height: "auto" }}
                            // autoSize={{
                            //   minRows: 4
                            // }}
                        />
                        <Checkbox
                            fieldid="ublinker-home-components-InitModal-SqlWhereModal-index-9964738-Checkbox"
                            defaultChecked={taskInfo.increment}
                            ref={(node) => (this.incrementCheckBox = node)}
                        >
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041800B8", "是否启用增量") /* "是否启用增量" */}
                        </Checkbox>
                    </div>
                ) : null}
            </Modal>
        );
    }
}

export default SqlWhereModal;
