import React, { useState, useEffect, useCallback, useMemo, Fragment } from "react";
import { Button, FormControl, Modal, Select, Progress } from "components/TinperBee";
// import ProgressBar from 'tinper-bee/lib/ProgressBar'
import Grid from "components/TinperBee/Grid";
// import {ConfigInfoItem} from "../../../../../../../erp/src/routes/config/routes/components/ConfigInfo";
// import {ConfigInfoItem} from "../ConfigInfo";
import { autoServiceMessage } from "utils/service";
import commonText from "constants/commonText";
import { initDocStatusService, getDefDocService, editDefDocService, pushDefDocService } from "../../../service";
import "./index.less";

export function useInitDoc(_useSetConnector, connectorConfig) {
    let [defDocList, setDefDocList] = useState([]); //档案列表
    let [selectedDefDoc, setSelectedDefDoc] = useState([]); //已选档案列表
    let [cloudDocSelectData, setCloudDocSelectedData] = useState([]); //diwork档案下拉数据
    let [docGridLoading, setDocGridLoading] = useState(false); //档案表格loading控制
    let [docPushProgress, setDocPushProgress] = useState(0); //档案推送进度
    let [docPushProgressMax, setDocPushProgressMax] = useState(0); //档案推送进度最大值
    let [errPushDocList, setErrPushDocList] = useState([]); //推送错误doc列
    // let [stepDone, setStepDone] = useState(false);
    let { erpVersion, instanceInfo } = _useSetConnector;

    //获取档案初始化状态
    // const getDefaultStatus = useCallback(async () => {
    //   let res = await autoServiceMessage({
    //     service: initDocStatusService(erpVersion)
    //   })
    //   if (res) {
    //     setStepDone(res.init)
    //   }
    // }, [erpVersion])

    // useEffect(() => {
    //   if (erpVersion && instanceInfo.configId) {
    //     getDefaultStatus()
    //   }
    // }, [erpVersion, instanceInfo.configId])

    //获取档案列表
    const getDefaultDoc = useCallback(async () => {
        let res = await autoServiceMessage({
            service: getDefDocService({ gatewayId: instanceInfo.connectId, erpVersion }),
        });
        if (res) {
            let { ncMappings = [], diworkData = [] } = res;
            // ncMappings=ncMappings.slice(0,25)
            let selectedList = [];
            ncMappings.forEach((item) => {
                if (item._checked) {
                    selectedList.push(item);
                }
            });
            let _cloudDocSelectData = diworkData.map((item) => {
                return {
                    key: item.cloudName,
                    value: item.cloudPk,
                };
            });
            setDefDocList(ncMappings); //表格数据
            setSelectedDefDoc(selectedList); //表格选中数据
            setCloudDocSelectedData(_cloudDocSelectData); //下拉框数据
        }
    }, [erpVersion, instanceInfo]);

    useEffect(() => {
        getDefaultDoc();
    }, []);

    //保存档案编辑项
    const saveEditDoc = useCallback(
        async (data, index) => {
            let _cloudData = cloudDocSelectData.find((item) => item.value === data._cloudPk);
            let res = await autoServiceMessage({
                service: editDefDocService({
                    cloudName: _cloudData.key,
                    cloudPk: _cloudData.value,
                    erpName: data.erpName,
                    erpPk: data.erpPk,
                    erpversion: erpVersion,
                }),
                success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804E0", "保存成功", undefined, {
                    returnStr: true,
                }) /* "保存成功" */,
            });
            if (res) {
                let docData = defDocList[index];
                docData.isEdit = false;
                docData.cloudPk = _cloudData.value;
                docData.cloudName = _cloudData.key;
                delete docData._cloudPk;
                setDefDocList([...defDocList]);
            }
        },
        [erpVersion, cloudDocSelectData, defDocList]
    );

    const pushDoc = useCallback(
        async (erpPks, total = docPushProgressMax, nowProgress = 0, errorList = []) => {
            let _erpPks = erpPks.splice(0, 1);
            let res = await autoServiceMessage({
                service: pushDefDocService({
                    erpVersion: erpVersion,
                    erpPks: _erpPks,
                }),
                error: () => {},
            });

            let errorDocList = errorList;
            if (!res) {
                errorDocList = errorDocList.concat(_erpPks);
                setErrPushDocList(errorDocList);
            }

            let _nowProgress = nowProgress + 1;
            setDocPushProgress(Math.ceil(((nowProgress + 1 - errorDocList.length) / total) * 100));
            if (_nowProgress < total) {
                pushDoc(erpPks, total, _nowProgress, errorDocList);
            } else {
                let errLength = errorDocList.length;
                Modal.info({
                    title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804E3", "完成情况统计") /* "完成情况统计" */,
                    backdropClosable: false,
                    content: (
                        <div>
                            <p>
                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804E5", "成功：") /* "成功：" */}
                                <strong style={{ color: "#19be6b" }}>{total - errLength}</strong>
                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804E4", "条") /* "条" */}
                            </p>
                            <p>
                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804E7", "失败：") /* "失败：" */}
                                <strong style={{ color: "#ed4014" }}>{errLength}</strong>
                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804E4", "条") /* "条" */}
                            </p>
                            {errLength > 0 ? (
                                <p>
                                    {
                                        lang.templateByUuid(
                                            "UID:P_UBL-FE_18D8CEF6041804D5",
                                            "已帮您记录下了保存错误的档案，您可以点击“再次提交保存失败的档案”按钮，再次保存它们。"//@notranslate
                                        ) /* "已帮您记录下了保存错误的档案，您可以点击“再次提交保存失败的档案”按钮，再次保存它们。" */
                                    }
                                </p>
                            ) : null}
                        </div>
                    ),
                    confirmType: "one",
                    onOk: () => {
                        setDocPushProgress(0);
                        setDocPushProgressMax(0);
                        setDocGridLoading(false);
                    },
                    onCancel: () => {
                        setDocPushProgress(0);
                        setDocPushProgressMax(0);
                        setDocGridLoading(false);
                    },
                });
            }
        },
        [erpVersion, docPushProgress, errPushDocList]
    );

    const againPushDoc = useCallback(() => {
        setDocGridLoading(true);
        setDocPushProgressMax(errPushDocList.length);
        pushDoc([...errPushDocList], errPushDocList.length);
    }, [errPushDocList]);

    const handleSave = useCallback(() => {
        let selectedDocLength = selectedDefDoc.length;
        console.log(selectedDocLength);
        Modal.confirm({
            fieldid: "************",
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804D6", "是否保存自定义档案？") /* "是否保存自定义档案？" */,
            content:
                lang.templateByUuid("UID:P_UBL-FE_18D9147C05300019", "当前共选择条数:", undefined, {
                    returnStr: true,
                }) + selectedDocLength,
            onOk: () => {
                setDocGridLoading(true);
                setDocPushProgressMax(selectedDocLength);
                let erpPks = selectedDefDoc.map((item) => item.erpPk);
                pushDoc(erpPks, selectedDocLength);
            },
        });
    }, [selectedDefDoc]);

    const defDocGridColumns = useMemo(
        () => [
            {
                title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804DD", "ERP档案") /* "ERP档案" */,
                dataIndex: "erpName",
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804DE", "diwork档案") /* "diwork档案" */,
                dataIndex: "cloudPk",
                render: (cloudPk, record, index) => {
                    if (record.isEdit) {
                        return (
                            <Select
                                fieldid="ublinker-home-components-InitModal-InitDoc-index-9287987-Select"
                                // data={cloudDocSelectData}
                                showSearch
                                optionFilterProp="children"
                                value={record.cloudName}
                                onChange={(value) => {
                                    defDocList[index]._cloudPk = value;
                                    defDocList[index].cloudName = cloudDocSelectData.find((item) => {
                                        return item.value === value;
                                    }).key;
                                    setDefDocList([...defDocList]);
                                }}
                            >
                                {cloudDocSelectData.map((item) => {
                                    return <Select.Option value={item.value}>{item.key}</Select.Option>;
                                })}
                            </Select>
                        );
                    } else {
                        return record.cloudName || cloudPk;
                    }
                },
            },
            {
                title: "",
                dataIndex: "$$save",
                render: (value, record, index) => {
                    return record.isEdit ? (
                        <Fragment>
                            <Button
                                fieldid="ublinker-home-components-InitModal-InitDoc-index-354861-Button"
                                {...Grid.hoverButtonPorps}
                                onClick={saveEditDoc.bind(null, record, index)}
                            >
                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804E1", "保存") /* "保存" */}
                            </Button>
                            <Button
                                fieldid="ublinker-home-components-InitModal-InitDoc-index-8994702-Button"
                                {...Grid.hoverButtonPorps}
                                colors="secondary"
                                onClick={() => {
                                    let docData = defDocList[index];
                                    docData.isEdit = false;
                                    delete docData._cloudPk;
                                    setDefDocList([...defDocList]);
                                }}
                            >
                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804E6", "取消") /* "取消" */}
                            </Button>
                        </Fragment>
                    ) : null;
                },
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804D8", "操作") /* "操作" */,
                dataIndex: "$$actions",
                width: 80,
                render: (value, record, index) => {
                    return (
                        <a
                            fieldid="ublinker-home-components-InitModal-InitDoc-index-9305804-a"
                            onClick={() => {
                                let docData = defDocList[index];
                                docData.isEdit = true;
                                docData._cloudPk = record.cloudPk;
                                setDefDocList([...defDocList]);
                            }}
                        >
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804DC", "编辑") /* "编辑" */}
                        </a>
                    );
                },
            },
        ],
        [defDocList, cloudDocSelectData]
    );

    return {
        defDocGridColumns,
        defDocList,
        setDefDocList,
        selectedDefDoc,
        setSelectedDefDoc,
        cloudDocSelectData,
        handleSave,
        docGridLoading,
        docPushProgress,
        docPushProgressMax,
        errPushDocList,
        againPushDoc,
    };
}

const InitDoc = (props) => {
    let { _useInitDoc } = props;
    let {
        defDocList,
        selectedDefDoc,
        setSelectedDefDoc,
        defDocGridColumns,
        handleSave,
        docGridLoading,
        docPushProgress,
        docPushProgressMax,
        errPushDocList,
        againPushDoc,
    } = _useInitDoc;

    const disabled = useMemo(() => selectedDefDoc.length <= 0, [selectedDefDoc]);

    return (
        <div className="initDoc-box ucg-mar-b-30">
            <div className="initDoc-box-header">
                <div className="box-header-title ucg-mar-r-10">
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804DA", "修改自定义档案") /* "修改自定义档案" */}
                </div>
                <p className="config-text">
                    {
                        lang.templateByUuid(
                            "UID:P_UBL-FE_18D8CEF6041804DB",
                            "如无需配置自定义档案，请直接点击“保存自定义档案”按钮以保存此步骤"//@notranslate
                        ) /* "如无需配置自定义档案，请直接点击“保存自定义档案”按钮以保存此步骤" */
                    }
                </p>
                <div className="box-header-btns">
                    <Button
                        fieldid="ublinker-home-components-InitModal-InitDoc-index-2573154-Button"
                        colors="primary"
                        disabled={disabled || docGridLoading}
                        onClick={handleSave}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804DF", "保存自定义档案") /* "保存自定义档案" */}
                    </Button>
                    {!docGridLoading && errPushDocList.length > 0 ? (
                        <Button
                            fieldid="ublinker-home-components-InitModal-InitDoc-index-6748945-Button"
                            className="ucg-mar-l-5"
                            bordered
                            onClick={againPushDoc}
                        >
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804E2", "再次提交保存失败的档案") /* "再次提交保存失败的档案" */}
                        </Button>
                    ) : null}
                </div>
            </div>
            <Grid
                fieldid="ublinker-home-components-InitModal-InitDoc-index-5352618-Grid"
                columns={defDocGridColumns}
                data={defDocList}
                multiSelect
                selectedList={selectedDefDoc}
                getSelectedDataFunc={setSelectedDefDoc}
                autoCheckedByClickRows={false}
                scroll={{ y: 300 }}
                empty={" "}
                // loading={docGridLoading}
                loading={{ spinning: docGridLoading, tip: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804D9", "加载中...") /* "加载中..." */ }}
                rowKey="pkId"
            />

            {docGridLoading ? (
                <div className="ucg-mar-t-5">
                    <Progress
                        fieldid="ublinker-home-components-InitModal-InitDoc-index-2740871-Progress"
                        now={docPushProgress}
                        percent={docPushProgress}
                        size="sm"
                        max={docPushProgressMax}
                    />
                </div>
            ) : null}
        </div>
    );
};

export default InitDoc;
