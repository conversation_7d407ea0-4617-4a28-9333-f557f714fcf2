import React, { useState, useEffect } from "react";
import Modal from "components/TinperBee/Modal";
import { Button, FormList } from "components/TinperBee";
import { autoServiceMessage } from "utils/service";
import ConfigureConnectMessage from "../ConfigureConnectMessage";
import { getLinkerSetParam } from "../../service";
import { getAllVersionService } from "services/common";
import { createCommonLinkConfig, editCommonLinkConfig, commonLinkTest } from "../../service";
import { Success, Error } from "utils/feedback";
import Highlight from "components/Highlight";
import { getCompleteImg } from "utils";
import Logo from "components/Card/Logo";

import InitDoc, { useInitDoc } from "./InitDoc";
import InitTaskView, { useInitTask } from "./InitTask";
import U8InitTaskView from "./U8InitTask";
import "./index.less";

//所有的连接器都用同一套  自定义档案， 初始化任务u8的一套，其余的连接器一套

// 添加连接弹窗
const AddLinkerModal = (props) => {
    const [form] = FormList.useForm();
    const { show, allConnector, onCancel, onOk, ModalTitle, InitModalData } = props;
    const _useInitDoc = useInitDoc(
        {
            erpVersion: InitModalData.erpversion,
            instanceInfo: {
                //'nc65' ||
                gatewayId: InitModalData.gatewayId || "",
                configId: InitModalData.config ? InitModalData.config.id || InitModalData.config.configid : "",
                connectId: InitModalData.id || "", //'0uJWQP76Qyba_gtfy3CZsa' ||
                useMainTenantGateway: InitModalData.fromMainTenant || "",
            },
        },
        {}
    ); //_useSetConnector _useErpConfig.erpConfig  没什么用

    let _useInitTask;
    if (InitModalData.type !== "U8NativeSQL") {
        _useInitTask = useInitTask(
            {
                erpVersion: InitModalData.erpversion,
                instanceInfo: {
                    //'nc65' ||
                    gatewayId: InitModalData.gatewayId || "",
                    configId: InitModalData.config ? InitModalData.config.id || InitModalData.config.configid : "",
                    connectId: InitModalData.id || "", //'62b173ef68bf8023e894ec34'||
                    useMainTenantGateway: InitModalData.fromMainTenant || "",
                },
            },
            _useInitDoc,
            InitModalData.type,
            {}
        ); //_useErpConfig.erpConfig  没什么用  connectorType:nc
    }

    const [selectedIndex, setSelectedIndex] = useState();
    const [connectorCode, setConnectorCode] = useState("");
    const [connectorName, setConnectorName] = useState("");
    const [connectorId, setConnectorId] = useState("");
    const [connectorAdapterCode, setConnectorAdapterCode] = useState("");
    const [selectedItem, setSelectedItem] = useState({});
    const labelCol = 130;

    // 表单字段验证方法
    const { validateFields, getFieldProps, getFieldError } = form;

    // 取消
    const handleCancel = () => {
        onCancel("cancel");
    };
    useEffect(() => {}, []);
    // 确认
    const handleCommit = () => {};
    const handleClickItem = (index, selectedItem) => {};
    return (
        <div className="">
            <Modal
                fieldid="ublinker-routes-home-components-InitModal-index-3169495-Modal"
                className="init-modal"
                cancelHide
                okHide
                width={"98%"}
                title={
                    <div className="modal-header">
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418008C", "初始化") /* "初始化" */}
                        <a
                            fieldid="ublinker-routes-home-components-InitModal-index-682397-a"
                            href="/iuap-ipaas-dataintegration/ucf-wh/ublinker-fe/html/ubl-doc/index.html"
                            target="_blank"
                        >
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418008B", "帮助说明") /* "帮助说明" */}
                        </a>
                    </div>
                }
                show={show}
                onCancel={handleCancel}
                onOk={handleCommit}
            >
                <InitDoc _useInitDoc={_useInitDoc} />
                {InitModalData.type == "U8NativeSQL" ? (
                    <U8InitTaskView InitModalData={InitModalData} />
                ) : (
                    <InitTaskView _useInitTask={_useInitTask} labelCol={labelCol} />
                )}
            </Modal>
        </div>
    );
};

export default AddLinkerModal;
