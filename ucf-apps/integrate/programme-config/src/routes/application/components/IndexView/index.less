.iuapIpaasDataintegrationFe-integrate {
    .integrate-tabs {
        flex: 1;
        .wui-tabs-bar {
            margin-bottom: 0;
        }
        .wui-tabs-content {
            background-color: #fff;
            overflow: hidden;
        }
        .wui-tabs-tabpane {
            height: 100%;
        }
    }
    .integrate-programme {
        height: 100%;
        display: flex;
        flex-direction: column;
    }
    .integrate-flow-iframe {
        border: medium none;
        width: 100%;
        height: 100%;
    }

    .intergrate-empty {
        flex: 1;
        background-color: #f7f9fd;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .sys-connector-common {
        &-content {
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        &-view-wrap {
            background: #f7f9fd;
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
    }
    .view-wrap-card-container {
        padding: 0 20px;
        width: 100%;
        overflow: auto;
        height: auto;
    }

    .sys-connector-common-tab .wui-tabs-bar {
        padding: 10px 0;
    }
    .tabBarExtraContent {
        display: flex;
        align-items: center;
    }

    .common-config .header {
        width: 100%;
        height: 46px;
        background: #f7f9fd;
        // box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 17px;
        z-index: 1000;
    }
    .header-left,
    .header-right {
        display: flex;
        align-items: center;
    }
    .header-left-title {
        font-size: 16px;
        font-family: PingFang-SC-Heavy, PingFang-SC;
        font-weight: 800;
        color: #333333;
    }
    .header-right .mix-search-input.search-input-lg {
        width: 200px;
    }
    .header-right-icon {
        font-size: 18px;
        background: #fff;
        width: 28px;
        height: 28px;
        border-radius: 2px;
        padding-top: 2px !important;
        cursor: pointer;
        padding: 0 5px;
    }
    .header-right-icon.cur {
        background: #f2f3f3;
    }
    .data-task-run-status {
        display: inline-block;
        // min-width: 60px;
        height: 22px;
        font-size: 12px;
        line-height: 22px;
        text-align: center;
        // border: 1px solid;
        border-radius: 3px;
        padding: 0 10px;
    }

    .data-task-run-status.start {
        background: #ebf9f8;
        color: #00b39e;
    }
    .data-task-run-status.stop {
        background: #f8f8f8;

        color: #999999;
    }
    // .info-header-tag{
    //     display: inline-block;
    //     margin-left:12px;
    //                         width: 49px;
    //                         height: 22px;
    //                         text-align: center;
    //                         background: #EBF9F8;
    //                         border-radius: 3px;
    //                         border: 1px solid #00B39E;
    //                         font-size: 12px;
    //                         font-family: PingFang-SC-Medium, PingFang-SC;
    //                         font-weight: 500;
    //                         color: #00B39E;
    //                         line-height: 20px;
    // }
    .wui-tooltip-inner {
    }
    .header-tooltip-title {
        padding: 7px;
        font-size: 12px;
        font-family:
            PingFangSC-Regular,
            PingFang SC;
        font-weight: 400;
        color: #666666;
        line-height: 22px;
    }
    .wui-tooltip-inverse .wui-tooltip-inner {
    }
    .tooltip-title {
        font-size: 12px;
        font-family: PingFang-SC-Heavy, PingFang-SC;
        font-weight: 800;
        color: #333333;
        line-height: 17px;
        margin-bottom: 9px;
    }
}
.new-version-notice {
    .wui-message-notice-description-content {
        max-width: 90%;
        padding-top: 6px;
        padding-bottom: 6px;
        .notice-content {
            display: flex;
            flex-wrap: nowrap;
            align-items: center;
            color: "black";
        }
        .notice-content-text {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}
