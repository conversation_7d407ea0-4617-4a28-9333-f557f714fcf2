import React, { Component, createRef } from "react";
import { <PERSON>ltip, Button, FormControl, Empty } from "components/TinperBee";
import { Modal, Form, Input, Space, Badge, Message, Tabs } from "@tinper/next-ui";
import { codeReg } from "utils/regExp";
import { navTo, withIframeSize } from "decorator/index";
import withRouter from "decorator/withRouter";
import ConnectorList from "../ConnecterList";
import TablesList from "../TablesList/index";
import "./index.less";
import query from "query-string";
import PageIntroduce from "components/PageIntroduce";
import ApplicationModal from "../Modal/ApplicationModal";
import InitApplicationModal from "../Modal/InitApplicationModal";
import { getCertificateVerification } from "services/common";
import { getPageParams } from "decorator/index";

import responseStyles from "styles/response.modules.css";
import classNames from "classnames";
import MigrationBtn from "components/MigrationBtn";
const { TabPane } = Tabs;
const { MainApplication } = window.tnsSdk;

@withIframeSize
@getPageParams
@withRouter
class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            showGuide: true,
            showApplicationModal: false,
            showInitApplicationModal: false,
            showCreateModal: false,
            isFromCreateModal: false,
            showInitModal: false,

            ModalCode: "",
            ModalTitle: "",
            ConnectModalData: {},
            InitModalData: {},
            ModalTestable: false,
            ModalTemplates: "",
            ModalType: "", //edit  add
            createType: "",

            connectorId: "",
            connectorAdapterCode: "",
            defaultSelectedVersion: "",

            // 添加连接时传递给弹窗的数据
            addModalData: {},
            // 编辑连接时传递给弹窗的数据
            editModalData: {},
            curApplication: {},
            isYb: true, // ys的时候隐藏新增
            // showType: "card", //card table,
            oldApplicationId: "",
            activeKey: "1",
        };

        this.pullFlag = false;
    }
    copyForm = createRef();
    formItemLayout = {
        labelCol: { span: 8 },
        wrapperCol: { span: 14 },
    };
    handleNavNewVersion = () => {
        if (window.jDiwork?.openService) {
            window.jDiwork?.openService(
                "kflj_jjfa",
                {
                    routePath: "/applicationV2",
                    providerHost: "/iuap-ipaas-base/ucf-wh/designer-fe/",
                },
                {
                    serviceType: "tns",
                    providerName: "integrationBus",
                    providerEntry: "bootstrap",
                    code: "jcyy-new",
                    // title: lang.templateByUuid("UID:P_UBL-FE_1FDA00120598000F", "", undefined, {
                    //     returnStr: true,
                    // }) /* "集成流设计" */,
                    multiTitle: {
                        zh_CN: "集成流设计", //@notranslate
                        en_US: "Integration Process Design",
                        zh_TW: "集成流設計", //@notranslate
                    },
                }
            );
        }
    };
    async componentDidMount() {
        Message.destroy();
        // Message.create({
        //     getPopupContainer: document.getElementById("programme-config"),
        //     content: (
        //         <div className="notice-content">
        //             <span className="notice-content-text">
        //                 {
        //                     lang.templateByUuid(
        //                         "UID:P_UBL-FE_1FDA00120598000D",
        //                         "全新升级的集成设计现已上线!新增丰富模版、灵活编排和低代码脚本等强大功能，交互体验全面优化!"
        //                     ) /* "全新升级的集成设计现已上线!新增丰富模版、灵活编排和低代码脚本等强大功能，交互体验全面优化!" */
        //                 }
        //             </span>
        //             <Button type="text" onClick={() => this.handleNavNewVersion()}>
        //                 {lang.templateByUuid("UID:P_UBL-FE_1FDA00120598000E", "立即体验") /* "立即体验" */}
        //             </Button>
        //         </div>
        //     ),
        //     wrapperClassName: "new-version-notice",
        //     color: "info",
        //     duration: 0,
        //     style: { width: "100%", top: 0, left: 0, transform: "none", borderRadius: "none", borderColor: "transparent" },
        // });

        if (window?.jDiwork?.getContext) {
            window.jDiwork.getContext((data) => {
                data.productLine && this.setState({ isYb: data.productLine === "diwork" });
            });
        }

        // console.log(this.props.appContext.currRouteParam.router)//接收来自同步任务的参数
        let pageParam2 = this.props.getPageParams(this.props); //接收来自最后保存成功返回当前页的参数
        if (!pageParam2.queryParams.back) {
            if (this.props.appContext && this.props.appContext.currRouteParam.router) {
                this.props.navigate({
                    pathname: `/${this.props.appContext.currRouteParam.router}`,
                    search:
                        "?" +
                        query.stringify({
                            id: this.props.appContext.currRouteParam.id,
                            empty: 0,
                        }),
                    type: "edit",
                });
            }
        }
        let { ownerStore, ownerState } = this.props;
        ownerStore.getListBaseLinker();
        ownerStore.getDataSource();
        ownerStore.createDefaultConnect();
    }
    componentWillUnmount() {}
    handleChangeSearchInfo = (searchVal) => {
        this.props.ownerStore.changeState({
            searchInfo: { ...this.props.ownerState.searchInfo, searchVal },
        });
    };
    handleSearch = (value) => {
        this.handleChangeSearchInfo(value);
        this.props.ownerStore.getDataSource({
            pageNo: 1,
        });
    };

    handleDelete = (data) => {
        let { ownerStore } = this.props;
        Modal.confirm({
            fieldid: "************",
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180050", "确认要删除吗？") /* "确认要删除吗？" */,
            content: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180051", "请慎重考虑，此操作不可逆！") /* "请慎重考虑，此操作不可逆！" */,
            onOk: ownerStore.DeleteById.bind(null, { id: data.id }),
        });
    };

    handleChangeView = (showType) => {
        this.props.ownerStore.setShowType(showType);
        return;
        this.setState({
            showType,
        });
    };

    handleNewApplication = async (type, info, createType = "new") => {
        if (type == "add" && createType == "new") {
            //新建的时候加限制
            let res = await getCertificateVerification();
            if (!res) {
                return;
            }
        }
        this.setState(
            {
                showApplicationModal: !this.state.showApplicationModal,
                ModalType: type,
                curApplication: info,
                createType,
            },
            () => {
                if (createType == "init") {
                    //初始化框点下一步到这，关掉初始化框
                    this.handleInitApplication();
                }
            }
        );
        // this.props.ownerStore.changeState({ applicationInfo: {} }); //新建或更新完要清空
    };
    handleInitApplication = async (type, info, createType = "new") => {
        this.setState({
            showInitApplicationModal: !this.state.showInitApplicationModal,
        });
    };

    handleExportApply = (info) => {
        this.props.ownerStore.ExportApply({ applicationCode: info.code }); //新建或更新完要清空
    };
    handleCopyApply = (data) => {
        this.setState({ oldApplicationId: data });
        // return ; //新建或更新完要清空
    };
    handleCopyCancel = () => {
        this.setState({ oldApplicationId: "" });
        this.form.current.resetFields(); // 清空表单
    };
    handleCopyOk = async () => {
        const data = await this.copyForm.current.validateFields();
        const res = await this.props.ownerStore.CopyApply({ ...data, oldApplicationId: this.state.oldApplicationId });
        if (res) {
            this.handleCopyCancel();
        }
    };
    handleGoScheme = (applicationCode, applicationInfo) => {
        this.props.ownerStore.changeApplicationInfo(applicationInfo);
        this.props.navigate({
            pathname: `/applicationList`,
            search:
                "?" +
                query.stringify({
                    applicationCode,
                }),
        });
    };
    handleCardScroll = async (e) => {
        const scrollEl = e.target;
        let {
            ownerState: { pagination },
            cardList,
        } = this.props;
        if (scrollEl.scrollTop + scrollEl.offsetHeight >= scrollEl.scrollHeight - 1) {
            if (!this.pullFlag && cardList.length < pagination.total) {
                this.pullFlag = true;
                await pagination?.onPageChange({ pageNo: pagination.activePage + 1 });
                this.pullFlag = false;
            }
        }
    };

    list = [];
    cardWrapClass = classNames(responseStyles.columns, "view-wrap-card-container");

    showMigration = () => {
        return (
            window.name === "_WORKBENCH_" &&
            window.cb?.context?.getCommonOptionValue("enableMigration") &&
            "compare" === window.jDiwork?.diworkContext()?.migrationVersion
        );
    };
    handleChangeTab = (activeKey) => {
        this.setState({
            activeKey,
        });
    };
    bodyRef = React.createRef();
    render() {
        let { curApplication, showApplicationModal, showInitApplicationModal, ModalType, connectorId, createType, oldApplicationId } = this.state;

        let { ownerState, ownerStore, cardList } = this.props;
        let { dataSource, loaded, testConnectSuccess, listBaseLinker, freezeImportBtn, pagination } = ownerState;
        let { showType } = ownerStore;

        let completeModalTitle =
            ModalType == "add"
                ? createType == "new"
                    ? lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180543", "新增") /* "新增" */
                    : createType == "import"
                      ? lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180054", "导入应用") /* "导入应用" */
                      : lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180052", "初始化应用") /* "初始化应用" */
                : lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180053", "编辑应用"); /* "编辑应用" */
        const renderContent = () => {
            if (ownerStore.loading) {
                return null;
            }
            return dataSource.list.length === 0 ? (
                <div style={{ paddingTop: "10%" }}>
                    <Empty fieldid="UCG-FE-routes-application-components-IndexView-index-5594734-Empty" />
                </div>
            ) : (
                <>
                    {showType == "card" ? (
                        <div className="sys-connector-common-view-wrap">
                            <div className={this.cardWrapClass} onScroll={this.handleCardScroll}>
                                <ConnectorList
                                    isYb={this.state.isYb}
                                    loaded={loaded}
                                    dataSource={cardList}
                                    onDelete={this.handleDelete}
                                    ownerState={ownerState}
                                    ownerStore={ownerStore}
                                    testConnectSuccess={testConnectSuccess}
                                    handleGoScheme={this.handleGoScheme}
                                    handleNewApplication={this.handleNewApplication}
                                    handleExportApply={this.handleExportApply}
                                    onCopyApply={this.handleCopyApply}
                                />
                            </div>
                        </div>
                    ) : (
                        <TablesList
                            dataSource={dataSource.list}
                            onDelete={this.handleDelete}
                            testConnectSuccess={testConnectSuccess}
                            ownerState={ownerState}
                            ownerStore={ownerStore}
                            handleGoScheme={this.handleGoScheme}
                            handleNewApplication={this.handleNewApplication}
                            handleExportApply={this.handleExportApply}
                            handleCopyApply={this.handleCopyApply}
                            scroll={{ y: this.props.iframeSize.height - 264 }}
                        />
                    )}
                </>
            );
        };

        return (
            <div className="sys-connector-common-content common-config" style={{ height: "100%" }} id="programme-config" ref={this.bodyRef}>
                <PageIntroduce
                    code="programmeConfig"
                    title={lang.templateByUuid("UID:P_UBL-FE_200CD9B80428000E", "集成设计") /* "集成设计" */}
                    descList={[
                        lang.templateByUuid(
                            "UID:P_UBL-FE_200CD9B80428000B",
                            "集成设计提供集成场景的设计、开发、调试、发布、监控一体化能力。" //@notranslate
                        ) /* "集成设计提供集成场景的设计、开发、调试、发布、监控一体化能力。" */,
                        lang.templateByUuid(
                            "UID:P_UBL-FE_200CD9B80428000C",
                            "集成方案：提供两个系统之间的快速数据集成" //@notranslate
                        ) /* "集成方案：提供两个系统之间的快速数据集成" */,
                        lang.templateByUuid(
                            "UID:P_UBL-FE_200CD9B80428000D",
                            "集成流：支持多系统间复杂集成场景，提供丰富的模板、灵活的编排和强大的客开能力" //@notranslate
                        ) /* "集成流：支持多系统间复杂集成场景，提供丰富的模板、灵活的编排和强大的客开能力" */,
                    ]}
                    linkList={[]}
                />
                <Tabs onChange={this.handleChangeTab} className="integrate-tabs" defaultActiveKey="1">
                    <TabPane tab={lang.templateByUuid("UID:P_UBL-FE_200CD9B80428000F", "集成方案") /* "集成方案" */} key="1">
                        <div className="integrate-programme">
                            <div className="header">
                                <div className="header-left">
                                    <div className="header-left-title">
                                        {lang.templateByUuid("UID:P_UBL-FE_1C007A8E05A8000D", "全部（") /* "全部（" */}
                                        {pagination?.total || "0"}）
                                    </div>
                                </div>
                                <div className="header-right">
                                    <FormControl
                                        fieldid="ublinker-routes-home-components-IndexView-index-************-FormControl"
                                        className="ucg-mar-r-10 "
                                        style={{ width: "300px", boxSizing: "border-box" }}
                                        onSearch={(value) => {
                                            // if (/[\/\\.\{}\[\]\s]/.test(value)) {
                                            //     Error(
                                            //         lang.templateByUuid(
                                            //             "UID:P_UBL-FE_1AFE6A0C04280007",
                                            //             "搜索内容不能包含特殊字符：/,\\ , .或空格" //@notranslate
                                            //         ) /* "搜索内容不能包含特殊字符：/,\\ , .或空格" */
                                            //     );
                                            //     return;
                                            // }
                                            this.handleSearch(value);
                                        }}
                                        onChange={this.handleChangeSearchInfo}
                                        value={this.props.ownerState?.searchInfo?.searchVal}
                                        placeholder={lang.templateByUuid("UID:P_UBL-FE_1C27C2C80438000D", "请输入编码/名称", undefined, {
                                            returnStr: true,
                                        })}
                                        showClose
                                        type="search"
                                    />
                                    <Space size={8}>
                                        <i
                                            fieldid="ublinker-routes-home-components-IndexView-index-4700791-i"
                                            className={`ipaas iPS-kapian header-right-icon ${showType == "card" ? "cur" : ""}`}
                                            onClick={this.handleChangeView.bind(null, "card")}
                                        ></i>
                                        <i
                                            fieldid="ublinker-routes-home-components-IndexView-index-7078826-i"
                                            className={`ipaas iPS-liebiao header-right-icon ${showType == "table" ? "cur" : ""}`}
                                            onClick={this.handleChangeView.bind(null, "table")}
                                        ></i>
                                        {this.state.isYb ? ( // diwork是yb，ys不显示新增
                                            <Button
                                                fieldid="ublinker-routes-home-components-IndexView-index-58708421-Button"
                                                // className="ucg-mar-l-10 "
                                                onClick={this.handleNewApplication.bind(null, "add", null, "new")}
                                            >
                                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180543", "新增") /* "新增" */}
                                            </Button>
                                        ) : null}

                                        {freezeImportBtn ? (
                                            <Tooltip
                                                fieldid="UCG-FE-routes-application-components-IndexView-index-9813696-Tooltip"
                                                overlayClassName="import-tip"
                                                placement="bottomRight"
                                                overlayInnerStyle={{ minWidth: "140px" }}
                                                overlay={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180056", "30秒后可继续导入") /* "30秒后可继续导入" */}
                                            >
                                                <Button
                                                    fieldid="ublinker-routes-home-components-IndexView-index-58708422-Button"
                                                    // className="ucg-mar-l-10 "
                                                    disabled={freezeImportBtn}
                                                    onClick={this.handleNewApplication.bind(null, "add", null, "import")}
                                                >
                                                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180057", "导入") /* "导入" */}
                                                </Button>
                                            </Tooltip>
                                        ) : (
                                            <Button
                                                fieldid="ublinker-routes-home-components-IndexView-index-58708422-Button"
                                                // className="ucg-mar-l-10 "
                                                disabled={freezeImportBtn}
                                                onClick={this.handleNewApplication.bind(null, "add", null, "import")}
                                            >
                                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180057", "导入") /* "导入" */}
                                            </Button>
                                        )}
                                        <Button
                                            fieldid="ublinker-routes-home-components-IndexView-index-58708423-Button"
                                            // className="ucg-mar-l-10 "
                                            onClick={this.handleInitApplication}
                                            colors="primary"
                                        >
                                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180059", "初始化") /* "初始化" */}
                                        </Button>
                                        {this.showMigration() ? (
                                            <MigrationBtn
                                                serviceCode="kflj_jjfa"
                                                popupContainer={this.bodyRef.current}
                                                buttonRender={({ count }) => {
                                                    return (
                                                        <Badge count={count} overflowCount={999} title={count}>
                                                            <Button colors="default" bordered>
                                                                {lang.templateByUuid("UID:P_UBL-FE_1FDA001205980010", "查看迁移清单") /* "查看迁移清单" */}
                                                            </Button>
                                                        </Badge>
                                                    );
                                                }}
                                            />
                                        ) : null}
                                    </Space>
                                </div>
                            </div>
                            {renderContent()}
                        </div>
                    </TabPane>
                    <TabPane tab={lang.templateByUuid("UID:P_UBL-FE_200CD9B804280010", "集成流") /* "集成流" */} key="2">
                        {window?.name === "_WORKBENCH_" ? (
                            <MainApplication
                                providerHost="/iuap-ipaas-base/ucf-wh/designer-fe/" //小应用提供方-host路径，可以为绝对地址或相对地址
                                providerName="integrationBus" //小应用提供方-服务名称
                                providerEntry="bootstrap" //小应用提供方-入口名
                                routePath="/applicationV2" //小应用提供方-初始化路由路径
                                routeParam={{ origin: "ublinker" }} //小应用提供方-初始化路由参数
                            />
                        ) : (
                            <iframe
                                className="integrate-flow-iframe"
                                src={`http://localhost:8080/iuap-ipaas-base/ucf-wh/designer-fe/index.html#/applicationV2?origin=ublinker`}
                            />
                        )}
                    </TabPane>
                </Tabs>

                {showApplicationModal ? (
                    <ApplicationModal
                        completeModalTitle={completeModalTitle}
                        show={showApplicationModal}
                        info={curApplication}
                        listBaseLinker={listBaseLinker}
                        ModalType={ModalType}
                        createType={createType}
                        onCancel={this.handleNewApplication}
                        ownerStore={ownerStore}
                        ownerState={ownerState}
                        connectorId={connectorId}
                    />
                ) : null}
                {showInitApplicationModal ? (
                    <InitApplicationModal
                        completeModalTitle={completeModalTitle}
                        show={showInitApplicationModal}
                        ModalType={ModalType}
                        createType={createType}
                        onCancel={this.handleInitApplication}
                        onOk={this.handleNewApplication}
                        ownerStore={ownerStore}
                        ownerState={ownerState}
                    />
                ) : null}
                {oldApplicationId ? (
                    <Modal
                        fieldid="2f301091-f0d5-48b2-a3d1-26db4d749a67"
                        title={lang.templateByUuid("UID:P_UBL-FE_1D6129940508000E", "复制当前应用需要重命名") /* "复制当前应用需要重命名" */}
                        visible={oldApplicationId}
                        onCancel={this.handleCopyCancel}
                        onOk={this.handleCopyOk}
                        forceRender
                    >
                        <Form fieldid="5859906a-e595-4b3f-8f1d-49e7dca2e463" ref={this.copyForm} {...this.formItemLayout}>
                            <Form.Item
                                label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180368", "应用名称")}
                                name="newApplicationName"
                                rules={[
                                    {
                                        required: true,
                                        message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803CB", "请输入"),
                                    },
                                    {
                                        pattern: /^[^\\.,;:、!@$&^%?*+()={}[\]|\\]+$/,
                                        message: lang.templateByUuid("UID:P_UBL-FE_1D964BDC05E00010", "名称不能包含特殊字符") /* "名称不能包含特殊字符" */,
                                    },
                                ]}
                            >
                                <Input fieldid="9e47c099-f285-42f0-8126-d9105025365f" maxLength="128" />
                            </Form.Item>
                            <Form.Item
                                label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180373", "应用编码")}
                                required
                                name="newApplicationCode"
                                rules={[
                                    {
                                        required: true,
                                        message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803CB", "请输入"),
                                    },
                                    {
                                        pattern: codeReg,
                                        message: lang.templateByUuid(
                                            "UID:P_UBL-FE_18D8CEF604180363",
                                            "应用编码由英文、数字、下划线组成" //@notranslate
                                        ),
                                    },
                                ]}
                            >
                                <Input fieldid="00634bd1-ba6d-4668-a235-9fc57d963ada" maxLength="128" />
                            </Form.Item>
                            <Form.Item
                                label={lang.templateByUuid("UID:P_UBL-FE_1D964BDC05E00012", "方案名称后缀") /* "方案名称后缀" */}
                                required
                                name="schemeNameSuffix"
                                rules={[
                                    {
                                        required: true,
                                        message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803CB", "请输入"),
                                    },
                                    {
                                        pattern: /^[^\\.,;:、!@$&^%?*+()={}[\]|\\]+$/,
                                        message: lang.templateByUuid("UID:P_UBL-FE_1D964BDC05E00010", "名称不能包含特殊字符") /* "名称不能包含特殊字符" */,
                                    },
                                ]}
                            >
                                <Input
                                    fieldid="7c6ac759-d66d-45da-a534-6d87f9bd2c92"
                                    placeholder={lang.templateByUuid(
                                        "UID:P_UBL-FE_1D6129940508000F",
                                        "复制后所有方案均需要重命名，支持直接增加后缀", //@notranslate
                                        undefined,
                                        {
                                            returnStr: true,
                                        }
                                    )}
                                    maxLength="128"
                                />
                            </Form.Item>
                            <Form.Item
                                label={lang.templateByUuid("UID:P_UBL-FE_1D964BDC05E00011", "方案编码后缀") /* "方案编码后缀" */}
                                name="schemeCodeSuffix"
                                rules={[
                                    {
                                        required: true,
                                        message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803CB", "请输入"),
                                    },
                                    {
                                        pattern: codeReg,
                                        message: lang.templateByUuid(
                                            "UID:P_UBL-FE_18D8CEF604180363",
                                            "应用编码由英文、数字、下划线组成" //@notranslate
                                        ),
                                    },
                                ]}
                            >
                                <Input
                                    fieldid="f389bc6d-f3a5-45e4-96cf-8f6f1391211e"
                                    placeholder={lang.templateByUuid(
                                        "UID:P_UBL-FE_1D6129940508000F",
                                        "复制后所有方案均需要重命名，支持直接增加后缀", //@notranslate
                                        undefined,
                                        {
                                            returnStr: true,
                                        }
                                    )}
                                    maxLength="128"
                                />
                            </Form.Item>
                        </Form>
                    </Modal>
                ) : null}
            </div>
        );
    }
}

export default IndexView;
