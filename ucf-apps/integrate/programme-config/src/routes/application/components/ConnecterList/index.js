import React, { useState } from "react";
import { Button, Form } from "@tinper/next-ui";

import "./index.less";
import Connector<PERSON>ogo from "components/ConnectorLogo";
import ArrowSvg from "../ArrowSvg";
import CreateCard from "components/CreateCard";

const ConnectorList = (props) => {
    let { dataSource, handleNewApplication, handleExportApply, onDelete, testConnectSuccess } = props;
    const handleEditConnect = (selfItem) => {
        handleNewApplication("edit", selfItem, "new");
    };
    const handleExportApply1 = (selfItem) => {
        handleExportApply(selfItem);
    };

    return (
        <>
            {props.isYb ? (
                <CreateCard
                    fieldid="d9830d4a-1c08-4266-8566-2d674ae9e661"
                    text={lang.templateByUuid("UID:P_UBL-FE_1D6129940508000D", "新增") /* "新增" */}
                    classProp="card-width sys-connector-common-item-content"
                    onClick={() => {
                        handleNewApplication("add", "", "new");
                    }}
                />
            ) : null}

            {dataSource.map((item) => {
                let { id } = item;

                return (
                    <div key={id} className={`sys-connector-common-item-content`}>
                        <div
                            className="item-content"
                            onClick={() => {
                                props.handleGoScheme(item.code, item);
                            }}
                        >
                            <div className="item-header">
                                <ConnectorLogo
                                    className="item-header-icon"
                                    iconfontsize={{ width: "52px", height: "52px" }}
                                    logo={item.logo1}
                                    title={item.connectTypeName}
                                    code={item.connectTypeCode}
                                />
                                <ArrowSvg isDouble={item.schemesDirection !== "single"} className="logo2-img" />
                                <ConnectorLogo
                                    className="item-header-icon"
                                    iconfontsize={{ width: "52px", height: "52px" }}
                                    logo={item.logo2}
                                    title={item.connectTypeNameTwo}
                                    code={item.connectTypeCodeTwo}
                                />
                            </div>
                            <div className="item-title" title={item.name}>
                                {item.name}
                            </div>
                            <div className="item-desc" title={item.code}>
                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180015", "编码") /* "编码" */}：{item.code}
                            </div>
                            <div className="item-desc" title={item.ccreator}>
                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180016", "创建人") /* "创建人" */}：{item.ccreator}
                            </div>
                            <div className="item-desc" title={item.ccreationtime}>
                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180018", "创建时间") /* "创建时间" */}：{item.ccreationtime}
                            </div>
                            <div className="item-desc" title={item.cmodifiedtime || ""}>
                                {lang.templateByUuid("UID:P_UBL-FE_1BC6805604C0000C", "修改时间：") /* "修改时间：" */}
                                {item.cmodifiedtime || ""}
                            </div>
                        </div>
                        <div className="item-footer">
                            <div className="item-footer-item">
                                <i fieldid="ublinker-routes-home-components-ConnecterList-index-6697967-i" className={`ipaas iPS-edit-public-line`}></i>
                                <Button
                                    fieldid="ublinker-routes-home-components-ConnecterList-index-71304-Button"
                                    type="text"
                                    onClick={handleEditConnect.bind(null, item)}
                                    bordered
                                >
                                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180012", "编辑") /* "编辑" */}
                                </Button>
                            </div>
                            <div className="item-footer-item">
                                <i fieldid="ublinker-routes-home-components-ConnecterList-index-66979671-i" className={`ipaas iPS-copy-public-line`}></i>
                                <Button
                                    fieldid="ublinker-routes-home-components-ConnecterList-index-71304-Button"
                                    type="text"
                                    onClick={() => props.onCopyApply(item?.id)}
                                    bordered
                                >
                                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180014", "复制") /* "复制" */}
                                </Button>
                            </div>
                            {
                                <div className={`item-footer-item  ${!testConnectSuccess ? "init-enabled" : ""}`}>
                                    <i fieldid="ublinker-routes-home-components-ConnecterList-index-8196794-i" className={`ipaas iPS-initial`}></i>
                                    <Button
                                        fieldid="ublinker-routes-home-components-ConnecterList-index-4793309-Button"
                                        type="text"
                                        onClick={handleExportApply1.bind(null, item)}
                                        bordered
                                    >
                                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180017", "导出") /* "导出" */}
                                    </Button>
                                </div>
                            }
                            {
                                //连接级默认和租户级默认都不让删
                                // (!item.linkerLevelDefault && !item.isDefault) &&
                                !item.isDefault && (
                                    <div className="item-footer-item">
                                        <i
                                            fieldid="ublinker-routes-home-components-ConnecterList-index-1540696-i"
                                            className={`ipaas iPS-delete-public-line`}
                                        ></i>
                                        <Button
                                            fieldid="ublinker-routes-home-components-ConnecterList-index-2180484-Button"
                                            type="text"
                                            onClick={onDelete.bind(null, item)}
                                            bordered
                                        >
                                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180013", "删除") /* "删除" */}
                                        </Button>
                                    </div>
                                )
                            }
                        </div>
                    </div>
                );
            })}
        </>
    );
};

export default ConnectorList;
