@import "~styles/textOverflow.less";

// .card-width() {
//     @card-width-small: 272px;
//     @card-width-large: 307px;
//     width: @card-width-small;

//     @media (min-width: 1440px) {
//         width: @card-width-large;
//     }
// }
// .card-width {
//     .card-width();
// }
.sys-connector-common {
    &-list {
        // display: flex;
        // // flex-direction: row;
        // justify-content: space-between;
        // flex-wrap: wrap;
        // width: 100%;
        // height: auto;
        // transition: 2s;
        // margin-bottom: 55px;
    }

    &-item {
        &-content {
            background-color: #fff;
            // clip-path: polygon(0% 0%, 45% 1px, 51% 18%, 100% 17%, 100% 100%, 0 100%);
            box-shadow: 0px 0px 16px 0px rgba(173, 180, 188, 0.2);
            border-radius: 9px;
            border: 1px solid rgb(222 228 237);
            position: relative;
            display: flex;
            flex-direction: column;
            height: 318px;

            .item-content {
                padding: 30px 19px;
                cursor: pointer;
                .item-number {
                    display: inline-block;
                    background: #eceff3;
                    border-radius: 9px;
                    padding: 2px 12px;
                    margin-bottom: 17px;
                    font-size: 12px;
                    font-family:
                        PingFangSC-Regular,
                        PingFang SC;
                    font-weight: 400;
                    color: #333333;
                }

                .item-title {
                    font-size: 16px;
                    font-family: PingFang-SC-Heavy, PingFang-SC;
                    font-weight: 800;
                    color: #000000;
                    height: 44px;
                    line-height: 22px;
                    margin-bottom: 4px;
                    word-break: break-all;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 2;
                    overflow: hidden;
                }

                .item-desc {
                    font-size: 12px;
                    font-family:
                        PingFangSC-Regular,
                        PingFang SC;
                    font-weight: 400;
                    color: #666666;
                    line-height: 20px;
                    word-break: break-all;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 1;
                    overflow: hidden;
                }

                // 已配置连接重新布局
                .item-header {
                    width: 135px;
                    height: 72px;
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    box-sizing: border-box;
                    justify-content: space-between;
                    margin-bottom: 10px;

                    .logo2-img {
                        width: 18px;
                        height: 18px;
                    }

                    &-icon {
                        width: 52px;
                        height: 52px;
                        background: #ffffff;
                        border-radius: 50%;
                        flex-shrink: 0;
                        background-size: 100% 100%;
                    }
                }
            }

            .item-footer {
                border-top: 1px solid #e8e9eb;
                height: 46px;
                // line-height: 35px;
                display: flex;
                align-items: center;
                justify-content: space-around;
                font-size: 12px;
                font-family:
                    PingFangSC-Regular,
                    PingFang SC;
                font-weight: 400;
                color: #4a8bf0;

                .item-footer-item {
                    display: flex;
                    cursor: pointer;
                    align-items: center;

                    .wui-button {
                        padding: 0;
                        min-width: 0px;
                        margin-left: 3px;
                    }

                    i {
                        color: #03c;
                    }
                }
            }
        }
        &-content:hover {
            box-shadow: 0px 0px 16px 0px rgba(173, 180, 188, 0.8);
        }
        &-content.add {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            padding-top: 0;
            border: 1px dashed #505766;
            cursor: pointer;
            min-height: 270px;
            .add-icon {
                width: 32px;
                height: 32px;
                background-size: 100% 100%;
                background-image: url(~static/images/gateway/plus.png);
            }

            .create-gate-way-button {
                font-size: 14px;
                font-family:
                    PingFangSC-Regular,
                    PingFang SC;
                font-weight: 400;
                color: #111111;
            }
        }
    }
}
