import { createEnum } from "constants/utils";

export const paramTypes = [
    {
        code: "Text",
        name: lang.templateByUuid("UID:P_UBL-FE_20096040042802B2", "文本") /* "文本" */,
    },
    {
        code: "Textarea",
        name: lang.templateByUuid("UID:P_UBL-FE_20096040042802B4", "多行文本") /* "多行文本" */,
    },
    {
        code: "Integer",
        name: lang.templateByUuid("UID:P_UBL-FE_20096040042802B6", "整数数值") /* "整数数值" */,
    },
    {
        code: "Select",
        name: lang.templateByUuid("UID:P_UBL-FE_20096040042802B1", "下拉选择") /* "下拉选择" */,
        hasItems: true,
    },
    {
        code: "Radio",
        name: lang.templateByUuid("UID:P_UBL-FE_20096040042802B3", "单选") /* "单选" */,
        hasItems: true,
    },
    {
        code: "Checkbox",
        name: lang.templateByUuid("UID:P_UBL-FE_20096040042802B5", "多选") /* "多选" */,
        hasItems: true,
    },
    {
        code: "Group",
        name: lang.templateByUuid("UID:P_UBL-FE_20096040042802B0", "文本框组") /* "文本框组" */,
        hasItems: true,
    },
    {
        code: "Password",
        name: lang.templateByUuid("UID:P_UBL-FE_20096040042802B0", "文本框组") /* "文本框组" */,
        hasItems: true,
    },
];

export const paramTypesEnum = createEnum(paramTypes);
