import { getLocalImg, getCompleteImg } from "utils/index";

const common_path = "connector/business/";

const logos = [
    {
        title: "NC",
        src: getPath("NC.svg"),
    },
    {
        title: "NCC",
        src: getPath("NCC.svg"),
    },
    {
        title: "Oracle",
        src: getPath("Oracle_EBS.svg"),
    },
    {
        title: "U8",
        src: getPath("U8.svg"),
    },
    {
        title: "U8cloud",
        // src: getPath('U8C.svg')
        src: getPath("u8c.png"),
    },
    {
        title: "YonBIP",
        src: getPath("YonBIP.svg"),
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_20096040042802FA", "三力士") /* "三力士" */,
        src: getPath("sanlisi.svg"),
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_20096040042802FD", "京东") /* "京东" */,
        src: getPath("jd.svg"),
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_2009604004280300", "办公伙伴") /* "办公伙伴" */,
        src: getPath("officemeta.svg"),
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_2009604004280303", "华为云") /* "华为云" */,
        src: getPath("huaweiCloud.svg"),
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_2009604004280306", "史泰博") /* "史泰博" */,
        src: getPath("staples.svg"),
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_2009604004280309", "固安捷") /* "固安捷" */,
        src: getPath("grainger.svg"),
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_200960400428030B", "广博") /* "广博" */,
        src: getPath("guangbo.svg"),
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_200960400428030D", "得力") /* "得力" */,
        src: getPath("deli.svg"),
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_20096040042802FB", "必联") /* "必联" */,
        src: getPath("LB.svg"),
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_20096040042802FE", "慧聪") /* "慧聪" */,
        src: getPath("hc360.svg"),
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_2009604004280301", "晨光科力普") /* "晨光科力普" */,
        src: getPath("colipu.svg"),
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_2009604004280304", "浙江物产") /* "浙江物产" */,
        src: getPath("zjmi.svg"),
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_2009604004280307", "米思米") /* "米思米" */,
        src: getPath("misumi.svg"),
    },
    {
        title: "SAP",
        src: getPath("SAP.svg"),
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_200960400428030C", "腾讯云") /* "腾讯云" */,
        src: getPath("qcloud.svg"),
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_200960400428030E", "苏宁") /* "苏宁" */,
        src: getPath("suning.svg"),
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_20096040042802FC", "西域") /* "西域" */,
        src: getPath("ehsy.svg"),
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_20096040042802FF", "金蝶") /* "金蝶" */,
        src: getPath("kingdee.svg"),
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_2009604004280302", "阿里云") /* "阿里云" */,
        src: getPath("aliyun.svg"),
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_2009604004280305", "震坤行") /* "震坤行" */,
        src: getPath("zkh.svg"),
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_2009604004280308", "领先未来") /* "领先未来" */,
        src: getPath("leading.svg"),
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_200960400428030A", "齐心") /* "齐心" */,
        src: getPath("comix.svg"),
    },
    {
        title: "md",
        src: getPath("md.svg"),
    },
    {
        title: "ncsql",
        src: getPath("ncsql.svg"),
    },
    {
        title: "yonbip-major",
        src: getPath("yonbip-major.svg"),
    },
    {
        title: "yonbip-high",
        src: getPath("yonbip-high.svg"),
    },
    {
        title: "hrps",
        src: getPath("hospital-logo.jpg"),
    },
    {
        title: "u9c",
        src: getPath("u9cloud-.png"),
    },
];

function getPath(logoName) {
    return getCompleteImg(common_path + logoName);
}

export default logos;
