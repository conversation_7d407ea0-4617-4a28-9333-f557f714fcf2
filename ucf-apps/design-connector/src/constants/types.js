import { createEnum } from "constants/utils";

export const types = [
    {
        code: 0,
        name: lang.templateByUuid("UID:P_UBL-FE_20096040042803E7", "业务连接器") /* "业务连接器" */,
        tab: "business",
    },
    {
        code: 1,
        name: lang.templateByUuid("UID:P_UBL-FE_20096040042803E8", "技术连接器") /* "技术连接器" */,
        tab: "technology",
    },
];

export const connectorTypesEnum = createEnum(types);
