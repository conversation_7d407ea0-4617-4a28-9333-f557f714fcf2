import { getLocalImg, getCompleteImg } from "utils/index";

const common_path = "connector/technology/";

const logos = [
    {
        title: "AMQP",
        src: getPath("AMQP.svg"),
    },
    {
        title: "Apache Kafka",
        src: getPath("Apache-Kafka.svg"),
    },
    {
        title: "Database",
        src: getPath("Database.svg"),
    },
    {
        title: "Email",
        src: getPath("Email.svg"),
    },
    {
        title: "FTP",
        src: getPath("FTP.svg"),
    },
    {
        title: "HTTP",
        src: getPath("HTTP.svg"),
    },
    {
        title: "IBM MQ",
        src: getPath("IBM-MQ.svg"),
    },
    {
        title: "MongoDB",
        src: getPath("MongoDB.svg"),
    },
    {
        title: "Redis",
        src: getPath("Redis.svg"),
    },
    {
        title: "scheduler",
        src: getPath("scheduler.png"),
    },
    {
        title: "db2",
        src: getPath("db2.svg"),
    },
    {
        title: "jdbc",
        src: getPath("jdbc.svg"),
    },
    {
        title: "mariadb",
        src: getPath("mariadb.png"),
    },
    {
        title: "mongo",
        src: getPath("mongo.png"),
    },
    {
        title: "mysql",
        src: getPath("mysql.png"),
    },
    {
        title: "postgre",
        src: getPath("postgre.png"),
    },
    {
        title: "sqlite",
        src: getPath("sqlite.png"),
    },
    {
        title: "sqlserver",
        src: getPath("sqlserver.png"),
    },
];

function getPath(logoName) {
    return getCompleteImg(common_path + logoName);
}

export default logos;
