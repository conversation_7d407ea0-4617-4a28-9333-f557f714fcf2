const storageKey = "currentConnector";

/**
 * 将将要配置版本 模板或者实体的连接器信息存入localStorage
 * 用于在对应页面使用
 * */
export function setConfigCntToStorage(data) {
    localStorage.setItem(storageKey, JSON.stringify(data));
}

/** 获取连接器信息 */
export function getConfigCntFormStorage() {
    let cntDataJson = localStorage.getItem(storageKey);
    return JSON.parse(cntDataJson);
}

export function clearConfigCntFormStorage() {
    localStorage.removeItem(storageKey);
}
