/**
 * 入口、导入组件样式、渲染
 */

import React from "react";
import { HashRouter } from "react-router";
import { render, Provider } from "core";
import Routes from "./routes";
import DesignHeader from "components/DesignHeader";
import "./app.less";

const App = () => {
    return (
        <Provider router={HashRouter}>
            <Routes />
        </Provider>
    );
};

render(App, undefined, "ManagementSide");
