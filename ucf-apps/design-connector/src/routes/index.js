import React from "react";
import { RoutesRender } from "core";

import ListRoute from "./list";
import TemplateRoute from "./templateList";
import TemplateConfigRoure from "./templateConfig";
import VersionsRoute from "./versions";
import EntityRoute from "./entityList";
import EntityConfigRoute from "./entityConfig";

import getConfigProvider from "components/ConfigProvider";
import config from "../config";
const ConfigProvider = getConfigProvider(config);

const routes = [ListRoute, TemplateRoute, TemplateConfigRoure, VersionsRoute, EntityRoute, EntityConfigRoute];

const Routes = () => {
    return (
        <ConfigProvider fieldid="UCG-FE-ucf-apps-design-connector-src-routes-index-9622968-ConfigProvider">
            <RoutesRender routes={routes} />
        </ConfigProvider>
    );
};
export default Routes;
