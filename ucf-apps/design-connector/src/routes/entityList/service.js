import { getInvokeService } from "utils/service";

/***
 * 获取连机器模板列表
 * @param {Object} data
 * @param {Number} data.pageIndex
 * @param {Number} data.pageSize
 * @param {entitiesRequestDTO} data.requestDTO
 * @return {*}
 */
export const getDesignCntEntityListService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/designer/entity/listEntityByPage",
        },
        data
    );
};

/***
 * 删除连接器模板
 * @param {Object} data
 * @param {String} data.id -模板ID
 * @return {*}
 */
export const deleteDesignCntEntityService = function (data) {
    return getInvokeService(
        {
            method: "GET",
            path: "/designer/entity/deleteEntityById",
        },
        data
    );
};
