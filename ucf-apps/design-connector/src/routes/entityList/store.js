import { observable, action, toJS, reaction, makeObservable } from "mobx";
import { defaultListMap, defaultPagination } from "utils/pageListUtils";
import DefaultStore from "utils/defaultStore";
import _merge from "lodash/merge";
import { getDesignCntVersionList } from "design-connector/routes/versions/service";
import { getDesignCntEntityListService, deleteDesignCntEntityService } from "./service";
import { autoServiceMessage } from "utils/service";
import commonText from "constants/commonText";

/***
 * @typedef {Object} entitiesRequestDTO
 * @property {String} linkerId -连接器ID
 * @property {String} versionCodes -版本code 字符列表， 多个逗号隔开
 * @property {String} condition -实体名称或编码模糊搜索
 */
const defaultEntitiesRequestDTO = {
    linkId: "",
    versionCodes: "",
    condition: "",
};

/***
 * 连机器模板管理列表
 * @type {Object}
 * @property {pageListType} dataSource -
 * @property {paginationType} pagination
 * @property {entitiesRequestDTO} requestDTO -搜索条件
 * @property {Array.<{key: String, value: String}>} -版本下拉数据
 */
const initState = {
    dataSource: {
        ...defaultListMap,
    },
    pagination: {
        ...defaultPagination,
    },
    requestDTO: defaultEntitiesRequestDTO,
    versionSelectData: [],
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    init = () => {
        this.state = initState;
    };

    getVersions = async () => {
        const res = await autoServiceMessage({
            service: getDesignCntVersionList({
                linkerId: this.state.requestDTO.linkId,
            }),
        });
        if (res) {
            const versions = res.data || [];
            const versionSelectData = versions.map((item) => {
                const { code, id } = item;
                return { key: code, value: code };
            });
            versionSelectData.unshift({
                key: lang.templateByUuid("UID:P_UBL-FE_200960400428021C", "所有版本") /* "所有版本" */,
                value: "",
            });
            this.state.versionSelectData = versionSelectData;
        }
    };

    setPageParams = ({ queryParams }) => {
        this.state.requestDTO.linkId = queryParams.connectorId;
    };

    getDataSource = async (reqData = {}) => {
        const { requestDTO } = this.state;
        const { versionCodes, condition, ...others } = reqData;

        const requestData = {
            requestDTO: _merge(
                { ...requestDTO },
                {
                    versionCodes,
                    condition,
                }
            ),
            ...others,
        };

        const res = await this.getPagesListFunc({
            service: getDesignCntEntityListService,
            requestData: requestData,
            paginationKey: "pagination",
            dataKey: "dataSource",
        });
        if (res) {
            this.state.requestDTO = requestData.requestDTO;
        }
    };

    deleteAction = async (template) => {
        const res = await autoServiceMessage({
            service: deleteDesignCntEntityService({
                id: template.id,
            }),
            success: window.lang.template(commonText.deleteSuccess),
        });
        if (res) {
            this.getDataSource();
        }
    };
}

/**
 * 根据具体模块修改 storeKey 为保证唯一性
 * storeKey 由 模块文件夹名称 + 页面名称 + 'Store' 小驼峰
 *
 * storeKey是作为当前 Store 的唯一键， storeKey与Store的实例会在 rootStore或者StoreMap中以 键值对的 形式存在
 * 1. 用于在模块的 storeMap（模块routes/store.js）中注册并初始化当前Store
 * 2. 用于在container的 inject 中关联当前Store的实例时，从模块的storeMap或者rootStore中获取对应store实例
 * */
export const storeKey = "designConnectorEntityListStore";
export default Store;
