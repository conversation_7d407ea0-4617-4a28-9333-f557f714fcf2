import React, { Component, Fragment } from "react";
import { <PERSON><PERSON>, Modal, Select, Popconfirm } from "components/TinperBee";
import { Header, Content } from "components/PageView";
import Grid from "components/TinperBee/Grid";
import commonText from "constants/commonText";
import { initStore } from "decorator/index";
import SearchInput from "components/TinperBee/SearchInput";
import HeaderTitle from "design-connector/components/HeaderTitle";
import { clearConfigCntFormStorage } from "../../../../utils";
import { pathname as entityConfigPathname } from "../../../entityConfig";
import { generatePath } from "react-router";
import "./index.less";
import withRouter from "decorator/withRouter";

@withRouter
@initStore()
class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            hoverRowRecord: null,
        };
    }

    navToTemplate = (viewType = "add", entity) => {
        const {
            ownerState: { requestDTO },
        } = this.props;
        this.props.navigate({
            pathname: generatePath(entityConfigPathname, { viewType }),
            search: `?connectorId=${requestDTO.linkId}&entityId=${entity ? entity.id : ""}`,
        });
    };

    handleAdd = () => {
        this.navToTemplate("add");
    };

    handleEdit = (template) => {
        this.navToTemplate("edit", template);
    };

    handleDelete = (data) => {
        const { ownerStore } = this.props;
        Modal.confirm({
            title: window.lang.template(commonText.confirmDelete),
            onOk: () => {
                ownerStore.deleteAction(data);
            },
        });
    };

    handleSearch = (value) => {
        const { ownerStore } = this.props;
        ownerStore.getDataSource({
            pageIndex: 1,
            condition: value,
        });
    };

    handleSelectChange = (value) => {
        const { ownerStore } = this.props;
        ownerStore.getDataSource({
            pageIndex: 1,
            versionCodes: value,
        });
    };

    columns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_200960400428037E", "序号") /* "序号" */,
            dataIndex: "$$index",
            width: 60,
            render: Grid.renderIndex,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280381", "实体编码") /* "实体编码" */,
            dataIndex: "entityCode",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280383", "实体名称") /* "实体名称" */,
            dataIndex: "entityName",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280385", "实体描述") /* "实体描述" */,
            dataIndex: "entityDesc",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280388", "版本") /* "版本" */,
            dataIndex: "versionCodes",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280389", "创建人") /* "创建人" */,
            dataIndex: "createUserName",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_200960400428037D", "更新时间") /* "更新时间" */,
            dataIndex: "gmtUpdate",
            render: Grid.renderTime,
        },
        {
            title: "",
            dataIndex: "$$action",
        },
    ];

    handleRowHover = (index, record = null) => {
        this.setState({ hoverRowRecord: record });
    };

    hoverContent = (record) => {
        const { hoverRowRecord } = this.state;
        return hoverRowRecord ? (
            <Fragment>
                <Button
                    fieldid="ublinker-routes-entityList-components-IndexView-index-2397145-Button"
                    {...Grid.hoverButtonPorps}
                    onClick={this.handleEdit.bind(null, hoverRowRecord)}
                >
                    {lang.templateByUuid("UID:P_UBL-FE_2009604004280384", "编辑") /* "编辑" */}
                </Button>
                <Popconfirm
                    fieldid="ublinker-routes-entityList-components-IndexView-index-7400044-Popconfirm"
                    content={lang.templateByUuid("UID:P_UBL-FE_2009604004280386", "确认删除此实体？") /* "确认删除此实体？" */}
                    rootClose
                    onClose={this.handleDelete.bind(null, hoverRowRecord)}
                    key={hoverRowRecord.id}
                >
                    <Button fieldid="ublinker-routes-entityList-components-IndexView-index-153101-Button" {...Grid.hoverButtonPorps}>
                        {lang.templateByUuid("UID:P_UBL-FE_2009604004280387", "删除") /* "删除" */}
                    </Button>
                </Popconfirm>
            </Fragment>
        ) : null;
    };

    render() {
        const { ownerState } = this.props;
        const { dataSource, pagination, requestDTO, versionSelectData } = ownerState;
        return (
            <Fragment>
                <Header title={<HeaderTitle />} back bordered fixed={false} />

                <Content>
                    <Grid
                        fieldid="ublinker-routes-entityList-components-IndexView-index-5330342-Grid"
                        title={lang.templateByUuid("UID:P_UBL-FE_200960400428037F", "实体列表") /* "实体列表" */}
                        header={
                            <Fragment>
                                <SearchInput
                                    className="ucg-mar-r-sm"
                                    inputSize="sm"
                                    defaultValue={requestDTO.condition}
                                    placeholder={lang.templateByUuid("UID:P_UBL-FE_2009604004280380", "请输入实体名称或编码") /* "请输入实体名称或编码" */}
                                    onSearch={this.handleSearch}
                                />
                                <Select
                                    fieldid="ublinker-routes-entityList-components-IndexView-index-7969823-Select"
                                    size="sm"
                                    className="ucg-mar-r-sm"
                                    style={{ width: "120px" }}
                                    data={versionSelectData}
                                    defaultValue={requestDTO.versionCodes}
                                    onChange={this.handleSelectChange}
                                />
                                <Button fieldid="ublinker-routes-entityList-components-IndexView-index-8920464-Button" bordered onClick={this.handleAdd}>
                                    {lang.templateByUuid("UID:P_UBL-FE_2009604004280382", "添加") /* "添加" */}
                                </Button>
                            </Fragment>
                        }
                        columns={this.columns}
                        data={dataSource.list}
                        hoverContent={this.hoverContent}
                        onRowHover={this.handleRowHover}
                        onBodyMouseLeave={this.handleRowHover}
                        pagination={pagination}
                    />
                </Content>
            </Fragment>
        );
    }

    componentDidMount() {
        const { ownerStore } = this.props;
        ownerStore.getVersions();
        ownerStore.getDataSource();
    }

    componentWillUnmount() {
        if (this.props.navigationType === "POP") clearConfigCntFormStorage();
    }
}

export default IndexView;
