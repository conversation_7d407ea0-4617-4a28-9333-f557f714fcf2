import React, { Component, Fragment, useCallback, useEffect, useState } from "react";
import { Button, FormControl, Popconfirm } from "components/TinperBee";
import FieldWrap from "components/RowField/FieldWrap";
import { getUuid } from "utils/index";
import { Header, Content, Footer } from "components/PageView";
import Grid from "components/TinperBee/Grid";
import HeaderTitle from "design-connector/components/HeaderTitle";
import "./index.less";

class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            hoverRowRecord: null,
        };
    }

    handleAdd = () => {
        const {
            ownerState: { linkerId },
        } = this.props;
        this.handleEdit({
            id: getUuid(),
            code: "",
            description: "",
            linkerId: linkerId,
            add: true,
        });
    };

    handleEdit = (data) => {
        const { ownerStore } = this.props;
        ownerStore.editAction(data);
    };

    handleCancel = () => {
        this.handleEdit(null);
    };

    handleDelete = (data) => {
        const { ownerStore } = this.props;
        ownerStore.deleteAction(data);
    };

    handleSave = () => {
        const { ownerStore } = this.props;
        ownerStore.saveAction();
    };

    isHoverRow = (record) => {
        const { hoverRowRecord } = this.state;
        return hoverRowRecord && record && hoverRowRecord.id === record.id;
    };

    hasEditRow = () => {
        const {
            ownerState: { editVersionId },
        } = this.props;
        return !!editVersionId;
    };

    handleInputFocus = (record) => {
        if (!record.add) {
            this.handleEdit(record);
        }
    };

    handleInputBlur = (record) => {
        if (!record.add) {
            if (record.changed) {
                this.handleSave();
            } else {
                this.handleCancel();
            }
        }
    };

    columnRender = (key) => {
        return (value, record, index) => {
            const { ownerStore } = this.props;
            if (record.add || (key !== "code" && (record.edit || (!this.hasEditRow() && this.isHoverRow(record))))) {
                return (
                    <FieldWrap>
                        <FormControl
                            fieldid="ublinker-routes-versions-components-IndexView-index-2648971-FormControl"
                            value={value}
                            size="xs"
                            onChange={(value) => {
                                ownerStore.editVersionChange(key, value);
                            }}
                            onFocus={this.handleInputFocus.bind(null, record)}
                            onBlur={this.handleInputBlur.bind(null, record)}
                        />
                    </FieldWrap>
                );
            } else {
                return value;
            }
        };
    };

    columns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_200960400428041E", "序号") /* "序号" */,
            dataIndex: "$$index",
            className: "ucg-pad-l-20",
            width: 50,
            render: Grid.renderIndex,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280421", "版本号") /* "版本号" */,
            dataIndex: "code",
            render: this.columnRender("code"),
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_200960400428041B", "版本描述") /* "版本描述" */,
            dataIndex: "description",
            render: this.columnRender("description"),
        },
        {
            title: "",
            dataIndex: "$$action",
        },
    ];

    handleRowHover = (index, record) => {
        this.setState({
            hoverRowRecord: record,
        });
    };

    // hoverContent = (record, index) => {
    //   const { hoverRowRecord } = this.state
    //   const { ownerState: { editVersionId } } = this.props;
    //   if (hoverRowRecord && !editVersionId) {
    //     return (
    //       <Fragment>
    //         <Popconfirm fieldid="ublinker-routes-versions-components-IndexView-index-5159996-Popconfirm"
    //           content="确认删除此版本？" rootClose
    //           onClose={this.handleDelete.bind(null, hoverRowRecord)}
    //           key={hoverRowRecord.id}
    //           stopbubble={1}>
    //           <Button fieldid="ublinker-routes-versions-components-IndexView-index-1475731-Button" {...Grid.hoverButtonPorps} >删除</Button>
    //         </Popconfirm>
    //       </Fragment>
    //     )
    //   }else {
    //     return  null
    //   }
    // }

    render() {
        const {
            ownerState,
            ownerStore: { editVersion },
        } = this.props;
        const { dataSource, editVersionId } = ownerState;
        return (
            <Fragment>
                <Header back fixed={true} title={<HeaderTitle />} />

                <Content hasFooter>
                    <Grid
                        fieldid="ublinker-routes-versions-components-IndexView-index-8922947-Grid"
                        title={lang.templateByUuid("UID:P_UBL-FE_200960400428041C", "版本列表") /* "版本列表" */}
                        header={
                            <Button
                                fieldid="ublinker-routes-versions-components-IndexView-index-4010709-Button"
                                bordered
                                disabled={!!editVersionId}
                                onClick={this.handleAdd}
                            >
                                {lang.templateByUuid("UID:P_UBL-FE_200960400428041F", "添加") /* "添加" */}
                            </Button>
                        }
                        rowClassName={this._rowClassName}
                        rowKey="id"
                        columns={this.columns}
                        data={dataSource}
                        hoverContent={this.hoverContent}
                        onRowHover={this.handleRowHover}
                        onBodyMouseLeave={this.handleRowHover}
                    />
                </Content>
                {editVersionId && editVersion.add ? (
                    <Footer align="right">
                        <Button
                            fieldid="ublinker-routes-versions-components-IndexView-index-1925175-Button"
                            className="ucg-mar-r-xs"
                            onClick={this.handleCancel}
                        >
                            {lang.templateByUuid("UID:P_UBL-FE_200960400428041D", "取消") /* "取消" */}
                        </Button>
                        <Button fieldid="ublinker-routes-versions-components-IndexView-index-6889235-Button" colors="primary" onClick={this.handleSave}>
                            {lang.templateByUuid("UID:P_UBL-FE_2009604004280420", "保存") /* "保存" */}
                        </Button>
                    </Footer>
                ) : null}
            </Fragment>
        );
    }

    componentDidMount() {
        const { ownerStore } = this.props;
        ownerStore.getDataSource();
    }

    componentWillUnmount() {
        const { ownerStore } = this.props;
        ownerStore.init();
    }
}

export default IndexView;
