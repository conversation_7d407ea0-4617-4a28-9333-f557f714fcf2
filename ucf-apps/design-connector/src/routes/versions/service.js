import { getInvokeService } from "utils/service";

/***
 * 获取连接器版本列表
 * @param {Object} data
 * @param {String} data.linkerId
 * @return {Promise | Promise<unknown>}
 */
export const getDesignCntVersionList = function (data) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwmanage/linkerManagement/listLinkerVersionByLinkerId",
        },
        data
    );
};

/***
 * 删除连接器版本
 * @param {Object} data
 * @param {String} data.id
 * @return {Promise | Promise<unknown>}
 */
export const deleteDesignCntVersion = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/gwmanage/linkerManagement/deleteLinkerVersionById",
        },
        {},
        data
    );
};

/***
 * 保存连接器版本
 * @param {versionInfo} data
 * @return {*}
 */
export const saveDeisgnCntVersion = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/gwmanage/linkerManagement/saveLinkerVersion",
        },
        data
    );
};
