import { observable, action, toJS, reaction, computed, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { autoServiceMessage } from "utils/service";
import * as ownerServices from "./service";
import { Error } from "utils/feedback";
import commonText from "constants/commonText";

/***
 * 版本基本信息
 * @typedef {Object} versionInfo
 * @property {String=} id -
 * @property {String} code -
 * @property {String} description
 * @property {String=} gmtUpdate
 * @property {String} linkerId
 */
const initVersionInfo = {
    code: "",
    description: "",
    linkerId: "",
    id: "",
};

/***
 * 连接器版本管理列表
 * @type {Object}
 * @property {Array.<versionInfo>} dataSource
 */
const initState = {
    dataSource: [],
    linkerId: "",
    editVersionId: "",
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    @computed get editVersion() {
        return this.findEditVersion();
    }

    init = () => {
        this.state = initState;
    };

    setPageParams = ({ queryParams }) => {
        this.state.linkerId = queryParams.connectorId;
    };

    getDataSource = () => {
        this.dispatchService({
            service: ownerServices.getDesignCntVersionList({
                linkerId: this.state.linkerId,
            }),
            dataKey: "dataSource",
        });
    };

    @action
    editAction = (version) => {
        const { dataSource } = this.state;
        if (version) {
            const editVersion = dataSource.find((item) => item.id === version.id);
            if (editVersion) {
                editVersion.oldData = JSON.stringify(editVersion);
                editVersion.edit = observable.box(true);
            } else {
                dataSource.push(version);
            }
        } else {
            const editVersion = dataSource.find((item) => item.edit);
            if (editVersion) {
                const oldData = JSON.parse(editVersion.oldData);
                delete editVersion.oldData;
                delete editVersion.edit;
                Object.assign(editVersion, oldData);
            } else {
                dataSource.pop();
            }
        }
        this.state.editVersionId = version ? version.id : "";
    };

    findEditVersion = () => {
        const { dataSource, editVersionId } = this.state;
        return editVersionId ? dataSource.find((item) => item.id === editVersionId) : null;
    };

    editVersionChange = (key, value) => {
        const editVersion = this.findEditVersion();
        if (!editVersion.changed) {
            editVersion.changed = true;
        }
        editVersion[key] = value;
    };

    deleteAction = async (data) => {
        const res = await autoServiceMessage({
            service: ownerServices.deleteDesignCntVersion({
                id: data.id,
            }),
            success: window.lang.template(commonText.deleteSuccess),
        });
        if (res) {
            this.getDataSource();
        }
    };

    saveAction = async () => {
        const editVersion = this.findEditVersion();
        if (!editVersion.code) {
            Error(lang.templateByUuid("UID:P_UBL-FE_20096040042801FE", "请输入版本号") /* "请输入版本号" */);
            return false;
        }
        const { code, description, gmtUpdate, linkerId } = editVersion;
        let requestData = {
            code,
            description,
            gmtUpdate,
            linkerId,
        };
        if (editVersion.edit) {
            requestData.id = editVersion.id;
        }
        const res = await autoServiceMessage({
            service: ownerServices.saveDeisgnCntVersion(requestData),
            success: window.lang.template(commonText.saveSuccess),
        });
        if (res) {
            this.state.editVersionId = "";
            this.getDataSource();
        }
    };
}

/**
 * 根据具体模块修改 storeKey 为保证唯一性
 * storeKey 由 模块文件夹名称 + 页面名称 + 'Store' 小驼峰
 *
 * storeKey是作为当前 Store 的唯一键， storeKey与Store的实例会在 rootStore或者StoreMap中以 键值对的 形式存在
 * 1. 用于在模块的 storeMap（模块routes/store.js）中注册并初始化当前Store
 * 2. 用于在container的 inject 中关联当前Store的实例时，从模块的storeMap或者rootStore中获取对应store实例
 * */
export const storeKey = "designConnectorVersionsStore";
export default Store;
