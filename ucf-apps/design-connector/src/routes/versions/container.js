/***
 * 连接器版本管理
 * 页面参数
 * location.search.connectorId -连接器id
 */

import React, { Component } from "react";
import IndexView from "./components/IndexView";
import { inject, observer } from "mobx-react";
import Store, { storeKey } from "./store";
import core from "core";
import { getPageParams } from "decorator/index";
core.addStore({
    storeKey: storeKey,
    store: new Store(),
});

@inject((store) => {
    const ownerStore = store[storeKey];
    return {
        ownerStore,
        ownerState: ownerStore.toJS(),
    };
})
@observer
@getPageParams
class Container extends Component {
    constructor(props) {
        super(props);
        this.pageParams = this.props.getPageParams(props, true);
    }

    render() {
        return <IndexView {...this.props} />;
    }
}
export default Container;
