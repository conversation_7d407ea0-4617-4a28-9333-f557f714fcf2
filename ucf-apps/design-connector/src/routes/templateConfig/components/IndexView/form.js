import _template from "lodash/template";
import commonText from "constants/commonText";
export const connectorRules = {
    name: [
        {
            required: true,
            message: _template(commonText.isRequired)({ prop: lang.templateByUuid("UID:P_UBL-FE_200960400428048E", "连接器名称") /* "连接器名称" */ }),
        },
    ],
    code: [
        {
            required: true,
            message: _template(commonText.isRequired)({ prop: lang.templateByUuid("UID:P_UBL-FE_2009604004280490", "连接器编码") /* "连接器编码" */ }),
        },
    ],
    product: [
        {
            required: true,
            message: _template(commonText.isRequired)({ prop: lang.templateByUuid("UID:P_UBL-FE_200960400428048D", "所属产品") /* "所属产品" */ }),
        },
    ],
    label: [
        {
            required: true,
            message: _template(commonText.isRequired)({ prop: lang.templateByUuid("UID:P_UBL-FE_200960400428048F", "视图标签") /* "视图标签" */ }),
        },
    ],
    entranceClass: [
        {
            required: true,
            message: _template(commonText.isRequired)({ prop: lang.templateByUuid("UID:P_UBL-FE_2009604004280491", "接口入口类") /* "接口入口类" */ }),
        },
    ],
};
