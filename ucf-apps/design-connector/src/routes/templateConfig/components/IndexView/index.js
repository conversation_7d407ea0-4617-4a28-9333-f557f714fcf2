import React, { Component, Fragment } from "react";
import { Header, Content, Footer } from "components/PageView";
import FieldWrap from "components/RowField/FieldWrap";
import { getInfoError, getPageParams, initStore } from "decorator/index";
import { Button, FormControl, Select, Icon, Popconfirm, FormList } from "components/TinperBee";
import DotSwitch from "components/DotSwitch";
import Grid from "components/TinperBee/Grid";
import ParamValueListModal from "../ParamValueListModal";

// import FormList from "components/TinperBee/Form";
import { paramTypesEnum } from "design-connector/constants/paramTypes";
import "./index.less";
import withRouter from "decorator/withRouter";
const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
};
const FormItem = FormList.Item;

@withRouter
@getPageParams
@initStore()
@getInfoError
class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            hoverRowIndex: "",
            hoverRowRecord: null,
        };
        this.form = React.createRef();
        this.pageTitle = this.getPageTitle();
    }

    getPageTitle = () => {
        const { matchParams } = this.props;
        let title = "";
        switch (matchParams.viewType) {
            case "add":
                title = lang.templateByUuid("UID:P_UBL-FE_20096040042802C9", "新增") /* "新增" */;
                break;
            case "edit":
                title = lang.templateByUuid("UID:P_UBL-FE_20096040042802CC", "编辑") /* "编辑" */;
                break;
        }
        return title;
    };

    handleConfigParam = (type, paramIndex) => {
        const { ownerStore } = this.props;
        ownerStore.configParamAction(type, paramIndex);
    };

    handleCancel = () => {
        // const { form: { isFieldsTouched } } = this.props;
        // Header.handleBackConfirm({
        //   back: true,
        //   backConfirm: isFieldsTouched()
        // })
        this.props.navigate(-1);
    };

    handleSave = async () => {
        const { ownerStore } = this.props;
        const res = await ownerStore.saveAction();
        if (res) {
            this.props.navigate(-1);
        }
    };

    getFieldError = (record, index, field) => {
        const {
            ownerState: { templateInfoErrors },
        } = this.props;
        const errorField = `paramList${record.parentId ? `.${record.parentId}` : ""}.${index}.${field}`;
        return this.getInfoError(templateInfoErrors, errorField);
    };

    renderText = (value, record, index, column) => {
        const { dataIndex } = column;
        const { ownerStore } = this.props;
        const { hoverRowIndex } = this.state;
        const errorMsg = this.getFieldError(record, index, dataIndex);
        if (hoverRowIndex === index || errorMsg) {
            return (
                <FieldWrap message={errorMsg}>
                    <FormControl
                        fieldid="ublinker-routes-templateConfig-components-IndexView-index-6250891-FormControl"
                        size="xs"
                        value={value}
                        onChange={(value) => {
                            ownerStore.changeParamInfoAction(index, dataIndex, value);
                        }}
                    />
                </FieldWrap>
            );
        } else {
            return value;
        }
    };

    renderSelect = (value, record, index, column) => {
        const { dataIndex } = column;
        const { ownerStore } = this.props;
        const { hoverRowIndex } = this.state;
        const errorMsg = this.getFieldError(record, index, dataIndex);
        if (hoverRowIndex === index || errorMsg) {
            return (
                <FieldWrap message={errorMsg}>
                    <Select
                        fieldid="ublinker-routes-templateConfig-components-IndexView-index-5543630-Select"
                        data={paramTypesEnum.selectData}
                        className="u-select-xs"
                        value={value}
                        onChange={(value) => {
                            ownerStore.changeParamInfoAction(index, dataIndex, value);
                        }}
                    />
                </FieldWrap>
            );
        } else {
            return paramTypesEnum.getNameByCode(value);
        }
    };

    renderDotSwitch = (value, record, index, column) => {
        const { dataIndex } = column;
        const { ownerStore } = this.props;
        const { hoverRowIndex } = this.state;
        return (
            <DotSwitch
                size="xs"
                checked={value}
                noEvent={hoverRowIndex !== index}
                onChange={(value) => {
                    ownerStore.changeParamInfoAction(index, dataIndex, value);
                }}
            />
        );
    };

    paramColumns = [
        {
            title: <span className="mix-required">{lang.templateByUuid("UID:P_UBL-FE_20096040042802D0", "名称") /* "名称" */}</span>,
            dataIndex: "name",
            render: this.renderText,
        },
        {
            title: <span className="mix-required">{lang.templateByUuid("UID:P_UBL-FE_20096040042802D3", "编码") /* "编码" */}</span>,
            dataIndex: "code",
            render: this.renderText,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042802D7", "用户配置") /* "用户配置" */,
            dataIndex: "userConfigurable",
            width: 100,
            render: this.renderDotSwitch,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042802C4", "租户编辑") /* "租户编辑" */,
            dataIndex: "tenantEditable",
            width: 100,
            render: this.renderDotSwitch,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042802C7", "是否必填") /* "是否必填" */,
            dataIndex: "required",
            width: 100,
            render: this.renderDotSwitch,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042802CE", "是否显示") /* "是否显示" */,
            dataIndex: "isShow",
            width: 100,
            render: this.renderDotSwitch,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042802D1", "是否禁用") /* "是否禁用" */,
            dataIndex: "disable",
            width: 100,
            render: this.renderDotSwitch,
        },
        {
            title: <span className="mix-required">{lang.templateByUuid("UID:P_UBL-FE_20096040042802D5", "类型") /* "类型" */}</span>,
            dataIndex: "type",
            width: 150,
            render: this.renderSelect,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042802DA", "按钮文字") /* "按钮文字" */,
            dataIndex: "btnName",
            width: 250,
            render: this.renderText,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042802C5", "描述") /* "描述" */,
            dataIndex: "description",
            width: 250,
            render: this.renderText,
        },
        {
            title: "",
            dataIndex: "$$actions",
        },
    ];

    handleConfigItemListClick = () => {
        const { hoverRowRecord, hoverRowIndex } = this.state;
        const { ownerStore } = this.props;
        ownerStore.setConfigParamItemList(hoverRowIndex, hoverRowRecord);
    };

    handleParamValueListModalCancel = () => {
        // this.handleConfigItemListClick(null, '')
        const { ownerStore } = this.props;
        ownerStore.setConfigParamItemList(null, "");
    };

    valueItemListTypes = ["Select", "Radio", "Checkbox"];

    hoverContent = () => {
        const { hoverRowRecord, hoverRowIndex } = this.state;
        if (hoverRowRecord) {
            const { type } = hoverRowRecord;
            const hasValueItemList = paramTypesEnum[type].hasItems;
            return (
                <Fragment>
                    {hasValueItemList ? (
                        <Button
                            fieldid="ublinker-routes-templateConfig-components-IndexView-index-8559099-Button"
                            {...Grid.hoverButtonPorps}
                            onClick={this.handleConfigItemListClick}
                        >
                            {lang.templateByUuid("UID:P_UBL-FE_20096040042802C8", "参数项") /* "参数项" */}
                        </Button>
                    ) : null}
                    <Popconfirm
                        fieldid="ublinker-routes-templateConfig-components-IndexView-index-6398999-Popconfirm"
                        content={lang.templateByUuid("UID:P_UBL-FE_20096040042802CB", "确认删除此参数？") /* "确认删除此参数？" */}
                        rootClose
                        key={hoverRowRecord.id}
                        onClose={this.handleConfigParam.bind(null, "delete", hoverRowIndex)}
                    >
                        <Button fieldid="ublinker-routes-templateConfig-components-IndexView-index-1987938-Button" {...Grid.hoverButtonPorps}>
                            {lang.templateByUuid("UID:P_UBL-FE_20096040042802CD", "删除") /* "删除" */}
                        </Button>
                    </Popconfirm>
                </Fragment>
            );
        } else {
            return null;
        }
    };

    handleRowHover = (index, record) => {
        this.setState({
            hoverRowIndex: index,
            hoverRowRecord: record,
        });
    };

    render() {
        const {
            ownerState: { infoLoaded },
        } = this.props;
        console.log(infoLoaded);
        if (infoLoaded) {
            const { ownerState, ownerStore } = this.props;

            const { templateInfo, configItemListParamInfo, configItemListErrors } = ownerState;
            const { name, paramList } = templateInfo;

            return (
                <Fragment>
                    <Header title={this.pageTitle} />
                    <Content hasFooter>
                        <FormList
                            fieldid="ublinker-routes-templateConfig-components-IndexView-index-4709105-FormList"
                            className="ucg-pad-sm"
                            size="sm"
                            title={lang.templateByUuid("UID:P_UBL-FE_20096040042802CF", "基本信息") /* "基本信息" */}
                            // layoutOpt={{lg: 3, md: 4}}
                            ref={this.form}
                            {...formItemLayout}
                        >
                            <FormItem
                                fieldid="ublinker-routes-templateConfig-components-IndexView-index-2079427-FormItem"
                                label={lang.templateByUuid("UID:P_UBL-FE_20096040042802D2", "模板名称") /* "模板名称" */}
                                name="name"
                                rules={[{ required: true }]}
                                initialValue={name}
                            >
                                <FormControl
                                    fieldid="ublinker-routes-templateConfig-components-IndexView-index-1698234-FormControl"
                                    placeholder={lang.templateByUuid("UID:P_UBL-FE_20096040042802D4", "请输入") /* "请输入" */}
                                    onChange={ownerStore.changeTemplateInfo.bind(null, "name")}
                                />
                            </FormItem>

                            <FormItem
                                fieldid="ublinker-routes-templateConfig-components-IndexView-index-1561525-FormItem"
                                label={lang.templateByUuid("UID:P_UBL-FE_20096040042802D8", "版本") /* "版本" */}
                                // name='name'
                                rules={[{ required: true }]}
                                initialValue={ownerStore.supportedVersionCodes}
                            >
                                <Select
                                    fieldid="ublinker-routes-templateConfig-components-IndexView-index-6378343-Select"
                                    multiple
                                    size="sm"
                                    placeholder={lang.templateByUuid("UID:P_UBL-FE_20096040042802DB", "请选择") /* "请选择" */}
                                    value={ownerStore.supportedVersionCodes}
                                    data={ownerStore.versionSelectData}
                                    onChange={ownerStore.changeTemplateInfo.bind(null, "supportedVersionCodeList")}
                                />
                            </FormItem>
                        </FormList>

                        <Grid
                            fieldid="ublinker-routes-templateConfig-components-IndexView-index-679967-Grid"
                            title={lang.templateByUuid("UID:P_UBL-FE_20096040042802C6", "参数定义") /* "参数定义" */}
                            header={
                                <Button
                                    fieldid="ublinker-routes-templateConfig-components-IndexView-index-8366840-Button"
                                    bordered
                                    onClick={this.handleConfigParam.bind(null, "add", null)}
                                >
                                    {lang.templateByUuid("UID:P_UBL-FE_20096040042802CA", "添加") /* "添加" */}
                                </Button>
                            }
                            rowClassName={this._rowClassName}
                            onRowHover={this.handleRowHover}
                            onBodyMouseLeave={this.handleRowHover}
                            hoverContent={this.hoverContent}
                            data={paramList}
                            columns={this.paramColumns}
                        />
                    </Content>

                    <ParamValueListModal
                        paramInfo={configItemListParamInfo}
                        configAction={ownerStore.configParamItemListAction}
                        onChange={ownerStore.changeParamItemListInfoAction}
                        onCancel={this.handleParamValueListModalCancel}
                        onOk={ownerStore.saveParamItemListAction}
                        errors={configItemListErrors}
                    />

                    <Footer align="right">
                        <Button
                            fieldid="ublinker-routes-templateConfig-components-IndexView-index-3348607-Button"
                            className="ucg-mar-r-sm"
                            onClick={this.handleCancel}
                        >
                            {lang.templateByUuid("UID:P_UBL-FE_20096040042802D6", "取消") /* "取消" */}
                        </Button>
                        <Button fieldid="ublinker-routes-templateConfig-components-IndexView-index-5779054-Button" colors="primary" onClick={this.handleSave}>
                            {lang.templateByUuid("UID:P_UBL-FE_20096040042802D9", "保存") /* "保存" */}
                        </Button>
                    </Footer>
                </Fragment>
            );
        } else {
            return null;
        }
    }

    componentDidMount() {
        const {
            ownerStore,
            queryParams: { templateId },
        } = this.props;
        ownerStore.getVersions();
        if (!!templateId) {
            ownerStore.getTemplateInfo();
        }
    }
}

export default IndexView;
