import React, { Component, Fragment } from "react";
import Grid from "components/TinperBee/Grid";
import { Button, FormControl, Select } from "components/TinperBee";
import ModalView from "components/TinperBee/Modal";
import FieldWrap from "components/RowField/FieldWrap";
import DotSwitch from "components/DotSwitch";
import { getInfoError } from "decorator/index";

@getInfoError
class ParamValueListModal extends Component {
    constructor(props) {
        super(props);
        this.state = {
            show: false,
            hoverRowIndex: "",
            hoverRowRecord: null,
        };
    }

    static getDerivedStateFromProps(nextProps, prevState) {
        const nextShow = !!nextProps.paramInfo;
        if (nextShow !== prevState.show) {
            return {
                show: nextShow,
            };
        }
        return null;
    }

    renderText = (value, record, index, column) => {
        const { dataIndex } = column;
        const { onChange, errors } = this.props;
        const { hoverRowIndex } = this.state;
        const error = this.getInfoError(errors, `itemList.${index}.${dataIndex}`);
        if (hoverRowIndex === index) {
            return (
                <FieldWrap message={error}>
                    <FormControl
                        fieldid="ublinker-routes-templateConfig-components-ParamValueListModal-index-5841250-FormControl"
                        // size="xs"
                        style={{ width: 0 }}
                        className={error ? "form-error" : ""}
                        value={value}
                        onChange={(value) => {
                            onChange(index, dataIndex, value);
                        }}
                    />
                </FieldWrap>
            );
        } else {
            return value;
        }
    };

    renderDotSwitch = (value, record, index, column) => {
        const { dataIndex } = column;
        const { onChange } = this.props;
        const { hoverRowIndex } = this.state;
        return (
            <DotSwitch
                size="xs"
                checked={value}
                noEvent={hoverRowIndex !== index}
                onChange={(value) => {
                    onChange(index, dataIndex, value);
                }}
            />
        );
    };

    columns = [
        {
            title: <span className="mix-required">{lang.templateByUuid("UID:P_UBL-FE_200960400428028D", "实际值") /* "实际值" */}</span>,
            dataIndex: "realValue",
            width: 150,
            render: this.renderText,
        },
        {
            title: <span className="mix-required">{lang.templateByUuid("UID:P_UBL-FE_200960400428028E", "显示值") /* "显示值" */}</span>,
            dataIndex: "displayValue",
            width: 150,
            render: this.renderText,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_200960400428028A", "是否默认") /* "是否默认" */,
            dataIndex: "defaultItem",
            width: 100,
            render: this.renderDotSwitch,
        },
        {
            title: "",
            dataIndex: "$$action",
        },
    ];

    handleValueItemConfig = (type, valueItemIndex) => {
        const { paramIndex, configAction } = this.props;
        configAction(type, paramIndex, valueItemIndex);
    };

    hoverContent = (record, index) => {
        if (record) {
            const {
                paramInfo: { itemList },
            } = this.props;
            // record = itemList[index];
            return (
                <Fragment>
                    {index > 0 ? (
                        <Button
                            fieldid="ublinker-routes-templateConfig-components-ParamValueListModal-index-1571842-Button"
                            {...Grid.hoverButtonPorps}
                            onClick={this.handleValueItemConfig.bind(null, "move", index)}
                        >
                            {lang.templateByUuid("UID:P_UBL-FE_200960400428028F", "上移") /* "上移" */}
                        </Button>
                    ) : null}
                    {index < itemList.length - 1 ? (
                        <Button
                            fieldid="ublinker-routes-templateConfig-components-ParamValueListModal-index-8872337-Button"
                            {...Grid.hoverButtonPorps}
                            onClick={this.handleValueItemConfig.bind(null, "move", index + 1)}
                        >
                            {lang.templateByUuid("UID:P_UBL-FE_2009604004280289", "下移") /* "下移" */}
                        </Button>
                    ) : null}
                    <Button
                        fieldid="ublinker-routes-templateConfig-components-ParamValueListModal-index-3053202-Button"
                        {...Grid.hoverButtonPorps}
                        onClick={this.handleValueItemConfig.bind(null, "delete", index)}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_200960400428028C", "删除") /* "删除" */}
                    </Button>
                </Fragment>
            );
        } else {
            return null;
        }
    };

    handleHoverRow = (index, record) => {
        this.setState({
            hoverRowIndex: index,
            hoverRowRecord: record,
        });
    };

    _rowClassName = (record, index) => {
        return this.state.hoverRowIndex === index ? Grid.rowEditClass : "";
    };

    render() {
        const { show } = this.state;
        const { paramInfo, onCancel, onOk } = this.props;
        return (
            <ModalView title={lang.templateByUuid("UID:P_UBL-FE_2009604004280288", "参数值") /* "参数值" */} show={show} onCancel={onCancel} onOk={onOk}>
                {show ? (
                    <Grid
                        fieldid="ublinker-routes-templateConfig-components-ParamValueListModal-index-5798608-Grid"
                        header={
                            <Button
                                fieldid="ublinker-routes-templateConfig-components-ParamValueListModal-index-6771931-Button"
                                bordered
                                onClick={this.handleValueItemConfig.bind(null, "add", null)}
                            >
                                {lang.templateByUuid("UID:P_UBL-FE_200960400428028B", "新增") /* "新增" */}
                            </Button>
                        }
                        rowKey="id"
                        rowClassName={this._rowClassName}
                        onRowHover={this.handleHoverRow}
                        hoverContent={this.hoverContent}
                        data={paramInfo.itemList || []}
                        columns={this.columns}
                    />
                ) : null}
            </ModalView>
        );
    }
}
export default ParamValueListModal;
