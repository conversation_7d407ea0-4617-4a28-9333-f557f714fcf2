import { observable, action, computed, makeObservable } from "mobx";
import _template from "lodash/template";
import DefaultStore from "utils/defaultStore";
import { getUuid } from "utils/index";
import * as ownerServices from "./service";
import { getDesignCntVersionList } from "design-connector/routes/versions/service";
import { autoServiceMessage } from "utils/service";
import commonText from "constants/commonText";
import { validate } from "decorator/validate";
import { paramTypesEnum } from "design-connector/constants/paramTypes";
import { templateReserveFields } from "design-connector/constants";

/***
 * 模板参数类型为 CheckBox|DropList|Radio 时的 选项
 * @typedef templateParamItemInfo
 * @property {String} id
 * @property {String} linkerId
 * @property {String} linkerTemplateId
 * @property {String} linkerParamId
 * @property {Boolean} defaultItem -是否为默认值
 * @property {String} displayValue -显示名称
 * @property {String} realValue -选项值
 */

const templateParamItemInfo = {
    id: "",
    linkerTemplateId: "",
    linkerParamId: "",
    defaultItem: false,
    displayValue: "",
    realValue: "",
};

/**
 * 模板参数项
 * @typedef {Object} templateParam
 * @property {String} id
 * @property {String} linkerId -连接器ID
 * @property {String} linkerTemplateId -连接器ID
 * @property {String} name -参数名称
 * @property {String} code -参数编码
 * @property {String} type=[Text|TextArea|Integer|CheckBox|DropList|Radio] -参数类型
 * @property {String} description -描述
 * @property {Boolean} userConfigurable -用户配置
 * @property {Boolean} tenantEditable -租户编辑
 * @property {Boolean} required -是否必填
 * @property {Array.<templateParamItemInfo>} itemList -CheckBox|DropList|Radio 选项列表
 * @property {String} gmtCreate
 * @property {String} gmtUpdate
 */
const templateParam = {
    id: "",
    linkerId: "",
    linkerTemplateId: "",
    code: "",
    name: "",
    type: "Text",
    btnName: "",
    isShow: true,
    disable: false, //可用
    description: "",
    userConfigurable: true,
    tenantEditable: false,
    required: true,
    itemList: null,
};

/***
 * @typedef templateInfo
 * @property {String} id -模板ID
 * @property {String} linkerId -连接器ID
 * @property {Array.<{linkerVersionCode: String}>} supportedVersionCodeList -已选version列表
 * @property {Array.<templateParam>} paramList -参数列表
 */
const templateInfo = {
    linkerId: "",
    name: "",
    supportedVersionCodeList: [],
    paramList: [],
};

/***
 * 连接器配置
 * @type {Object}
 * @property {String} pageType=[add|edit]
 * @property {String} linkerId -连接器Id
 * @property {String} templateId -模板ID
 * @property {templateInfo} templateInfo -模板信息
 * @property {String} paramEditModalType=[hide|add|edit] -编辑连接器参数Modal状态
 * @property {Number|String} configItemListParamIndex -编辑参数值选择列表的参数项索引
 * @property {templateParam} configItemListParamInfo -编辑参数值选择列表的参数项
 * @property {Array.<{id: String, code: String}>} versions -连接器版本列表
 */
const initState = {
    pageType: "add",
    templateId: "",
    linkerId: "",
    versions: [],
    templateInfo: templateInfo,
    templateInfoErrors: [],
    infoLoaded: false,
    configItemListParamIndex: "",
    configItemListParamInfo: null,
    configItemListErrors: [],
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    init = () => (this.state = initState);

    setPageParams = (pageParams) => {
        const { matchParams, queryParams } = pageParams;
        this.state.pageType = matchParams.viewType;
        this.state.linkerId = queryParams.connectorId || "";
        this.state.templateId = queryParams.templateId || "";
        if (matchParams.viewType === "add") {
            this.state.infoLoaded = true;
        }
    };

    /** 通过版本列表 生成 Select Data */
    @computed get versionSelectData() {
        return this.state.versions.map((item) => {
            return {
                key: item.code,
                value: item.code,
            };
        });
    }

    @computed get supportedVersionCodes() {
        return this.state.templateInfo.supportedVersionCodeList.map((item) => {
            return item;
        });
    }

    /** 获取版本列表 */
    getVersions = () => {
        this.dispatchService({
            service: getDesignCntVersionList({ linkerId: this.state.linkerId }),
            dataKey: "versions",
        });
    };

    getTemplateInfo = async () => {
        const { templateId } = this.state;
        this.changeState({
            templateInfo: JSON.parse(sessionStorage.getItem("templateInfo")),
        });
        this.state.infoLoaded = true;
    };

    changeTemplateInfo = (prop, value) => {
        this.state.templateInfo[prop] = value;
    };

    /***
     * 参数定义新增，删除动作
     * @param {Store} type=[add|delete] -动作类型
     * @param {Number} paramIndex -操作参数索引
     * @param paramIndex
     */
    @action
    configParamAction = (type, paramIndex) => {
        const { templateInfo, linkerId, templateId } = this.state;
        switch (type) {
            case "add":
                templateInfo.paramList.push({
                    ...templateParam,
                    id: getUuid(),
                    linkerId: linkerId,
                    linkerTemplateId: templateId,
                    add: true,
                });
                break;
            case "delete":
                templateInfo.paramList.splice(paramIndex, 1);
                break;
        }
    };

    /***
     * 修改参数信息
     * @param {Number} index -参数项索引
     * @param {String} prop -修改信息 key
     * @param {*} value -信息值
     * */
    @action
    changeParamInfoAction = (index, prop, value) => {
        const { templateInfo } = this.state;
        templateInfo.paramList[index][prop] = value;
        this.changeState({
            templateInfo,
        });
        console.log(templateInfo);
    };

    /** 设置编辑参数值选项列表的参数项信息 */
    setConfigParamItemList = (paramIndex = "", paramInfo = null) => {
        this.state.configItemListParamIndex = paramIndex;
        this.state.configItemListParamInfo = paramInfo;
        this.state.configItemListErrors = [];
    };

    /****
     * 下拉、单选、多选等参数类型 可选值列表操作
     * @param {String} type -操作类型
     * @param {Number} paramIndex -操作参数项索引
     * @param {Number} valueItemIndex -操作值索引
     */
    @action
    configParamItemListAction = (type, paramIndex, valueItemIndex) => {
        const { configItemListParamInfo, templateId } = this.state;
        let { itemList } = configItemListParamInfo;
        switch (type) {
            case "add":
                if (!itemList) {
                    configItemListParamInfo.itemList = itemList = observable.array([]);
                }
                itemList.push({
                    ...templateParamItemInfo,
                    id: getUuid(),
                    linkerTemplateId: templateId,
                    linkerParamId: configItemListParamInfo.id,
                    add: true,
                });
                break;
            case "delete":
                itemList.splice(valueItemIndex, 1);
                if (itemList.length <= 0) {
                    configItemListParamInfo.itemList = null;
                }
                break;
            case "move":
                //上移和下移都可以看做是把某一项，移动到另一项的前面，
                //上移 valueItemIndex  为当前项，把当前项移动到下一项的后面
                //下移 valueItemIndex  为操作项的后一项，即把当前项的后一项项移动到当前项前面
                const moveItem = itemList.splice(valueItemIndex, 1)[0];
                itemList.splice(valueItemIndex - 1, 0, moveItem);
                break;
        }
    };

    /***
     * 修改参数信息
     * @param {Number} itemIndex -操作值索引
     * @param {String} prop -修改信息 key
     * @param {*} value -信息值
     * */
    @action
    changeParamItemListInfoAction = (itemIndex, prop, value) => {
        const { configItemListParamInfo } = this.state;
        //是否默认具有唯一性, 当一项设置为true 其他需要置为false
        if (prop === "defaultItem" && value) {
            const defaultItemValue = configItemListParamInfo.itemList.find((item) => item.defaultItem);
            if (defaultItemValue) {
                defaultItemValue.defaultItem = false;
            }
        }
        configItemListParamInfo.itemList[itemIndex][prop] = value;
    };

    saveParamItemListAction = async () => {
        const descriptor = {
            itemList: {
                fields: {
                    displayValue: { required: true, message: lang.templateByUuid("UID:P_UBL-FE_200960400428021D", "请输入实际值") /* "请输入实际值" */ },
                    realValue: { required: true, message: lang.templateByUuid("UID:P_UBL-FE_200960400428021E", "请输入显示值") /* "请输入显示值" */ },
                },

                validator: (rule, value, callback, source) => {
                    const { fields, field } = rule;
                    let errors = [];
                    let onlyFieldIndexMap = new Map();
                    value.forEach((item, index) => {
                        var parentField = `${field}.${index}`;
                        if (item.displayValue) {
                            const codeIndex = onlyFieldIndexMap.get[item.code];
                            if (typeof codeIndex === "undefined") {
                                onlyFieldIndexMap.set(item.code, index);
                            } else {
                                errors.push({
                                    field: `${parentField}.code`,
                                    message: `${lang.templateByUuid("UID:P_UBL-FE_200960400428021F", "实际值冲突") /* "实际值冲突" */}`,
                                });
                            }
                        }
                        validate(fields, item, (_errors) => {
                            if (errors) {
                                _errors.forEach((error) => {
                                    error.field = `${parentField}.${error.field}`;
                                    errors.push(error);
                                });
                            }
                        });
                    });
                    return errors;
                },
            },
        };

        const errors = await validate(descriptor, this.toJS(this.state.configItemListParamInfo));
        if (errors) {
            this.state.configItemListErrors = errors;
        } else {
            const { configItemListParamInfo, configItemListParamIndex } = this.state;
            this.state.templateInfo.paramList[configItemListParamIndex].itemList = configItemListParamInfo.itemList;
            this.setConfigParamItemList();
        }
    };

    saveAction = async () => {
        const { templateInfo } = this.state;
        const _templateInfo = this.toJS(templateInfo);
        const descriptor = {
            name: { required: true, message: lang.templateByUuid("UID:P_UBL-FE_2009604004280220", "请输入模板名称") /* "请输入模板名称" */ },
            paramList: {
                fields: {
                    name: { required: true, message: lang.templateByUuid("UID:P_UBL-FE_2009604004280221", "请输入参数名称") /* "请输入参数名称" */ },
                    code: { required: true, message: lang.templateByUuid("UID:P_UBL-FE_2009604004280223", "请输入参数编码") /* "请输入参数编码" */ },
                },

                validator: (rule, value, callback, source) => {
                    const { fields, field } = rule;
                    let errors = [];
                    let onlyFieldIndexMap = new Map();

                    value.forEach((item, index) => {
                        let parentField = `${field}.${index}`;

                        if (item.code) {
                            if (templateReserveFields.includes(item.code)) {
                                errors.push({
                                    field: `${parentField}.code`,
                                    message: _template(
                                        lang.templateByUuid(
                                            "UID:P_UBL-FE_2009604004280222",
                                            "参数编码不能使用下列保留字段：<%= reserveFields %>" //@notranslate
                                        ) /* "参数编码不能使用下列保留字段：<%= reserveFields %>" */
                                    )({
                                        reserveFields: templateReserveFields.join("|"),
                                    }),
                                });
                            } else {
                                if (onlyFieldIndexMap.has(item.code)) {
                                    errors.push({
                                        field: `${parentField}.code`,
                                        message: `${lang.templateByUuid("UID:P_UBL-FE_2009604004280224", "参数编码冲突") /* "参数编码冲突" */}`,
                                    });
                                } else {
                                    onlyFieldIndexMap.set(item.code, index);
                                }
                            }
                        }

                        validate(fields, item, (_errors) => {
                            if (errors) {
                                _errors.forEach((error) => {
                                    error.field = `${parentField}.${error.field}`;
                                    errors.push(error);
                                });
                            }
                        });
                    });
                    return errors;
                },
            },
        };

        const errors = await validate(descriptor, _templateInfo);

        if (errors) {
            this.state.templateInfoErrors = errors;
        } else {
            const { id, gmtUpdate, name, version, supportedVersionCodeList, paramList } = _templateInfo;
            const requestData = {
                id,
                gmtUpdate,
                name,
                version,
                linkerId: this.state.linkerId,
                supportedVersionCodeList: supportedVersionCodeList,
                paramList: paramList.map((param, index) => {
                    let { name, code, type, description, btnName, isShow, disable, userConfigurable, tenantEditable, itemList, required } = param;
                    let _param = {
                        name,
                        code,
                        type,
                        description,
                        btnName,
                        isShow,
                        disable,
                        userConfigurable,
                        tenantEditable,
                        required: Boolean(required),
                        order: index + 1,
                    };

                    const hasValueItemList = paramTypesEnum[type].hasItems;

                    if (hasValueItemList && itemList && itemList.length > 0) {
                        _param.itemList = itemList.map((item, index) => {
                            const { defaultItem, displayValue, realValue } = item;
                            return { defaultItem, displayValue, realValue, order: index + 1 };
                        });
                    }
                    return _param;
                }),
            };
            const res = await autoServiceMessage({
                service: ownerServices.saveDesignCntTemplateService(requestData),
                success: window.lang.template(commonText.saveSuccess),
            });
            return res;
        }
    };
}

/**
 * 根据具体模块修改 storeKey 为保证唯一性
 * storeKey 由 模块文件夹名称 + 页面名称 + 'Store' 小驼峰
 *
 * storeKey是作为当前 Store 的唯一键， storeKey与Store的实例会在 rootStore或者StoreMap中以 键值对的 形式存在
 * 1. 用于在模块的 storeMap（模块routes/store.js）中注册并初始化当前Store
 * 2. 用于在container的 inject 中关联当前Store的实例时，从模块的storeMap或者rootStore中获取对应store实例
 * */
export const storeKey = "designConnectorTemplateConfigStore";
export default Store;
