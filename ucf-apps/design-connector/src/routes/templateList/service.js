import { getInvokeService } from "utils/service";

/***
 * 获取连机器模板列表
 * @param {Object} data
 * @param {String} data.linkerId -连机器ID
 * @return {*}
 */
export const getDesignCntTemplateListService = function (data) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwmanage/linkerManagement/listLinkerTemplateByLinkerId",
        },
        data
    );
};

/***
 * 删除连接器模板
 * @param {Object} data
 * @param {String} data.id -模板ID
 * @return {*}
 */
export const deleteDesignCntTemplateService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/gwmanage/linkerManagement/deleteLinkerTemplateById",
        },
        {},
        data
    );
};
