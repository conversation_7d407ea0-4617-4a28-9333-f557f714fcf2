import React, { Component, Fragment, useCallback, useEffect, useState } from "react";
import { <PERSON><PERSON>, Modal, Popconfirm } from "components/TinperBee";
import { Header, Content } from "components/PageView";
import Grid from "components/TinperBee/Grid";
import { initStore } from "decorator/index";
import HeaderTitle from "design-connector/components/HeaderTitle";
import { pathname as templateConfigPathname } from "../../../templateConfig";
import { generatePath } from "react-router";
import "./index.less";
import withRouter from "decorator/withRouter";

@withRouter
@initStore()
class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            hoverRowRecord: null,
        };
    }

    navToTemplate = (viewType = "add", template) => {
        const {
            ownerState: { linkerId },
        } = this.props;
        sessionStorage.setItem("templateInfo", JSON.stringify(template));
        this.props.navigate({
            pathname: generatePath(templateConfigPathname, { viewType }),
            search: `?connectorId=${linkerId}&templateId=${template ? template.id : ""}`,
        });
    };

    handleAdd = () => {
        this.navToTemplate("add");
    };

    handleEdit = (template) => {
        // debugger
        template.paramList.length > 0 &&
            template.paramList.forEach((item) => {
                item.btnName = item.btnName ? item.btnName : "";
            });
        this.navToTemplate("edit", template);
    };

    handleDelete = (data) => {
        const { ownerStore } = this.props;
        ownerStore.deleteAction(data);
    };

    columns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_200960400428025E", "序号") /* "序号" */,
            dataIndex: "$$index",
            render: Grid.renderIndex,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280262", "名称") /* "名称" */,
            dataIndex: "name",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280265", "支持版本") /* "支持版本" */,
            dataIndex: "supportedVersionCodeList",
            render: (value) => {
                if (value.length === 0) {
                    return lang.templateByUuid("UID:P_UBL-FE_200960400428025D", "全部版本") /* "全部版本" */;
                } else {
                    return value.join(",");
                }
            },
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280260", "更新时间") /* "更新时间" */,
            dataIndex: "lastUpdateTime",
            render: Grid.renderTime,
        },
    ];

    handleRowHover = (index, record) => {
        this.setState({ hoverRowRecord: record });
    };

    hoverContent = (hoverRowRecord) => {
        return (
            <Fragment>
                <Button
                    fieldid="ublinker-routes-templateList-components-IndexView-index-9923249-Button"
                    {...Grid.hoverButtonPorps}
                    onClick={this.handleEdit.bind(null, hoverRowRecord)}
                >
                    {lang.templateByUuid("UID:P_UBL-FE_200960400428025F", "编辑") /* "编辑" */}
                </Button>
                <Popconfirm
                    fieldid="ublinker-routes-templateList-components-IndexView-index-3889536-Popconfirm"
                    content={lang.templateByUuid("UID:P_UBL-FE_2009604004280261", "确认删除此模板？") /* "确认删除此模板？" */}
                    rootClose
                    key={hoverRowRecord.id}
                    onClose={this.handleDelete.bind(null, hoverRowRecord)}
                >
                    <Button fieldid="ublinker-routes-templateList-components-IndexView-index-7254588-Button" {...Grid.hoverButtonPorps}>
                        {lang.templateByUuid("UID:P_UBL-FE_2009604004280263", "删除") /* "删除" */}
                    </Button>
                </Popconfirm>
            </Fragment>
        );
    };

    render() {
        const { ownerState } = this.props;
        const { dataSource } = ownerState;
        return (
            <Fragment>
                <Header title={<HeaderTitle />} back bordered fixed={false}></Header>

                <Content minus={16}>
                    <Grid
                        fieldid="ublinker-routes-templateList-components-IndexView-index-3065486-Grid"
                        title={lang.templateByUuid("UID:P_UBL-FE_2009604004280264", "模板列表") /* "模板列表" */}
                        header={
                            <Button fieldid="ublinker-routes-templateList-components-IndexView-index-2953634-Button" bordered onClick={this.handleAdd}>
                                {lang.templateByUuid("UID:P_UBL-FE_2009604004280266", "添加") /* "添加" */}
                            </Button>
                        }
                        columns={this.columns}
                        data={dataSource}
                        hoverContent={this.hoverContent}
                    />
                </Content>
            </Fragment>
        );
    }

    componentDidMount() {
        const { ownerStore } = this.props;
        ownerStore.getDataSource();
    }
}

export default IndexView;
