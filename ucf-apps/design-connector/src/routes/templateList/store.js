import { observable, action, toJS, reaction, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { getDesignCntTemplateListService, deleteDesignCntTemplateService } from "./service";
import { autoServiceMessage } from "utils/service";
import commonText from "constants/commonText";

/***
 * 连机器模板管理列表
 * @type {Object}
 * @property {Array} dataSource -
 * @property {String} linkerId -连接器ID
 */
const initState = {
    dataSource: [],
    linkerId: "",
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    init = () => {
        this.state = initState;
    };

    setPageParams = ({ queryParams }) => {
        this.state.linkerId = queryParams.connectorId;
    };

    getDataSource = () => {
        this.dispatchService({
            service: getDesignCntTemplateListService({
                linkerId: this.state.linkerId,
            }),
            dataKey: "dataSource",
        });
    };

    deleteAction = async (template) => {
        const res = await autoServiceMessage({
            service: deleteDesignCntTemplateService({
                id: template.id,
            }),
            success: window.lang.template(commonText.deleteSuccess),
        });
        if (res) {
            this.getDataSource();
        }
    };
}

/**
 * 根据具体模块修改 storeKey 为保证唯一性
 * storeKey 由 模块文件夹名称 + 页面名称 + 'Store' 小驼峰
 *
 * storeKey是作为当前 Store 的唯一键， storeKey与Store的实例会在 rootStore或者StoreMap中以 键值对的 形式存在
 * 1. 用于在模块的 storeMap（模块routes/store.js）中注册并初始化当前Store
 * 2. 用于在container的 inject 中关联当前Store的实例时，从模块的storeMap或者rootStore中获取对应store实例
 * */
export const storeKey = "designConnectorTemplateListStore";
export default Store;
