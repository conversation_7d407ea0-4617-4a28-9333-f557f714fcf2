import { observable, action, computed, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { getUuid, loopTree } from "utils/index";
import * as ownerServices from "./service";
import { getDesignCntVersionList } from "design-connector/routes/versions/service";
import { autoServiceMessage } from "utils/service";
import commonText from "constants/commonText";
import { validate } from "decorator/validate";
import _findIndex from "lodash/findIndex";
import { Error } from "utils/feedback";

/**
 * 实体属性数据结构
 * @typedef {Object} entityParamInfo
 * @property {String} id
 * @property {String} name -实体属性编码
 * @property {String} paramType -实体属性字段类型
 * @property {Boolean} array -是否数组
 * @property {String} paramDesc -实体属性描述
 * @property {String} defaultValue -视图属性默认参数
 * @property {Boolean} required -实体属性是否必填
 * @property {Boolean} baseType -是否是基本数据类型，还是对象关联
 * @property {String} childId -如果是对象关联，此为对象的ID
 * @property {String} parentId -父级别Id
 * @property {Array.<entityParamInfo>} children
 * @property {String} gmtCreate
 * @property {String} gmtUpdate
 */
const entityParamInfo = {
    name: "",
    paramType: "",
    array: false,
    paramDesc: "",
    defaultValue: "",
    required: true,
    baseType: true,
    childId: "",
    parentId: null,
    children: null,
};

/***
 * @typedef entityInfo
 * @property {String} id -实体ID
 * @property {String} linkerId -连接器ID
 * @property {String} entityName -实体名称
 * @property {String} entityCode -实体编码
 * @property {String} entityDesc -实体描述
 * @property {String} sqlView -sql语句
 * @property {Array.<{id: String, versionCode: String}>} linkerEntityRelationDTOS -已选version列表
 * @property {String} versionCodes -已选version codes 字符串 code1,code2,code3
 * @property {Array.<entityParamInfo>} entityParamDTOS -实体属性列表
 * @property {Array.<templateParam>} paramList -参数列表
 */
const entityInfo = {
    id: "",
    linkerId: "",
    entityName: "",
    entityCode: "",
    entityDesc: "",
    sqlView: "",
    linkerEntityRelationDTOS: [],
    versionCodes: "",
    entityParamDTOS: [],
    paramList: [],
};

/***
 * 连接器配置
 * @type {Object}
 * @property {String} pageType=[add|edit]
 * @property {String} linkerId -连接器Id
 * @property {String} templateId -模板ID
 * @property {templateInfo} templateInfo -模板信息
 * @property {String} paramEditModalType=[hide|add|edit] -编辑连接器参数Modal状态
 * @property {Number|String} configItemListParamIndex -编辑参数值选择列表的参数项索引
 * @property {templateParam} configItemListParamInfo -编辑参数值选择列表的参数项
 * @property {Array.<{id: String, code: String}>} versions -连接器版本列表
 * @property {Array.<{id: String, text: String, type: Boolean}>}
 *  entityParamTypes -实体属性类型列表
 *  实体属性类型 分为基础类型和实体类型 以 type 来区分 type 基本数据类型true,实体类型false
 */
const initState = {
    pageType: "add",
    entityId: "",
    linkerId: "",
    versions: [],
    entityParamTypes: [],
    entityInfo: entityInfo,
    entityInfoErrors: [],
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }

    @observable state = initState;

    init = () => (this.state = initState);

    setPageParams = (pageParams) => {
        const { matchParams, queryParams } = pageParams;
        this.state.pageType = matchParams.viewType;
        this.state.linkerId = queryParams.connectorId || "";
        this.state.entityId = queryParams.entityId || "";
    };

    /** 通过版本列表 生成 Select Data */
    @computed get versionSelectData() {
        return this.state.versions.map((item) => {
            return {
                key: item.code,
                value: item.code,
            };
        });
    }

    /** linkerEntityRelationDTOS 根据已选版本数组获取对应 id数组 供版本Select value 使用  */
    @computed get selectedVersionCodes() {
        const {
            entityInfo: { linkerEntityRelationDTOS },
        } = this.state;
        return linkerEntityRelationDTOS.map((item) => item.versionCode);
    }

    @computed get paramTypeSelectData() {
        return this.state.entityParamTypes.map((item) => {
            return { key: item.text, value: item.id };
        });
    }

    /** 获取版本列表 */
    getVersions = () => {
        this.dispatchService({
            service: getDesignCntVersionList({ linkerId: this.state.linkerId }),
            dataKey: "versions",
        });
    };

    /** 获取实体属性参数类型列表 */
    getEntityParamTypes = () => {
        this.dispatchService({
            service: ownerServices.getDesignEntityPropTypesService({
                linkId: this.state.linkerId,
                entityId: this.state.entityId,
            }),
            dataKey: "entityParamTypes",
        });
    };

    getEntityInfo = async (entityId) => {
        const res = await autoServiceMessage({
            service: ownerServices.getDesignCntEntityInfoService({
                id: entityId || this.state.entityId,
            }),
        });
        if (res && !entityId) {
            loopTree(res.data.entityParamDTOS, (item, itemIndex, dataArray, parent) => {
                if (parent) {
                    item.parentId = parent.id;
                }
            });
            this.state.entityInfo = res.data;
        }
        return res;
    };

    changeEntityInfo = (prop, value) => {
        if (prop === "linkerEntityRelationDTOS") {
            value = value.map((code) => {
                return {
                    linkId: this.state.linkerId,
                    versionCode: code,
                };
            });
        }
        this.state.entityInfo[prop] = value;
    };

    /**
     * 通过sql生成参数
     * @param sql
     * @return {Promise<void>}
     */
    parseSqlToEntityParams = async (sql) => {
        const data = new FormData();
        data.append("sql", sql);
        const res = await autoServiceMessage({
            service: ownerServices.parseSqlToDesignCntEntityParamService(data),
        });
        if (res) {
            let entityParamDTOS = res.data || [];
            entityParamDTOS.forEach((item) => {
                item.id = getUuid();
                item.add = true;
            });
            this.state.entityInfo.entityParamDTOS = entityParamDTOS;
        }
        return res;
    };

    getNewParam = (parentId = null) => {
        return {
            ...entityParamInfo,
            id: getUuid(),
            parentId: parentId,
            entityId: this.state.entityId,
            add: true,
        };
    };

    findParam = (entityParams, paramId) => {
        let param = null,
            paramIndex = null;
        loopTree(entityParams, (item, index) => {
            const isParam = item.id === paramId;
            if (isParam) {
                param = item;
                paramIndex = index;
            }
            return !isParam;
        });
        return {
            paramIndex,
            param,
        };
    };

    /***
     * 参数定义新增，删除动作
     * @param {String} type=[add|addChildren|delete] -动作类型
     * @param {Number} paramInfo -操作参数索引
     */
    @action
    configParamAction = async (type, paramInfo) => {
        const {
            entityInfo: { entityParamDTOS },
            linkerId,
            entityId,
        } = this.state;
        switch (type) {
            case "add":
                entityParamDTOS.push(this.getNewParam());
                break;
            case "addChildren":
                const { param } = this.findParam(entityParamDTOS, paramInfo.id);
                const newParam = this.getNewParam(paramInfo.id);
                if (!param.children) {
                    param.children = observable.array([]);
                }
                param.children.push(newParam);
                break;
            case "delete":
                if (!paramInfo.add) {
                    let validateDelRes = await autoServiceMessage({
                        service: ownerServices.validateDesignCntEntityParamDeleteService({
                            id: paramInfo.id,
                        }),
                    });
                    if (validateDelRes && validateDelRes.data && !validateDelRes.data.validate) {
                        Error(validateDelRes.data.messageInfo);
                        return false;
                    }
                }
                if (paramInfo.parentId) {
                    const { param: parentParam } = this.findParam(entityParamDTOS, paramInfo.parentId);
                    const paramIndex = _findIndex(parentParam.children, (item) => {
                        return (item.id = paramInfo.id);
                    });
                    parentParam.children.splice(paramIndex, 1);
                    if (parentParam.children.length <= 0) {
                        parentParam.children = null;
                    }
                } else {
                    const paramIndex = _findIndex(entityParamDTOS, (item) => {
                        return item.id === paramInfo.id;
                    });
                    entityParamDTOS.splice(paramIndex, 1);
                }
                break;
        }
    };

    /***
     * 修改参数信息
     * @param {entityParamInfo} param -参数项索引
     * @param {String} prop -修改信息 key
     * @param {*} value -信息值
     * */
    @action
    changeParamInfoAction = async (param, prop, value) => {
        const {
            entityInfo: { entityParamDTOS },
            entityParamTypes,
        } = this.state;
        const { param: _param } = this.findParam(entityParamDTOS, param.id);
        if (prop === "paramType") {
            const paramType = entityParamTypes.find((item) => item.id === value);
            if (paramType) {
                const { id, type } = paramType;
                const validateRes = await autoServiceMessage({
                    service: ownerServices.validateDesignCntEntityParamTypeChangeService({
                        entityId: this.state.entityId,
                        id: _param.id,
                        baseType: _param.baseType,
                        paramType: id,
                    }),
                });
                if (validateRes && validateRes.data && !validateRes.data.validate) {
                    Error(validateRes.data.messageInfo);
                    return false;
                }
                let childId = null,
                    children = null;
                if (!type) {
                    const res = await this.getEntityInfo(id);
                    if (res) {
                        const { entityParamDTOS = [] } = res.data || {};
                        loopTree(
                            entityParamDTOS,
                            (item, index, treeArray, parent) => {
                                if (parent) {
                                    item.parentId = parent.id;
                                }
                                return true;
                            },
                            _param
                        );
                        childId = id;
                        children = entityParamDTOS;
                    }
                }
                Object.assign(_param, { childId, children, baseType: type, [prop]: value });
            }
        } else {
            _param[prop] = value;
        }
    };

    saveAction = async () => {
        const { entityInfo } = this.state;
        const _entityInfo = this.toJS(entityInfo);
        const descriptor = {
            entityCode: { required: true, message: lang.templateByUuid("UID:P_UBL-FE_2009604004280400", "实体编码不能为空") /* "实体编码不能为空" */ },
            entityName: { required: true, message: lang.templateByUuid("UID:P_UBL-FE_2009604004280401", "实体名称不能为空") /* "实体名称不能为空" */ },
            entityParamDTOS: {
                fields: {
                    name: { required: true, message: lang.templateByUuid("UID:P_UBL-FE_2009604004280402", "属性编码不能为空") /* "属性编码不能为空" */ },
                    paramType: { required: true, message: lang.templateByUuid("UID:P_UBL-FE_2009604004280403", "属性类型不能为空") /* "属性类型不能为空" */ },
                    paramDesc: { required: true, message: lang.templateByUuid("UID:P_UBL-FE_20096040042803FF", "属性描述不能为空") /* "属性描述不能为空" */ },
                },

                validator: (rule, value, callback, source) => {
                    const { fields, field } = rule;
                    let errors = [];
                    value.forEach((item, index) => {
                        let parentField = `${field}${item.parentId ? `.${item.parentId}` : ""}.${index}`;
                        validate(fields, item, (_errors) => {
                            if (errors) {
                                _errors.forEach((error) => {
                                    error.field = `${parentField}.${error.field}`;
                                    errors.push(error);
                                });
                            }
                        });
                    });
                    return errors;
                },
            },
        };

        const errors = await validate(descriptor, _entityInfo);

        if (errors) {
            this.state.entityInfoErrors = errors;
        } else {
            _entityInfo.linkId = this.state.linkerId;
            _entityInfo.entityParamDTOS.forEach((item, index) => {
                if (item.add) {
                    delete item.id;
                    delete item.add;
                }
                item.children = null;
                item.order = index;
            });

            const res = await autoServiceMessage({
                service: ownerServices.saveDesignCntEntityService(_entityInfo),
                success: window.lang.template(commonText.saveSuccess),
            });
            return res;
        }
    };
}

/**
 * 根据具体模块修改 storeKey 为保证唯一性
 * storeKey 由 模块文件夹名称 + 页面名称 + 'Store' 小驼峰
 *
 * storeKey是作为当前 Store 的唯一键， storeKey与Store的实例会在 rootStore或者StoreMap中以 键值对的 形式存在
 * 1. 用于在模块的 storeMap（模块routes/store.js）中注册并初始化当前Store
 * 2. 用于在container的 inject 中关联当前Store的实例时，从模块的storeMap或者rootStore中获取对应store实例
 * */
export const storeKey = "designConnectorEntityConfigStore";
export default Store;
