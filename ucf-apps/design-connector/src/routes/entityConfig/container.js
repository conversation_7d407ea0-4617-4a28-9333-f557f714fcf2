/***
 * 模板配置页面
 * 页面路由参数
 * match.params.viewType=[add|edit] -页面类型
 * location.search.entityId -实体Id
 * location.search.connectorId -连接器Id
 * */

import React, { Component } from "react";
import IndexView from "./components/IndexView";
import { inject, observer } from "mobx-react";
import Store, { storeKey } from "./store";
import core from "core";
import { getPageParams } from "decorator/index";
core.addStore({
    storeKey: storeKey,
    store: new Store(),
});

@inject((store) => {
    const ownerStore = store[storeKey];
    return {
        ownerStore,
        ownerState: ownerStore.toJS(),
    };
})
@observer
@getPageParams
class Container extends Component {
    constructor(props) {
        super(props);
        this.pageParams = this.props.getPageParams(props, true);
    }

    render() {
        return <IndexView {...this.props} {...this.pageParams} />;
    }
}

export default Container;
