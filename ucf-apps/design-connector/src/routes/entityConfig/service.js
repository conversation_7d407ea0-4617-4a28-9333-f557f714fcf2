import { getInvokeService } from "utils/service";

/***
 * 获取实体属性字段类型下拉列表
 * @param {Object} data
 * @param {String} data.linkerId
 * @param {String=} data.versionCodes
 * @param {String} data.entityId
 * @return {*}
 */
export const getDesignEntityPropTypesService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/designer/entity/select2",
        },
        data
    );
};

/***
 * 根据实体ID获取实体详情方法
 * @param {Object} data
 * @param {String} data.id -实体ID
 * @return {*}
 */
export const getDesignCntEntityInfoService = function (data) {
    return getInvokeService(
        {
            method: "GET",
            path: "/designer/entity/getEntityById",
        },
        data
    );
};

/**
 * 保存实体
 * @param {templateInfo} data
 * @return {*}
 */
export const saveDesignCntEntityService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/designer/entity/saveEntity",
        },
        data
    );
};

/**
 * 校验实体参数是否可删除
 * @param {Object} data
 * @param {String} data.id
 * @return {Promise<unknown>}
 */
export const validateDesignCntEntityParamDeleteService = function (data) {
    return getInvokeService(
        {
            method: "GET",
            path: "/designer/EntityParam/paramDeleteValidateById",
        },
        data
    );
};

/***
 * 校验实体参数类型是否可修改
 * @param {Object} data
 * @param {String} data.entityId -实体id
 * @param {String} data.id -param.id 当前参数id
 * @param {Boolean} data.baseType -当前参数是否为基本数据类型
 * @param {String} data.paramType -要修改的参数类型
 * @return {Promise<unknown>}
 */
export const validateDesignCntEntityParamTypeChangeService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/designer/EntityParam/paramChangeValidate",
        },
        data
    );
};

/***
 * 根据Sql生成实体参数列表
 * @param {FormData} data
 * @param {String} data.sql
 * @return {Promise<unknown>}
 */
export const parseSqlToDesignCntEntityParamService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/designer/entity/parseQuerySqlParam",
        },
        data
    );
};
