import _template from "lodash/template";
import commonText from "constants/commonText";
export const connectorRules = {
    name: [
        {
            required: true,
            message: _template(commonText.isRequired)({ prop: lang.templateByUuid("UID:P_UBL-FE_200960400428048B", "连接器名称") /* "连接器名称" */ }),
        },
    ],
    code: [
        {
            required: true,
            message: _template(commonText.isRequired)({ prop: lang.templateByUuid("UID:P_UBL-FE_200960400428048C", "连接器编码") /* "连接器编码" */ }),
        },
    ],
};
