import React, { Component, Fragment } from "react";
import { Header, Content, Footer } from "components/PageView";
import FieldWrap from "components/RowField/FieldWrap";
import { getInfoError, getPageParams, initStore } from "decorator/index";
import { Button, FormControl, Select, Popconfirm } from "components/TinperBee";
import DotSwitch from "components/DotSwitch";
import Grid from "components/TinperBee/Grid";
import SqlModal from "../SqlModal";
import EntityInfoForm from "../EntityInfoForm";
import { getConfigCntFormStorage } from "../../../../utils";

import "./index.less";
import withRouter from "decorator/withRouter";

const expandIconStyles = { verticalAlign: "text-top", marginRight: "8px" };
const CollapsedIcon = <i fieldid="ublinker-routes-entityConfig-components-IndexView-index-1238723-i" className="cl cl-add" style={expandIconStyles} />;
const ExpandedIcon = <i fieldid="ublinker-routes-entityConfig-components-IndexView-index-4114442-i" className="cl cl-Minus_sign" style={expandIconStyles} />;

@withRouter
@getPageParams
@initStore()
@getInfoError
class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            hoverRowIndex: "",
            hoverRowRecord: null,
            sqlModalShow: false,
        };
        this.pageTitle = this.getPageTitle();
        this.configCntInfo = getConfigCntFormStorage();
    }

    titles = {
        add: lang.templateByUuid("UID:P_UBL-FE_20096040042802DD", "新增") /* "新增" */,
        edit: lang.templateByUuid("UID:P_UBL-FE_20096040042802DE", "编辑") /* "编辑" */,
    };

    getPageTitle = () => {
        const { matchParams } = this.props;
        return this.titles[matchParams.viewType];
    };

    handleCancel = () => {
        this.props.navigate(-1);
    };

    handleSave = async () => {
        const { ownerStore } = this.props;
        const res = await ownerStore.saveAction();
        res && this.handleCancel();
    };

    isHoverRow = (record) => {
        const { hoverRowRecord } = this.state;
        return hoverRowRecord && hoverRowRecord.id === record.id;
    };

    getFieldError = (record, index, field) => {
        const {
            ownerState: { entityInfoErrors },
        } = this.props;
        const errorField = `entityParamDTOS${record.parentId ? `.${record.parentId}` : ""}.${index}.${field}`;
        return this.getInfoError(entityInfoErrors, errorField);
    };

    notParent = (record) => {
        return !record.parentId;
    };

    renderText = (value, record, index, column) => {
        const { dataIndex } = column;
        const { ownerStore } = this.props;
        const errorMsg = this.getFieldError(record, index, dataIndex);
        if (this.notParent(record) && (this.isHoverRow(record) || errorMsg)) {
            return (
                <FieldWrap message={errorMsg}>
                    <FormControl
                        fieldid="ublinker-routes-entityConfig-components-IndexView-index-6847220-FormControl"
                        size="xs"
                        value={value}
                        onChange={(value) => {
                            ownerStore.changeParamInfoAction(record, dataIndex, value);
                        }}
                    />
                </FieldWrap>
            );
        } else {
            return value;
        }
    };

    renderSelect = (selectData, value, record, index, column) => {
        const { dataIndex } = column;
        const { ownerStore } = this.props;
        const errorMsg = this.getFieldError(record, index, dataIndex);
        if (this.notParent(record) && (this.isHoverRow(record) || errorMsg)) {
            return (
                <FieldWrap message={errorMsg}>
                    <Select
                        fieldid="ublinker-routes-entityConfig-components-IndexView-index-2225760-Select"
                        data={selectData}
                        className="u-select-xs"
                        value={value}
                        onChange={(value) => {
                            ownerStore.changeParamInfoAction(record, dataIndex, value);
                        }}
                    />
                </FieldWrap>
            );
        } else {
            const selected = selectData.find((item) => item.value === value);
            return selected ? selected.key : "";
        }
    };

    renderDotSwitch = (value, record, index, column) => {
        const { dataIndex } = column;
        const { ownerStore } = this.props;
        return (
            <DotSwitch
                size="xs"
                checked={value}
                noEvent={!this.notParent(record) || !this.isHoverRow(record)}
                onChange={(value) => {
                    ownerStore.changeParamInfoAction(record, dataIndex, value);
                }}
            />
        );
    };

    paramColumns = [
        {
            title: <span className="mix-required">{lang.templateByUuid("UID:P_UBL-FE_20096040042802E0", "编码") /* "编码" */}</span>,
            dataIndex: "name",
            width: 250,
            render: this.renderText,
        },
        {
            title: <span className="mix-required">{lang.templateByUuid("UID:P_UBL-FE_20096040042802E3", "类型") /* "类型" */}</span>,
            dataIndex: "paramType",
            render: (...arg) => {
                const { ownerStore } = this.props;
                return this.renderSelect(ownerStore.paramTypeSelectData, ...arg);
            },
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042802E8", "是否数组") /* "是否数组" */,
            dataIndex: "array",
            width: 100,
            render: this.renderDotSwitch,
        },
        {
            title: <span className="mix-required">{lang.templateByUuid("UID:P_UBL-FE_20096040042802EA", "描述") /* "描述" */}</span>,
            dataIndex: "paramDesc",
            render: this.renderText,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042802DC", "默认值") /* "默认值" */,
            dataIndex: "defaultValue",
            render: this.renderText,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042802DF", "是否必填") /* "是否必填" */,
            dataIndex: "required",
            width: 100,
            render: this.renderDotSwitch,
        },
        {
            title: "",
            dataIndex: "$$actions",
        },
    ];

    handleConfigParam = (type) => {
        const { ownerStore } = this.props;
        const { hoverRowRecord, hoverRowIndex } = this.state;
        ownerStore.configParamAction(type, hoverRowRecord, hoverRowIndex);
    };

    handleAddConfigParam = () => {
        this.handleConfigParam("add");
    };

    handleAddChildren = () => {
        this.handleConfigParam("addChildren");
    };

    handleDeleteParam = () => {
        this.handleConfigParam("delete");
    };

    hoverContent = () => {
        const { hoverRowRecord, hoverContentShow } = this.state;
        if (hoverContentShow && hoverRowRecord && this.notParent(hoverRowRecord)) {
            const { paramType } = hoverRowRecord;
            return (
                <Fragment>
                    {paramType === "object" ? (
                        <Button
                            fieldid="ublinker-routes-entityConfig-components-IndexView-index-4201712-Button"
                            {...Grid.hoverButtonPorps}
                            onClick={this.handleAddChildren}
                        >
                            {lang.templateByUuid("UID:P_UBL-FE_20096040042802E2", "增加子行") /* "增加子行" */}
                        </Button>
                    ) : null}
                    <Popconfirm
                        fieldid="ublinker-routes-entityConfig-components-IndexView-index-3115603-Popconfirm"
                        content={lang.templateByUuid("UID:P_UBL-FE_20096040042802E4", "确认删除此参数？") /* "确认删除此参数？" */}
                        rootClose
                        onClose={this.handleDeleteParam}
                        key={hoverRowRecord.id}
                    >
                        <Button fieldid="ublinker-routes-entityConfig-components-IndexView-index-3529784-Button" {...Grid.hoverButtonPorps}>
                            {lang.templateByUuid("UID:P_UBL-FE_20096040042802E6", "删除") /* "删除" */}
                        </Button>
                    </Popconfirm>
                </Fragment>
            );
        } else {
            return null;
        }
    };

    handleHoverRow = (index, record) => {
        this.setState({ hoverRowIndex: index, hoverRowRecord: record, hoverContentShow: true });
    };

    handleBodyMouseLeave = () => {
        this.setState({
            hoverContentShow: false,
        });
    };

    changeSqlModalShow = () => {
        this.setState({ sqlModalShow: !this.state.sqlModalShow });
    };

    handleSqlModalOk = async (sql) => {
        const { ownerStore } = this.props;
        const res = await ownerStore.parseSqlToEntityParams(sql);
        if (res) {
            this.changeSqlModalShow();
        }
    };

    EntityPropsGridHeader = (
        <>
            <Button fieldid="ublinker-routes-entityConfig-components-IndexView-index-677627-Button" bordered onClick={this.handleAddConfigParam}>
                {lang.templateByUuid("UID:P_UBL-FE_20096040042802DD", "新增") /* "新增" */}
            </Button>
            <Button
                fieldid="ublinker-routes-entityConfig-components-IndexView-index-5748587-Button"
                bordered
                className="ucg-mar-l-sm"
                onClick={this.changeSqlModalShow}
            >
                {lang.templateByUuid("UID:P_UBL-FE_20096040042802E9", "SQL生成") /* "SQL生成" */}
            </Button>
        </>
    );

    render() {
        const { ownerState, ownerStore } = this.props;

        const { changeEntityInfo } = ownerStore;

        const { entityInfo, entityInfoErrors } = ownerState;

        const { entityParamDTOS } = entityInfo;

        return (
            <Fragment>
                <Header title={this.pageTitle} />
                <Content hasFooter>
                    <EntityInfoForm
                        entityInfo={entityInfo}
                        getInfoError={this.getInfoError.bind(null, entityInfoErrors)}
                        connectorName={this.configCntInfo.name}
                        onChange={changeEntityInfo}
                        ownerStore={ownerStore}
                    />

                    <Grid
                        fieldid="ublinker-routes-entityConfig-components-IndexView-index-6837380-Grid"
                        title={lang.templateByUuid("UID:P_UBL-FE_20096040042802E1", "实体属性") /* "实体属性" */}
                        rowKey="id"
                        header={this.EntityPropsGridHeader}
                        rowClassName={this._rowClassName}
                        onRowHover={this.handleHoverRow}
                        onBodyMouseLeave={this.handleBodyMouseLeave}
                        hoverContent={this.hoverContent}
                        data={entityParamDTOS}
                        columns={this.paramColumns}
                        collapsedIcon={CollapsedIcon}
                        expandedIcon={ExpandedIcon}
                    />
                </Content>

                <Footer align="right">
                    <Button
                        fieldid="ublinker-routes-entityConfig-components-IndexView-index-8954434-Button"
                        className="ucg-mar-r-sm"
                        onClick={this.handleCancel}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_20096040042802E5", "取消") /* "取消" */}
                    </Button>
                    <Button fieldid="ublinker-routes-entityConfig-components-IndexView-index-5115518-Button" colors="primary" onClick={this.handleSave}>
                        {lang.templateByUuid("UID:P_UBL-FE_20096040042802E7", "保存") /* "保存" */}
                    </Button>
                </Footer>

                <SqlModal show={this.state.sqlModalShow} sqlView={entityInfo.sqlView} onCancel={this.changeSqlModalShow} onOk={this.handleSqlModalOk} />
            </Fragment>
        );
    }

    componentDidMount() {
        const {
            ownerStore,
            queryParams: { entityId },
        } = this.props;
        ownerStore.getVersions();
        ownerStore.getEntityParamTypes();
        if (!!entityId) {
            ownerStore.getEntityInfo();
        }
    }
}

export default IndexView;
