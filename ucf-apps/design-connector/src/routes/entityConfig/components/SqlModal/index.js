import React, { useState, useCallback, useLayoutEffect } from "react";
import ModelView from "components/TinperBee/Modal";
// import FormList from "components/TinperBee/Form";
import { FormControl, FormList } from "components/TinperBee";

const FormItem = FormList.Item;

const SqlModal = (props) => {
    const { sqlView = "", onCancel, onOk, show } = props;

    const [selfSqlView, setSelfSqlView] = useState("");

    useLayoutEffect(() => {}, [sqlView]);

    const inputs = [selfSqlView];

    const handleOk = useCallback(() => {
        onOk(selfSqlView);
    }, inputs);

    const handleChange = useCallback((value) => {
        setSelfSqlView(value);
    }, inputs);

    return (
        <ModelView
            show={show}
            title={lang.templateByUuid("UID:P_UBL-FE_2009604004280366", "SQL生成") /* "SQL生成" */}
            width="520px"
            onCancel={onCancel}
            onOk={handleOk}
            okDisabled={!selfSqlView}
        >
            <FormList fieldid="ublinker-routes-entityConfig-components-SqlModal-index-6033178-FormList" size="sm" className="ucg-pad-20" layoutOpt={{ md: 10 }}>
                <FormItem
                    fieldid="ublinker-routes-entityConfig-components-SqlModal-index-5057490-FormItem"
                    label={lang.templateByUuid("UID:P_UBL-FE_2009604004280367", "内容") /* "内容" */}
                    labelAlign={"top"}
                >
                    <FormControl.TextArea
                        rows={4}
                        placeholder={lang.templateByUuid("UID:P_UBL-FE_2009604004280368", "请输入") /* "请输入" */}
                        value={selfSqlView}
                        onChange={handleChange}
                        // componentClass="textarea"
                        style={{ height: "100px" }}
                        // autoSize={{minRows: 4}}
                    />
                </FormItem>
            </FormList>
        </ModelView>
    );
};

export default SqlModal;
