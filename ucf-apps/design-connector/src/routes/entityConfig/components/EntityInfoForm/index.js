import React from "react";
import { FormControl, Select, FormList } from "components/TinperBee";
// import FormList from "components/TinperBee/Form";

const FormItem = FormList.Item;

const labelCol = 100;

function EntityInfoForm(props) {
    const { entityInfo, connectorName, onChange, getInfoError, ownerStore } = props;
    const { entityCode, entityName, entityDesc } = entityInfo;

    return (
        <FormList
            fieldid="ublinker-routes-entityConfig-components-EntityInfoForm-index-5818218-FormList"
            className="ucg-pad-sm"
            size="sm"
            layoutOpt={{ md: 4 }}
            title={lang.templateByUuid("UID:P_UBL-FE_2009604004280272", "基本信息") /* "基本信息" */}
        >
            <FormItem
                fieldid="ublinker-routes-entityConfig-components-EntityInfoForm-index-7262199-FormItem"
                label={lang.templateByUuid("UID:P_UBL-FE_2009604004280273", "实体编码") /* "实体编码" */}
                required
                labelCol={labelCol}
                error={getInfoError("entityCode")}
            >
                <FormControl
                    fieldid="ublinker-routes-entityConfig-components-EntityInfoForm-index-521801-FormControl"
                    placeholder={lang.templateByUuid("UID:P_UBL-FE_200960400428026D", "请输入") /* "请输入" */}
                    value={entityCode}
                    onChange={onChange.bind(null, "entityCode")}
                />
            </FormItem>

            <FormItem
                fieldid="ublinker-routes-entityConfig-components-EntityInfoForm-index-7732073-FormItem"
                label={lang.templateByUuid("UID:P_UBL-FE_200960400428026E", "实体名称") /* "实体名称" */}
                required
                labelCol={labelCol}
                error={getInfoError("entityName")}
            >
                <FormControl
                    fieldid="ublinker-routes-entityConfig-components-EntityInfoForm-index-722435-FormControl"
                    placeholder={lang.templateByUuid("UID:P_UBL-FE_200960400428026D", "请输入") /* "请输入" */}
                    value={entityName}
                    onChange={onChange.bind(null, "entityName")}
                />
            </FormItem>

            <FormItem
                fieldid="ublinker-routes-entityConfig-components-EntityInfoForm-index-2071372-FormItem"
                label={lang.templateByUuid("UID:P_UBL-FE_200960400428026F", "连接器") /* "连接器" */}
                labelCol={labelCol}
            >
                <FormControl fieldid="ublinker-routes-entityConfig-components-EntityInfoForm-index-4654872-FormControl" value={connectorName} disabled />
            </FormItem>

            <FormItem
                fieldid="ublinker-routes-entityConfig-components-EntityInfoForm-index-9210351-FormItem"
                label={lang.templateByUuid("UID:P_UBL-FE_2009604004280270", "版本") /* "版本" */}
                labelCol={labelCol}
            >
                <Select
                    fieldid="ublinker-routes-entityConfig-components-EntityInfoForm-index-7290793-Select"
                    multiple
                    placeholder={lang.templateByUuid("UID:P_UBL-FE_2009604004280271", "请选择") /* "请选择" */}
                    size="sm"
                    value={ownerStore.selectedVersionCodes}
                    data={ownerStore.versionSelectData}
                    onChange={onChange.bind(null, "linkerEntityRelationDTOS")}
                />
            </FormItem>

            <FormItem
                fieldid="ublinker-routes-entityConfig-components-EntityInfoForm-index-5834306-FormItem"
                label={lang.templateByUuid("UID:P_UBL-FE_2009604004280274", "实体描述") /* "实体描述" */}
                labelCol={labelCol}
            >
                <FormControl
                    fieldid="ublinker-routes-entityConfig-components-EntityInfoForm-index-2052853-FormControl"
                    placeholder={lang.templateByUuid("UID:P_UBL-FE_200960400428026D", "请输入") /* "请输入" */}
                    value={entityDesc}
                    onChange={onChange.bind(null, "entityDesc")}
                />
            </FormItem>
        </FormList>
    );
}

export default EntityInfoForm;
