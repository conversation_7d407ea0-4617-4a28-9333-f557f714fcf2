import _template from "lodash/template";
import commonText from "constants/commonText";
export const connectorRules = {
    name: [
        {
            required: true,
            message: _template(commonText.isRequired)({ prop: lang.templateByUuid("UID:P_UBL-FE_2009604004280258", "连接器名称") /* "连接器名称" */ }),
        },
    ],
    code: [
        {
            required: true,
            message: _template(commonText.isRequired)({ prop: lang.templateByUuid("UID:P_UBL-FE_200960400428025A", "连接器编码") /* "连接器编码" */ }),
        },
    ],
    adapterCode: [
        {
            required: true,
            message: _template(commonText.isRequired)({ prop: lang.templateByUuid("UID:P_UBL-FE_2009604004280256", "适配器编码") /* "适配器编码" */ }),
        },
    ],
    isv: [
        {
            required: true,
            message: _template(commonText.isRequired)({ prop: lang.templateByUuid("UID:P_UBL-FE_2009604004280259", "归属厂商") /* "归属厂商" */ }),
        },
    ],
    product: [
        {
            required: true,
            message: _template(commonText.isRequired)({ prop: lang.templateByUuid("UID:P_UBL-FE_200960400428025B", "连接产品") /* "连接产品" */ }),
        },
    ],
    linkTypes: [
        {
            required: true,
            type: "array",
            message: lang.templateByUuid("UID:P_UBL-FE_2009604004280257", "请选择至少一项连接类型") /* "请选择至少一项连接类型" */,
        },
    ],
};
