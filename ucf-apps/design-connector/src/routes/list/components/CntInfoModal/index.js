/*
 * @Author: your name
 * @Date: 2021-07-19 17:02:25
 * @LastEditTime: 2021-07-20 14:38:06
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: \ublinker-fe\ucf-apps\design-connector\src\routes\list\components\CntInfoModal\index.js
 */
import React, { useEffect } from "react";
import { FormControl, Select, Checkbox, Switch, FormList } from "components/TinperBee";
import useEditModal from "components/TinperBee/Form/useEditModal";
// import FormList from "components/TinperBee/Form";
import ModalView from "components/TinperBee/Modal";
import { connectorRules } from "./form";
import { connectorTypesEnum } from "design-connector/constants/types";
import FormSelectLogo from "components/LogoSelect/SelectItem";
import businessLogos from "design-connector/constants/businessLogos";
import technologyLogos from "design-connector/constants/technologyLogos";
import { getCompleteImg } from "utils";

//根据连接器类型 0 1 取不通下标的logo列表
const logos = [businessLogos, technologyLogos];

const FormItem = FormList.Item;

/**
 * 编辑链接器基本信息
 * @param {Object} props
 * @returns {*}
 * @constructor
 */
const CntInfoModal = (props) => {
    const [form] = FormList.useForm();
    const { formValues } = props;

    const formProps = ["name", "code", "adapterCode", "linkerType", "testable", "logo", "description", "isv", "product"];

    const { show, title, onCancel, onOk } = useEditModal({ ...props, form }, formProps);

    useEffect(() => {
        if (formValues) {
            const { startLinker, endLinker } = formValues;
            let linkTypes = [];
            if (startLinker) {
                linkTypes.push("startLinker");
            }
            if (endLinker) {
                linkTypes.push("endLinker");
            }
            form.setFieldsValue({
                linkTypes: linkTypes,
            });
        }
    }, [formValues]);

    const { name, code, adapterCode, linkerType, testable, logo, description } = formValues;

    // const logoFormProps = getFieldProps('logo', {
    // 	initialValue: logo
    // })
    const formItemLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 12 },
    };
    return (
        <ModalView title={title} show={show} onCancel={onCancel} onOk={onOk} width="520px">
            <FormList
                fieldid="ublinker-routes-list-components-CntInfoModal-index-5982324-FormList"
                size="sm"
                className="ucg-pad-20"
                form={form}
                name="form122"
                layoutOpt={{ md: 10 }}
                {...formItemLayout}
                // itemLabelPosition={'top'}
            >
                <FormItem
                    fieldid="ublinker-routes-list-components-CntInfoModal-index-2944742-FormItem"
                    label={lang.templateByUuid("UID:P_UBL-FE_20096040042802BB", "连接器名称") /* "连接器名称" */}
                    name="name"
                    rules={connectorRules.name}
                    initialValue={name}
                >
                    <FormControl
                        fieldid="ublinker-routes-list-components-CntInfoModal-index-532861-FormControl"
                        placeholder={lang.templateByUuid("UID:P_UBL-FE_20096040042802B7", "请输入") /* "请输入" */}
                    />
                </FormItem>

                <FormItem
                    fieldid="ublinker-routes-list-components-CntInfoModal-index-4705656-FormItem"
                    label={lang.templateByUuid("UID:P_UBL-FE_20096040042802BC", "连接器编码") /* "连接器编码" */}
                    name="code"
                    rules={connectorRules.code}
                    initialValue={code}
                >
                    <FormControl
                        fieldid="ublinker-routes-list-components-CntInfoModal-index-7545694-FormControl"
                        placeholder={lang.templateByUuid("UID:P_UBL-FE_20096040042802B7", "请输入") /* "请输入" */}
                    />
                </FormItem>
                <FormItem
                    fieldid="ublinker-routes-list-components-CntInfoModal-index-5764301-FormItem"
                    label={lang.templateByUuid("UID:P_UBL-FE_20096040042802BE", "适配器编码") /* "适配器编码" */}
                    name="adapterCode"
                    rules={connectorRules.adapterCode}
                    initialValue={adapterCode}
                >
                    <FormControl
                        fieldid="ublinker-routes-list-components-CntInfoModal-index-76628-FormControl"
                        placeholder={lang.templateByUuid("UID:P_UBL-FE_20096040042802B7", "请输入") /* "请输入" */}
                    />
                </FormItem>
                <FormItem
                    fieldid="ublinker-routes-list-components-CntInfoModal-index-8228081-FormItem"
                    label={lang.templateByUuid("UID:P_UBL-FE_20096040042802BF", "归属厂商") /* "归属厂商" */}
                    name="isv"
                    rules={connectorRules.isv}
                    initialValue={adapterCode}
                >
                    <FormControl
                        fieldid="ublinker-routes-list-components-CntInfoModal-index-3922627-FormControl"
                        placeholder={lang.templateByUuid("UID:P_UBL-FE_20096040042802B7", "请输入") /* "请输入" */}
                    />
                </FormItem>
                <FormItem
                    fieldid="ublinker-routes-list-components-CntInfoModal-index-7916429-FormItem"
                    label={lang.templateByUuid("UID:P_UBL-FE_20096040042802C0", "连接产品") /* "连接产品" */}
                    name="product"
                    rules={connectorRules.product}
                    initialValue={adapterCode}
                >
                    <FormControl
                        fieldid="ublinker-routes-list-components-CntInfoModal-index-4866603-FormControl"
                        placeholder={lang.templateByUuid("UID:P_UBL-FE_20096040042802B7", "请输入") /* "请输入" */}
                    />
                </FormItem>

                <FormItem
                    fieldid="ublinker-routes-list-components-CntInfoModal-index-7161768-FormItem"
                    label={lang.templateByUuid("UID:P_UBL-FE_20096040042802C2", "连接器类型") /* "连接器类型" */}
                    name="linkerType"
                    initialValue={linkerType + ""}
                >
                    <Select fieldid="ublinker-routes-list-components-CntInfoModal-index-9193941-Select" size="sm" data={connectorTypesEnum.selectData} />
                </FormItem>

                <FormItem
                    fieldid="ublinker-routes-list-components-CntInfoModal-index-7958440-FormItem"
                    label={lang.templateByUuid("UID:P_UBL-FE_20096040042802B8", "是否支持测试") /* "是否支持测试" */}
                    name="testable"
                    initialValue={testable}
                    valuePropName={"checked"}
                >
                    <Switch fieldid="ublinker-routes-list-components-CntInfoModal-index-5185355-Switch" size="sm" colors="blue" />
                </FormItem>

                <FormItem fieldid="ublinker-routes-list-components-CntInfoModal-index-9056481-FormItem" name="linkTypes" rules={connectorRules.linkTypes}>
                    <Checkbox.Group fieldid="UCG-FE-routes-list-components-CntInfoModal-index-6084827-Checkbox.Group">
                        <Checkbox fieldid="ublinker-routes-list-components-CntInfoModal-index-7222173-Checkbox" value="startLinker">
                            {lang.templateByUuid("UID:P_UBL-FE_20096040042802B9", "开始连接器") /* "开始连接器" */}
                        </Checkbox>
                        <Checkbox fieldid="ublinker-routes-list-components-CntInfoModal-index-6003853-Checkbox" value="endLinker">
                            {lang.templateByUuid("UID:P_UBL-FE_20096040042802BA", "结束连接器") /* "结束连接器" */}
                        </Checkbox>
                    </Checkbox.Group>
                </FormItem>

                <div layout={{ width: "100%", className: "clearfix" }} />
                <FormItem
                    fieldid="ublinker-routes-list-components-CntInfoModal-index-5003720-FormItem"
                    label={lang.templateByUuid("UID:P_UBL-FE_20096040042802BD", "应用图标") /* "应用图标" */}
                    labelAlign={"top"}
                    name="logo"
                    initialValue={logo}
                >
                    <FormSelectLogo
                        // logos={logos[form.getFieldValue('linkerType')]}
                        logos={logos[linkerType]}
                    />
                </FormItem>
                <FormItem
                    fieldid="ublinker-routes-list-components-CntInfoModal-index-3700512-FormItem"
                    label={lang.templateByUuid("UID:P_UBL-FE_20096040042802C1", "描述") /* "描述" */}
                    // layout={{md: 10}}
                    labelAlign={"top"}
                    name="description"
                    initialValue={description}
                >
                    <FormControl.TextArea
                        placeholder={lang.templateByUuid("UID:P_UBL-FE_20096040042802B7", "请输入") /* "请输入" */}
                        style={{ height: "auto" }}
                        rows={4}
                    />
                </FormItem>
            </FormList>
        </ModalView>
    );
};

export default CntInfoModal;
