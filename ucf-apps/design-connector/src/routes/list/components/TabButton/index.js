import React, { useMemo, useCallback } from "react";
import { Button } from "components/TinperBee";
import "./index.less";

const TabButtons = (props) => {
    let { activeKey, onTabClick, tabs } = props;

    const handleTabClick = useCallback(
        (key, notActive) => {
            if (typeof onTabClick === "function" && notActive) {
                onTabClick(key);
            }
        },
        [activeKey]
    );

    const ButtonNodes = useMemo(() => {
        return tabs.map((item) => {
            const { key, name } = item;
            const noActive = activeKey !== key;
            return (
                <Button
                    fieldid="ublinker-routes-list-components-TabButton-index-4900857-Button"
                    key={key}
                    colors="dark"
                    bordered={noActive}
                    onClick={handleTabClick.bind(null, key)}
                >
                    {name}
                </Button>
            );
        });
    }, [activeKey, tabs]);

    return <Button.Group className="mix-tab-button-group">{ButtonNodes}</Button.Group>;
};

export default TabButtons;
