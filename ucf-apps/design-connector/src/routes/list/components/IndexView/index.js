import React, { Component, Fragment } from "react";
import { Header, Content } from "components/PageView";
import { But<PERSON>, Modal, Tabs } from "components/TinperBee";
import SearchInput from "components/TinperBee/SearchInput";
import CardList from "components/ConnectorCardList";
import Grid from "components/TinperBee/Grid";
import CntInfoModal from "../CntInfoModal";
import { pathname as templatesPathname } from "../../../templateList";
import { pathname as versionsPathname } from "../../../versions";
import { pathname as entitiesPathname } from "../../../entityList";
import { clearConfigCntFormStorage, setConfigCntToStorage } from "design-connector/utils";

import "./index.less";
import { RefreshButton } from "components/TinperBee/Button";
import withRouter from "decorator/withRouter";

@withRouter
class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            listType: "card",
        };
        this.actions = this.getActions();
        this.columns = this.getColumns();
    }

    componentWillUnmount() {
        if (this.props.navigationType === "POP") clearConfigCntFormStorage();
    }

    componentDidMount() {
        const { ownerStore } = this.props;
        ownerStore.getDataSource();
    }

    handleCntInfoAction = (type, cntInfo = null) => {
        const { ownerStore } = this.props;
        ownerStore.cntInfoAction(type, cntInfo);
    };

    handleAdd = () => {
        this.handleCntInfoAction("add");
    };

    handleSearch = (value) => {
        const { ownerStore } = this.props;
        ownerStore.getDataSource({
            nameAndCodePattern: value,
        });
    };

    handleEdit = (data) => {
        this.handleCntInfoAction("edit", data);
    };

    handleCancel = () => {
        this.handleCntInfoAction("hide");
    };

    handleSave = (values) => {
        const { ownerStore } = this.props;
        const allLinkTypes = ["startLinker", "endLinker"];
        let linkTypes = values.linkTypes;
        delete values.linkTypes;
        allLinkTypes.forEach((type) => {
            values[type] = linkTypes.includes(type);
        });
        ownerStore.saveCntInfoAction(values);
    };

    handlePublish = (data) => {
        const { ownerStore } = this.props;
        // version
        ownerStore.publishAction({
            id: data.id,
            version: data.version,
        });
    };

    handleTemplates = (data) => {
        this.props.navigate({
            pathname: templatesPathname,
            search: `?connectorId=${data.id}`,
        });
        setConfigCntToStorage(data);
    };

    handleVersions = (data) => {
        this.props.navigate({
            pathname: versionsPathname,
            search: `?connectorId=${data.id}`,
        });
        setConfigCntToStorage(data);
    };

    handleEntities = (data) => {
        this.props.navigate({
            pathname: entitiesPathname,
            search: `?connectorId=${data.id}`,
        });
        setConfigCntToStorage(data);
    };

    handleDelete = (data) => {
        const { ownerStore } = this.props;
        Modal.confirm({
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042801DC", "确认删除此连接器") /* "确认删除此连接器" */,
            onOk: () => {
                ownerStore.deleteAction({
                    id: data.id,
                });
            },
        });
    };

    handleChangeListType = (type) => {
        this.setState({ listType: type });
    };

    renderReleased = (value) => {
        let text = "",
            cls = "";
        if (value) {
            text = lang.templateByUuid("UID:P_UBL-FE_20096040042801E3", "已发布") /* "已发布" */;
            cls = "yes";
        } else {
            text = lang.templateByUuid("UID:P_UBL-FE_20096040042801D4", "未发布") /* "未发布" */;
            cls = "not";
        }
        return <span className={`design-card-item-status-tag ${cls}`}>{text}</span>;
    };

    renderLinkerType = (value) => {
        value += "";
        let text = "",
            color = "";
        switch (value) {
            case "0":
                text = lang.templateByUuid("UID:P_UBL-FE_20096040042801DD", "业务") /* "业务" */;
                color = "#F77306";
                break;
            case "1":
                text = lang.templateByUuid("UID:P_UBL-FE_20096040042801E0", "技术") /* "技术" */;
                color = "#0064FF";
                break;
        }
        return (
            <span className="design-card-item-status-tag bordered" style={{ color: color, borderColor: color }}>
                {text}
            </span>
        );
    };

    getColumns = () => {
        return [
            {
                title: lang.templateByUuid("UID:P_UBL-FE_20096040042801E5", "连接器名称") /* "连接器名称" */,
                dataIndex: "name",
                $$type: "title",
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_20096040042801D5", "编码") /* "编码" */,
                dataIndex: "code",
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_20096040042801D9", "发布状态") /* "发布状态" */,
                dataIndex: "released",
                $$type: "tag",
                render: this.renderReleased,
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_20096040042801DF", "类型") /* "类型" */,
                dataIndex: "linkerType",
                $$type: "tag",
                render: this.renderLinkerType,
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_20096040042801E2", "发布时间") /* "发布时间" */,
                dataIndex: "releaseTime",
            },
        ];
    };

    gridHoverContent = (index, record) => {
        return (
            <Fragment>
                {this.actions.map((item) => {
                    const { key, name, onClick } = item;
                    return (
                        <Button
                            fieldid="ublinker-routes-list-components-IndexView-index-8732862-Button"
                            key={key}
                            {...Grid.hoverButtonPorps}
                            onClick={onClick.bind(null, record)}
                        >
                            {name}
                        </Button>
                    );
                })}
            </Fragment>
        );
    };

    getActions = () => {
        return [
            {
                key: "versions",
                name: lang.templateByUuid("UID:P_UBL-FE_20096040042801DE", "版本管理") /* "版本管理" */,
                icon: "cl cl-banbenguanli",
                onClick: this.handleVersions,
            },
            {
                key: "templates",
                name: lang.templateByUuid("UID:P_UBL-FE_20096040042801E1", "模板管理") /* "模板管理" */,
                icon: "cl cl-mobanguanli",
                onClick: this.handleTemplates,
            },
            {
                //   key: 'entity',
                //   name: '实体管理',
                //   icon: 'cl cl-shitiguanli',
                //   onClick: this.handleEntities
                // }, {
                key: "edit",
                name: lang.templateByUuid("UID:P_UBL-FE_20096040042801E4", "编辑") /* "编辑" */,
                icon: "cl cl-edit-l",
                onClick: this.handleEdit,
            },
            {
                key: "publish",
                name: lang.templateByUuid("UID:P_UBL-FE_20096040042801D6", "发布") /* "发布" */,
                icon: "cl cl-yifabu",
                onClick: this.handlePublish,
            },
            {
                key: "delete",
                name: lang.templateByUuid("UID:P_UBL-FE_20096040042801DA", "删除") /* "删除" */,
                icon: "cl cl-delet",
                onClick: this.handleDelete,
            },
        ];
    };

    handleRefresh = () => {
        this.props.ownerStore.getDataSource();
    };

    render() {
        const { listType } = this.state;
        const { ownerState, ownerStore } = this.props;
        const { dataSource, tabs, tabActiveKey, connectorInfo, connectorEditModalType } = ownerState;
        return (
            <Fragment>
                <Header
                    fixed={false}
                    tabs={
                        <Tabs
                            fieldid="ublinker-routes-list-components-IndexView-index-3444441-Tabs"
                            activeKey={tabActiveKey}
                            onChange={ownerStore.changeTabActive}
                        >
                            {tabs.map((tab) => {
                                const { key, name } = tab;
                                return <Tabs.TabPane fieldid="UCG-FE-routes-list-components-IndexView-index-6958128-Tabs.TabPane" key={key} tab={name} />;
                            })}
                        </Tabs>
                    }
                >
                    <SearchInput
                        inputSize="sm"
                        placeholder={lang.templateByUuid("UID:P_UBL-FE_20096040042801D8", "请输入实体名称或编码") /* "请输入实体名称或编码" */}
                        onSearch={this.handleSearch}
                    />

                    <RefreshButton className={"ucg-mar-l-sm"} onClick={this.handleRefresh} />

                    <Button
                        fieldid="ublinker-routes-list-components-IndexView-index-2293010-Button"
                        className="ucg-mar-l-sm"
                        colors="primary"
                        onClick={this.handleAdd}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_20096040042801DB", "新增") /* "新增" */}
                    </Button>
                </Header>

                <Content className="mix-transparent">
                    {listType === "card" ? (
                        <CardList
                            fieldid="UCG-FE-routes-list-components-IndexView-index-217632-CardList"
                            cardType="1"
                            topBorderColor={(data) => {
                                if (data.linkerType + "" === "0") {
                                    return "rgba(247,167,0, 0.4)";
                                } else {
                                    return "rgba(0,100,255, 0.4)";
                                }
                            }}
                            itemShadow
                            createFun={this.handleAdd}
                            dataSource={dataSource}
                            actions={this.actions}
                            columns={this.columns}
                            isEmpty={ownerStore.isEmpty}
                        />
                    ) : null}
                </Content>

                <CntInfoModal
                    title={lang.templateByUuid("UID:P_UBL-FE_20096040042801D7", "基本信息") /* "基本信息" */}
                    editType={connectorEditModalType}
                    formValues={connectorInfo}
                    onCancel={this.handleCancel}
                    onOk={this.handleSave}
                />
            </Fragment>
        );
    }
}

export default IndexView;
