import React, { Component } from "react";
import IndexView from "./components/IndexView";
import { inject, observer } from "mobx-react";
import Store, { storeKey } from "./store";
import core from "core";
core.addStore({
    storeKey: storeKey,
    store: new Store(),
});

@inject((store) => {
    const ownerStore = store[storeKey];
    return {
        ownerStore,
        ownerState: ownerStore.toJS(),
    };
})
@observer
class Container extends Component {
    constructor(props) {
        super(props);
    }

    render() {
        return <IndexView {...this.props} />;
    }
}
export default Container;
