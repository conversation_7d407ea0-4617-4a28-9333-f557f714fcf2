import { computed, observable, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { autoServiceMessage } from "utils/service";
import { getDesignConnectorListService } from "design-connector/common/services";
import * as ownerService from "./service";
import commonText from "constants/commonText";
import { types } from "../../constants/types";

function getTabs() {
    let tabs = [
        {
            key: "all",
            name: lang.templateByUuid("UID:P_UBL-FE_20096040042801BB", "全部") /* "全部" */,
        },
    ];
    types.forEach((type) => {
        const { code, name, tab } = type;
        tabs.push({
            key: tab,
            value: code,
            name: name,
        });
    });
    return tabs;
}

/***
 * @typedef {Object} connectorInfoType
 * @property {String} id -连接器器Id
 * @property {String} name -连接器名称
 * @property {String} code -连接器编码
 * @property {Number} linkerType=[0|1] -连接器类型 0:业务连接器， 1:中间件连接器
 * @property {String} description -描述
 * @property {Boolean} startLinker
 * @property {Boolean} endLinker
 * @property {Boolean} testable -是否支持测试
 * @property {Boolean} release
 * @property {String} releaseTime
 * @property {String} gmtUpdate
 * @property {String} gmtCreate
 */

const defaultConnectorInfo = {
    id: "",
    name: "",
    code: "",
    linkerType: 0, //0： 业务连接器， 1：中间件连接器
    logo: "", //存储图片logo的路径
    description: "",
    startLinker: true,
    endLinker: false,
    testable: true,
    released: false,
    releaseTime: "",
    gmtCreate: "",
    gmtUpdate: "",
};

/***
 * @typedef {Object} cntRequestDTO -连接器搜索条件
 * @property {String} code -连接器编码
 * @property {String} name -连接器名称
 * @property {Number} linkerType=[0|1] -连接器类型
 * @property {Boolean} released -是否发布
 * @property {Boolean} startLinker -是否开始选择器
 * @property {Boolean} endLinker -是否结束选择器
 */

/**
 * 连接器列表Store
 * @type {Object}
 * @property {Array} tabs -列表tabs
 * @property {String} tabActiveKey -当前选中tab
 * @property {Array} dataSource -列表数据
 * @property {Boolean} loaded -列表数据加载完成
 * @property {String} label -搜索框内容
 * @property {connectorInfoType} connectorInfo -连接器基本信息
 * @property {requestDTO} requestDTO -连接器搜索
 * @property {String} connectorEditModalType=[hide|edit|add|view] -连接器基本信息
 * */
const initState = {
    tabs: getTabs(),
    tabActiveKey: "all",
    dataSource: [],
    loaded: false,
    connectorInfo: defaultConnectorInfo,
    connectorEditModalType: "hide",
    requestDTO: {
        code: "",
        name: "",
        linkerType: void 0,
    },
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    @computed get isEmpty() {
        const { dataSource, loaded } = this.state;
        return dataSource.length <= 0 && loaded;
    }

    changeTabActive = (key) => {
        this.state.tabActiveKey = key;
        this.getDataSource();
    };

    getDataSource = async (reqData = {}) => {
        const { tabActiveKey, requestDTO, tabs } = this.state;
        const activeTab = tabs.find((item) => item.key === tabActiveKey);

        const requestData = {
            ...requestDTO,
            ...reqData,
            linkerType: activeTab.value,
        };
        const res = await this.dispatchService({
            service: getDesignConnectorListService(requestData),
            dataKey: "dataSource",
        });
        this.state.requestDTO = requestData;
        this.state.loaded = true;
        return res;
    };

    publishAction = async (data) => {
        const res = await autoServiceMessage({
            service: ownerService.publishDesignConnectorService(data),
            success: lang.templateByUuid("UID:P_UBL-FE_20096040042801BA", "发布成功") /* "发布成功" */,
        });
        if (res) {
            await this.getDataSource();
        }
    };

    deleteAction = async (data) => {
        const res = await autoServiceMessage({
            service: ownerService.deleteDesignConnectorService(data),
            success: window.lang.template(commonText.deleteSuccess),
        });
        if (res) {
            await this.getDataSource();
        }
    };

    cntInfoAction = (type, cntInfo) => {
        let state = { connectorEditModalType: type, connectorInfo: cntInfo || initState.connectorInfo };
        this.changeState(state);
    };

    saveCntInfoAction = async (values) => {
        const connectorInfo = this.toJS(this.state.connectorInfo);
        const requestData = {
            ...connectorInfo,
            ...values,
        };
        const res = await autoServiceMessage({
            service: ownerService.saveDesignConnectorInfoService(requestData),
            success: requestData.id ? window.lang.template(commonText.changeSuccess) : window.lang.template(commonText.addSuccess),
        });
        if (res) {
            this.cntInfoAction("hide");
            await this.getDataSource();
        }
    };
}

/**
 * 根据具体模块修改 storeKey 为保证唯一性
 * storeKey 由 模块文件夹名称 + 页面名称 + 'Store' 小驼峰
 *
 * storeKey是作为当前 Store 的唯一键， storeKey与Store的实例会在 rootStore或者StoreMap中以 键值对的 形式存在
 * 1. 用于在模块的 storeMap（模块routes/store.js）中注册并初始化当前Store
 * 2. 用于在container的 inject 中关联当前Store的实例时，从模块的storeMap或者rootStore中获取对应store实例
 * */
export const storeKey = "designConnectorListStore";
export default Store;
