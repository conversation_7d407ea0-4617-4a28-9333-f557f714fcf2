import { getInvokeService } from "utils/service";

/**
 * 发布连接器
 * @param {Object} data
 * @param {String} data.id -连接器ID
 * @param {String} data.gmtUpdate -数据中gmtUpdate
 * @return {Promise<unknown>}
 */
export const publishDesignConnectorService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/gwmanage/linkerManagement/releaseLinker",
        },
        data
    );
};

/***
 * 删除连接器
 * @param {Object} data
 * @param {String} data.id -连接器ID
 * @return {Promise<unknown>}
 */
export const deleteDesignConnectorService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/gwmanage/linkerManagement/deleteLinkerById",
        },
        {},
        data
    );
};

/**
 * 保存连接器
 * @param {connectorInfo} data
 * @return {*}
 */
export const saveDesignConnectorInfoService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/gwmanage/linkerManagement/saveLinker",
        },
        data
    );
};
