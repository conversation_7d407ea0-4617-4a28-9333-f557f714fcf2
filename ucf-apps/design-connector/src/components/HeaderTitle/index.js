import React, { useRef } from "react";
import Logo from "components/CardList/Logo";
import { getConfigCntFormStorage } from "design-connector/utils";

import "./index.less";

const HeaderTitle = () => {
    const configCntRef = useRef(getConfigCntFormStorage());
    const { logo, name } = configCntRef.current;
    return (
        <>
            <Logo className="header-logo" logo={"/iuap-ipaas-dataintegration/ucf-wh" + logo} title={name} />
            <span className="vertical-align-md">{name}</span>
        </>
    );
};

export default HeaderTitle;
