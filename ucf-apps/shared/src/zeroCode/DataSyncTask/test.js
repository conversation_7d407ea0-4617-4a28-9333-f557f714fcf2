import React, { useState, useEffect, useCallback, createContext, useContext } from "react";
import { Provider } from "core";
import { MemoryRouter as Router, useNavigate, useLocation, Link as RouterLink } from "react-router";
import { Modal } from "@tinper/next-ui";
import Routes from "ucf-apps/data/sync-task/src/routes";
import lang from "tne-core-fe/i18n";
import withMultiLang from "components/AsyncComponent/withMultiLang";

const MultiLangRouter = withMultiLang(Routes, ["YS_IPAAS_UBL-FE"], { serviceCode: "kfljsjtbrw" });
lang.init({ zhcn: {}, zhtw: {}, enus: {} }, null);
import "ucf-apps/data/sync-task/src/app.less";
import "static/cl/iconfont.css";
import "static/ipaas/iconfont.css";
import "static/icon";

// 创建Modal上下文
const ModalContext = createContext({
    modalRoutes: [],
    openModal: () => {},
});

// 自定义Link组件，拦截Modal路由
function InterceptedLink({ to, children, ...props }) {
    const { modalRoutes, openModal } = useContext(ModalContext);

    const handleClick = (e) => {
        const targetPath = typeof to === "string" ? to : to.pathname;

        if (modalRoutes.includes(targetPath)) {
            e.preventDefault(); // 阻止默认路由跳转
            const search = typeof to === "string" ? "" : to.search || "";
            const state = typeof to === "string" ? null : to.state;
            openModal(targetPath, search, state);
        }
    };

    return (
        <RouterLink to={to} onClick={handleClick} {...props}>
            {children}
        </RouterLink>
    );
}

// 重写navigate方法来拦截编程式导航
function useInterceptedNavigate(modalRoutes, openModal) {
    const navigate = useNavigate();

    useEffect(() => {
        // 创建一个包装的navigate函数
        const interceptedNavigate = (to, options = {}) => {
            const targetPath = typeof to === "string" ? to : to.pathname;

            if (modalRoutes.includes(targetPath)) {
                const search = typeof to === "string" ? "" : to.search || "";
                const state = options.state;
                openModal(targetPath, search, state);
            } else {
                navigate(to, options);
            }
        };

        // 将拦截的navigate函数挂载到window上，供其他组件使用
        window.__interceptedNavigate = interceptedNavigate;

        return () => {
            delete window.__interceptedNavigate;
        };
    }, [navigate, modalRoutes, openModal]);

    return navigate;
}

// Modal路由拦截组件
function ModalRouteInterceptor({ modalRoutes, onRouteChange, children }) {
    const openModal = useCallback(
        (path, search, state) => {
            onRouteChange(path, search, state);
        },
        [onRouteChange]
    );

    // 拦截编程式导航
    useInterceptedNavigate(modalRoutes, openModal);

    return <ModalContext.Provider value={{ modalRoutes, openModal }}>{children}</ModalContext.Provider>;
}

// Modal内容渲染组件
function ModalContent({ route, search, state, otherSource }) {
    if (!route) return null;

    return (
        <Provider serviceCodeDiwork="kfljsjtbrw">
            <Router initialEntries={[`${route}${search || ""}`]} initialIndex={0}>
                <MultiLangRouter otherSource={otherSource} />
            </Router>
        </Provider>
    );
}

export default function IntegratedTask(props) {
    // Modal状态管理
    const [modalVisible, setModalVisible] = useState(false);
    const [modalRoute, setModalRoute] = useState(null);
    const [modalSearch, setModalSearch] = useState("");

    // 默认需要Modal化的路由
    const defaultModalRoutes = ["/data-detail", "/sync-log", "/batch-log", "/batch-detail", "/columns"];

    // 从props获取配置，支持自定义
    const { modalRoutes = defaultModalRoutes, onModalOpen, onModalClose, ...otherProps } = props;

    // 路由变化处理
    const handleRouteChange = useCallback(
        (route, search) => {
            setModalRoute(route);
            setModalSearch(search);
            setModalVisible(true);

            // 触发外部回调
            if (onModalOpen) {
                onModalOpen(route, search);
            }
        },
        [onModalOpen]
    );

    // Modal关闭处理
    const handleModalClose = useCallback(() => {
        setModalVisible(false);
        setModalRoute(null);
        setModalSearch("");

        // 触发外部回调
        if (onModalClose) {
            onModalClose();
        }
    }, [onModalClose]);

    // 获取Modal标题
    const getModalTitle = (route) => {
        const titleMap = {
            "/data-detail": "数据详情",
            "/sync-log": "同步日志",
            "/batch-log": "批量日志",
            "/batch-detail": "批量详情",
            "/columns": "字段映射",
        };
        return titleMap[route] || "详情";
    };

    return (
        <div className="iuapIpaasDataintegrationFe-sync" style={{ height: "100%", overflow: "auto", transform: "translate(0)" }}>
            <Provider serviceCodeDiwork="kfljsjtbrw">
                <Router initialEntries={["/"]}>
                    <ModalRouteInterceptor modalRoutes={modalRoutes} onRouteChange={handleRouteChange}>
                        <MultiLangRouter otherSource={otherProps} />
                    </ModalRouteInterceptor>
                </Router>
            </Provider>

            {/* Modal弹窗 */}
            <Modal title={getModalTitle(modalRoute)} visible={modalVisible} onCancel={handleModalClose} footer={null} isMaximize={true}>
                <ModalContent route={modalRoute} search={modalSearch} otherSource={otherProps} />
            </Modal>
        </div>
    );
}
