import React, { useState, useCallback } from "react";
import { render, Provider } from "core";
import { MemoryRouter as Router } from "react-router";
import { Modal } from "@tinper/next-ui";
import Routes from "ucf-apps/data/sync-task/src/routes";
import lang from "tne-core-fe/i18n";
import withMultiLang from "components/AsyncComponent/withMultiLang";
const MultiLangRouter = withMultiLang(Routes, ["YS_IPAAS_UBL-FE"], { serviceCode: "kfljsjtbrw" });
lang.init({ zhcn: {}, zhtw: {}, enus: {} }, null);
import "ucf-apps/data/sync-task/src/app.less";
import "static/cl/iconfont.css";
import "static/ipaas/iconfont.css";
import "static/icon";

import ModalRouteListener from "./RouteListener";

// Modal内容渲染组件
function ModalContent({ route, search, otherSource }) {
    if (!route) return null;

    return (
        <Provider serviceCodeDiwork="kfljsjtbrw">
            <Router initialEntries={[`${route}${search || ""}`]} initialIndex={0}>
                <MultiLangRouter otherSource={otherSource} />
            </Router>
        </Provider>
    );
}

export default function IntegratedTask(props) {
    const [isEnter, setIsEnter] = useState(false);
    console.log("8888----", props);

    // Modal状态管理
    const [modalVisible, setModalVisible] = useState(false);
    const [modalRoute, setModalRoute] = useState(null);
    const [modalSearch, setModalSearch] = useState("");

    // 默认需要Modal化的路由
    const defaultModalRoutes = ["/data-detail", "/sync-log", "/batch-log", "/batch-detail", "/columns"];

    // 从props获取配置，支持自定义
    const {
        modalRoutes = defaultModalRoutes,
        modalConfig = {
            height: "auto",
            destroyOnClose: true,
            maskClosable: false,
        },
        onModalOpen,
        onModalClose,
        ...otherProps
    } = props;

    // 路由变化
    const handleRouteChange = useCallback(
        (route, search) => {
            setIsEnter(true);
            setModalRoute(route);
            setModalSearch(search);
            setModalVisible(true);

            if (onModalOpen) {
                onModalOpen(route, search);
            }
        },
        [onModalOpen]
    );

    const handleModalClose = useCallback(() => {
        setModalVisible(false);
        setModalRoute(null);
        setModalSearch("");

        if (onModalClose) {
            onModalClose();
        }
    }, [onModalClose]);

    // Modal标题
    const getModalTitle = (route) => {
        const titleMap = {
            "/data-detail": "数据详情",
            "/sync-log": "同步日志",
            "/batch-log": "批量日志",
            "/batch-detail": "批量详情",
            "/columns": "字段映射",
        };
        return titleMap[route] || "详情";
    };

    return (
        <div className="iuapIpaasDataintegrationFe-sync" style={{ height: "100%", overflow: "auto", transform: "translate(0)" }}>
            <Provider serviceCodeDiwork="kfljsjtbrw">
                <Router initialEntries={["/"]}>
                    <ModalRouteListener modalRoutes={modalRoutes} onRouteChange={handleRouteChange}>
                        <MultiLangRouter otherSource={{ ...otherProps, _isSharedEnter: isEnter }} />
                    </ModalRouteListener>
                </Router>
            </Provider>

            <Modal
                visible={modalVisible}
                title={getModalTitle(modalRoute)}
                footer={null}
                isMaximize={true}
                destroyOnClose={modalConfig.destroyOnClose}
                onCancel={handleModalClose}
                style={{ paddingTop: "7px", paddingLeft: "7px" }}
            >
                <ModalContent route={modalRoute} search={modalSearch} otherSource={otherProps} />
            </Modal>
        </div>
    );
}
