import { useNavigate, useLocation } from "react-router";
import React, { useEffect } from "react";

// Modal路由监听组件
function ModalRouteListener({ modalRoutes, onRouteChange, children }) {
    const navigate = useNavigate();
    const location = useLocation();

    useEffect(() => {
        if (modalRoutes.includes(location.pathname)) {
            onRouteChange(location.pathname, location.search, location.state);
            // 回退到主页面
            navigate("/", { replace: true });
        }
    }, [location, modalRoutes, onRouteChange, navigate]);

    return children;
}
export default ModalRouteListener;
