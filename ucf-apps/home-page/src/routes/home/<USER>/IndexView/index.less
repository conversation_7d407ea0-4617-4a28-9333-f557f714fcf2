.page {
  width: 100%;
  height:100vh;
  background: #EBECF0;
  padding:24px 28px;
  box-sizing: border-box;
  .header {
      // text-align: center;
      // line-height: 48px;
      background: #fff;
      margin-bottom:20px;
      padding:30px;
  }
  .title{
    font-size: 16px;
font-family: PingFangSC-Regular, PingFang SC;
font-weight: 400;
color: #212121;
line-height: 22px;
  }
  .stepBox{
    padding:0 125px;
    box-sizing: border-box;
  }
     
      .stepBox .wui-steps-item-custom .wui-steps-item-icon{  //图标   新
        background: none;
        border: 0;
        width: 70px;
        height: 70px;
        cursor: pointer;
        display: block;
      }
      .wui-steps-item-custom .wui-steps-item-icon > .wui-steps-icon {
        display: inline-block;
        font-size: 14px;
        height: 32px;
        line-height: 32px;
         width: auto; 
    }
      .stepBox .u-steps-item-custom .u-steps-item-content span{   //文字
      
    
      }

  .stepBox .wui-steps-horizontal .wui-steps-item:not(:last-child) .wui-steps-item-title:after {
    background: #e8e8e8;
    content: "";
    display: block;
    height: 1px;
    left: 100%;
    position: absolute;
    top: -21px;
    width: 9999px;
    border-bottom:1px dashed #979797;
}
    .stepBox .wui-steps-horizontal .wui-steps-item:not(:last-child) .wui-steps-item-icon:before {  //三角块
      content: "";
      width: 0;
    height: 0;
    border: 5px solid;
            border-color:  transparent transparent transparent #979797;
    // border-left: 50px solid #ccc;
    // border-top: 50px solid transparent;
    // border-bottom: 50px solid transparent;
    // border-right: 50px solid transparent;
      // background: blue;
      display: block;
      position: absolute;
      top: 44px;
      right: -5px;
  }
  .stepBox .wui-steps-horizontal .wui-steps-item:nth-child(5) .wui-steps-item-icon:before {  //三角块
    content: "";
    width: 0;
  height: 0;
  border: 5px solid;
          border-color:  transparent #979797 transparent transparent ;
  // border-left: 50px solid #ccc;
  // border-top: 50px solid transparent;
  // border-bottom: 50px solid transparent;
  // border-right: 50px solid transparent;
    // background: blue;
    display: block;
    position: absolute;
    top: 44px;
    left: 65px;
}
    .stepBox .wui-steps-item-title {   //标题
      font-size: 12px;
      color: #212121;
      display: inline-block;
       padding-right: 0px; 
      position: relative;
      line-height: 32px;
      width:70px;
      text-align: center;
      cursor: pointer;
  }
  .stepBox .jumpAdd{
    // width:10px;
    // height:10px;
    font-size: 16px;

  }
  .stepBox .wui-steps-item-title .box:hover  .addBox{
    display: block;
  }
  .stepBox .wui-steps-item-title .box .addBox{
    display: none;
  }
  .stepBox .wui-steps-item-title .box .word{
    display: block;
  }
  .stepBox .wui-steps-item-title .box:hover  .word{
    display: none;
  }
  // searchArea
  .search-area {
      text-align: center;
      line-height: 40px;
      background: #F7F9FB;
  }

  //table展示区域
  .table-container {
      min-height: 500px;
      text-align: center;
      padding-top: 100px;
      background: #fff;
  }

  // 主内容区
  .content {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  //左侧展示区域
  .leftContent,.rightContent {
    width: 49%;
height: 542px;
    background: #fff;
    padding:30px;
    box-sizing: border-box;
  }

  //右侧展示区域
  .rightContent {
   
  }
}
