"use strict";
var _typeof =
    "function" == typeof Symbol && "symbol" == typeof Symbol.iterator
        ? function (n) {
              return typeof n;
          }
        : function (n) {
              return n && "function" == typeof Symbol && n.constructor === Symbol && n !== Symbol.prototype ? "symbol" : typeof n;
          };
!(function () {
    var l,
        a,
        o,
        c,
        e,
        t,
        i,
        d,
        r,
        n = "undefined" != typeof window ? window : "undefined" != typeof global ? global : "undefined" != typeof self ? self : this;
    n.jDiwork =
        ((l = {}),
        (a = {}),
        (o = !{}),
        (c = 0),
        (e = "JDIWORK"),
        (t = function (n) {
            try {
                var e = n.type.split(":"),
                    o = e[0],
                    t = e[1],
                    i = void 0 !== n.data && n.data,
                    c = l[o],
                    a = void 0 === n.destroy || n.destroy;
            } catch (n) {
                return void console.log(n);
            }
            if (c && c[t]) {
                clearTimeout(c[t].timer);
                i = c[t].callback(i);
                return a && delete c[t], i;
            }
        }),
        (i = function (n, e, o) {
            var t = l[n],
                i = ++c,
                o = {
                    callback: e,
                    timer: (o = void 0 === o || o)
                        ? setTimeout(function () {
                              e && e(!1), delete t[i];
                          }, 500)
                        : 0,
                };
            return t ? (t[i] = o) : (((t = {})[i] = o), (l[n] = t)), n + ":" + i;
        }),
        (d = function (o) {
            (o.messType = e),
                "_WORKBENCH_" === window.name
                    ? window.postMessage(JSON.stringify(o), "*")
                    : (function n(e) {
                          e !== window.top && (e.parent.postMessage(JSON.stringify(o), "*"), n(e.parent));
                      })(window);
        }),
        (r = function () {
            d({ detail: data, callbackId: i("closeDialogNew", callback || function () {}) });
        }),
        window.addEventListener(
            "DOMContentLoaded",
            function () {
                o = !0;
            },
            !1
        ),
        window.addEventListener(
            "message",
            function (n) {
                n = n.data;
                if (n) {
                    try {
                        "string" == typeof n && (n = JSON.parse(n));
                    } catch (n) {
                        return void console.log(n);
                    }
                    n &&
                        "object" === (void 0 === n ? "undefined" : _typeof(n)) &&
                        n.type &&
                        (n.type.indexOf(":") < 0
                            ? function (n) {
                                  try {
                                      var e = n.type,
                                          o = n.data,
                                          t = a[e];
                                  } catch (n) {
                                      return console.log(n);
                                  }
                                  if (t && t.length) for (var i = 0, c = t.length; i < c; i++) t[i](o);
                              }
                            : t)(n);
                }
            },
            !1
        ),
        window.addEventListener(
            "click",
            function () {
                d({ callbackId: "rootClick" });
            },
            !1
        ),
        {
            ready: function (n) {
                var e = { type: i("ready", n) };
                o
                    ? t(e)
                    : setTimeout(function () {
                          t(e);
                      }, 0);
            },
            openService: function (n, e, o, t) {
                d({ detail: { serviceCode: n, data: e, type: o }, callbackId: i("openService", t || function () {}) });
            },
            reOpenService: function (n, e) {
                d({ detail: n, callbackId: i("reOpenService", e || function () {}) });
            },
            recordLog: function (n, e, o) {
                d({ detail: { serviceCode: n, data: e }, callbackId: i("recordLog", o || function () {}) });
            },
            renewalPing: function () {
                d({ detail: {}, callbackId: i("renewalPing", function () {}) });
            },
            dismissEnterprise: function (n) {
                d({ detail: {}, callbackId: i("dismissEnterprise", n || function () {}) });
            },
            updateService: function (n, e, o) {
                d({ detail: { serviceCode: n, data: e }, callbackId: i("updateService", o || function () {}) });
            },
            getContext: function (n) {
                d({ callbackId: i("getContext", n, !1) });
            },
            onData: function (n, e) {
                var o, t;
                "function" == typeof n && void 0 === e && ((e = n), (n = void 0)),
                    n && window.diworkTools
                        ? window.diworkTools.on(n, function (n) {
                              e(n);
                          })
                        : ((t = e), (n = a[(o = "data")]) ? n.push(t) : (a[o] = [t]));
            },
            switchChatTo: function (n, e) {
                n.id || n.yht_id
                    ? d({ detail: n, callbackId: i("switchChatTo", e || function () {}) })
                    : console.log("function switchChatTo need id or yht_id");
            },
            refreshUserInfo: function (n) {
                d({ callbackId: i("refreshUserInfo", n || function () {}) });
            },
            showDialog: function (n, e) {
                d({ detail: n, callbackId: i("showDialog", e || function () {}) });
            },
            closeDialog: r,
            closeDialogNew: function (n, e) {
                d({ detail: n, callbackId: i("closeDialogNew", e || function () {}) });
            },
            showTabs: function (n, e) {
                d({ detail: { serviceCode: n }, callbackId: i("showTabs", e || function () {}) });
            },
            openWin: function (n, e) {
                d({ detail: n, callbackId: i("openWin", e || function () {}) });
            },
            closeWin: function (n) {
                d({ callbackId: i("closeWin", n || function () {}) });
            },
            getData: function (n, e) {
                d({ detail: n, callbackId: i("getData", e, !1) });
            },
            getState: function (n) {
                d({ callbackId: i("getState", n, !1) });
            },
            openFrame: function (n, e) {
                d({ detail: n, callbackId: i("openFrame", e || function () {}) });
            },
            closeFrame: function (n) {
                d({ callbackId: i("closeFrame", n || function () {}) });
            },
            openServicePublish: function (n, e) {
                d({ detail: n, callbackId: i("openServicePublish", e || function () {}) });
            },
            closeServicePublish: function (n) {
                d({ callbackId: i("closeServicePublish", n || function () {}) });
            },
            getPageParam: function (n) {
                d({ callbackId: i("getPageParam", n, !1) });
            },
            openHomePage: function (n, e) {
                d({ detail: n, callbackId: i("openHomePage", e || function () {}) });
            },
            onGroupUpdated: function (n) {
                d({ callbackId: i("onGroupUpdated", n, !1) });
            },
            getImGroupData: function (n) {
                d({ callbackId: i("getImGroupData", n || function () {}) });
            },
            openNotifyCenter: function (n, e) {
                d({ detail: n, callbackId: i("openNotifyCenter", e || function () {}) });
            },
            onUnReadedNumChanged: function (n) {
                d({ callbackId: i("onUnReadedNumChanged", n, !1) });
            },
            checkServiceOpen: function (n, e) {
                d({ detail: { serviceCode: n }, callbackId: i("checkServiceOpen", e) });
            },
            postDataToService: function (n, e, o) {
                d({ detail: { serviceCode: n, data: e }, callbackId: i("postDataToService", o) });
            },
            execScript: function (n, e) {
                d({ detail: n, callbackId: i("execScript", e || function () {}) });
            },
            getHostForGlobal: function (n, e) {
                d({ detail: n, callbackId: i("getHostForGlobal", e, !1) });
            },
            openDialog: function (t, n) {
                var e;
                t.btns &&
                    t.btns.length &&
                    (t.btns = t.btns.map(function (n, e) {
                        var o;
                        return (
                            n.fun
                                ? ((o = n.fun),
                                  (n.fun = i(
                                      "dialogBtnClick",
                                      function () {
                                          o(r.bind(t));
                                      },
                                      !1
                                  )))
                                : (n.fun = i("dialogBtnClick", r.bind(t), !1)),
                            n
                        );
                    })),
                    t.onClose
                        ? ((e = t.onClose),
                          (t.onClose = i(
                              "dialogOnClose",
                              function () {
                                  e() && r.call(t);
                              },
                              !1
                          )))
                        : (t.onClose = i("dialogOnClose", r.bind(t), !1)),
                    d({ detail: { options: t }, callbackId: i("openDialog", n) });
            },
            addBrm: function (n, e) {
                d({ detail: { name: n, url: window.location.href }, callbackId: i("addBrm", e) });
            },
            popBrm: function (n, e) {
                d({ detail: { index: n, url: window.location.href }, callbackId: i("popBrm", e) });
            },
            getBrm: function (n) {
                d({ callbackId: i("getBrm", n) });
            },
            openMessage: function (n, e) {
                d({ detail: n, callbackId: i("openMessage", e || function () {}) });
            },
            singleRender: function (n) {
                var o = { messType: e + "-SINGLE", render: n || !0 };
                "_WORKBENCH_" === window.name
                    ? window.postMessage(JSON.stringify(o), "*")
                    : (function n(e) {
                          e !== window.top && (e.parent.postMessage(JSON.stringify(o), "*"), n(e.parent));
                      })(window);
            },
        });
})();
