import React, { Component, Fragment } from "react";
import withRouter from "decorator/withRouter";
import { Select, Icon, Steps } from "components/TinperBee";
import MonitorCard from "monitor/components/MonitorCard";
import ChartLine from "monitor/components/ChartLine";
import ChartPie from "monitor/components/ChartPie";
import { timeSpace } from "monitor/utils";
import { echartsResize, echartsReset } from "monitor/echartsUtils";
import MonitorTimeRange from "monitor/components/TimeRange";
import "./index.less";
const Option = Select.Option;
const home1 = require("./home1.svg");
const home2 = require("./home2.svg");
const home3 = require("./home3.svg");
const home4 = require("./home4.svg");
const home5 = require("./home5.svg");
const home6 = require("./home6.svg");
@echartsResize()
@withRouter
class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            // showAddModal: false,
            // modalType: "create",
            editRecord: {},
            expandedKeys: ["1"], //树的
            searchValue: "", //树的
            query: {
                condition: "",
                sysId: "",
            },
            selectedColumns: [],
            showImport: false,
            navData: [],
        };
    }

    async componentDidMount() {
        const { ownerStore } = this.props;
        ownerStore.getTenantOfflineViewData();
        ownerStore.findStartSchemeCount();
        ownerStore.findSyncTask();
        let processList = await ownerStore.findProcess();
        this.setNavData(processList);
        ownerStore.findTaskErrorLogCount();
    }
    setNavData = (processList) => {
        this.setState({
            navData: [
                {
                    iconSrc: home1,
                    word: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050952") + processList.gateWayNum + ")", //网关管理
                    serviceCode: "kflj_wggl",
                },
                {
                    iconSrc: home2,
                    word: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050976") + processList.connNum + ")", //连接配置
                    serviceCode: "kflj_ljpz",
                },
                {
                    iconSrc: home3,
                    word: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050945") + processList.systemNum + ")", //集成系统
                    serviceCode: "kflj_jjxt",
                },
                {
                    iconSrc: home4,
                    word: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050979") + processList.objNum + ")", //集成对象
                    serviceCode: "kflj_jjdx",
                },
                {
                    iconSrc: home5,
                    word: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050953") + processList.schemeNum + ")", //集成方案
                    serviceCode: "kflj_jjfa",
                },
                {
                    iconSrc: home6,
                    word: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050928") + processList.startSchemeNum + ")", //启动方案
                    serviceCode: "kflj_qdfa",
                },
            ],
        });
    };
    offlineChartDblclickHandler = (params) => {
        // const { data: { param } } = params;
        // const { ownerStore } = this.props
        // ownerStore.changeActiveViewId(param)
    };
    handleTimeRangeSearch = async (timeSpace) => {
        let { ownerStore } = this.props;
        await ownerStore.changeTimeSpace(timeSpace);
        // echartsReset(this.echartNode.echartKey)
    };
    handleChange = async (key) => {
        // console.log(value)
        const { ownerStore } = this.props;
        await ownerStore.findTaskErrorLogCount(key);
        echartsReset(this.echartNode.echartKey);
    };
    gotoPage = (serviceCode) => {
        jDiwork.ready(() => {
            jDiwork.openService(serviceCode);
        });
    };
    gotoPageAdd = (serviceCode) => {
        jDiwork.ready(() => {
            jDiwork.openService(serviceCode);
        });
    };
    render() {
        let { mouseOn, showIframe, path, navData } = this.state;
        let { ownerState, ownerStore } = this.props;
        const { activeTimeSpace, tenantOfflineViewData, startSchemeCountList, syncTaskList, processList, taskErrorLogCountList } = ownerState;
        const { info: tenantOfflineViewDataInfo } = tenantOfflineViewData;
        let dataSource = {
            height: 400,
            ref: (node) => (this.startechartNode = node),
            title: {
                text: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050934") /* "某站点用户访问来源" */,
                subtext: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050944") /* "纯属虚构" */,
                left: "center",
            },
            tooltip: {
                trigger: "item",
            },
            legend: {
                orient: "vertical",
                left: "left",
            },
            // grid={{ top: '5%', bottom: '20%' }}
            // xAxis={tenantOfflineViewDataInfo.x}
            // xAxisOptions={{
            //   axisLabel: {
            //     formatter: (value) => {
            //       return value.replace(" ", '\n')
            //     }
            //   }
            // }}
            series: [
                {
                    name: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050964") /* "访问来源" */,
                    type: "pie",
                    radius: "50%",
                    data: [
                        { value: 1048, name: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050947") /* "搜索引擎" */ },
                        { value: 735, name: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050935") /* "直接访问" */ },
                        { value: 580, name: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050978") /* "邮件营销" */ },
                        { value: 484, name: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050970") /* "联盟广告" */ },
                        { value: 300, name: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050937") /* "视频广告" */ },
                    ],
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: "rgba(0, 0, 0, 0.5)",
                        },
                    },
                },
            ],
        };
        return (
            <Fragment>
                <div className="page">
                    <div className="header">
                        <div className="title">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050929") /* "流程向导" */}</div>
                        <div className="stepBox">
                            <Steps fieldid="UCG-FE-routes-home-components-IndexView-index-2686448-Steps">
                                {navData.map((item, index) => {
                                    return (
                                        <Steps.Step
                                            status="finish"
                                            title={
                                                <div className="box">
                                                    <div className="addBox" style={{ width: 72, height: 32 }}>
                                                        <Icon
                                                            fieldid="ublinker-routes-home-components-IndexView-index-9077382-Icon"
                                                            className="jumpAdd"
                                                            type="uf-plus"
                                                            onClick={this.gotoPageAdd.bind(null, item.serviceCode)}
                                                        />
                                                    </div>
                                                    <span className="word">{item.word}</span>
                                                </div>
                                            }
                                            icon={
                                                <img
                                                    fieldid="ublinker-routes-home-components-IndexView-index-7613532-img"
                                                    onClick={this.gotoPage.bind(null, item.serviceCode)}
                                                    src={item.iconSrc}
                                                ></img>
                                            }
                                        />
                                    );
                                })}
                            </Steps>
                        </div>
                    </div>

                    <div className="content">
                        <div className="leftContent">
                            <div className="title">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050973") /* "出错日志图" */}</div>
                            <div className="ucg-pad-10 clearfix">
                                {/* <MonitorTimeRange
                  className="ucg-float-r"
                  rangeTags={timeSpace}
                  searchType={activeTimeSpace.searchType}
                  activeRange={activeTimeSpace.timeRange}
                  dateTimes={[activeTimeSpace.startTime, activeTimeSpace.endTime]}
                  dateRate={activeTimeSpace.rate}
                  onSearch={this.handleTimeRangeSearch}
                /> */}
                                {/* <Select fieldid="ublinker-routes-home-components-IndexView-index-2593967-Select" 
              className="u-select-xs"
              data={selectData}
              showSearch
              optionFilterProp="children"
              value={value}
              size={'xs'}
              onChange={this.handleChange.bind(null, index, field)}
            /> */}
                                <Select
                                    fieldid="ublinker-routes-home-components-IndexView-index-6025800-Select"
                                    size="sm"
                                    style={{ width: 200, float: "right" }}
                                    onChange={this.handleChange.bind(null)}
                                >
                                    {syncTaskList &&
                                        syncTaskList.map((item, index) => {
                                            return (
                                                <Option fieldid="UCG-FE-routes-home-components-IndexView-index-8744044-Option" key={index} value={item.value}>
                                                    {item.name}
                                                </Option>
                                            );
                                        })}
                                </Select>
                            </div>
                            <ChartLine
                                height={400}
                                ref={(node) => (this.echartNode = node)}
                                grid={{ top: "5%", bottom: "20%" }}
                                // xAxis={tenantOfflineViewDataInfo.x}
                                xAxis={taskErrorLogCountList.x}
                                xAxisOptions={{
                                    axisLabel: {
                                        formatter: (value) => {
                                            return value.replace(" ", "\n");
                                        },
                                    },
                                }}
                                series={[
                                    {
                                        type: "bar",
                                        // source: tenantOfflineViewDataInfo.y
                                        source: taskErrorLogCountList.y,
                                        itemStyle: {
                                            normal: {
                                                color: "#EF7575",
                                            },
                                            emphasis: {
                                                // 普通图表的高亮颜色
                                                color: "#7E94F0",
                                                // 地图区域的高亮颜色
                                                areaColor: "blue",
                                            },
                                        },
                                    },
                                ]}
                                events={[
                                    {
                                        type: "dblclick",
                                        handler: this.offlineChartDblclickHandler,
                                    },
                                ]}
                            />
                        </div>
                        <div className="leftContent">
                            <div className="title">{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050956") /* "启动方案" */}</div>
                            <ChartPie
                                dataSource={startSchemeCountList}
                                series={[
                                    {
                                        type: "pie",
                                        radius: ["40%", "70%"],
                                    },
                                ]}
                                //   dataSource={[
                                //     {value: 1048, name: '搜索引擎'},
                                //     {value: 735, name: '直接访问'},
                                //     {value: 580, name: '邮件营销'},
                                //     {value: 484, name: '联盟广告'},
                                //     {value: 300, name: '视频广告'}
                                // ]}
                                //   height={400}
                                //   ref={node => this.startechartNode = node}
                                //   title={{
                                //     text: '某站点用户访问来源',
                                //     subtext: '纯属虚构',
                                //     left: 'center'
                                // }}
                                // tooltip={{
                                //     trigger: 'item'
                                // }}
                                // legend= {{
                                //     orient: 'vertical',
                                //     left: 'left',
                                // }}
                                //   // grid={{ top: '5%', bottom: '20%' }}
                                //     // xAxis={tenantOfflineViewDataInfo.x}
                                //     // xAxisOptions={{
                                //     //   axisLabel: {
                                //     //     formatter: (value) => {
                                //     //       return value.replace(" ", '\n')
                                //     //     }
                                //     //   }
                                //     // }}
                                //   series={[
                                //     {
                                //         name: '访问来源',
                                //         type: 'pie',
                                //         radius: '50%',
                                //         data: [
                                //             {value: 1048, name: '搜索引擎'},
                                //             {value: 735, name: '直接访问'},
                                //             {value: 580, name: '邮件营销'},
                                //             {value: 484, name: '联盟广告'},
                                //             {value: 300, name: '视频广告'}
                                //         ],
                                //         emphasis: {
                                //             itemStyle: {
                                //                 shadowBlur: 10,
                                //                 shadowOffsetX: 0,
                                //                 shadowColor: 'rgba(0, 0, 0, 0.5)'
                                //             }
                                //         }
                                //     }
                                // ]}
                                // series={[{
                                //   type: 'bar',
                                //   source: tenantOfflineViewDataInfo.y
                                // }]}
                                // events={[{
                                //   type: 'dblclick',
                                //   handler: this.offlineChartDblclickHandler
                                // }]}
                            />
                        </div>
                    </div>
                    {showIframe ? <iframe style={{ width: 1000, height: 1000 }} src={`/ublinker-fe/${path}/index.html?from=diwork#/`}></iframe> : null}
                </div>
            </Fragment>
        );
    }
}

export default IndexView;
