/*
 * @Author: your name
 * @Date: 2021-07-19 17:02:25
 * @LastEditTime: 2021-07-22 21:15:10
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \ublinker-fe\ucf-apps\start\start-config\src\routes\list\components\AddModal\index.js
 */
import React, { useState, useEffect } from "react";
import Modal from "components/TinperBee/Modal";
import { FormControl, Switch, Icon, Select, Upload, Button } from "components/TinperBee";
import FormList from "components/TinperBee/Form";
import "./index.less";
const FormItem = FormList.Item;
const Option = Select.Option;
const labelCol = 120;

const AddAKSKModal = (props) => {
    const { show, onCancel, form, type, editRecord, sysList } = props;

    // const [remark, setRemark] = useState(type === "create" ? "": editRecord.remark);
    const [file, setFile] = useState();
    // 表单字段验证方法
    const { validateFields, getFieldProps, getFieldError } = form;
    useEffect(() => {}, []);
    const onOk = () => {
        validateFields(async (error, values) => {
            if (!error) {
                console.log({ ...values, file });
                props.onOk({ ...values, file });
            }
        });
    };
    const uploadData = {
        name: "file",
        // action: '/upload.do',
        headers: {
            authorization: "authorization-text",
        },
        beforeUpload(zipFile, list) {
            console.log(zipFile, list);
            setFile(zipFile);
            return false; // 禁止上传
        },
    };
    return (
        <Modal
            fieldid="ublinker-routes-home-components-AddModal-index-3204617-Modal"
            show={show}
            cancelText={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050063") /* "取消" */}
            title={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050967") /* "导入" */}
            onOk={onOk}
            onCancel={onCancel}
            size={"xlg"}
        >
            <FormList
                fieldid="ublinker-routes-home-components-AddModal-index-1331141-FormList"
                className=""
                style={{ paddingLeft: 100 }}
                layoutOpt={{ md: 12 }}
            >
                <FormItem
                    fieldid="ublinker-routes-home-components-AddModal-index-8717965-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050926") /* "来源集成系统" */}
                    labelCol={labelCol}
                    required
                    error={getFieldError("currentSourceSysId")}
                >
                    <Select
                        fieldid="ublinker-routes-home-components-AddModal-index-7884287-Select"
                        size="sm"
                        {...getFieldProps("currentSourceSysId", {
                            // initialValue: orderSelectList[0].value
                            rules: [
                                {
                                    required: true,
                                    message: <span>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050957") /* "请选择来源集成系统" */}</span>,
                                },
                            ],
                        })}
                    >
                        {sysList.map((item, index) => {
                            return (
                                <Option fieldid="UCG-FE-routes-home-components-AddModal-index-8053056-Option" key={item.id} value={item.id}>
                                    {item.systemName}
                                </Option>
                            );
                        })}
                    </Select>
                </FormItem>
                <FormItem
                    fieldid="ublinker-routes-home-components-AddModal-index-5358056-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050959") /* "目标集成系统" */}
                    labelCol={labelCol}
                    required
                    error={getFieldError("currentTargetSysId")}
                >
                    <Select
                        fieldid="ublinker-routes-home-components-AddModal-index-3758568-Select"
                        size="sm"
                        {...getFieldProps("currentTargetSysId", {
                            // initialValue: orderSelectList[0].value
                            rules: [
                                {
                                    required: true,
                                    message: <span>{window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050946") /* "请选择目标集成系统" */}</span>,
                                },
                            ],
                        })}
                    >
                        {sysList.map((item, index) => {
                            return (
                                <Option fieldid="UCG-FE-routes-home-components-AddModal-index-2832576-Option" key={item.id} value={item.id}>
                                    {item.systemName}
                                </Option>
                            );
                        })}
                    </Select>
                </FormItem>
                <FormItem
                    fieldid="ublinker-routes-home-components-AddModal-index-8136840-FormItem"
                    label={window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050965") /* "上传" */}
                    labelCol={labelCol}
                >
                    <Upload
                        fieldid="UCG-FE-routes-home-components-AddModal-index-693195-Upload"
                        {...uploadData}
                        //   beforeUpload={(file, fileList) => {
                        //     console.log(file)
                        //     setFile(file)
                        //     return false
                        // }}
                    >
                        <Button fieldid="ublinker-routes-home-components-AddModal-index-1980086-Button" shape="border">
                            <Icon fieldid="ublinker-routes-home-components-AddModal-index-7376390-Icon" type="uf-upload" />{" "}
                            {window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050951") /* "点击上传" */}
                        </Button>
                    </Upload>
                </FormItem>
            </FormList>
        </Modal>
    );
};

export default FormList.createForm()(AddAKSKModal);
