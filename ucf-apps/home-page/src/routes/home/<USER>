/*
 * @Author: your name
 * @Date: 2021-04-15 21:04:42
 * @LastEditTime: 2021-04-20 15:35:25
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \ublinker-fe\ucf-apps\start\start-config\src\routes\list\container.js
 */
import React, { Component } from "react";
import IndexView from "./components/IndexView";
import { inject, observer } from "mobx-react";
import { addStore } from "core";
import Store, { storeKey } from "./store";
addStore({
    storeKey, //StartConfigListStore
    store: new Store(),
});

@inject((rootStore) => {
    let ownerStore = rootStore[storeKey]; //rootSotre是info store和 list sotre的和，这里的sotrekey是
    return {
        // StartConfigListStore，所以拿到list的store
        ownerState: ownerStore.toJS(), //   ./sotre.js.state
        ownerStore: ownerStore, //   ./store.js
    };
})
@observer
class Container extends Component {
    constructor(props) {
        super(props);
    }

    render() {
        return <IndexView {...this.props} />;
    }
    componentDidMount() {}
}
export default Container;
