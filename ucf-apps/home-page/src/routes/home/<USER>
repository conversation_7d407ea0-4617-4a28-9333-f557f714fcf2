/*
 * @Author: your name
 * @Date: 2021-04-15 21:04:42
 * @LastEditTime: 2021-07-22 20:08:38
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \ublinker-fe\ucf-apps\start\start-config\src\routes\list\store.js
 */
import { observable, action, toJS, reaction, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { defaultListMap } from "utils/pageListUtils";
import { autoServiceMessage } from "utils/service";
import * as ownerService from "./service";
const initState = {
    // 页面数据
    dataSource: {
        ...defaultListMap,
    },
    searchKey: "",
    treeData: [],
    sysList: [],
    sysId: "",
    activeTimeSpace: {
        searchType: "1",
        timeRange: "4",
        startTime: null,
        endTime: null,
        rate: "1",
        spaceName: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050972") /* "最近一月" */,
    },
    tenantOfflineViewData: {
        info: {
            x: [],
            y: [],
        },
    },
    taskErrorLogCountList: {},
    processList: {},
    syncTaskList: [],
    startSchemeCountList: [],
};
class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;
    //获取树
    getTree = async (id) => {
        let res = await autoServiceMessage({
            service: ownerService.treeDataService(id),
            // success: '获取成功'
        });
        let tempSystemTree = [
            {
                name: window.lang.template("MIX_UBL_ALL_UBL_FE_LOC_00050954") /* "集成系统" */,
                key: "1",
                children: [],
            },
        ];
        if (res && res.data.length > 0) {
            res.data.forEach((item) => {
                tempSystemTree[0].children.push({ name: item.systemName, key: item.id });
            });
            this.changeState({
                treeData: tempSystemTree,
                sysList: res.data,
            });
        }
        // if (res) {

        //   this.changeState({
        //     treeData:res.data
        //   })
        //   this.getDataSource();
        // }
    };

    //获取表格列表
    getDataSource = async (reqData) => {
        let requestData = {
            key: this.state.searchKey,
            ...reqData,
        };
        this.getPagesListFunc({
            service: ownerService.gridDataService,
            requestData,
            dataKey: "dataSource",
        });
        this.state.searchKey = requestData.key;
    };
    //停用 启用
    stopListItem = async (id) => {
        const { sysId } = this.state;
        let res = await autoServiceMessage({
            service: ownerService.stopService(id),
            // success: '操作成功'
        });

        if (res) {
            this.getDataSource({ sysId });
        }
    };
    //立即执行
    doIt = async (id) => {
        let res = await autoServiceMessage({
            service: ownerService.doItService(id),
            // success: '操作成功'
        });
        console.log(res);
        // if (res) {
        //   this.getDataSource();
        // }
    };
    //下载
    download = async (id) => {
        let res = await autoServiceMessage({
            service: ownerService.downloadService(id),
        });
        if (res) {
            const blob = res.data;
            const reader = new FileReader();
            reader.readAsDataURL(blob);
            reader.onload = (e) => {
                const a = document.createElement("a");
                a.download = res.headers["content-disposition"].split("=")[1];
                // a.download = `文件名称.zip`;
                // 后端设置的文件名称在res.headers的 "content-disposition": "form-data; name=\"attachment\"; filename=\"20181211191944.zip\"",
                a.href = e.target.result;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
            };
        }
    };
    //导入
    import = async (values, cb) => {
        const { sysId } = this.state;
        let res = await autoServiceMessage({
            service: ownerService.importService(values),
        });
        if (res && res.status == 1) {
            cb();
            this.getDataSource({ sysId });
        }
    };
    //删除列表
    del = async (id) => {
        const { sysId } = this.state;
        let res = await autoServiceMessage({
            service: ownerService.deleteListItemService(id),
            // success: '删除成功'
        });

        if (res) {
            this.getDataSource({ sysId });
        }
    };
    aa = () => {
        console.log(333);
    };

    getTenantOfflineViewData = async () => {
        await this.dispatchService({
            service: ownerService.getMonitorGwTenantOfflineViewService({
                searchType: 1,
                timeRange: 4,
            }),
            dataKey: "tenantOfflineViewData",
        });
    };
    changeTimeSpace = async (timeSpace) => {
        this.state.activeTimeSpace = {
            ...this.state.activeTimeSpace,
            ...timeSpace,
        };
        await this.getTenantOfflineViewData();
        this.state.activeViewParam = null;
        this.state.tenantOfflineData = initState.tenantOfflineData;
        this.state.tenantOfflinePagination = null;
    };

    findStartSchemeCount = async (id) => {
        let res = await autoServiceMessage({
            service: ownerService.findStartSchemeCount(id),
        });
        if (res) {
            // {value: 1048, name: '搜索引擎'}
            let data = res.data;
            let startSchemeCountList = [];
            for (let key in data) {
                startSchemeCountList.push({ name: key, value: data[key] });
            }
            console.log(startSchemeCountList);
            this.changeState({
                startSchemeCountList,
            });
        }
    };
    findSyncTask = async (id) => {
        let res = await autoServiceMessage({
            service: ownerService.findSyncTask(id),
        });
        if (res) {
            let data = res.data;
            let syncTaskList = [];
            for (let key in data) {
                syncTaskList.push({ name: data[key], value: key });
            }
            console.log(syncTaskList);
            this.changeState({
                syncTaskList,
            });
        }
    };
    findProcess = async (id) => {
        let res = await autoServiceMessage({
            service: ownerService.findProcess(id),
        });
        if (res) {
            this.changeState({
                processList: res.data,
            });
            return res.data;
        }
    };
    findTaskErrorLogCount = async (key) => {
        let res = await autoServiceMessage({
            service: ownerService.findTaskErrorLogCount(key),
        });
        if (res) {
            let data = res.data;
            let x = [],
                y = [];
            let taskErrorLogCountList = {};
            for (let key in data) {
                x.push(key);
                y.push({ value: data[key] });
            }
            taskErrorLogCountList.x = x;
            taskErrorLogCountList.y = y;
            console.log(taskErrorLogCountList);
            this.changeState({
                taskErrorLogCountList: taskErrorLogCountList,
            });
        }
    };
}

/**
 * 根据具体模块修改 storeKey 为保证唯一性
 * storeKey 由 模块文件夹名称 + 页面名称 + 'Store' 小驼峰
 *
 * storeKey是作为当前 Store 的唯一键， storeKey与Store的实例会在 rootStore或者StoreMap中以 键值对的 形式存在
 * 1. 用于在模块的 storeMap（模块routes/store.js）中注册并初始化当前Store
 * 2. 用于在container的 inject 中关联当前Store的实例时，从模块的storeMap或者rootStore中获取对应store实例
 * */
export const storeKey = "HomePageHomeStore";

export default Store;
