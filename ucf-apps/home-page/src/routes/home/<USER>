/*
 * @Author: your name
 * @Date: 2021-04-17 22:02:53
 * @LastEditTime: 2021-07-22 21:19:13
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \ublinker-fe\ucf-apps\start\start-config\src\routes\list\service.js
 */
import { defineService, getInvokeService } from "utils/service";

// 树列表
export const treeDataService = function (data) {
    let service = defineService({
        method: "GET",
        path: "/gwmanage/gwportal/mygwapp/integrated/system/findAll",
    });

    return service.invoke(data);
};
// 表格列表
export const gridDataService = function (data) {
    let service = defineService({
        method: "GET",
        path: "/gwmanage/gwportal/diwork/startscheme/page",
    });

    return service.invoke(data);
};
// 停用、启用
export const stopService = function (data) {
    let service = defineService({
        method: "GET",
        path: "/gwmanage/gwportal/diwork/integscheme/page",
    });

    return service.invoke(data);
};
//立即执行
// export const getMyConnectorInfoService = function (data) {
//     let { id, ..._data } = data;
//     return getInvokeService({
//       method: 'GET',
//       path: "/mygwapp/connector/common/config/" + id
//     }, _data)
//   }
export const doItService = function (id) {
    let service = defineService({
        method: "POST",
        path: "/gwmanage/gwportal/diwork/startscheme/runScheme/" + id,
    });
    return service.invoke();
};
export const downloadService = function (id) {
    let service = defineService({
        method: "GET",
        path: "/gwmanage/gwportal/diwork/integscheme/exportSchemeSetting/" + id,
        responseType: "blob", //下载流文件需要在这声明
    });
    return service.invoke();
};

export const importService = function (values) {
    const { currentSourceSysId, currentTargetSysId, file } = values;
    /*接口以formatData形式接收*/
    const formdata = new FormData();
    formdata.append("file", file);
    let service = defineService({
        method: "POST",
        path: `/gwmanage/gwportal/diwork/integscheme/importSchemeSetting/${currentSourceSysId}/${currentTargetSysId}`,
        header: { "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundary6Jdq1PXI3LkQLVe0" },
    });
    return service.invoke(formdata);
};

// 删除
export const deleteListItemService = function (id) {
    let service = defineService({
        method: "POST",
        path: "/gwmanage/gwportal/diwork/startscheme/delete/" + id,
    });
    return service.invoke();
};
export const getErpVersionService = function (gwId) {
    return getInvokeService({
        method: "GET",
        path: "/diwork/version/getErpVersion/" + gwId,
        showLoading: false,
    });
};

// 删除aksk密钥
export const deleteAkskService = function (data) {
    let service = defineService({
        method: "POST",
        path: "/gwmanage/gwportal/diwork/integscheme/deleteSchemeInfo/" + data,
    });

    return service.invoke(data);
};

// 更新启停状态
export const updateStatusService = function (data) {
    let service = defineService({
        method: "POST",
        path: "/si-channel/auth/aksk/updateStatus",
    });

    return service.invoke(data);
};

// 更新备注
export const updateRemarkService = function (data) {
    let service = defineService({
        method: "POST",
        path: "/si-channel/auth/aksk/updateRemark",
    });

    return service.invoke(data);
};
export const getMonitorGwTenantOfflineViewService = function (data) {
    return getInvokeService(
        {
            method: "GET",
            // path: commonServicePath + '/tenant/gateway/close/index'
            path: "/gwmanage/gwportal/mygwapp/count/tenant/gateway/close/index",
        },
        data
    );
};
//首页-查询启动类型及数量
export const findStartSchemeCount = function (data) {
    let service = defineService({
        method: "GET",
        path: "/gwmanage/gwportal/diwork/firstpage/findStartSchemeCount",
    });

    return service.invoke(data);
};
//首页-查询所有的同步任务
export const findSyncTask = function (data) {
    let service = defineService({
        method: "GET",
        path: "/gwmanage/gwportal/diwork/firstpage/findSyncTask",
    });

    return service.invoke(data);
};
//首页-查询流程中每个节点的数量
export const findProcess = function (data) {
    let service = defineService({
        method: "POST",
        path: "/gwmanage/gwportal/diwork/firstpage/findProcess",
    });

    return service.invoke(data);
};
//首页-获取同步任务报错数量
export const findTaskErrorLogCount = function (key) {
    let service = defineService({
        method: "GET",
        path: "/gwmanage/gwportal/diwork/firstpage/findTaskErrorLogCount",
    });

    return service.invoke({ key });
};
