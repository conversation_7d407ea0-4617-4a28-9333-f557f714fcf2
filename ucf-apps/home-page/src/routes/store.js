/*
 * @Author: your name
 * @Date: 2021-04-15 21:04:42
 * @LastEditTime: 2021-04-16 14:32:40
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \ublinker-fe\ucf-apps\start\start-config\src\routes\store.js
 */
/**
 * 汇总模块中的各个业务 store，形成一个总的 storeMap
 * 1. 方便模块中各个业务页面之间数据共享和交互
 * 2. 方便将业务模块store合并的 rootStore 中（待规划）
 * */

// import ListStore, {storeKey as listStoreKey} from './list/store';
// import InfoStore, {storeKey as infoStoreKey} from './info/store';
import HomeStore, { storeKey as homeStoreKey } from "./home/<USER>";
import mixCore from "core";

mixCore.addStore([{ storeKey: homeStoreKey, store: new HomeStore() }]);
