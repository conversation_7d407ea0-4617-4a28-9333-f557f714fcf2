import React from "react";
import { RoutesRender } from "core";

// import ListRoute from './list';
// import DetailRoute from './info';
import HomeRoute from "./home";

import getConfigProvider from "components/ConfigProvider";
import config from "../config";
const ConfigProvider = getConfigProvider(config);

const routes = [HomeRoute];

const Routes = () => {
    return (
        <ConfigProvider fieldid="UCG-FE-ucf-apps-home-page-src-routes-index-1770827-ConfigProvider">
            <RoutesRender routes={routes} />
        </ConfigProvider>
    );
};
export default Routes;
