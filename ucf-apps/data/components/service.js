import { getInvokeService, defineService, getServicePath } from "utils/service";

// 集成流类型的定时任务详情
export const getScheduleDetailService = async (data) => {
    return getInvokeService(
        {
            method: "POST",
            path: `/gwportal/diwork/erpdata/task/queryScheduleTaskTimeData`,
        },
        data
    );
};
// 集成流类型的定时任务详情
export const updateScheduleService = async (data) => {
    return getInvokeService(
        {
            method: "POST",
            path: `/gwportal/diwork/erpdata/task/saveScheduleTaskTimeData`,
        },
        data
    );
};
