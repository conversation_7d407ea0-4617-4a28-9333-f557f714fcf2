import React, { useState, forwardRef, useImperativeHandle, useEffect, useRef } from "react";
import { Modal } from "@tinper/next-ui";
// import "./index.less";

const DispatchModal = forwardRef((props, ref) => {
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [modalType, setModalType] = useState("edit"); // edit detail
    const [iframeSrc, setIframeSrc] = useState("");
    const [iframeRef, setIframeRef] = useState(null);
    const [smallAppId, setSmallAppId] = useState("");
    useImperativeHandle(ref, () => ({
        openDispatchModal: (uri, type) => {
            setIsModalVisible(true);
            setModalType(type);
            setIframeSrc(uri);
        },
    }));

    useEffect(() => {
        if (iframeRef) {
            const iframeWindow = iframeRef.contentWindow;
            const oldCloseDispatchModal = iframeWindow.closeDispatchModal;
            iframeWindow.closeDispatchModal = (...args) => {
                console.log("iframeWindow.closeDispatchModal---", ...args);
                handleCloseDispatchModal(args?.[0]);
                oldCloseDispatchModal?.(...args);
            };
        }
    }, [iframeRef]);
    useEffect(() => {
        if (window.jDiwork?.getState) {
            window.jDiwork.getState((data) => {
                setSmallAppId(data?.activeTab);
            });
        }
    }, []);
    const handleCloseDispatchModal = (type) => {
        setIsModalVisible(false);
        setIframeRef(null);
        if (type === "edit") {
            props.onClose?.();
        }
        // 可以在这里刷新数据或执行其他操作
        // 例如： this.props.ownerStore.getDataSource();
    };

    return (
        <Modal
            header={modalType === "edit" ? null : ""}
            title={null}
            visible={isModalVisible}
            onCancel={handleCloseDispatchModal} // 使用 closeDispatchModal 关闭modal
            footer={null}
            isMaximize
            getPopupContainer={document.getElementById(smallAppId)?.firstElementChild ?? "body"}
            contentStyle={props.otherSource?.source === 3 ? undefined : { transform: "none" }}
        >
            <iframe
                ref={(node) => {
                    if (node && !iframeRef) {
                        setIframeRef(node);
                    }
                }}
                src={iframeSrc}
                width="100%"
                height="100%"
                style={{ border: "none", height: "100%" }}
            />
        </Modal>
    );
});

export default DispatchModal;
