import React, { useState, useEffect } from "react";
import { Modal } from "@tinper/next-ui";
import Scheduler from "iuap-ip-commonui-fe/Scheduler";
import { autoServiceMessage } from "utils/service";
import { getScheduleDetailService, updateScheduleService } from "../service";

const FlowScheduler = (props) => {
    const [flowSchedulerData, setFlowSchedulerData] = useState({});
    const { taskId, taskCode, onCancel } = props;

    useEffect(() => {
        const fetchData = async () => {
            if (taskCode) {
                // 获取定时任务详情数据
                let res = await autoServiceMessage({
                    service: getScheduleDetailService({ taskId, taskCode }),
                });
                if (res?.data?.timeData) {
                    setFlowSchedulerData(res.data?.timeData);
                }
            } else {
                setFlowSchedulerData({});
            }
        };
        fetchData();
    }, [taskCode]);
    // 保存 集成流类型 定时任务
    const handleSaveScheduler = async (data) => {
        setFlowSchedulerData(data);
        const res = await autoServiceMessage({
            service: updateScheduleService({ taskId, taskCode, timeData: data }),
        });
        if (res && res.data) {
            handleCancelScheduler();
        }
    };
    const handleCancelScheduler = () => {
        setFlowSchedulerData({});
        onCancel();
    };
    return (
        <Modal
            fieldid="a86197e9-d735-400b-b9fd-ab4bd30c8921"
            title={cb.lang.templateByUuid("UID:P_UBL-FE_20076E520428000A", "定时任务")}
            visible
            width={880}
            footer={null}
            bodyStyle={{ padding: 0 }}
            onCancel={handleCancelScheduler}
        >
            <Scheduler timeData={flowSchedulerData} taskCode={taskCode} onSave={handleSaveScheduler} onCancel={handleCancelScheduler} />
        </Modal>
    );
};

export default FlowScheduler;
