import { observable, action, toJS, reaction, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { defaultListMap } from "utils/pageListUtils";
import { autoServiceMessage } from "utils/service";
import * as ownerService from "./service";
const initState = {
    dataSource: { ...defaultListMap },
    selectedData: {},
    searchValue: {},
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    setSearchValue = (value) => {
        this.changeState({ searchValue: value });
    };
    setSelectedData = (data) => {
        this.changeState({ selectedData: data });
    };

    getDataSource = async (requestData) => {
        requestData = {
            ...this.state.searchValue,
            ...requestData,
        };
        this.getPagesListFunc({
            service: ownerService.getList,
            requestData,
            dataKey: "dataSource",
        });
    };

    updateDataType = async (data, cb) => {
        let res = await autoServiceMessage({
            service: ownerService.updateDataTypeService(data),
            success: data?.typetag
                ? lang.templateByUuid("UID:P_UBL-FE_20076E580428002A", "编辑成功") /* "编辑成功" */
                : lang.templateByUuid("UID:P_UBL-FE_20076E5804280029", "新增成功") /* "新增成功" */,
        });

        if (res) {
            cb && cb();
            this.getDataSource({ pageNo: 1 });
        }
    };
}

export const storeKey = "dataTypeStore";

export default Store;
