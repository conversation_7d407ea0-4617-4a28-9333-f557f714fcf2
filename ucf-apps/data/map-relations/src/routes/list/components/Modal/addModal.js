import React, { useEffect } from "react";
import { Modal, Form, Input } from "@tinper/next-ui";
import PropTypes from "prop-types";

const FormItem = Form.Item;

const AddModal = (props) => {
    const { visible, onCancel, onOk, record = {}, title } = props;
    const [form] = Form.useForm();

    // 当弹窗显示或记录变化时，设置表单值
    useEffect(() => {
        if (visible) {
            if (record && record.typetag) {
                // 编辑模式，设置表单初始值
                form.setFieldsValue(record);
            } else {
                // 新增模式，清空表单
                form.resetFields();
            }
        }
    }, [visible, record, form]);

    // 处理确认按钮点击
    const handleOk = async () => {
        try {
            const values = await form.validateFields();
            onOk({ ...record, ...values });
        } catch (errInfo) {
            console.log("验证失败:", errInfo);
        }
    };

    // 表单布局配置
    const formItemLayout = {
        labelCol: { span: 4 },
        wrapperCol: { span: 20 },
    };

    return (
        <Modal
            title={
                title ||
                (record && record.typetag
                    ? lang.templateByUuid("UID:P_UBL-FE_20076E5804280047", "编辑") /* "编辑" */
                    : lang.templateByUuid("UID:P_UBL-FE_20076E580428004A", "新增")) /* "新增" */
            }
            visible={visible}
            onCancel={onCancel}
            onOk={handleOk}
            okText={lang.templateByUuid("UID:P_UBL-FE_20076E5804280048", "确定") /* "确定" */}
            cancelText={lang.templateByUuid("UID:P_UBL-FE_20076E5804280049", "取消") /* "取消" */}
            width={600}
            destroyOnClose
        >
            <Form form={form} {...formItemLayout}>
                <FormItem
                    label={lang.templateByUuid("UID:P_UBL-FE_20076E580428004B", "编码") /* "编码" */}
                    name="typetag"
                    rules={[{ required: true, message: lang.templateByUuid("UID:P_UBL-FE_20076E5804280045", "请输入") /* "请输入" */ }]}
                >
                    <Input placeholder={lang.templateByUuid("UID:P_UBL-FE_20076E5804280045", "请输入") /* "请输入" */} disabled={record && record.typetag} />
                </FormItem>

                <FormItem
                    label={lang.templateByUuid("UID:P_UBL-FE_20076E580428004C", "名称") /* "名称" */}
                    name="typename"
                    rules={[{ required: true, message: lang.templateByUuid("UID:P_UBL-FE_20076E5804280045", "请输入") /* "请输入" */ }]}
                >
                    <Input placeholder={lang.templateByUuid("UID:P_UBL-FE_20076E5804280045", "请输入") /* "请输入" */} />
                </FormItem>

                <FormItem label={lang.templateByUuid("UID:P_UBL-FE_20076E5804280046", "描述") /* "描述" */} name="typeDesc">
                    <Input.TextArea
                        placeholder={lang.templateByUuid("UID:P_UBL-FE_20076E5804280045", "请输入") /* "请输入" */}
                        autoSize={{ minRows: 3, maxRows: 6 }}
                    />
                </FormItem>
            </Form>
        </Modal>
    );
};

AddModal.propTypes = {
    visible: PropTypes.bool,
    onCancel: PropTypes.func,
    onOk: PropTypes.func,
    record: PropTypes.object,
    title: PropTypes.string,
};

export default AddModal;
