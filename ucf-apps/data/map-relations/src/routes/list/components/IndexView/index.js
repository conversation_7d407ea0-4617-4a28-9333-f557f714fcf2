import React, { useState, useRef, useEffect } from "react";
import { Button, Icon, Input, Space, Table, Pagination, Upload, Notification, Progress } from "@tinper/next-ui";
import { PageTopHelp, PageLayout } from "iuap-ip-commonui-fe";
import ResizeObserver from "resize-observer-polyfill";
import styles from "./index.modules.less";
import AddModal from "../Modal/addModal";
import { autoServiceMessage } from "utils/service";
import * as ownerService from "../../service";
import { downloadFile } from "utils";
import withRouter from "decorator/withRouter";
import { useSize } from "ahooks";

let newNotification = null;
Notification.newInstance({ placement: "BottomRight" }, (n) => (newNotification = n));

const IndexView = (props) => {
    let { ownerStore, ownerState } = props;
    let { dataSource, pagination } = ownerState;
    const [showModal, setShowModal] = useState(false);
    const [record, setRecord] = useState({});
    const [importProgress, setImportProgress] = useState(0);
    const noticeKey = useRef(0);
    const bodyRef = useRef(null);
    const size = useSize(bodyRef);
    const tableScrollY = size ? size.height - 31 : 300;

    // 获取数据源
    useEffect(() => {
        ownerStore.getDataSource();
    }, []);

    // 处理搜索
    const handleSearch = (value) => {
        ownerStore.setSearchValue({ info: value });
        ownerStore.getDataSource({ pageNo: 1 });
    };

    // 处理新增
    const handleAdd = () => {
        setRecord({});
        setShowModal(true);
    };

    // 处理编辑
    const handleEdit = (record) => {
        setRecord(record);
        setShowModal(true);
    };

    // 查看数据详情
    const viewDataDetail = (task) => {
        ownerStore.setSelectedData(task);
        // task.tableview,  //视图 查询数据详情时统一传该参数
        props.navigate({
            pathname: "/detail",
            search: `?tableview=${task.tableview}`,
        });
    };

    // 关闭模态框
    const handleCloseModal = () => {
        setShowModal(false);
    };

    // 保存模型
    const handleUpdateDataType = (param) => {
        ownerStore.updateDataType(param, () => {
            handleCloseModal();
        });
    };

    // 导入数据
    const handleImport = () => {
        // 处理导入逻辑
        console.log("导入数据");
    };

    const handleShowProgressNotice = () => {
        const contentNode = (
            <Space size={12} align="center">
                <Progress fieldid="e6d472a8-ae32-45d0-9b6b-4afd628eea5f" type="circle" width={40} percent={importProgress} />
                <span>{lang.templateByUuid("UID:P_UBL-FE_1CA6430A04780016", "正在处理") /* "正在处理" */}</span>
            </Space>
        );
        noticeKey.current = Date.now();
        newNotification.notice({
            description: contentNode,
            key: noticeKey.current,
            duration: null,
            closable: false,
        });
        return;
    };
    const uploadImport = {
        name: "file",
        action: `/iuap-ipaas-dataintegration/gwmanage/gwportal/diwork/erpdata/datamapping/excel/import`,
        data: {},
        xscf: true,
        accept: ".xls,.xlsx",
        withCredentials: true,
        showUploadList: false,
        beforeUpload: () => {
            setImportProgress(0);
            handleShowProgressNotice();
            return true;
        },
        onChange: (_info) => {
            if (_info.file?.status === "done") {
                console.log("info ---", _info);
                if (_info.file?.response?.status === 0) {
                    Error(_info.file.response.msg);
                    newNotification.removeNotice(noticeKey.current);
                }
            }
        },
    };
    // 导出数据
    const handleExport = async () => {
        const res = await autoServiceMessage({
            service: ownerService.downloadExcelTempService(),
            success: lang.templateByUuid("UID:P_UBL-FE_1CF09D180458000A", "下载成功") /* "下载成功" */,
        });
        if (res) {
            downloadFile(res);
        }
    };

    // 查看数据源映射
    const viewDataSourceMapping = () => {
        // 处理查看数据源映射逻辑
    };

    // 定义表格列
    const columns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20076E580428003D", "编码") /* "编码" */,
            dataIndex: "typetag",
            key: "typetag",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20076E5804280041", "名称") /* "名称" */,
            dataIndex: "typename",
            key: "typename",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20076E5804280039", "描述") /* "描述" */,
            dataIndex: "typeDesc",
            key: "typeDesc",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20076E580428003C", "创建人") /* "创建人" */,
            dataIndex: "creator",
            key: "creator",
            render: (text) => text || "—", //@notranslate
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20076E5804280042", "创建时间") /* "创建时间" */,
            dataIndex: "creationtime",
            key: "creationtime",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20076E580428003A", "数据记录时间") /* "数据记录时间" */,
            dataIndex: "",
            key: "",
        },
    ];
    const hoverContent = (record) => {
        return (
            <Space size={6}>
                <Button size="sm" colors="dark" onClick={() => viewDataDetail(record)}>
                    {lang.templateByUuid("UID:P_UBL-FE_20076E5804280040", "数据详情") /* "数据详情" */}
                </Button>
                {
                    // 1 模型  0 集成方案   注：集成方案时不允许修改模型编码与名称
                    record.datatype === 1 && record?.ytenantId !== "0" && (
                        <Button size="sm" colors="dark" onClick={() => handleEdit(record)}>
                            {lang.templateByUuid("UID:P_UBL-FE_20076E5804280038", "编辑") /* "编辑" */}
                        </Button>
                    )
                }
            </Space>
        );
    };
    return (
        <PageLayout style={{ background: "#ffffff", height: "100%" }}>
            <PageTopHelp
                title={lang.templateByUuid("UID:P_UBL-FE_20076E580428003F", "数据映射关系") /* "数据映射关系" */}
                descList={[
                    lang.templateByUuid(
                        "UID:P_UBL-FE_20076E580428003E",
                        "支持数据同步场景的通用数据映射关系管理，可支持导入、导出、新增、修改、删除等，可应用在数据翻译、数据层监控问题排查等场景" //@notranslate
                    ) /* "支持数据同步场景的通用数据映射关系管理，可支持导入、导出、新增、修改、删除等，可应用在数据翻译、数据层监控问题排查等场景" */,
                ]}
                code="appLicationV2Help"
                linkList={[]}
            />
            <div className={styles["map-relations-list"]}>
                <div className={styles["header"]}>
                    <div>
                        <Input
                            placeholder={lang.templateByUuid("UID:P_UBL-FE_20076E5804280043", "名称或者编码的搜索") /* "名称或者编码的搜索" */}
                            type="search"
                            allowClear
                            onSearch={handleSearch}
                            style={{ width: "300px" }}
                        />
                    </div>
                    <div>
                        <Space size={8}>
                            <Button colors="primary" onClick={handleAdd}>
                                <Icon type="uf-plus" /> {lang.templateByUuid("UID:P_UBL-FE_20076E580428003B", "新增") /* "新增" */}
                            </Button>

                            {/* <Upload {...uploadImport}>
                                <Button onClick={handleImport}>
                                    <Icon type="uf-upload" /> 导入
                                </Button>
                            </Upload>
                            <Button onClick={handleExport}>
                                <Icon type="uf-download" /> 导出
                            </Button>
                            <Button onClick={viewDataSourceMapping}>查看迁移清单</Button> */}
                        </Space>
                    </div>
                </div>

                <div className={styles["map-relations-table"]} ref={bodyRef}>
                    <Table
                        rowKey="typetag"
                        columns={columns}
                        data={dataSource.list}
                        pagination={false}
                        hoverContent={hoverContent}
                        scroll={{ y: tableScrollY }}
                        bodyStyle={{ height: tableScrollY }}
                    />
                </div>

                {pagination && pagination.total > 0 && (
                    <div style={{ textAlign: "right" }}>
                        <Pagination
                            showSizeChanger
                            showQuickJumper
                            current={pagination.activePage || 1}
                            pageSize={pagination.pageSize || 10}
                            total={pagination.total || 0}
                            onChange={(pageNo, pageSize) => {
                                ownerStore.getDataSource({ pageNo, pageSize });
                            }}
                            onShowSizeChange={(current, size) => {
                                ownerStore.getDataSource({ pageNo: 1, pageSize: size });
                            }}
                        />
                    </div>
                )}

                {showModal && <AddModal visible={showModal} onCancel={handleCloseModal} onOk={handleUpdateDataType} record={record} />}
            </div>
        </PageLayout>
    );
};

export default withRouter(IndexView);
