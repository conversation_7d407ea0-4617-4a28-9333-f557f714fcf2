import { getInvokeService, defineService } from "utils/service";

export const getList = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/gwportal/diwork/datatype/queryPageList",
        },
        data
    );
};

// 保存模型接口
export const updateDataTypeService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/gwportal/diwork/datatype/add",
        },
        data
    );
};

export const downloadExcelTempService = function () {
    let service = defineService({
        method: "GET",
        path: `/gwportal/diwork/erpdata/datamapping/excel/download`,
        showLoading: false,
        responseType: "blob",
    });
    return service.invoke();
};
