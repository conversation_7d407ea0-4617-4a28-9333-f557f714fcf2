import React, { Component } from "react";
import IndexView from "./components/IndexView";
import { inject, observer } from "mobx-react";
import Store, { storeKey } from "./store";
import { storeKey as homeStoreKey } from "../list/store";

@inject((rootStore) => {
    let ownerStore = rootStore[storeKey];
    let homeStore = rootStore[homeStoreKey];
    return {
        ownerState: ownerStore.toJS(),
        ownerStore: ownerStore,
        homeState: homeStore.toJS(),
    };
})
@observer
class Container extends Component {
    constructor(props) {
        super(props);
    }

    render() {
        return <IndexView {...this.props} />;
    }
}
export default Container;
