import { getInvokeService, defineService } from "utils/service";

export const getList = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/gwportal/diwork/erpdata/task/pageWithFlow",
        },
        {
            key: lang.templateByUuid("UID:P_UBL-FE_20076E5804280044", "物料业务计量") /* "物料业务计量" */,
            pageNo: 1,
            pageSize: 20,
        }
    );
};

// 保存模型接口
export const updateDataTypeService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/gwportal/diwork/datatype/add",
        },
        data
    );
};

export const downloadExcelTempService = function () {
    let service = defineService({
        method: "GET",
        path: `/gwportal/diwork/erpdata/datamapping/excel/download`,
        showLoading: false,
        responseType: "blob",
    });
    return service.invoke();
};
export const downloadExcelErrorService = function (schemeCode) {
    let service = defineService({
        method: "GET",
        path: `/gwportal/diwork/erpdata/datamapping/excel/import/downloadFailData?viewtag=${schemeCode}`,
        showLoading: false,
        responseType: "blob",
    });
    return service.invoke();
};
