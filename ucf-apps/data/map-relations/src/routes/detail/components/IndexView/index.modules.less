.map-relations-list {
  display: flex;
  flex-direction: column;
  height: 100%;

  .header {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    background: #fff;
    align-items: center;
  }

  .map-relations-table {
    flex: 1;
    overflow: auto;
    background: #fff;
    margin-top: 10px;

    :global {
      .u-table-tbody > tr:hover > td {
        background: #ebf7ff;
      }
    }
  }
}

