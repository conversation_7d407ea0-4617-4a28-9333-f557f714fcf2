import React from "react";
import DataMapModal from "ucf-apps/data/sync-task/src/routes/list/components/DataMapModal";
import withRouter from "decorator/withRouter";
import { Header } from "components/PageView";

const IndexView = (props) => {
    const { selectedData } = props.homeState;
    return (
        <div>
            <Header
                back={() => {
                    props.navigate(-1);
                }}
                title={selectedData?.typename}
            />
            <DataMapModal taskInfo={{ ...selectedData, startSchemeCode: selectedData.typetag }} />
        </div>
    );
};

export default withRouter(IndexView);
