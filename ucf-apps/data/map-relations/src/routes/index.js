import React from "react";
import { RoutesRender } from "core";

import ListContainer from "./list/container";
import DetailContainer from "./detail/container";

import getConfigProvider from "components/ConfigProvider";
import config from "../config";

import CommonStore, { storeKey as commonStoreKey } from "./list/store";
import core from "core";

const commonStore = new CommonStore();
core.addStore({
    storeKey: commonStoreKey,
    store: commonStore,
});

const ConfigProvider = getConfigProvider(config);

const routes = [
    {
        path: "/",
        exact: true,
        component: ListContainer,
    },
    {
        path: "/list",
        component: ListContainer,
    },
    {
        path: "/detail",
        component: DetailContainer,
    },
];

const Routes = () => {
    return (
        <ConfigProvider>
            <RoutesRender routes={routes} />
        </ConfigProvider>
    );
};
export default Routes;
