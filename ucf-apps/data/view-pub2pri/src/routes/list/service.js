import { defineService } from "utils/service";

export const getErpConnectorList = function (data) {
    let service = defineService({
        method: "GET",
        path: "/gwmanage/erpdata/erpconnector/page",
    });

    return service.invoke(data);
};

export const deleteErpConnector = function (data) {
    let service = defineService({
        method: "POST",
        path: "/gwmanage/erpdata/erpconnector/del/" + data,
    });

    return service.invoke();
};

/**
 * 新建erp连接器
 * @param string code
 * @param string name
 * @param string erptype
 * @param string viewcode
 */
export const addErpconnector = function (data) {
    let service = defineService({
        method: "POST",
        path: "/gwmanage/erpdata/erpconnector/add",
    });

    return service.invoke(data);
};

/**
 * 编辑erp连接器
 * @param string id
 * @param string code
 * @param string name
 */
export const editErpconnector = function (data) {
    let service = defineService({
        method: "POST",
        path: "/gwmanage/erpdata/erpconnector/edit/" + data.id,
    });

    return service.invoke(data);
};

/**
 * 查询绑定事件的档案信息
 */
export const getDataTypeService = function (data) {
    let service = defineService({
        method: "GET",
        path: "/gwmanage/erpdata/datatype/getDataWithEvent",
    });

    return service.invoke(data);
};

/**
 * 分页获取视图数据
 * @param string key 过滤条件
 * @param string pageNo 页码
 * @param string pageSize 每页条数
 */
export const getDataViewService = function (data) {
    let service = defineService({
        method: "GET",
        path: "/gwmanage/erpdata/dataview/page",
    });

    return service.invoke(data);
};
