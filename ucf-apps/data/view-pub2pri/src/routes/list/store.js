import { observable, action, toJS, reaction, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { defaultListMap } from "utils/pageListUtils";
import { autoServiceMessage } from "utils/service";
import * as ownerService from "./service";
const initState = {
    dataSource: {
        ...defaultListMap,
    },
    typeList: [],
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    getDataSource = async (query) => {
        if (!query) {
            let dataSource = this.state;
            dataSource = this.toJS(dataSource);
            dataSource.pageNo = 1;
            // eslint-disable-next-line no-self-assign
            dataSource.pageSize = dataSource.pageSize;
            this.changeState({ dataSource });
        }

        let res = await autoServiceMessage({
            service: ownerService.getErpConnectorList(query),
        });

        if (res) {
            this.changeState({
                dataSource: {
                    list: res.data.items,
                    pageNo: res.data.pageIndex,
                    pageSize: res.data.pageSize,
                    total: res.data.itemCount,
                    totalPages: res.data.pageCount,
                },
            });
        }
    };

    del = async (id) => {
        let res = await autoServiceMessage({
            service: ownerService.deleteErpConnector(id),
            success: lang.templateByUuid("UID:P_UBL-FE_2009604004280194", "删除成功") /* "删除成功" */,
        });

        if (res) {
            this.getDataSource();
        }
    };

    addErpconnectorService = async (param) => {
        let res = await autoServiceMessage({
            service: ownerService.addErpconnector(param),
            success: lang.templateByUuid("UID:P_UBL-FE_2009604004280195", "操作成功") /* "操作成功" */,
        });

        if (res) {
            return true;
        }
    };

    editErpconnectorService = async (param) => {
        let res = await autoServiceMessage({
            service: ownerService.editErpconnector(param),
            success: lang.templateByUuid("UID:P_UBL-FE_2009604004280195", "操作成功") /* "操作成功" */,
        });

        if (res) {
            return true;
        }
    };

    getDataType = async () => {
        let res = await autoServiceMessage({
            service: ownerService.getDataTypeService(),
        });

        if (res) {
            this.changeState({ typeList: res.data });
        }
    };
}
/**
 * 根据具体模块修改 storeKey 为保证唯一性
 * storeKey 由 模块文件夹名称 + 页面名称 + 'Store' 小驼峰
 *
 * storeKey是作为当前 Store 的唯一键， storeKey与Store的实例会在 rootStore或者StoreMap中以 键值对的 形式存在
 * 1. 用于在模块的 storeMap（模块routes/store.js）中注册并初始化当前Store
 * 2. 用于在container的 inject 中关联当前Store的实例时，从模块的storeMap或者rootStore中获取对应store实例
 * */
export const storeKey = "systemConnectorMaListStore";

export default Store;
