import React, { Component, Fragment } from "react";
import { <PERSON>ton, FormControl, Modal, FormList, Icon, Select } from "components/TinperBee";
import UcfReferInput from "../Refer/index";
import "./index.less";
const Option = Select.Option;
const FormItem = FormList.FormItem;
const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
};
const AddModal = class AddModal extends Component {
    constructor(props) {
        super(props);
        this.state = {
            viewcode: [],
            show: false,
        };
        this.form = React.createRef();
    }

    componentDidMount() {
        let { data } = this.props;
        if (data) {
            let viewcode = [];
            "tableviews" in data &&
                data.tableviews.map((item) => {
                    viewcode.push({ tableview: item });
                });
            this.setState({ viewcode, show: data.linktype !== 0 ? true : false });
            this.form.current.setFieldsValue({
                typetag: data.typetag,
                callbackurl: data.callbackurl,
                erptype: data.erptype,
                linktype: data.linktype,
                interfaceurl: data.interfaceurl || "",
                patchMethod: data.patchMethod || "",
                patchInterface: data.patchInterface || "",
            });
        }
    }

    componentDidUpdate(prevProps, prevState) {
        let { data } = this.props;
        if (this.state.show != prevState.show) {
            this.form.current.setFieldsValue({
                interfaceurl: data.interfaceurl || "",
                patchMethod: data.patchMethod || "",
                patchInterface: data.patchInterface || "",
            });
        }
    }

    handleSave = () => {
        let { save, data, typeList } = this.props;
        let { viewcode } = this.state;

        this.form.current.validateFields().then((values) => {
            let typename = null;
            typeList.find((item) => {
                if (item.typetag == values.typetag) {
                    typename = item.typename;
                }
            });
            let tableviews = [];
            viewcode.forEach((item) => {
                tableviews.push(item.tableview);
            });
            let param = {
                defdocname: typename,
                tableviews: tableviews,
                typetag: values.typetag,
                callbackurl: values.callbackurl,
                erptype: values.erptype,
                linktype: values.linktype,
                interfaceurl: values.interfaceurl || "",
                patchMethod: values.patchMethod || "",
                patchInterface: values.patchInterface || "",
            };
            if ("id" in data) {
                param.id = data.id;
            }
            save(param);
        });

        // this.form.current.validateFields((err, values) => {
        //   console.log(err)
        //   console.log(values)
        //   if (!err) {
        //     let typename = null;
        //     typeList.find((item) => {
        //       if (item.typetag == values.typetag) {
        //         typename = item.typename;
        //       }
        //     });
        //     let tableviews = [];
        //     viewcode.forEach(item => {
        //       tableviews.push(item.tableview);
        //     });
        //     let param = {
        //       defdocname: typename,
        //       tableviews: tableviews,
        //       typetag: values.typetag,
        //       callbackurl: values.callbackurl,
        //       erptype: values.erptype,
        //       linktype: values.linktype,
        //       interfaceurl: values.interfaceurl || '',
        //       patchMethod: values.patchMethod || '',
        //       patchInterface: values.patchInterface || ''
        //     };
        //     if ('id' in data) {
        //       param.id = data.id;
        //     }
        //     save(param);
        //   }
        // });
    };

    handleChange = (value) => {
        console.log(value);
        let viewcode = [];
        value.forEach((item) => viewcode.push({ tableview: item.tableview }));
        this.setState({ viewcode });
    };

    handleSelect = (value) => {
        if (value !== 0) {
            this.setState({ show: true });
        } else {
            this.setState({ show: false });
        }
    };

    render() {
        let { showModal, close, typeList, data } = this.props;
        let { viewcode, show } = this.state;
        return (
            <Modal
                fieldid="ublinker-routes-list-components-Modal-editModal-4772819-Modal"
                show={showModal}
                onHide={close}
                backdropClosable={false}
                className="ucg-ma-modal modal-add"
            >
                <Modal.Header closeButton>
                    <Modal.Title>
                        {
                            "id" in data
                                ? lang.templateByUuid("UID:P_UBL-FE_2009604004280480", "编辑") /* "编辑" */
                                : lang.templateByUuid("UID:P_UBL-FE_200960400428047F", "新增") /* "新增" */
                        }
                    </Modal.Title>
                </Modal.Header>

                <Modal.Body>
                    <FormList
                        fieldid="ublinker-routes-list-components-Modal-editModal-3681430-FormList"
                        size="sm"
                        ref={this.form}
                        // layout='inline'
                        {...formItemLayout}
                        className="form"
                    >
                        <FormItem
                            fieldid="ublinker-routes-list-components-Modal-editModal-8791082-FormItem"
                            label={lang.templateByUuid("UID:P_UBL-FE_2009604004280486", "档案") /* "档案" */}
                            name="typetag"
                            rules={[
                                {
                                    required: true,
                                    message: lang.templateByUuid("UID:P_UBL-FE_200960400428048A", "请选择档案") /* "请选择档案" */,
                                },
                            ]}
                        >
                            <Select fieldid="UCG-FE-routes-list-components-Modal-editModal-5182933-Select">
                                {typeList.map((item) => {
                                    return (
                                        <Option fieldid="UCG-FE-routes-list-components-Modal-editModal-8817240-Option" key={item.typetag} value={item.typetag}>
                                            {item.typename}
                                        </Option>
                                    );
                                })}
                            </Select>
                        </FormItem>

                        <FormItem
                            fieldid="ublinker-routes-list-components-Modal-editModal-5030925-FormItem"
                            label={lang.templateByUuid("UID:P_UBL-FE_2009604004280482", "回调接口") /* "回调接口" */}
                            name="callbackurl"
                            rules={[
                                {
                                    required: true,
                                    message: lang.templateByUuid("UID:P_UBL-FE_2009604004280483", "请输入回调接口") /* "请输入回调接口" */,
                                },
                            ]}
                        >
                            <FormControl fieldid="ublinker-routes-list-components-Modal-editModal-2208955-FormControl" />
                        </FormItem>

                        <FormItem
                            fieldid="ublinker-routes-list-components-Modal-editModal-2227857-FormItem"
                            label={lang.templateByUuid("UID:P_UBL-FE_2009604004280489", "ERP类型") /* "ERP类型" */}
                            name="erptype"
                            rules={[
                                {
                                    required: true,
                                    message: lang.templateByUuid("UID:P_UBL-FE_2009604004280478", "请选择ERP类型") /* "请选择ERP类型" */,
                                },
                            ]}
                        >
                            <Select fieldid="ublinker-routes-list-components-Modal-editModal-9187225-Select">
                                <Option fieldid="UCG-FE-routes-list-components-Modal-editModal-8477830-Option" value="nc">
                                    NC
                                </Option>
                                <Option fieldid="UCG-FE-routes-list-components-Modal-editModal-5983624-Option" value="nccloud">
                                    NCCLoud
                                </Option>
                                <Option fieldid="UCG-FE-routes-list-components-Modal-editModal-1457354-Option" value="u8cloud">
                                    U8Cloud
                                </Option>
                                <Option fieldid="UCG-FE-routes-list-components-Modal-editModal-3699607-Option" value="u8">
                                    U8
                                </Option>
                                <Option fieldid="UCG-FE-routes-list-components-Modal-editModal-63691-Option" value="u9">
                                    U9
                                </Option>
                            </Select>
                        </FormItem>

                        <FormItem
                            fieldid="ublinker-routes-list-components-Modal-editModal-6252291-FormItem"
                            label={lang.templateByUuid("UID:P_UBL-FE_2009604004280488", "回调连接类型") /* "回调连接类型" */}
                            name="linktype"
                            rules={[
                                {
                                    required: true,
                                    message: lang.templateByUuid("UID:P_UBL-FE_2009604004280477", "请选择回调连接类型") /* "请选择回调连接类型" */,
                                },
                            ]}
                        >
                            <Select fieldid="ublinker-routes-list-components-Modal-editModal-4605324-Select" onChange={this.handleSelect}>
                                <Option fieldid="UCG-FE-routes-list-components-Modal-editModal-8704256-Option" value={0}>
                                    {lang.templateByUuid("UID:P_UBL-FE_200960400428047B", "友企连") /* "友企连" */}
                                </Option>
                                <Option fieldid="UCG-FE-routes-list-components-Modal-editModal-8877140-Option" value={1}>
                                    {lang.templateByUuid("UID:P_UBL-FE_200960400428047E", "开放平台") /* "开放平台" */}
                                </Option>
                                <Option fieldid="UCG-FE-routes-list-components-Modal-editModal-8986143-Option" value={2}>
                                    Rest
                                </Option>
                            </Select>
                        </FormItem>

                        {show ? (
                            <FormItem
                                fieldid="ublinker-routes-list-components-Modal-editModal-2825248-FormItem"
                                label={lang.templateByUuid("UID:P_UBL-FE_2009604004280484", "回调地址") /* "回调地址" */}
                                name="interfaceurl"
                                rules={[
                                    {
                                        required: true,
                                        message: lang.templateByUuid("UID:P_UBL-FE_2009604004280487", "请输入回调地址") /* "请输入回调地址" */,
                                    },
                                ]}
                            >
                                <FormControl fieldid="ublinker-routes-list-components-Modal-editModal-15567-FormControl" />
                            </FormItem>
                        ) : (
                            <Fragment>
                                <FormItem
                                    fieldid="ublinker-routes-list-components-Modal-editModal-154963-FormItem"
                                    label={lang.templateByUuid("UID:P_UBL-FE_2009604004280479", "补丁接口地址") /* "补丁接口地址" */}
                                    name="patchInterface"
                                    rules={[
                                        {
                                            required: true,
                                            message: lang.templateByUuid("UID:P_UBL-FE_200960400428047C", "请输入补丁接口地址") /* "请输入补丁接口地址" */,
                                        },
                                    ]}
                                >
                                    <FormControl fieldid="ublinker-routes-list-components-Modal-editModal-7059777-FormControl" />
                                </FormItem>
                                <FormItem
                                    fieldid="ublinker-routes-list-components-Modal-editModal-6189108-FormItem"
                                    label={lang.templateByUuid("UID:P_UBL-FE_2009604004280481", "补丁方法名称") /* "补丁方法名称" */}
                                    name="patchMethod"
                                >
                                    <FormControl fieldid="ublinker-routes-list-components-Modal-editModal-2411907-FormControl" />
                                </FormItem>
                            </Fragment>
                        )}

                        <FormItem
                            fieldid="ublinker-routes-list-components-Modal-editModal-9320747-FormItem"
                            label={lang.templateByUuid("UID:P_UBL-FE_2009604004280485", "互斥视图") /* "互斥视图" */}
                        >
                            {/* <Label fieldid="UCG-FE-routes-list-components-Modal-editModal-8111676-Label" style={{ verticalAlign: 'top' }}>互斥视图</Label> */}
                            <UcfReferInput
                                buttonType={"a"}
                                searchField={"tableview"}
                                selectedList={viewcode}
                                onOk={(value) => this.handleChange(value)}
                                onTagClose={(value) => {
                                    this.handleChange(value);
                                }}
                            ></UcfReferInput>
                        </FormItem>
                    </FormList>
                </Modal.Body>

                <Modal.Footer>
                    <Button
                        fieldid="ublinker-routes-list-components-Modal-editModal-8950979-Button"
                        onClick={close}
                        colors="secondary"
                        style={{ marginRight: 16 }}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_200960400428047A", "取消") /* "取消" */}
                    </Button>
                    <Button fieldid="ublinker-routes-list-components-Modal-editModal-7445504-Button" onClick={this.handleSave} colors="primary">
                        {lang.templateByUuid("UID:P_UBL-FE_200960400428047D", "保存") /* "保存" */}
                    </Button>
                </Modal.Footer>
            </Modal>
        );
    }
};

export default AddModal;
