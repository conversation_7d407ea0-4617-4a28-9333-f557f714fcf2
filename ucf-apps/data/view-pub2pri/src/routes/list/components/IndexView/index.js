import React, { Component, Fragment } from "react";
import { Content } from "components/PageView";
import { FormControl, Button, Modal, Icon } from "components/TinperBee";
import Grid from "components/TinperBee/Grid";
import Highlight from "components/Highlight";
import EditModal from "../Modal/editModal.js";
import "./index.less";
import withRouter from "decorator/withRouter";

@withRouter
class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            showModal: false,
            selectedData: [],
            selectValue: "",
        };
    }

    onPagiSelect = (active) => {
        this.props.ownerStore.getDataSource({
            pageNo: active,
        });
    };

    onDataNumSelect = (index, value) => {
        this.props.ownerStore.getDataSource({
            pageSize: value,
            pageNo: 1,
        });
    };

    handleSearch = () => {
        let { selectValue } = this.state;
        this.props.ownerStore.getDataSource({ key: selectValue });
    };

    handleDelete = (record) => {
        Modal.confirm({
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280425", "确定要删除这条数据吗？") /* "确定要删除这条数据吗？" */,
            content: lang.templateByUuid("UID:P_UBL-FE_2009604004280426", "删除后将不能恢复。") /* "删除后将不能恢复。" */,
            onOk: async () => {
                this.props.ownerStore.del(record.id);
            },
        });
    };

    goDetail = (record) => {
        this.props.navigate({
            pathname: "/info",
            search: "id=" + record.id,
        });
    };

    handleAdd = () => {
        this.setState({ showModal: true });
    };

    handleEdit = (record) => {
        this.setState({ showModal: true, selectedData: record });
    };

    handleChange = (value) => {
        this.setState({ selectValue: value });
    };

    onKeyDown = (event) => {
        if (event.keyCode == 13) {
            this.handleSearch();
        }
    };

    clearValue = () => {
        this.setState({ selectValue: "" });
    };

    close = () => {
        this.setState({ showModal: false, selectedData: [] });
    };

    save = async (param) => {
        let { editErpconnectorService, addErpconnectorService, getDataSource } = this.props.ownerStore;
        let res = null;
        if ("id" in param) {
            res = await editErpconnectorService(param);
        } else {
            res = await addErpconnectorService(param);
        }
        if (res) {
            getDataSource({
                pageSize: 10,
                pageNo: 1,
            });
            this.close();
        }
    };

    render() {
        let { ownerState } = this.props;
        let { dataSource, typeList } = ownerState;
        let { showModal, selectedData, selectValue } = this.state;
        let pagination = {
            total: dataSource.itemCount,
            items: dataSource.pageCount,
            pageSize: dataSource.pageSize,
            activePage: dataSource.pageNo,
            onSelect: this.onPagiSelect,
            onDataNumSelect: this.onDataNumSelect,
        };
        let columns = [
            {
                title: lang.templateByUuid("UID:P_UBL-FE_2009604004280429", "档案名称") /* "档案名称" */,
                dataIndex: "defdocname",
                key: "defdocname",
                width: "20%",
                render: (content) => {
                    return <Highlight content={content} keyword={selectValue} />;
                },
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_200960400428042D", "ERP类型") /* "ERP类型" */,
                dataIndex: "erptype",
                key: "erptype",
                width: "10%",
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_2009604004280430", "数据查询接口") /* "数据查询接口" */,
                dataIndex: "callbackurl",
                key: "callbackurl",
                width: "20%",
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_2009604004280422", "互斥视图") /* "互斥视图" */,
                dataIndex: "tableviews",
                key: "tableviews",
                width: "20%",
                render: (value) => {
                    let text = "";
                    value &&
                        value.map((item, index) => {
                            if (index == value.length - 1) {
                                text = text + item;
                            } else {
                                text = text + item + "，"; //@notranslate
                            }
                        });
                    return text;
                },
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_200960400428042A", "回调连接类型") /* "回调连接类型" */,
                dataIndex: "linktype",
                key: "linktype",
                width: "10%",
                render: (value) => {
                    let text = "";
                    switch (value) {
                        case 0:
                            text = lang.templateByUuid("UID:P_UBL-FE_200960400428042E", "友企连") /* "友企连" */;
                            break;
                        case 1:
                            text = lang.templateByUuid("UID:P_UBL-FE_200960400428042F", "开放平台") /* "开放平台" */;
                            break;
                        case 2:
                            text = "REST";
                            break;
                    }
                    return text;
                },
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_2009604004280424", "操作") /* "操作" */,
                dataIndex: "action",
                key: "action",
                width: "10%",
                render: (value, record) => {
                    return (
                        <Fragment>
                            <a fieldid="ublinker-routes-list-components-IndexView-index-2776016-a" onClick={this.handleEdit.bind(null, record)}>
                                {lang.templateByUuid("UID:P_UBL-FE_2009604004280428", "编辑") /* "编辑" */}
                            </a>{" "}
                            &nbsp;&nbsp;&nbsp;
                            <a fieldid="ublinker-routes-list-components-IndexView-index-1137008-a" onClick={this.goDetail.bind(null, record)}>
                                {lang.templateByUuid("UID:P_UBL-FE_200960400428042B", "依赖接口") /* "依赖接口" */}
                            </a>
                            &nbsp;&nbsp;&nbsp;
                            <a fieldid="ublinker-routes-list-components-IndexView-index-3386272-a" onClick={this.handleDelete.bind(null, record)}>
                                {lang.templateByUuid("UID:P_UBL-FE_200960400428042C", "删除") /* "删除" */}
                            </a>
                        </Fragment>
                    );
                },
            },
        ];

        return (
            <Fragment>
                <Content>
                    <div className="search-bar">
                        <Button
                            fieldid="ublinker-routes-list-components-IndexView-index-3324186-Button"
                            className="search-bar-right"
                            colors="primary"
                            onClick={this.handleAdd}
                        >
                            {lang.templateByUuid("UID:P_UBL-FE_2009604004280423", "新增") /* "新增" */}
                        </Button>
                        <FormControl
                            fieldid="ublinker-routes-list-components-IndexView-index-4009474-FormControl"
                            className="search-bar-input"
                            placeholder={lang.templateByUuid("UID:P_UBL-FE_2009604004280427", "搜索档案名称") /* "搜索档案名称" */}
                            value={selectValue}
                            onChange={this.handleChange}
                            onKeyDown={this.onKeyDown}
                            suffix={
                                <Fragment>
                                    {selectValue.length > 0 ? (
                                        <div className="search-bar-icon-left" onClick={this.clearValue}>
                                            <Icon fieldid="ublinker-routes-list-components-IndexView-index-2876444-Icon" type="uf-close-c" />
                                        </div>
                                    ) : null}
                                    <div className="search-bar-icon-right" onClick={this.handleSearch}>
                                        <Icon fieldid="ublinker-routes-list-components-IndexView-index-975612-Icon" type="uf-search" />
                                    </div>
                                </Fragment>
                            }
                        />
                    </div>
                    <Grid
                        fieldid="ublinker-routes-list-components-IndexView-index-7413012-Grid"
                        data={dataSource.list}
                        columns={columns}
                        pagination={dataSource.itemCount > 10 ? pagination : null}
                    />

                    {showModal ? <EditModal showModal={showModal} data={selectedData} typeList={typeList} close={this.close} save={this.save} /> : null}
                </Content>
            </Fragment>
        );
    }
}

export default IndexView;
