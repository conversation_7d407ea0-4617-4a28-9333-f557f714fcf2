import React, { Component } from "react";
import { WithInput as WithInputBase } from "components/Refer";
import * as ownerService from "../../service";
import Highlight from "components/Highlight";

class UcfReferInput extends Component {
    constructor(props) {
        super(props);
        this.state = {
            requestParam: "",
        };
    }

    render() {
        let columns = [
            {
                title: lang.templateByUuid("UID:P_UBL-FE_200960400428023C", "视图名称") /* "视图名称" */,
                dataIndex: "tableview",
                key: "tableview",
                width: "30%",
                render: (content) => {
                    let { requestParam } = this.state;
                    return <Highlight content={content} keyword={requestParam} />;
                },
            },
            { title: lang.templateByUuid("UID:P_UBL-FE_200960400428023E", "版本") /* "版本" */, dataIndex: "viewversion", key: "viewversion", width: "20%" },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_200960400428023F", "视图编码") /* "视图编码" */,
                dataIndex: "datatype",
                key: "datatype",
                width: "40%",
                render: (value) => {
                    return value.typename;
                },
            },
        ];
        let referProps = {
            columns: columns,
            hasPage: true,
            isMulti: true,
            title: lang.templateByUuid("UID:P_UBL-FE_200960400428023D", "互斥视图") /* "互斥视图" */,
            service: ownerService.getDataViewService,
            pkKey: "tableview",
            nameKey: "tableview",
            componentClass: "textarea",
            onSearch: (v) => {
                this.setState({ requestParam: v });
            },
        };
        return <WithInputBase {...this.props} {...referProps} />;
    }
}
export default UcfReferInput;
