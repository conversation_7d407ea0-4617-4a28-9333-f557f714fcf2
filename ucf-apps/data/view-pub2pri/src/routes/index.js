import React from "react";
import { RoutesRender } from "core";

import ListContainer from "./list/container";
import InfoContainer from "./info/container";

import getConfigProvider from "components/ConfigProvider";
import config from "../config";
const ConfigProvider = getConfigProvider(config);

const routes = [
    {
        path: "/",
        exact: true,
        component: ListContainer,
    },
    {
        path: "/list",
        component: ListContainer,
    },
    {
        path: "/info",
        component: InfoContainer,
    },
];

const Routes = () => {
    return (
        <ConfigProvider fieldid="UCG-FE-data-view-pub2pri-src-routes-index-5295137-ConfigProvider">
            <RoutesRender routes={routes} />
        </ConfigProvider>
    );
};
export default Routes;
