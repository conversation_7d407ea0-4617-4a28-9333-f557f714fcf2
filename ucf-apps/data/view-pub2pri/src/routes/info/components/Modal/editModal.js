import React, { Component, Fragment } from "react";
import { Button, FormControl, Modal, FormList, Radio, Message, Select, Icon } from "components/TinperBee";
import Grid from "components/TinperBee/Grid";
// import FormList from 'components/TinperBee/Form';
import UcfReferInput from "../Refer/index";
import "./index.less";
const RadioGroup = Radio.Group;
const Option = Select.Option;
const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 12 },
};
const EditModal = class EditModal extends Component {
    constructor(props) {
        super(props);
        this.state = {
            master: "0",
            attachment: "0",
        };
        this.form = React.createRef();
    }

    columns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042801CF", "事件类型") /* "事件类型" */,
            dataIndex: "eventtype",
            key: "eventtype",
            width: "40%",
            render: (value, record, index) => {
                return (
                    <Select
                        fieldid="ublinker-routes-info-components-Modal-editModal-1186132-Select"
                        value={value}
                        onChange={this.handleChange.bind(null, "eventtype", index)}
                    >
                        {this.props.eventList.map((item) => {
                            return (
                                <Option fieldid="UCG-FE-routes-info-components-Modal-editModal-8374130-Option" key={item.code} value={item.code}>
                                    {item.name}
                                </Option>
                            );
                        })}
                    </Select>
                );
            },
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042801C0", "关联VO对照") /* "关联VO对照" */,
            dataIndex: "vomapcode",
            key: "vomapcode",
            width: "40%",
            render: (value, record, index) => {
                return (
                    <UcfReferInput
                        buttonType={"a"}
                        searchField={"key"}
                        selectedList={[{ billtype: value, name: value }]}
                        onOk={(v) => this.handleChange("vomapcode", index, v[0].billtype)}
                    ></UcfReferInput>
                );
            },
        },
        // {
        //   title: '链路', dataIndex: 'linktype', key: 'linktype', width: '20%',
        //   render: (value, record, index) => {
        //     return (
        //       <Select fieldid="ublinker-routes-info-components-Modal-editModal-5144424-Select"
        //         value={value}
        //         onChange={this.handleChange.bind(null, 'linktype', index)}
        //       >
        //         <Option fieldid="UCG-FE-routes-info-components-Modal-editModal-2075933-Option" value='open'>开放平台</Option>
        //         <Option fieldid="UCG-FE-routes-info-components-Modal-editModal-6368178-Option" value='ubl'>友企连</Option>
        //       </Select>
        //     );
        //   }
        // },
        // {
        //   title: '接口地址', dataIndex: 'interfaceurl', key: 'interfaceurl', width: '30%',
        //   render: (value, record, index) => {
        //     return (
        //       <FormControl fieldid="ublinker-routes-info-components-Modal-editModal-3850458-FormControl"
        //         value={value}
        //         onChange={this.handleChange.bind(null, 'interfaceurl', index)}
        //       />
        //     );
        //   }
        // },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042801C6", "操作") /* "操作" */,
            dataIndex: "action",
            key: "action",
            width: "20%",
            render: (value, record, index) => {
                return (
                    <Fragment>
                        <a fieldid="ublinker-routes-info-components-Modal-editModal-5434345-a" onClick={this.handleDelete.bind(null, index)}>
                            {lang.templateByUuid("UID:P_UBL-FE_20096040042801CC", "删除") /* "删除" */}
                        </a>
                    </Fragment>
                );
            },
        },
    ];

    initData = {
        eventtype: "",
        vomapcode: "",
        // linktype: 'open',
        // interfaceurl: ''
    };

    BasicInfo = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042801BF", "名称") /* "名称" */,
            field: "name",
            type: "text",
            require: true,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042801C2", "编码") /* "编码" */,
            field: "code",
            type: "text",
            require: true,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042801CB", "执行顺序") /* "执行顺序" */,
            field: "order",
            type: "text",
            require: true,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042801D0", "云端主键") /* "云端主键" */,
            field: "cloudpk",
            type: "text",
            require: false,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042801D2", "ERP主键") /* "ERP主键" */,
            field: "erppk",
            type: "text",
            require: false,
            //validate: phoneReg
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042801BE", "是否主档") /* "是否主档" */,
            field: "master",
            type: "radio",
            require: true,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042801C1", "附件") /* "附件" */,
            field: "attachment",
            type: "radio",
            require: true,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042801CA", "附件调用地址") /* "附件调用地址" */,
            field: "attachmenturl",
            type: "text",
            require: false,
        },
    ];

    componentDidMount() {
        let { dataSource } = this.props;
        this.BasicInfo.map((item) => {
            if (item.type == "text") {
                this.form.current.setFieldsValue({ [item.field]: dataSource[item.field] });
            }
        });
        this.setState({ attachment: dataSource.attachment, master: dataSource.master });
    }

    handleChange = (type, index, value) => {
        let { dataSource, changeDataSource } = this.props;
        dataSource.erpconnectormapitfs[index][type] = value;
        changeDataSource(dataSource);
    };

    handleAdd = () => {
        let { dataSource, changeDataSource } = this.props;
        if (!dataSource.erpconnectormapitfs || dataSource.erpconnectormapitfs.length == 0) {
            let erpconnectormapitfs = [];
            erpconnectormapitfs.push(this.initData);
            dataSource.erpconnectormapitfs = erpconnectormapitfs;
        } else {
            dataSource.erpconnectormapitfs.push(this.initData);
        }
        changeDataSource(dataSource);
    };

    handleDelete = (index) => {
        Modal.confirm({
            title: lang.templateByUuid("UID:P_UBL-FE_20096040042801CD", "确定要删除这条数据吗？") /* "确定要删除这条数据吗？" */,
            content: lang.templateByUuid("UID:P_UBL-FE_20096040042801CE", "删除后将不能恢复。") /* "删除后将不能恢复。" */,
            onOk: async () => {
                let { dataSource, changeDataSource } = this.props;
                dataSource.erpconnectormapitfs.splice(index, 1);
                changeDataSource(dataSource);
            },
        });
    };

    onConfirm = () => {
        let { save, dataSource, changeDataSource, close, id } = this.props;
        let { attachment, master } = this.state;
        this.form.current.validateFields().then(async (values) => {
            let con = true;
            if (dataSource.erpconnectormapitfs) {
                dataSource.erpconnectormapitfs.map((item) => {
                    if (con == false) {
                        return null;
                    }
                    for (let key in this.initData) {
                        if (item[key].length == 0) {
                            Message.create({
                                content: lang.templateByUuid("UID:P_UBL-FE_20096040042801C3", "关键事件类型不允许有空值！") /* "关键事件类型不允许有空值！" */,
                                color: "danger",
                            });
                            con = false;
                            break;
                        }
                    }
                });
                if (!con) {
                    return null;
                }
            }
            dataSource.erpconnectorid = id;
            dataSource.name = values.name;
            dataSource.code = values.code;
            dataSource.order = values.order;
            dataSource.cloudpk = values.cloudpk;
            dataSource.erppk = values.erppk;
            dataSource.master = master;
            dataSource.attachment = attachment;
            dataSource.attachmenturl = values.attachmenturl || "";
            changeDataSource(dataSource);
            let res = await save();
            if (res) {
                close();
            }
        });
        // this.props.form.validateFields(async (err, values) => {
        //   if (err) {
        //     // for (let key in err) {
        //     //   this.BasicInfo.find((item) => {
        //     //     if (item.field == key) {
        //     //       Message.create({ content: item.title + '不能为空', color: 'danger' });
        //     //     }
        //     //   });
        //     // }
        //     return null;
        //   } else {
        //     let con = true;
        //     if (dataSource.erpconnectormapitfs) {
        //       dataSource.erpconnectormapitfs.map((item) => {
        //         if (con == false) {
        //           return null;
        //         }
        //         for (let key in this.initData) {
        //           if (item[key].length == 0) {
        //             Message.create({ content: '关键事件类型不允许有空值！', color: 'danger' });
        //             con = false;
        //             break;
        //           }
        //         }
        //       });
        //       if (!con) {
        //         return null;
        //       }
        //     }
        //     dataSource.erpconnectorid = id;
        //     dataSource.name = values.name;
        //     dataSource.code = values.code;
        //     dataSource.order = values.order;
        //     dataSource.cloudpk = values.cloudpk;
        //     dataSource.erppk = values.erppk;
        //     dataSource.master = master;
        //     dataSource.attachment = attachment;
        //     dataSource.attachmenturl = values.attachmenturl || '';
        //     changeDataSource(dataSource);
        //     let res = await save();
        //     if (res) {
        //       close();
        //     }
        //   }
        // });
    };

    render() {
        // let { getFieldProps, getFieldError } = this.props.form;
        let { showModal, close, dataSource } = this.props;
        const self = this;
        return (
            <Modal
                fieldid="ublinker-routes-info-components-Modal-editModal-1249871-Modal"
                show={showModal}
                onHide={close}
                className="ucg-ma-modal modal-edit"
                size={"xlg"}
            >
                <Modal.Header closeButton>
                    <Modal.Title>{lang.templateByUuid("UID:P_UBL-FE_20096040042801D1", "依赖接口") /* "依赖接口" */}</Modal.Title>
                </Modal.Header>

                <Modal.Body>
                    <FormList
                        fieldid="ublinker-routes-info-components-Modal-editModal-5077870-FormList"
                        layoutOpt={{ md: 4, xs: 4 }}
                        ref={this.form}
                        // layout='inline'
                        {...formItemLayout}
                    >
                        {this.BasicInfo.map((item) => {
                            let rules = [];
                            if (item.require) {
                                rules.push({
                                    required: true,
                                    message: (
                                        <span>
                                            <Icon fieldid="ublinker-routes-info-components-Modal-editModal-6006128-Icon" type="uf-exc-t"></Icon>
                                            <span>{item.title + lang.templateByUuid("UID:P_UBL-FE_20096040042801C7", "不能为空") /* "不能为空" */}</span>
                                        </span>
                                    ),
                                });
                            }
                            return (
                                // eslint-disable-next-line react/jsx-key
                                <FormList.Item label={item.title} name={item.field} rules={rules}>
                                    {item.type == "text" ? (
                                        <FormControl
                                            fieldid="ublinker-routes-info-components-Modal-editModal-5123440-FormControl"
                                            // className={getFieldError(item.field) ? 'form-error relative' : ''}
                                            // {...getFieldProps(item.field, {
                                            //   validateTrigger: 'onBlur',
                                            //   rules: rules
                                            // })}
                                        />
                                    ) : (
                                        <RadioGroup
                                            selectedValue={this.state[item.field]}
                                            // {
                                            // ...getFieldProps(item.field, {
                                            //   initialValue: '0',
                                            // }
                                            onChange={(value) => {
                                                self.setState({ [item.field]: value });
                                            }}
                                            // )}
                                        >
                                            <Radio fieldid="ublinker-routes-info-components-Modal-editModal-6988828-Radio" value="1">
                                                {lang.templateByUuid("UID:P_UBL-FE_20096040042801C4", "是") /* "是" */}
                                            </Radio>
                                            <Radio fieldid="ublinker-routes-info-components-Modal-editModal-9937541-Radio" defaultChecked={true} value="0">
                                                {lang.templateByUuid("UID:P_UBL-FE_20096040042801C8", "否") /* "否" */}
                                            </Radio>
                                        </RadioGroup>
                                    )}
                                    {/* <div className='error'>
                      {getFieldError(item.field)}
                    </div> */}
                                </FormList.Item>
                            );
                        })}
                    </FormList>

                    <Grid
                        fieldid="ublinker-routes-info-components-Modal-editModal-3521521-Grid"
                        header={
                            <Fragment>
                                <div className="grid-header-title">
                                    {lang.templateByUuid("UID:P_UBL-FE_20096040042801BC", "关键事件类型") /* "关键事件类型" */}
                                </div>
                                <Button
                                    fieldid="ublinker-routes-info-components-Modal-editModal-6527635-Button"
                                    onClick={this.handleAdd}
                                    bordered
                                    className="grid-header-right"
                                >
                                    {lang.templateByUuid("UID:P_UBL-FE_20096040042801BD", "新增") /* "新增" */}
                                </Button>
                            </Fragment>
                        }
                        data={dataSource.erpconnectormapitfs || []}
                        columns={this.columns}
                    />
                </Modal.Body>

                <Modal.Footer>
                    <Button
                        fieldid="ublinker-routes-info-components-Modal-editModal-4795948-Button"
                        onClick={close}
                        colors="secondary"
                        style={{ marginRight: 16 }}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_20096040042801C5", "取消") /* "取消" */}
                    </Button>
                    <Button fieldid="ublinker-routes-info-components-Modal-editModal-1153263-Button" onClick={this.onConfirm} colors="primary">
                        {lang.templateByUuid("UID:P_UBL-FE_20096040042801C9", "确定") /* "确定" */}
                    </Button>
                </Modal.Footer>
            </Modal>
        );
    }
};

export default EditModal;
