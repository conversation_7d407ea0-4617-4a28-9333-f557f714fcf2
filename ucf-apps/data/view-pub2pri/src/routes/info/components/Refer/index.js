import React, { Component } from "react";
import { WithInput as WithInputBase } from "components/Refer";
import * as ownerService from "../../service";
import Highlight from "components/Highlight";

class UcfReferInput extends Component {
    constructor(props) {
        super(props);
        this.state = {
            requestParam: "",
        };
    }

    render() {
        let columns = [
            {
                title: lang.templateByUuid("UID:P_UBL-FE_2009604004280355", "单据类型") /* "单据类型" */,
                dataIndex: "billtype",
                key: "billtype",
                render: (content) => {
                    let { requestParam } = this.state;
                    return <Highlight content={content} keyword={requestParam} />;
                },
            },
        ];

        let referProps = {
            columns: columns,
            hasPage: true,
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280356", "关联VO对照") /* "关联VO对照" */,
            service: ownerService.getVOChange,
            pkKey: "pk_id",
            onSearch: (v) => {
                this.setState({ requestParam: v });
            },
        };
        return <WithInputBase {...this.props} {...referProps} />;
    }
}

export default UcfReferInput;
