import React, { Component, Fragment } from "react";
import { Header, Content } from "components/PageView";
import { <PERSON><PERSON>, <PERSON>dal, FormList } from "components/TinperBee";
import Grid from "components/TinperBee/Grid";
import EditModal from "../Modal/editModal.js";
import "./index.less";
import query from "query-string";
import withRouter from "decorator/withRouter.js";

const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
};
@withRouter
class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            showModal: false,
        };
        this.form = React.createRef();
    }

    initData = {
        code: "",
        name: "",
        description: "",
        order: "",
        master: "",
        attachment: 1,
        attachmenturl: "",
        cloudpk: "",
        erppk: "",
        erpconnectorid: "",
        erpconnectormapitfs: [],
    };

    columns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280244", "子业务对象") /* "子业务对象" */,
            dataIndex: "name",
            key: "name",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280246", "对象编码") /* "对象编码" */,
            dataIndex: "code",
            key: "code",
        },
        { title: lang.templateByUuid("UID:P_UBL-FE_200960400428024C", "执行顺序") /* "执行顺序" */, dataIndex: "order", key: "order" },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_200960400428024D", "是否主档") /* "是否主档" */,
            dataIndex: "master",
            key: "master",
            render: (value) => {
                return value == "1"
                    ? lang.templateByUuid("UID:P_UBL-FE_200960400428024F", "是") /* "是" */
                    : lang.templateByUuid("UID:P_UBL-FE_2009604004280250", "否") /* "否" */;
            },
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280253", "附件") /* "附件" */,
            dataIndex: "attachment",
            key: "attachment",
            render: (value) => {
                return value == "1"
                    ? lang.templateByUuid("UID:P_UBL-FE_200960400428024F", "是") /* "是" */
                    : lang.templateByUuid("UID:P_UBL-FE_2009604004280250", "否") /* "否" */;
            },
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_2009604004280242", "操作") /* "操作" */,
            dataIndex: "action",
            key: "action",
            render: (value, record) => {
                return (
                    <Fragment>
                        <a fieldid="ublinker-routes-info-components-IndexView-index-8103671-a" onClick={this.open.bind(null, record)}>
                            {lang.templateByUuid("UID:P_UBL-FE_2009604004280245", "编辑") /* "编辑" */}
                        </a>
                        &nbsp;&nbsp;&nbsp;
                        <a fieldid="ublinker-routes-info-components-IndexView-index-5151826-a" onClick={this.handleDelete.bind(null, record)}>
                            {lang.templateByUuid("UID:P_UBL-FE_2009604004280249", "删除") /* "删除" */}
                        </a>
                    </Fragment>
                );
            },
        },
    ];

    componentDidMount() {
        const params = query.parse(this.props.location.search);
        const { id } = params;
        let { ownerStore } = this.props;
        ownerStore.changeState({ id: id });
        if (id && id.length > 0) {
            ownerStore.getAllEventType();
            ownerStore.getVOChange();
            ownerStore.getDataSource(id);
            ownerStore.getDataSourceMap(id);
        }
    }

    onPagiSelect = (active) => {
        this.props.ownerStore.getDataSource({
            pageNo: active,
        });
    };

    onDataNumSelect = (index, value) => {
        this.props.ownerStore.getDataSource({
            pageSize: value,
            pageNo: 1,
        });
    };

    open = async (record) => {
        if ("id" in record) {
            let { getErpconnectorMapDetail } = this.props.ownerStore;
            await getErpconnectorMapDetail(record.id);
        }
        this.setState({ showModal: true });
    };

    close = () => {
        this.props.ownerStore.handleChange("eventDataSource", this.initData);
        this.setState({ showModal: false });
    };

    // save = async () => {

    // }

    // cancel = () => {
    //   this.props.history.goBack();
    // }

    openSave = async () => {
        let { saveErpconnectorMap } = this.props.ownerStore;
        let res = await saveErpconnectorMap();
        if (res) {
            return true;
        }
    };

    handleDelete = async (record) => {
        Modal.confirm({
            title: lang.templateByUuid("UID:P_UBL-FE_200960400428024A", "确定要删除这条数据吗？") /* "确定要删除这条数据吗？" */,
            content: lang.templateByUuid("UID:P_UBL-FE_200960400428024B", "删除后将不能恢复。") /* "删除后将不能恢复。" */,
            onOk: async () => {
                let { ownerState, ownerStore } = this.props;
                let { id } = ownerState;
                let { delErpconnectorMap, getDataSourceMap } = ownerStore;
                let res = await delErpconnectorMap(record.id);
                if (res) {
                    getDataSourceMap(id);
                }
            },
        });
    };

    renderERPType = (type) => {
        let text = null;
        switch (type) {
            case "nc6":
                text = "NC6";
                break;
            case "nc5":
                text = "NC5";
                break;
            case "u9":
                text = "U9";
                break;
            case "u8":
                text = "U8";
                break;
            case "u8cloud":
                text = "U8Cloud";
                break;
            case "nccloud":
                text = "NCCloud";
                break;
            case "outsideerp":
                text = lang.templateByUuid("UID:P_UBL-FE_200960400428024E", "外部ERP") /* "外部ERP" */;
                break;
            case "tplus":
                text = "T+";
                break;
            default:
                break;
        }
        return <p className="card-text">{text}</p>;
    };

    render() {
        let { ownerState, ownerStore } = this.props;
        let { showModal } = this.state;
        let { dataSource, eventDataSource, query, eventList, voList, id } = ownerState;
        let { handleChange } = ownerStore;
        let pagination = {
            total: dataSource.total,
            items: dataSource.totalPages,
            pageSize: dataSource.pageSize,
            activePage: dataSource.pageNo,
            onSelect: this.onPagiSelect,
            onDataNumSelect: this.onDataNumSelect,
        };
        return (
            <Fragment>
                <Header title={lang.templateByUuid("UID:P_UBL-FE_2009604004280247", "连接器管理") /* "连接器管理" */} back></Header>
                <Content
                    contentTop={
                        <FormList
                            fieldid="ublinker-routes-info-components-IndexView-index-1375272-FormList"
                            layoutOpt={{ md: 3, xs: 3 }}
                            ref={this.form}
                            // layout='inline'
                            {...formItemLayout}
                        >
                            <FormList.Item
                                label={lang.templateByUuid("UID:P_UBL-FE_2009604004280251", "档案") /* "档案" */}
                                // labelCol={100}
                            >
                                <p className="card-text">{query.name}</p>
                            </FormList.Item>

                            <FormList.Item
                                label={lang.templateByUuid("UID:P_UBL-FE_2009604004280255", "回调接口") /* "回调接口" */}
                                // labelCol={100}
                            >
                                <p className="card-text">{query.callbackurl}</p>
                            </FormList.Item>

                            <FormList.Item
                                label={lang.templateByUuid("UID:P_UBL-FE_2009604004280243", "ERP类型") /* "ERP类型" */}
                                // labelCol={100}
                            >
                                {this.renderERPType(query.erptype)}
                            </FormList.Item>

                            <FormList.Item
                                label={lang.templateByUuid("UID:P_UBL-FE_2009604004280248", "互斥视图") /* "互斥视图" */}
                                // labelCol={100}
                            >
                                <p className="card-text">{query.viewcode && query.viewcode.join(",")}</p>
                            </FormList.Item>
                        </FormList>
                    }
                >
                    <Grid
                        fieldid="ublinker-routes-info-components-IndexView-index-948055-Grid"
                        header={
                            <div className="grid-header">
                                <div className="grid-header-title">
                                    {lang.templateByUuid("UID:P_UBL-FE_2009604004280252", "关联依赖接口") /* "关联依赖接口" */}
                                </div>
                                <Button
                                    fieldid="ublinker-routes-info-components-IndexView-index-3802217-Button"
                                    onClick={this.open}
                                    bordered
                                    className="grid-header-right"
                                >
                                    {lang.templateByUuid("UID:P_UBL-FE_2009604004280254", "新增") /* "新增" */}
                                </Button>
                            </div>
                        }
                        data={dataSource.list}
                        columns={this.columns}
                        pagination={dataSource.itemCount > 10 ? pagination : null}
                    />

                    {showModal ? (
                        <EditModal
                            showModal={showModal}
                            close={this.close}
                            save={this.openSave}
                            dataSource={eventDataSource}
                            eventList={eventList}
                            changeDataSource={(v) => handleChange("eventDataSource", v)}
                            voList={voList}
                            id={id}
                        />
                    ) : null}
                </Content>

                {/*<div className='footer-fixed footer-align-left'>
          <Button fieldid="ublinker-routes-info-components-IndexView-index-2111243-Button" colors='primary' onClick={this.save}>保存</Button>
          <Button fieldid="ublinker-routes-info-components-IndexView-index-4523263-Button" colors='secondary' onClick={this.cancel}>取消</Button>
        </div>*/}
            </Fragment>
        );
    }
}

export default IndexView;
