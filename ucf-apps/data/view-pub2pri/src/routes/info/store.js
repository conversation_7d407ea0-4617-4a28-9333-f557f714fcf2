import { observable, computed, makeObservable } from "mobx";
import DefaultS<PERSON> from "utils/defaultStore";
import { defaultListMap } from "utils/pageListUtils";
import { autoServiceMessage } from "utils/service";
import * as ownerService from "./service";
const initState = {
    dataSource: {
        ...defaultListMap,
    },
    eventDataSource: {
        code: "",
        name: "",
        description: "",
        order: "",
        master: "",
        attachment: "",
        attachmenturl: "",
        cloudpk: "",
        erppk: "",
        erpconnectorid: "",
        erpconnectormapitfs: [
            // {
            //   eventtype: '',
            //   vomapid: '',
            //   vomapcode: '',
            //   linktype: '',
            //   interfaceurl: '',
            //   erpconnectormapid: ''
            // }
        ],
    },
    id: "", //申请id
    query: {
        name: "",
        code: "",
        callbackurl: "",
        erptype: "",
        viewcode: [],
    },
    eventList: [],
    voList: [],
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    init = () => {
        this.state = initState;
    };

    //获取数据
    getDataSource = async (id) => {
        let res = await autoServiceMessage({
            service: ownerService.getErpConnectorDetail(id),
        });

        if (res) {
            let { defdocname, defdocid, callbackurl, erptype, tableviews } = res.data;
            let query = {
                name: defdocname,
                code: defdocid,
                callbackurl: callbackurl,
                erptype: erptype,
                viewcode: tableviews,
            };
            this.changeState({ query });
        }
        return true;
    };

    /**
     * 分页获取erp连接器依赖
     */
    getDataSourceMap = async (id) => {
        let res = await autoServiceMessage({
            service: ownerService.getErpConnectorMap({ erpconnectorid: id }),
        });

        if (res) {
            this.changeState({
                dataSource: {
                    list: res.data.items,
                    pageNo: res.data.pageIndex,
                    pageSize: res.data.pageSize,
                    total: res.data.itemCount,
                    totalPages: res.data.pageCount,
                },
            });
        }
        return true;
    };

    getAllEventType = async () => {
        let res = await autoServiceMessage({
            service: ownerService.getAllEventType(),
        });

        if (res) {
            this.changeState({ eventList: res.data });
        }
    };

    getVOChange = async () => {
        let res = await autoServiceMessage({
            service: ownerService.getVOChange(),
        });

        if (res) {
            this.changeState({ voList: res.data });
        }
    };

    delErpconnectorMap = async (id) => {
        let res = await autoServiceMessage({
            service: ownerService.delErpconnectorMap(id),
            success: lang.templateByUuid("UID:P_UBL-FE_20096040042801D3", "操作成功") /* "操作成功" */,
        });

        if (res) {
            return true;
        }
    };

    getErpconnectorMapDetail = async (id) => {
        let res = await autoServiceMessage({
            service: ownerService.getErpconnectorMapDetail(id),
        });

        if (res) {
            this.changeState({ eventDataSource: res.data });
        }
    };

    handleChange = (type, value) => {
        this.changeState({ [type]: value });
    };

    saveErpconnectorMap = async () => {
        let { id, eventDataSource } = this.state;
        let res = await autoServiceMessage({
            service: "id" in eventDataSource ? ownerService.editErpconnectorMap(eventDataSource) : ownerService.addErpconnectorMap(eventDataSource),
            success: lang.templateByUuid("UID:P_UBL-FE_20096040042801D3", "操作成功") /* "操作成功" */,
        });

        if (res) {
            await this.getDataSourceMap(id);
            return true;
        }
    };
}

export const storeKey = "systemConnectorMaInfoStore";
export default Store;
