/**
 * 数据网关 视图详情信息页面
 * match.params.type -页面类型 add添加|edit编辑|view查看
 * location.search.id -编辑或查看态 调用详情接口 参数
 * */

import React, { Component } from "react";
import IndexView from "./components/IndexView";
import { inject, observer } from "mobx-react";
import { storeKey } from "./store";
@inject((rootStore) => {
    let ownerStore = rootStore[storeKey];
    let { detail } = ownerStore;
    let { ...props } = detail;
    return {
        ...props,
        ownerState: ownerStore.toJS(),
        ownerStore: ownerStore,
    };
})
@observer
class Container extends Component {
    constructor(props) {
        super(props);
    }

    render() {
        return <IndexView {...this.props} />;
    }
}
export default Container;
