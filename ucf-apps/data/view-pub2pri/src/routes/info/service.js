import { defineService } from "utils/service";

/**
 * 根据id获取连接器详情
 * @param string id 连接器id
 */
export const getErpConnectorDetail = function (data) {
    let service = defineService({
        method: "GET",
        path: `/gwmanage/erpdata/erpconnector/get/${data}`,
    });

    return service.invoke();
};

/**
 * 分页获取erp连接器依赖
 */
export const getErpConnectorMap = function (data) {
    let service = defineService({
        method: "GET",
        path: "/gwmanage/erpdata/erpconnectormap/page",
    });

    return service.invoke(data);
};

/**
 * 获取全部事件类型数据
 */
export const getAllEventType = function (data) {
    let service = defineService({
        method: "GET",
        path: "/gwmanage/erpdata/eventtype/listAll",
    });

    return service.invoke(data);
};

/**
 * 获取关联对照数据
 */
export const getVOChange = function (data) {
    let service = defineService({
        method: "GET",
        path: "/gwmanage/gwapp/erpdata/vochange/page",
    });

    return service.invoke(data);
};

/**
 * 删除erp连接器依赖
 * @param string erpconnectorid
 */
export const delErpconnectorMap = function (data) {
    let service = defineService({
        method: "POST",
        path: `/gwmanage/erpdata/erpconnectormap/del/${data}`,
    });

    return service.invoke(data);
};

/**
 * 新增erp连接器依赖
 */
export const addErpconnectorMap = function (data) {
    let service = defineService({
        method: "POST",
        path: "/gwmanage/erpdata/erpconnectormap/add",
    });

    return service.invoke(data);
};

/**
 * 编辑erp连接器依赖
 */
export const editErpconnectorMap = function (data) {
    let service = defineService({
        method: "POST",
        path: `/gwmanage/erpdata/erpconnectormap/edit/${data.id}`,
    });

    return service.invoke(data);
};

/**
 * 根据id获取erp连接器依赖详情
 * @param string erpconnectorid
 */
export const getErpconnectorMapDetail = function (data) {
    let service = defineService({
        method: "GET",
        path: `/gwmanage/erpdata/erpconnectormap/get/${data}`,
    });

    return service.invoke();
};
