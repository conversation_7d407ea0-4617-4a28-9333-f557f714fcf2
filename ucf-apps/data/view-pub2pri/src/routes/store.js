/**
 * 汇总模块中的各个业务 store，形成一个总的 storeMap
 * 1. 方便模块中各个业务页面之间数据共享和交互
 * 2. 方便将业务模块store合并的 rootStore 中（待规划）
 * */

import ListStore, { storeKey as listStoreKey } from "./list/store";
import InfoStore, { storeKey as infoStoreKey } from "./info/store";
import mixCore from "core";

mixCore.addStore([
    { storeKey: listStoreKey, store: new ListStore() },
    { storeKey: infoStoreKey, store: new InfoStore() },
]);
