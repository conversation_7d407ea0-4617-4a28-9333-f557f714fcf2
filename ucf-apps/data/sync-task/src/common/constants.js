/*
 * @Author: your name
 * @Date: 2021-04-15 17:40:03
 * @LastEditTime: 2021-06-09 10:48:05
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \ublinker-fe\ucf-apps\data\sync-task\src\common\constants.js
 */
import { createEnum } from "constants/utils";

export const initTaskTypes = createEnum([
    {
        code: "u9",
        name: "U9",
        hide: true,
    },
    {
        code: "u8",
        name: "U8",
        hide: true,
    },
    {
        code: "u8cloud",
        name: "U8Cloud",
        hide: true,
    },
    {
        code: "nc",
        name: "NC",
        hide: true,
    },
    {
        code: "nccloud",
        name: "NCCloud",
        hide: true,
    },
    {
        code: "outsideerp",
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804F2", "外部ERP数据") /* "外部ERP数据" */,
        hide: true,
    },
    {
        code: "tplus",
        name: "T+",
    },
]);

export const taskStatusTypes = createEnum([
    {
        code: "0",
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804F5", "未运行") /* "未运行" */,
        cls: "task-status-not",
    },
    {
        code: "1",
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804E9", "运行中") /* "运行中" */,
        cls: "task-status-doing",
    },
    {
        code: "2",
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804EB", "运行成功") /* "运行成功" */,
        cls: "task-status-success",
    },
    {
        code: "3",
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804EE", "运行失败") /* "运行失败" */,
        cls: "task-status-fail",
    },
    {
        code: "4",
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804F0", "部分成功") /* "部分成功" */,
        // name: "运行成功(含异常数据)",
        // tagName: "部分成功",
        cls: "task-status-success-second",
    },
    {
        code: "5",
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804F1", "运行冲突") /* "运行冲突" */,
        cls: "task-status-fail",
    },
    {
        code: "6",
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804F3", "跳过") /* "跳过" */,
        cls: "task-status-jump",
    },
    {
        code: "7",
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804F4", "等待") /* "等待" */,
        cls: "task-status-success-second",
    },
    {
        code: "8",
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804E8", "数据处理中") /* "数据处理中" */,
        cls: "task-status-success-second",
    },
]);

export const dataMapSearchKeys = createEnum([
    {
        code: "erp",
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804EA", "来源系统主键") /* "来源系统主键" */,
    },
    {
        code: "saas",
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804EC", "目标系统主键") /* "目标系统主键" */,
    },
    {
        code: "code",
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804ED", "编码") /* "编码" */,
    },
    {
        code: "name",
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804EF", "名称") /* "名称" */,
    },
]);
