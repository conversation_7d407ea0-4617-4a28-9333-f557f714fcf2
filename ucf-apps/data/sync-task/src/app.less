// @import '~@tinper/next-ui/dist/tinper-next.css';
@import "~styles/common.less";

.data-task-run-status {
  display: inline-block;
  min-width: 60px;
  height: 20px;
  font-size: 12px;
  line-height: 20px;
  text-align: center;
  border: 1px solid;
  border-radius: 2px;
  padding: 0 5px;
}

.task-status-success {
  border-color: #18b681;
  color: #18b681;
  background: rgba(242, 255, 251, 1);
}

.task-status-success-second {
  border: 1px solid #f77d00;
  color: #f77d00;
  background: #fffaef;
}

.task-status-doing {
  border-color: #588ce9;
  color: #588ce9;
  background: rgba(245, 249, 255, 1);
}

.task-status-fail {
  border-color: #f47576;
  color: #ee2223;
  background: rgba(254, 244, 244, 1);
}
.task-status-jump {
  border-color: #a878f6;
  color: #a878f6;
  background-color: #faf7ff;
}
.task-status-not {
  border-color: #adb4bc;
  color: #708091;
  background: rgba(246, 250, 255, 1);
}

body.enus {
  .data-task-run-status {
    min-width: 80px;
  }
}
