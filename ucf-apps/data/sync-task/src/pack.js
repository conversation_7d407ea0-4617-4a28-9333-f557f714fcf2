export default {
    zhcn: {
        MIX_UBL_ALL_UBL_FE_LOC_00050198: "字段名称不能为空",
        MIX_UBL_ALL_UBL_FE_LOC_00050133: "新增主键映射",
        MIX_UBL_ALL_UBL_FE_LOC_00050197: "关闭",
        MIX_UBL_ALL_UBL_FE_LOC_00050132: "请输入视图SQL",
        MIX_UBL_ALL_UBL_FE_LOC_00050196: "设置时间表达式",
        MIX_UBL_ALL_UBL_FE_LOC_00050131: "所属组织",
        MIX_UBL_ALL_UBL_FE_LOC_00050195: "网关ID",
        MIX_UBL_ALL_UBL_FE_LOC_00050130: "输入时间表达式",
        MIX_UBL_ALL_UBL_FE_LOC_00050194: "未匹配：",
        MIX_UBL_ALL_UBL_FE_LOC_00050193: "主键类型",
        MIX_UBL_ALL_UBL_FE_LOC_00050192: "已匹配：",
        MIX_UBL_ALL_UBL_FE_LOC_00050191: "公共档案关联",
        MIX_UBL_ALL_UBL_FE_LOC_00050190: "请输入ERP主键",
        MIX_UBL_ALL_UBL_FE_LOC_00050129: "外部ERP数据",
        MIX_UBL_ALL_UBL_FE_LOC_00050128: "网关连接实例ID",
        MIX_UBL_ALL_UBL_FE_LOC_00050127: "ERP主键",
        MIX_UBL_ALL_UBL_FE_LOC_00050126: "视图版本",
        MIX_UBL_ALL_UBL_FE_LOC_00050125: "任务管理",
        MIX_UBL_ALL_UBL_FE_LOC_00050189: "数据详情",
        MIX_UBL_ALL_UBL_FE_LOC_00050124: "SQL条件",
        MIX_UBL_ALL_UBL_FE_LOC_00050188: "初始化ERP任务",
        MIX_UBL_ALL_UBL_FE_LOC_00050187: "视图标签",
        MIX_UBL_ALL_UBL_FE_LOC_00050122: "运行中",
        MIX_UBL_ALL_UBL_FE_LOC_00050186: "是否启用增量",
        MIX_UBL_ALL_UBL_FE_LOC_00050121: "视图所属应用",
        MIX_UBL_ALL_UBL_FE_LOC_00050185: "字段列名",
        MIX_UBL_ALL_UBL_FE_LOC_00050120: "请输入视图版本或数据视图名称",
        MIX_UBL_ALL_UBL_FE_LOC_00050184: "未运行",
        MIX_UBL_ALL_UBL_FE_LOC_00050025: "档案数据",
        MIX_UBL_ALL_UBL_FE_LOC_00050183:
            '查询条件，无需输入where关键词，示例：code\u003d"2212" and ts\u003e@lastupdatetime[@lastupdatetime为最后一次成功同步时间',
        MIX_UBL_ALL_UBL_FE_LOC_00050182: "耗时",
        MIX_UBL_ALL_UBL_FE_LOC_00050181: "此任务未关联公共档案",
        MIX_UBL_ALL_UBL_FE_LOC_00050180: "初始化\u003c%\u003d initName %\u003e任务成功",
        MIX_UBL_ALL_UBL_FE_LOC_00050083: "视图列定义",
        MIX_UBL_ALL_UBL_FE_LOC_00050216: "查看任务",
        MIX_UBL_ALL_UBL_FE_LOC_00050215: "数据映射",
        MIX_UBL_ALL_UBL_FE_LOC_00050214: "按条件拉取数据",
        MIX_UBL_ALL_UBL_FE_LOC_00050213: "运行失败",
        MIX_UBL_ALL_UBL_FE_LOC_00050217: "运行冲突",
        MIX_UBL_ALL_UBL_FE_LOC_00050119: "数据同步任务已删除",
        MIX_UBL_ALL_UBL_FE_LOC_00050212: "ERP主键名称",
        MIX_UBL_ALL_UBL_FE_LOC_00050118: "是否在线",
        MIX_UBL_ALL_UBL_FE_LOC_00050211: "输入视图SQL",
        MIX_UBL_ALL_UBL_FE_LOC_00050117: "切换成功后，同步任务需手动触发或单独创建定时任务",
        MIX_UBL_ALL_UBL_FE_LOC_00050210: "编辑数据视图",
        MIX_UBL_ALL_UBL_FE_LOC_00050116: "初始化\u003c%\u003d initName %\u003e任务失败",
        MIX_UBL_ALL_UBL_FE_LOC_00050115: "SQL语句",
        MIX_UBL_ALL_UBL_FE_LOC_00050114: "该同步任务没有视图标签",
        MIX_UBL_ALL_UBL_FE_LOC_00050179: "最后一次任务结束时间",
        MIX_UBL_ALL_UBL_FE_LOC_00050178: "分页大小",
        MIX_UBL_ALL_UBL_FE_LOC_00050220: "修改人",
        MIX_UBL_ALL_UBL_FE_LOC_00050219: "修改时间",
        MIX_UBL_ALL_UBL_FE_LOC_00050221: "方案管理",
        MIX_UBL_ALL_UBL_FE_LOC_00050222: "集成对象属性",
        MIX_UBL_ALL_UBL_FE_LOC_00050223: "映射关系",
        MIX_UBL_ALL_UBL_FE_LOC_00050224: "启动方案调整",
        MIX_UBL_ALL_UBL_FE_LOC_2021891451: "集成方案调整",
        MIX_UBL_ALL_UBL_FE_LOC_00050177: "字符",

        MIX_UBL_ALL_UBL_FE_LOC_00050175: "模糊匹配",
        MIX_UBL_ALL_UBL_FE_LOC_00050174: "显示未匹配的数据",
        MIX_UBL_ALL_UBL_FE_LOC_00050173: "请输入ERP主键名称",
        MIX_UBL_ALL_UBL_FE_LOC_00050172: "查看详细数据",
        MIX_UBL_ALL_UBL_FE_LOC_00050077: "查看所有",
        MIX_UBL_ALL_UBL_FE_LOC_00050171: "视图管理",
        MIX_UBL_ALL_UBL_FE_LOC_00050072: "字段类型",
        MIX_UBL_ALL_UBL_FE_LOC_00050209: "最后一次任务执行时间",
        MIX_UBL_ALL_UBL_FE_LOC_00050208: "参照",
        MIX_UBL_ALL_UBL_FE_LOC_00050207: "枚举",
        MIX_UBL_ALL_UBL_FE_LOC_00050206: "切换定时执行",
        MIX_UBL_ALL_UBL_FE_LOC_00050205: "参照本地",
        MIX_UBL_ALL_UBL_FE_LOC_00050204: "翻译参照",
        MIX_UBL_ALL_UBL_FE_LOC_00050203: "查看日志",
        MIX_UBL_ALL_UBL_FE_LOC_00050202: "执行",
        MIX_UBL_ALL_UBL_FE_LOC_216231735: "问题数据重传",
        MIX_UBL_ALL_UBL_FE_LOC_216231739: "重传",
        MIX_UBL_ALL_UBL_FE_LOC_00050201: "数据来源",
        MIX_UBL_ALL_UBL_FE_LOC_00050200: "日期",
        MIX_UBL_ALL_UBL_FE_LOC_00050169: "参照档案类型",
        MIX_UBL_ALL_UBL_FE_LOC_00050168: "是否定时任务",
        MIX_UBL_ALL_UBL_FE_LOC_00050167: "视图条件定义",
        MIX_UBL_ALL_UBL_FE_LOC_00050166: "成功",
        MIX_UBL_ALL_UBL_FE_LOC_00050165: "枚举映射",
        MIX_UBL_ALL_UBL_FE_LOC_00050164: "切换任务网关",
        MIX_UBL_ALL_UBL_FE_LOC_00050163: "初始化\u003c%\u003d initName %\u003e任务",
        MIX_UBL_ALL_UBL_FE_LOC_00050162: "切换成功后，同步任务由平台预设定时任务执行，同步时间为每日凌晨1点至5点之间",
        MIX_UBL_ALL_UBL_FE_LOC_00050161: "请输入Saas主键",
        MIX_UBL_ALL_UBL_FE_LOC_00050160: "网关切换成功",
        MIX_UBL_ALL_UBL_FE_LOC_00050159: "整数",
        MIX_UBL_ALL_UBL_FE_LOC_00050158: "浮点数",
        MIX_UBL_ALL_UBL_FE_LOC_00050157: "创建定时任务",
        MIX_UBL_ALL_UBL_FE_LOC_00050156: "一键保存",
        MIX_UBL_ALL_UBL_FE_LOC_00050155: "字段名称",
        MIX_UBL_ALL_UBL_FE_LOC_00050154: "关联成功",
        MIX_UBL_ALL_UBL_FE_LOC_00050153: "选择NC版本",
        MIX_UBL_ALL_UBL_FE_LOC_00050152: "视图任务编辑",
        MIX_UBL_ALL_UBL_FE_LOC_00050151: "任务会在执行完本次分页同步后停止",
        MIX_UBL_ALL_UBL_FE_LOC_00050150: "运行成功",
        MIX_UBL_ALL_UBL_FE_LOC_00050149: "查看失败",
        MIX_UBL_ALL_UBL_FE_LOC_00050148: "手动匹配",
        MIX_UBL_ALL_UBL_FE_LOC_00050147: "是否立即执行?",
        MIX_UBL_ALL_UBL_FE_LOC_00050146: "匹配结果",
        MIX_UBL_ALL_UBL_FE_LOC_00050145: "依赖",
        MIX_UBL_ALL_UBL_FE_LOC_00050144: "网关",
        MIX_UBL_ALL_UBL_FE_LOC_00050143: "下载详细日志",
        MIX_UBL_ALL_UBL_FE_LOC_00050142: "是否mdm",
        MIX_UBL_ALL_UBL_FE_LOC_00050141: "数据推送",
        MIX_UBL_ALL_UBL_FE_LOC_00050140: "别名",
        MIX_UBL_ALL_UBL_FE_LOC_00050139: "将要执行的数据同步任务：\u003c%\u003d viewNames %\u003e",
        MIX_UBL_ALL_UBL_FE_LOC_00050138: "确认切换？",
        MIX_UBL_ALL_UBL_FE_LOC_00050137: "Saas主键",
        MIX_UBL_ALL_UBL_FE_LOC_00050136: "最后一次状态",
        MIX_UBL_ALL_UBL_FE_LOC_00050135: "查看数据映射",
        MIX_UBL_ALL_UBL_FE_LOC_00050199: "数据视图",
        MIX_UBL_ALL_UBL_FE_LOC_20217272024: "启动方案编码",
        MIX_UBL_ALL_UBL_FE_LOC_20217272025: "启动方案名称",
        MIX_UBL_ALL_UBL_FE_LOC_20217272026: "启动类型",
        MIX_UBL_ALL_UBL_FE_LOC_00050134: "日期和时间",
        MIX_UBL_ALL_UBL_FE_LOC_00050658: "数据管理",
        MIX_UBL_ALL_UBL_FE_LOC_00050692: "若用户使用新网关同步数据，请先设置默认网关（ERP连接配置中点击设置默认）后全量执行人员任务,设置能查询到所有数据的条件",
        MIX_UBL_ALL_UBL_FE_LOC_00050691: "耗时:",
        MIX_UBL_ALL_UBL_FE_LOC_00050690: "状态:",
        MIX_UBL_ALL_UBL_FE_LOC_00050726: "移动审批无法获取正确的NC/NCC用户关联关系",
        MIX_UBL_ALL_UBL_FE_LOC_00050725: "友户通关联关系问题",
        MIX_UBL_ALL_UBL_FE_LOC_00050724: "执行的视图",
        MIX_UBL_ALL_UBL_FE_LOC_00050723: "3、若关联关系为一条，同样单独同步一下对应员工的数据，再确定一下关联关系",
        MIX_UBL_ALL_UBL_FE_LOC_00050722: "运行结束时间",
        MIX_UBL_ALL_UBL_FE_LOC_00050721: "定时触发",
        MIX_UBL_ALL_UBL_FE_LOC_00050720: "问题说明",
        MIX_UBL_ALL_UBL_FE_LOC_00050689: "启用/停用",
        MIX_UBL_ALL_UBL_FE_LOC_00050688: "总数量",
        MIX_UBL_ALL_UBL_FE_LOC_00050687: "查看执行详情",
        MIX_UBL_ALL_UBL_FE_LOC_00050686: "未运行：",
        MIX_UBL_ALL_UBL_FE_LOC_00050685: "跳过",
        MIX_UBL_ALL_UBL_FE_LOC_00050684: "日志详情:",
        MIX_UBL_ALL_UBL_FE_LOC_00050683: "触发类型",
        MIX_UBL_ALL_UBL_FE_LOC_00050682: "移动审批打开白页或者提示无法获取友户通关联关系，其他情况请找移动审批确认",
        MIX_UBL_ALL_UBL_FE_LOC_00050681: "修复类型",
        MIX_UBL_ALL_UBL_FE_LOC_00050680: "操作说明：",
        MIX_UBL_ALL_UBL_FE_LOC_00050719: "查看详情",
        MIX_UBL_ALL_UBL_FE_LOC_00050718: "注：查询语句如下（NC/NCC/U8C）",
        MIX_UBL_ALL_UBL_FE_LOC_00050717: "结束时间",
        MIX_UBL_ALL_UBL_FE_LOC_00050716: "运行开始时间",
        MIX_UBL_ALL_UBL_FE_LOC_00050715: "同步任务执行日志",
        MIX_UBL_ALL_UBL_FE_LOC_00005714: "视图",
        MIX_UBL_ALL_UBL_FE_LOC_00050713: "任务执行状态",
        MIX_UBL_ALL_UBL_FE_LOC_00050712: "执行详情",
        MIX_UBL_ALL_UBL_FE_LOC_00050711: "友户通ID",
        MIX_UBL_ALL_UBL_FE_LOC_00050710: "数据修复",
        MIX_UBL_ALL_UBL_FE_LOC_00050678: "详情",
        MIX_UBL_ALL_UBL_FE_LOC_00050677: "查看出厂SQL",
        MIX_UBL_ALL_UBL_FE_LOC_00050676: "执行中",
        MIX_UBL_ALL_UBL_FE_LOC_00050675: "开始时间",
        MIX_UBL_ALL_UBL_FE_LOC_00050674:
            "4、若干关联关系为多条，则确认移动审批所要使用的关联关系数据（即友户通和ERP用户主键对应关系）是否为最新的数据，若不为则单独同步一下对应员工的数据",
        MIX_UBL_ALL_UBL_FE_LOC_00050673: "同步时间",
        MIX_UBL_ALL_UBL_FE_LOC_00050709: "运行中：",
        MIX_UBL_ALL_UBL_FE_LOC_00050708: "1、输入用户信息查询用户关联关系详情数据",
        MIX_UBL_ALL_UBL_FE_LOC_00050707: "手动触发",
        MIX_UBL_ALL_UBL_FE_LOC_00050706: "按照用户名查询：select cuserid as id, user_code, user_name from sm_user where user_name \u003d \u0027用户名\u0027",
        MIX_UBL_ALL_UBL_FE_LOC_00050705: "执行状态",
        MIX_UBL_ALL_UBL_FE_LOC_00050704: "用户ID",
        MIX_UBL_ALL_UBL_FE_LOC_00050703: "网关ID:",
        MIX_UBL_ALL_UBL_FE_LOC_00050702: "视图编码",
        MIX_UBL_ALL_UBL_FE_LOC_00050701: "搜索字段类型",
        MIX_UBL_ALL_UBL_FE_LOC_00050699: "出厂SQL",
        MIX_UBL_ALL_UBL_FE_LOC_00050698: "任务数量",
        MIX_UBL_ALL_UBL_FE_LOC_00050697: "2、若关联关系数据若为空，则单独同步一下员工的数据",
        MIX_UBL_ALL_UBL_FE_LOC_00050696: "序号",
        MIX_UBL_ALL_UBL_FE_LOC_00050695: "复制",
        MIX_UBL_ALL_UBL_FE_LOC_00050694: "跳过：",
        MIX_UBL_ALL_UBL_FE_LOC_00050693: "执行结束",
        MIX_UBL_ALL_UBL_FE_LOC_00050879: "部分成功",
        MIX_UBL_ALL_UBL_FE_LOC_00050878: "同步ID",
        MIX_UBL_ALL_UBL_FE_LOC_00050877: "失败日志",
        MIX_UBL_ALL_UBL_FE_LOC_00050876: "未启用",
        MIX_UBL_ALL_UBL_FE_LOC_00050875: "查看详细日志",
        MIX_UBL_ALL_UBL_FE_LOC_00050874: "运行状态",
        MIX_UBL_ALL_UBL_FE_LOC_00050873: "当前分页日志未回写",
        MIX_UBL_ALL_UBL_FE_LOC_00050872: "分页数",
        MIX_UBL_ALL_UBL_FE_LOC_00050871: "详情日志",
        MIX_UBL_ALL_UBL_FE_LOC_00050870: "已启用",
        MIX_UBL_ALL_UBL_FE_LOC_00050882: "全部日志",
        MIX_UBL_ALL_UBL_FE_LOC_00050881: "数据量",
        MIX_UBL_ALL_UBL_FE_LOC_00050880: "下载",
        MIX_UBL_ALL_UBL_FE_LOC_00050739: "全部",
        MIX_UBL_ALL_UBL_FE_LOC_00050884: "只显示错误日志",
        MIX_UBL_ALL_UBL_FE_LOC_00050883: "当前日志页码：",

        MIX_UBL_ALL_UBL_FE_LOC_00050926: "查看Sql",
        MIX_UBL_ALL_UBL_FE_LOC_2021891533: "类型",
        MIX_UBL_ALL_UBL_FE_LOC_2021891534: "编码",
        MIX_UBL_ALL_UBL_FE_LOC_2021891535: "名称",
        MIX_UBL_ALL_UBL_FE_LOC_2021891536: "主键",
        MIX_UBL_ALL_UBL_FE_LOC_2021891537: "候选键",
        MIX_UBL_ALL_UBL_FE_LOC_2021891538: "必填",
        MIX_UBL_ALL_UBL_FE_LOC_2021891539: "默认值",
        MIX_UBL_ALL_UBL_FE_LOC_2021891540: "主键名称",
        MIX_UBL_ALL_UBL_FE_LOC_2021891541: "保存",
        MIX_UBL_ALL_UBL_FE_LOC_20218231439: "是否更新所有",

        MIX_UBL_ALL_UBL_FE_LOC_20218261625: "请先处理正在编辑的数据",
        MIX_UBL_ALL_UBL_FE_LOC_20219271106: "查看历史SQL",
        MIX_UBL_ALL_UBL_FE_LOC_202112201500: "历史SQL",
        MIX_UBL_ALL_UBL_FE_LOC_20219271107: "修改数据映射",
        MIX_UBL_ALL_UBL_FE_LOC_20219271108: "修改档案",
        MIX_UBL_ALL_UBL_FE_LOC_20219271109: "Sass主键",
        MIX_UBL_ALL_UBL_FE_LOC_20219271110: "编码",
        MIX_UBL_ALL_UBL_FE_LOC_20219271111: "名称",
        MIX_UBL_ALL_UBL_FE_LOC_20219271112: "手机号",
        MIX_UBL_ALL_UBL_FE_LOC_20219271113: "邮箱",
        MIX_UBL_ALL_UBL_FE_LOC_20219271114: "查询",
        MIX_UBL_ALL_UBL_FE_LOC_20219271115: "修改",

        MIX_UBL_ALL_UBL_FE_LOC_202111291525: "查看",
        MIX_UBL_ALL_UBL_FE_LOC_20211211022: "排查执行",
        MIX_UBL_ALL_UBL_FE_LOC_20211211023: "排查日志",
        MIX_UBL_ALL_UBL_FE_LOC_20211211024: "条件",
        MIX_UBL_ALL_UBL_FE_LOC_20211211025: "排查执行仅支持5条数据，用于排查问题，超过5条不支持",
        MIX_UBL_ALL_UBL_FE_LOC_20211211026: "请稍后点击排查执行下的排查日志查看",
        MIX_UBL_ALL_UBL_FE_LOC_20211211027: "检测",
        MIX_UBL_ALL_UBL_FE_LOC_20211211028: "执行",
        MIX_UBL_ALL_UBL_FE_LOC_20211211029: "来源数据查询日志",
        MIX_UBL_ALL_UBL_FE_LOC_20211211030: "目标数据查询日志",
        MIX_UBL_ALL_UBL_FE_LOC_20211211031: "请求body",
        MIX_UBL_ALL_UBL_FE_LOC_20211211032: "返回结果",
        MIX_UBL_ALL_UBL_FE_LOC_20211211033: "开始时间",
        MIX_UBL_ALL_UBL_FE_LOC_20211211034: "结束时间",
        MIX_UBL_ALL_UBL_FE_LOC_20211211035: "请求url",
        MIX_UBL_ALL_UBL_FE_LOC_20211211036: "请求header",
        MIX_UBL_ALL_UBL_FE_LOC_20211211037: "回调url",
        MIX_UBL_ALL_UBL_FE_LOC_20211211038: "执行失败",
        MIX_UBL_ALL_UBL_FE_LOC_20211261524: "检测成功",

        MIX_UBL_ALL_UBL_FE_LOC_00050618: "请输入正确的邮箱",
        MIX_UBL_ALL_UBL_FE_LOC_00050441: "请输入正确的手机号码",

        MIX_UBL_ALL_UBL_FE_LOC_2022161700: "如需修改sql条件，请取消选中",

        MIX_UBL_ALL_UBL_FE_LOC_2022761555: "切换运行网关",

        MIX_UBL_ALL_UBL_FE_LOC_2022781656: "运行记录",
        MIX_UBL_ALL_UBL_FE_LOC_2022781657: "自定义定时任务",
        MIX_UBL_ALL_UBL_FE_LOC_2022781658: "系统级定时任务",
        MIX_UBL_ALL_UBL_FE_LOC_2022781659: "定时执行",
        MIX_UBL_ALL_UBL_FE_LOC_2022781660: "全量重新执行",
        MIX_UBL_ALL_UBL_FE_LOC_2022781661: "定时执行列表",
        MIX_UBL_ALL_UBL_FE_LOC_20227151435: "连接配置",

        MIX_UBL_ALL_UBL_FE_LOC_20227191053: "是否默认",
        MIX_UBL_ALL_UBL_FE_LOC_2022781662: " 触发方式",
        MIX_UBL_ALL_UBL_FE_LOC_2022781663: "被动执行",
        MIX_UBL_ALL_UBL_FE_LOC_2022781664: "主动执行",
        MIX_UBL_ALL_UBL_FE_LOC_20227131640: "方案编码",
        MIX_UBL_ALL_UBL_FE_LOC_20227131641: "方案名称",

        MIX_UBL_ALL_UBL_FE_LOC_20227261340: "集成方案配置",

        MIX_UBL_ALL_UBL_FE_LOC_20227261614: "ERP集成方案配置",
        MIX_UBL_ALL_UBL_FE_LOC_20227261615: "启用系统级定时任务",

        MIX_UBL_ALL_UBL_FE_LOC_20227271540: "将全量覆盖已有数据，请确认执行该任务?",

        MIX_UBL_ALL_UBL_FE_LOC_20227281100: "切换连接",

        MIX_UBL_ALL_UBL_FE_LOC_20227281101: "本方案支持强制推送，无视目标数据是否存在，是否启用",
        MIX_UBL_ALL_UBL_FE_LOC_20227281102: "立即执行",
        MIX_UBL_ALL_UBL_FE_LOC_20227281103: "全部记录",
        MIX_UBL_ALL_UBL_FE_LOC_20227281104: "失败记录",
        MIX_UBL_ALL_UBL_FE_LOC_20227281105: "方案",

        MIX_UBL_ALL_UBL_FE_LOC_2022851533: "请选择连接",

        MIX_UBL_ALL_UBL_FE_LOC_2022891122: "视图管理现在移入到方案名称中点击显示",

        MIX_UBL_ALL_UBL_FE_LOC_20228111443: "等待",

        MIX_UBL_ALL_UBL_FE_LOC_20228121003: "来源系统主键",
        MIX_UBL_ALL_UBL_FE_LOC_20228121004: "目标系统主键",

        MIX_UBL_ALL_UBL_FE_LOC_20228171609: "切换连接成功",

        MIX_UBL_ALL_UBL_FE_LOC_20229161413: "失败原因",

        MIX_UBL_ALL_UBL_FE_LOC_20229211546: "查看执行sql",
        MIX_UBL_ALL_UBL_FE_LOC_20229211547: "统计sql",
        MIX_UBL_ALL_UBL_FE_LOC_20229211548: "执行sql",
        MIX_UBL_ALL_UBL_FE_LOC_20229211549: "数据处理中",

        MIX_UBL_ALL_UBL_FE_LOC_202210271514: "增量查询时间",

        MIX_UBL_ALL_UBL_FE_LOC_202211151751: "重置成功",

        MIX_UBL_ALL_UBL_FE_LOC_202211221110: "增量不支持单独设置条件",

        MIX_UBL_ALL_UBL_FE_LOC_202302131100: "目标档案数据处理进度",
        MIX_UBL_ALL_UBL_FE_LOC_202302131101: "未开始",

        MIX_UBL_ALL_UBL_FE_LOC_202302201110: "同步数据详情",
        MIX_UBL_ALL_UBL_FE_LOC_202302201111: "问题数据",
        MIX_UBL_ALL_UBL_FE_LOC_202302201112: "同步数据详情",
        MIX_UBL_ALL_UBL_FE_LOC_202302201113: "错误类型",
        MIX_UBL_ALL_UBL_FE_LOC_202302201114: "错误原因",
        MIX_UBL_ALL_UBL_FE_LOC_202302201115: "上次更新",
        MIX_UBL_ALL_UBL_FE_LOC_202302201116: "请按照提示原因，移步至相应系统调整，然后再点击【重新上传】即可修复问题数据",
        MIX_UBL_ALL_UBL_FE_LOC_202302201117: "重新上传",
        MIX_UBL_ALL_UBL_FE_LOC_202302201118: "其他",
        MIX_UBL_ALL_UBL_FE_LOC_202302201119: "依赖数据错误",
        MIX_UBL_ALL_UBL_FE_LOC_202302201120: "查看依赖关系图",
        MIX_UBL_ALL_UBL_FE_LOC_202302201121: "所有同步数据",
        MIX_UBL_ALL_UBL_FE_LOC_202302201122: "所有映射",
        MIX_UBL_ALL_UBL_FE_LOC_202302201123: "加载依赖",
        MIX_UBL_ALL_UBL_FE_LOC_202302201124: "根节点",
        MIX_UBL_ALL_UBL_FE_LOC_202302201125: "原因",
        MIX_UBL_ALL_UBL_FE_LOC_202302201126: "查看依赖数据",
        MIX_UBL_ALL_UBL_FE_LOC_202302201127: "依赖数据错误已查找完",
        MIX_UBL_ALL_UBL_FE_LOC_202302201128: "当前",

        MIX_UBL_ALL_UBL_FE_LOC_202304061040: "任务运行监控",

        MIX_UBL_ALL_UBL_FE_LOC_202304111723: "强制推送",
        MIX_UBL_ALL_UBL_FE_LOC_202304111724: "时间补录",
        MIX_UBL_ALL_UBL_FE_LOC_202304111725: "增量时间",

        MIX_UBL_ALL_UBL_FE_LOC_202304191050: "定时任务将只支持增量查询,不会全量查询与指定条件查询",
        MIX_UBL_ALL_UBL_FE_LOC_202304191051: "本方案支持指定时间段补录数据",
        MIX_UBL_ALL_UBL_FE_LOC_202304191052: "本方案支持修改本次执行增量时间戳",

        MIX_UBL_ALL_UBL_FE_LOC_202305041935: "格式化",

        MIX_UBL_ALL_UBL_FE_LOC_2023053119585: "请选择枚举映射",

        MIX_UBL_ALL_UBL_FE_LOC_202307191532: "excel导入",
        MIX_UBL_ALL_UBL_FE_LOC_202307241000: "来源系统编码",
        MIX_UBL_ALL_UBL_FE_LOC_202307241001: "目标系统编码",

        MIX_UBL_ALL_UBL_FE_LOC_202308011500: "mdm与bip主数据",
        MIX_UBL_ALL_UBL_FE_LOC_202308011501: "mdm与erp关系",
        MIX_UBL_ALL_UBL_FE_LOC_202308011502: "导入成功",
    },
    zhtw: {
        MIX_UBL_ALL_UBL_FE_LOC_00050198: "字段名稱不能為空",
        MIX_UBL_ALL_UBL_FE_LOC_00050133: "新增主鍵映射",
        MIX_UBL_ALL_UBL_FE_LOC_00050197: "關閉",
        MIX_UBL_ALL_UBL_FE_LOC_00050132: "請輸入視圖SQL",
        MIX_UBL_ALL_UBL_FE_LOC_00050196: "設置時間表達式",
        MIX_UBL_ALL_UBL_FE_LOC_00050131: "所屬組織",
        MIX_UBL_ALL_UBL_FE_LOC_00050195: "網關ID",
        MIX_UBL_ALL_UBL_FE_LOC_00050130: "輸入時間表達式",
        MIX_UBL_ALL_UBL_FE_LOC_00050194: "未匹配：",
        MIX_UBL_ALL_UBL_FE_LOC_00050193: "主鍵類型",
        MIX_UBL_ALL_UBL_FE_LOC_00050192: "已匹配：",
        MIX_UBL_ALL_UBL_FE_LOC_00050191: "公共檔案關聯",
        MIX_UBL_ALL_UBL_FE_LOC_00050190: "請輸入ERP主鍵",
        MIX_UBL_ALL_UBL_FE_LOC_00050129: "外部ERP數據",
        MIX_UBL_ALL_UBL_FE_LOC_00050128: "網關連接實例ID",
        MIX_UBL_ALL_UBL_FE_LOC_00050127: "ERP主鍵",
        MIX_UBL_ALL_UBL_FE_LOC_00050126: "視圖版本",
        MIX_UBL_ALL_UBL_FE_LOC_00050125: "任務管理",
        MIX_UBL_ALL_UBL_FE_LOC_00050189: "數據詳情",
        MIX_UBL_ALL_UBL_FE_LOC_00050124: "SQL條件",
        MIX_UBL_ALL_UBL_FE_LOC_00050188: "初始化ERP任務",
        MIX_UBL_ALL_UBL_FE_LOC_00050187: "視圖標簽",
        MIX_UBL_ALL_UBL_FE_LOC_00050122: "運行中",
        MIX_UBL_ALL_UBL_FE_LOC_00050186: "是否啟用增量",
        MIX_UBL_ALL_UBL_FE_LOC_00050121: "視圖所屬應用",
        MIX_UBL_ALL_UBL_FE_LOC_00050185: "字段列名",
        MIX_UBL_ALL_UBL_FE_LOC_00050120: "請輸入視圖版本或數據視圖名稱",
        MIX_UBL_ALL_UBL_FE_LOC_00050184: "未運行",
        MIX_UBL_ALL_UBL_FE_LOC_00050025: "檔案數據",
        MIX_UBL_ALL_UBL_FE_LOC_00050183: '查詢條件，無需輸入where關鍵詞，示例：code="2212" and ts>@lastupdatetime[@lastupdatetime為最後一次成功同步時間',
        MIX_UBL_ALL_UBL_FE_LOC_00050182: "耗時",
        MIX_UBL_ALL_UBL_FE_LOC_00050181: "此任務未關聯公共檔案",
        MIX_UBL_ALL_UBL_FE_LOC_00050180: "初始化<%= initName %>任務成功",
        MIX_UBL_ALL_UBL_FE_LOC_00050083: "視圖列定義",
        MIX_UBL_ALL_UBL_FE_LOC_00050216: "查看任務",
        MIX_UBL_ALL_UBL_FE_LOC_00050215: "數據映射",
        MIX_UBL_ALL_UBL_FE_LOC_00050214: "按條件拉取數據",
        MIX_UBL_ALL_UBL_FE_LOC_00050213: "運行失敗",
        MIX_UBL_ALL_UBL_FE_LOC_00050217: "運行沖突",
        MIX_UBL_ALL_UBL_FE_LOC_00050119: "數據同步任務已刪除",
        MIX_UBL_ALL_UBL_FE_LOC_00050212: "ERP主鍵名稱",
        MIX_UBL_ALL_UBL_FE_LOC_00050118: "是否在線",
        MIX_UBL_ALL_UBL_FE_LOC_00050211: "輸入視圖SQL",
        MIX_UBL_ALL_UBL_FE_LOC_00050117: "切換成功後，同步任務需手動觸發或單獨創建定時任務",
        MIX_UBL_ALL_UBL_FE_LOC_00050210: "編輯數據視圖",
        MIX_UBL_ALL_UBL_FE_LOC_00050116: "初始化<%= initName %>任務失敗",
        MIX_UBL_ALL_UBL_FE_LOC_00050115: "SQL語句",
        MIX_UBL_ALL_UBL_FE_LOC_00050114: "該同步任務沒有視圖標簽",
        MIX_UBL_ALL_UBL_FE_LOC_00050179: "最後一次任務結束時間",
        MIX_UBL_ALL_UBL_FE_LOC_00050178: "分頁大小",
        MIX_UBL_ALL_UBL_FE_LOC_00050220: "修改人",
        MIX_UBL_ALL_UBL_FE_LOC_00050219: "修改時間",
        MIX_UBL_ALL_UBL_FE_LOC_00050221: "方案管理",

        MIX_UBL_ALL_UBL_FE_LOC_00050222: "集成對象屬性",
        MIX_UBL_ALL_UBL_FE_LOC_00050223: "映射關系",
        MIX_UBL_ALL_UBL_FE_LOC_00050224: "啟動方案調整",
        MIX_UBL_ALL_UBL_FE_LOC_2021891451: "集成方案調整",
        MIX_UBL_ALL_UBL_FE_LOC_00050177: "字符",
        MIX_UBL_ALL_UBL_FE_LOC_00050175: "模糊匹配",
        MIX_UBL_ALL_UBL_FE_LOC_00050174: "顯示未匹配的數據",
        MIX_UBL_ALL_UBL_FE_LOC_00050173: "請輸入ERP主鍵名稱",
        MIX_UBL_ALL_UBL_FE_LOC_00050172: "查看詳細數據",
        MIX_UBL_ALL_UBL_FE_LOC_00050077: "查看所有",
        MIX_UBL_ALL_UBL_FE_LOC_00050171: "視圖管理",
        MIX_UBL_ALL_UBL_FE_LOC_00050072: "字段類型",
        MIX_UBL_ALL_UBL_FE_LOC_00050209: "最後一次任務執行時間",
        MIX_UBL_ALL_UBL_FE_LOC_00050208: "參照",
        MIX_UBL_ALL_UBL_FE_LOC_00050207: "枚舉",
        MIX_UBL_ALL_UBL_FE_LOC_00050206: "切換定時執行",
        MIX_UBL_ALL_UBL_FE_LOC_00050205: "參照本地",
        MIX_UBL_ALL_UBL_FE_LOC_00050204: "翻譯參照",
        MIX_UBL_ALL_UBL_FE_LOC_00050203: "查看日誌",
        MIX_UBL_ALL_UBL_FE_LOC_00050202: "執行",
        MIX_UBL_ALL_UBL_FE_LOC_216231735: "問題數據重傳",
        MIX_UBL_ALL_UBL_FE_LOC_216231739: "重傳",
        MIX_UBL_ALL_UBL_FE_LOC_00050201: "數據來源",
        MIX_UBL_ALL_UBL_FE_LOC_00050200: "日期",
        MIX_UBL_ALL_UBL_FE_LOC_00050169: "參照檔案類型",
        MIX_UBL_ALL_UBL_FE_LOC_00050168: "是否定時任務",
        MIX_UBL_ALL_UBL_FE_LOC_00050167: "視圖條件定義",
        MIX_UBL_ALL_UBL_FE_LOC_00050166: "成功",
        MIX_UBL_ALL_UBL_FE_LOC_00050165: "枚舉映射",
        MIX_UBL_ALL_UBL_FE_LOC_00050164: "切換任務網關",
        MIX_UBL_ALL_UBL_FE_LOC_00050163: "初始化<%= initName %>任務",
        MIX_UBL_ALL_UBL_FE_LOC_00050162: "切換成功後，同步任務由平台預設定時任務執行，同步時間為每日淩晨1點至5點之間",
        MIX_UBL_ALL_UBL_FE_LOC_00050161: "請輸入Saas主鍵",
        MIX_UBL_ALL_UBL_FE_LOC_00050160: "網關切換成功",
        MIX_UBL_ALL_UBL_FE_LOC_00050159: "整數",
        MIX_UBL_ALL_UBL_FE_LOC_00050158: "浮點數",
        MIX_UBL_ALL_UBL_FE_LOC_00050157: "創建定時任務",
        MIX_UBL_ALL_UBL_FE_LOC_00050156: "一鍵保存",
        MIX_UBL_ALL_UBL_FE_LOC_00050155: "字段名稱",
        MIX_UBL_ALL_UBL_FE_LOC_00050154: "關聯成功",
        MIX_UBL_ALL_UBL_FE_LOC_00050153: "選擇NC版本",
        MIX_UBL_ALL_UBL_FE_LOC_00050152: "視圖任務編輯",
        MIX_UBL_ALL_UBL_FE_LOC_00050151: "任務會在執行完本次分頁同步後停止",
        MIX_UBL_ALL_UBL_FE_LOC_00050150: "運行成功",
        MIX_UBL_ALL_UBL_FE_LOC_00050149: "查看失敗",
        MIX_UBL_ALL_UBL_FE_LOC_00050148: "手動匹配",
        MIX_UBL_ALL_UBL_FE_LOC_00050147: "是否立即執行?",
        MIX_UBL_ALL_UBL_FE_LOC_00050146: "匹配結果",
        MIX_UBL_ALL_UBL_FE_LOC_00050145: "依賴",
        MIX_UBL_ALL_UBL_FE_LOC_00050144: "網關",
        MIX_UBL_ALL_UBL_FE_LOC_00050143: "下載詳細日誌",
        MIX_UBL_ALL_UBL_FE_LOC_00050142: "是否mdm",
        MIX_UBL_ALL_UBL_FE_LOC_00050141: "數據推送",
        MIX_UBL_ALL_UBL_FE_LOC_00050140: "別名",
        MIX_UBL_ALL_UBL_FE_LOC_00050139: "將要執行的數據同步任務：<%= viewNames %>",
        MIX_UBL_ALL_UBL_FE_LOC_00050138: "確認切換？",
        MIX_UBL_ALL_UBL_FE_LOC_00050137: "Saas主鍵",
        MIX_UBL_ALL_UBL_FE_LOC_00050136: "最後一次狀態",
        MIX_UBL_ALL_UBL_FE_LOC_00050135: "查看數據映射",
        MIX_UBL_ALL_UBL_FE_LOC_00050199: "數據視圖",
        MIX_UBL_ALL_UBL_FE_LOC_20217272024: "啟動方案編碼",
        MIX_UBL_ALL_UBL_FE_LOC_20217272025: "啟動方案名稱",
        MIX_UBL_ALL_UBL_FE_LOC_20217272026: "啟動類型",
        MIX_UBL_ALL_UBL_FE_LOC_00050134: "日期和時間",
        MIX_UBL_ALL_UBL_FE_LOC_00050658: "數據管理",
        MIX_UBL_ALL_UBL_FE_LOC_00050692: "若用戶使用新網關同步數據，請先設置默認網關（ERP連接配置中點擊設置默認）後全量執行人員任務,設置能查詢到所有數據的條件",
        MIX_UBL_ALL_UBL_FE_LOC_00050691: "耗時:",
        MIX_UBL_ALL_UBL_FE_LOC_00050690: "狀態:",
        MIX_UBL_ALL_UBL_FE_LOC_00050726: "移動審批無法獲取正確的NC/NCC用戶關聯關係",
        MIX_UBL_ALL_UBL_FE_LOC_00050725: "友戶通關聯關係問題",
        MIX_UBL_ALL_UBL_FE_LOC_00050724: "執行的視圖",
        MIX_UBL_ALL_UBL_FE_LOC_00050723: "3、若關聯關係為一條，同樣單獨同步一下對應員工的數據，再確定一下關聯關係",
        MIX_UBL_ALL_UBL_FE_LOC_00050722: "運行結束時間",
        MIX_UBL_ALL_UBL_FE_LOC_00050721: "定時觸發",
        MIX_UBL_ALL_UBL_FE_LOC_00050720: "問題說明",
        MIX_UBL_ALL_UBL_FE_LOC_00050689: "啓用/停用",
        MIX_UBL_ALL_UBL_FE_LOC_00050688: "總數量",
        MIX_UBL_ALL_UBL_FE_LOC_00050687: "查看執行詳情",
        MIX_UBL_ALL_UBL_FE_LOC_00050686: "未運行：",
        MIX_UBL_ALL_UBL_FE_LOC_00050685: "跳過",
        MIX_UBL_ALL_UBL_FE_LOC_00050684: "日誌詳情:",
        MIX_UBL_ALL_UBL_FE_LOC_00050683: "觸發類型",
        MIX_UBL_ALL_UBL_FE_LOC_00050682: "移動審批打開白頁或者提示無法獲取友戶通關聯關係，其他情況請找移動審批確認",
        MIX_UBL_ALL_UBL_FE_LOC_00050681: "修復類型",
        MIX_UBL_ALL_UBL_FE_LOC_00050680: "操作說明：",
        MIX_UBL_ALL_UBL_FE_LOC_00050719: "查看詳情",
        MIX_UBL_ALL_UBL_FE_LOC_00050718: "注：查詢語句如下（NC/NCC/U8C）",
        MIX_UBL_ALL_UBL_FE_LOC_00050717: "結束時間",
        MIX_UBL_ALL_UBL_FE_LOC_00050716: "運行開始時間",
        MIX_UBL_ALL_UBL_FE_LOC_00050715: "同步任務執行日誌",
        MIX_UBL_ALL_UBL_FE_LOC_00050714: "視圖",
        MIX_UBL_ALL_UBL_FE_LOC_00050713: "任務執行狀態",
        MIX_UBL_ALL_UBL_FE_LOC_00050712: "執行詳情",
        MIX_UBL_ALL_UBL_FE_LOC_00050711: "友戶通ID",
        MIX_UBL_ALL_UBL_FE_LOC_00050710: "數據修復",
        MIX_UBL_ALL_UBL_FE_LOC_00050678: "詳情",
        MIX_UBL_ALL_UBL_FE_LOC_00050677: "查看出廠SQL",
        MIX_UBL_ALL_UBL_FE_LOC_00050676: "執行中",
        MIX_UBL_ALL_UBL_FE_LOC_00050675: "開始時間",
        MIX_UBL_ALL_UBL_FE_LOC_00050674:
            "4、若幹關聯關係為多條，則確認移動審批所要使用的關聯關係數據（即友戶通和ERP用戶主鍵對應關係）是否為最新的數據，若不為則單獨同步一下對應員工的數據",
        MIX_UBL_ALL_UBL_FE_LOC_00050673: "同步時間",
        MIX_UBL_ALL_UBL_FE_LOC_00050709: "運行中：",
        MIX_UBL_ALL_UBL_FE_LOC_00050708: "1、輸入用戶信息查詢用戶關聯關係詳情數據",
        MIX_UBL_ALL_UBL_FE_LOC_00050707: "手動觸發",
        MIX_UBL_ALL_UBL_FE_LOC_00050706: "按照用戶名查詢：select cuserid as id, user_code, user_name from sm_user where user_name = '用戶名'",
        MIX_UBL_ALL_UBL_FE_LOC_00050705: "執行狀態",
        MIX_UBL_ALL_UBL_FE_LOC_00050704: "用戶ID",
        MIX_UBL_ALL_UBL_FE_LOC_00050703: "網關ID:",
        MIX_UBL_ALL_UBL_FE_LOC_00050702: "視圖編碼",
        MIX_UBL_ALL_UBL_FE_LOC_00050701: "搜索字段類型",
        MIX_UBL_ALL_UBL_FE_LOC_00050699: "出廠SQL",
        MIX_UBL_ALL_UBL_FE_LOC_00050698: "任務數量",
        MIX_UBL_ALL_UBL_FE_LOC_00050697: "2、若關聯關係數據若為空，則單獨同步一下員工的數據",
        MIX_UBL_ALL_UBL_FE_LOC_00050696: "序號",
        MIX_UBL_ALL_UBL_FE_LOC_00050695: "複製",
        MIX_UBL_ALL_UBL_FE_LOC_00050694: "跳過：",
        MIX_UBL_ALL_UBL_FE_LOC_00050693: "執行結束",
        MIX_UBL_ALL_UBL_FE_LOC_00050879: "部分成功",
        MIX_UBL_ALL_UBL_FE_LOC_00050878: "同步ID",
        MIX_UBL_ALL_UBL_FE_LOC_00050877: "失敗日誌",
        MIX_UBL_ALL_UBL_FE_LOC_00050876: "未啟用",
        MIX_UBL_ALL_UBL_FE_LOC_00050875: "查看詳細日誌",
        MIX_UBL_ALL_UBL_FE_LOC_00050874: "運行狀態",
        MIX_UBL_ALL_UBL_FE_LOC_00050873: "當前分頁日誌未回寫",
        MIX_UBL_ALL_UBL_FE_LOC_00050872: "分頁數",
        MIX_UBL_ALL_UBL_FE_LOC_00050871: "詳情日誌",
        MIX_UBL_ALL_UBL_FE_LOC_00050870: "已啟用",
        MIX_UBL_ALL_UBL_FE_LOC_00050882: "全部日誌",
        MIX_UBL_ALL_UBL_FE_LOC_00050881: "數據量",
        MIX_UBL_ALL_UBL_FE_LOC_00050880: "下載",
        MIX_UBL_ALL_UBL_FE_LOC_00050739: "全部",
        MIX_UBL_ALL_UBL_FE_LOC_00050884: "隻顯示錯誤日誌",
        MIX_UBL_ALL_UBL_FE_LOC_00050883: "當前日誌頁碼：",

        MIX_UBL_ALL_UBL_FE_LOC_00050926: "查看Sql",
        MIX_UBL_ALL_UBL_FE_LOC_2021891533: "類型",
        MIX_UBL_ALL_UBL_FE_LOC_2021891534: "編碼",
        MIX_UBL_ALL_UBL_FE_LOC_2021891535: "名稱",
        MIX_UBL_ALL_UBL_FE_LOC_2021891536: "主鍵",
        MIX_UBL_ALL_UBL_FE_LOC_2021891537: "候選鍵",
        MIX_UBL_ALL_UBL_FE_LOC_2021891538: "必填",
        MIX_UBL_ALL_UBL_FE_LOC_2021891539: "默認值",
        MIX_UBL_ALL_UBL_FE_LOC_2021891540: "主鍵名稱",
        MIX_UBL_ALL_UBL_FE_LOC_2021891541: "保存",
        MIX_UBL_ALL_UBL_FE_LOC_20218231439: "是否更新所有",

        MIX_UBL_ALL_UBL_FE_LOC_20218261625: "請先處理正在編輯的數據",
        MIX_UBL_ALL_UBL_FE_LOC_20219271106: "查看歷史SQL",
        MIX_UBL_ALL_UBL_FE_LOC_202112201500: "歷史SQL",
        MIX_UBL_ALL_UBL_FE_LOC_20219271107: "修改數據映射",
        MIX_UBL_ALL_UBL_FE_LOC_20219271108: "修改檔案",
        MIX_UBL_ALL_UBL_FE_LOC_20219271109: "Sass主鍵",
        MIX_UBL_ALL_UBL_FE_LOC_20219271110: "編碼",
        MIX_UBL_ALL_UBL_FE_LOC_20219271111: "名稱",
        MIX_UBL_ALL_UBL_FE_LOC_20219271112: "手機號",
        MIX_UBL_ALL_UBL_FE_LOC_20219271113: "郵箱",
        MIX_UBL_ALL_UBL_FE_LOC_20219271114: "查詢",
        MIX_UBL_ALL_UBL_FE_LOC_20219271115: "修改",

        MIX_UBL_ALL_UBL_FE_LOC_202111291525: "查看",
        MIX_UBL_ALL_UBL_FE_LOC_20211211022: "排查執行",
        MIX_UBL_ALL_UBL_FE_LOC_20211211023: "排查日誌",
        MIX_UBL_ALL_UBL_FE_LOC_20211211024: "條件",
        MIX_UBL_ALL_UBL_FE_LOC_20211211025: "排查執行僅支持5條數據，用於排查問題，超過5條不支持",
        MIX_UBL_ALL_UBL_FE_LOC_20211211026: "請稍後點擊排查執行下的排查日誌查看",
        MIX_UBL_ALL_UBL_FE_LOC_20211211027: "檢測",
        MIX_UBL_ALL_UBL_FE_LOC_20211211028: "執行",
        MIX_UBL_ALL_UBL_FE_LOC_20211211029: "來源數據查詢日誌",
        MIX_UBL_ALL_UBL_FE_LOC_20211211030: "目標數據查詢日誌",
        MIX_UBL_ALL_UBL_FE_LOC_20211211031: "請求body",
        MIX_UBL_ALL_UBL_FE_LOC_20211211032: "返回結果",
        MIX_UBL_ALL_UBL_FE_LOC_20211211033: "開始時間",
        MIX_UBL_ALL_UBL_FE_LOC_20211211034: "結束時間",
        MIX_UBL_ALL_UBL_FE_LOC_20211211035: "請求url",
        MIX_UBL_ALL_UBL_FE_LOC_20211211036: "請求header",
        MIX_UBL_ALL_UBL_FE_LOC_20211211037: "回調url",
        MIX_UBL_ALL_UBL_FE_LOC_20211211038: "執行失敗",
        MIX_UBL_ALL_UBL_FE_LOC_20211261524: "檢測成功",

        MIX_UBL_ALL_UBL_FE_LOC_00050618: "請輸入正確的郵箱",
        MIX_UBL_ALL_UBL_FE_LOC_00050441: "請輸入正確的手機號碼",

        MIX_UBL_ALL_UBL_FE_LOC_2022161700: "如需修改sql條件，請取消選中",

        MIX_UBL_ALL_UBL_FE_LOC_2022761555: "切換運行網關",

        MIX_UBL_ALL_UBL_FE_LOC_2022781656: "運行記錄",
        MIX_UBL_ALL_UBL_FE_LOC_2022781657: "自定義定時任務",
        MIX_UBL_ALL_UBL_FE_LOC_2022781658: "系統級定時任務",
        MIX_UBL_ALL_UBL_FE_LOC_2022781659: "定時執行",
        MIX_UBL_ALL_UBL_FE_LOC_2022781660: "全量重新執行",
        MIX_UBL_ALL_UBL_FE_LOC_2022781661: "定時執行列表",
        MIX_UBL_ALL_UBL_FE_LOC_20227151435: "連接配置",

        MIX_UBL_ALL_UBL_FE_LOC_20227191053: "是否默認",
        MIX_UBL_ALL_UBL_FE_LOC_2022781662: " 觸發方式",
        MIX_UBL_ALL_UBL_FE_LOC_2022781663: "被動執行",
        MIX_UBL_ALL_UBL_FE_LOC_2022781664: "主動執行",
        MIX_UBL_ALL_UBL_FE_LOC_20227131640: "方案編碼",
        MIX_UBL_ALL_UBL_FE_LOC_20227131641: "方案名稱",

        MIX_UBL_ALL_UBL_FE_LOC_20227261340: "集成方案配置",

        MIX_UBL_ALL_UBL_FE_LOC_20227261614: "ERP集成方案配置",
        MIX_UBL_ALL_UBL_FE_LOC_20227261615: "啟用系統級定時任務",

        MIX_UBL_ALL_UBL_FE_LOC_20227271540: "將全量覆蓋已有數據，請確認執行該任務?",

        MIX_UBL_ALL_UBL_FE_LOC_20227281100: "切換連接",

        MIX_UBL_ALL_UBL_FE_LOC_20227281101: "本方案支持強製推送，無視目標數據是否存在，是否啟用",
        MIX_UBL_ALL_UBL_FE_LOC_20227281102: "立即執行",
        MIX_UBL_ALL_UBL_FE_LOC_20227281103: "全部記錄",
        MIX_UBL_ALL_UBL_FE_LOC_20227281104: "失敗記錄",
        MIX_UBL_ALL_UBL_FE_LOC_20227281105: "方案",

        MIX_UBL_ALL_UBL_FE_LOC_2022851533: "請選擇連接",

        MIX_UBL_ALL_UBL_FE_LOC_2022891122: "視圖管理現在移入到方案名稱中點擊顯示",

        MIX_UBL_ALL_UBL_FE_LOC_20228111443: "等待",

        MIX_UBL_ALL_UBL_FE_LOC_20228121003: "來源系統主鍵",
        MIX_UBL_ALL_UBL_FE_LOC_20228121004: "目標系統主鍵",

        MIX_UBL_ALL_UBL_FE_LOC_20228171609: "切換連接成功",

        MIX_UBL_ALL_UBL_FE_LOC_20229161413: "失敗原因",

        MIX_UBL_ALL_UBL_FE_LOC_20229211546: "查看執行sql",
        MIX_UBL_ALL_UBL_FE_LOC_20229211547: "統計sql",
        MIX_UBL_ALL_UBL_FE_LOC_20229211548: "執行sql",
        MIX_UBL_ALL_UBL_FE_LOC_20229211549: "數據處理中",

        MIX_UBL_ALL_UBL_FE_LOC_202210271514: "增量查詢時間",

        MIX_UBL_ALL_UBL_FE_LOC_202211151751: "重置成功",

        MIX_UBL_ALL_UBL_FE_LOC_202211221110: "增量不支持單獨設置條件",

        MIX_UBL_ALL_UBL_FE_LOC_202302131100: "目標檔案數據處理進度",
        MIX_UBL_ALL_UBL_FE_LOC_202302131101: "未開始",

        MIX_UBL_ALL_UBL_FE_LOC_202302201110: "同步數據詳情",
        MIX_UBL_ALL_UBL_FE_LOC_202302201111: "問題數據",
        MIX_UBL_ALL_UBL_FE_LOC_202302201112: "同步數據詳情",
        MIX_UBL_ALL_UBL_FE_LOC_202302201113: "錯誤類型",
        MIX_UBL_ALL_UBL_FE_LOC_202302201114: "錯誤原因",
        MIX_UBL_ALL_UBL_FE_LOC_202302201115: "上次更新",
        MIX_UBL_ALL_UBL_FE_LOC_202302201116: "請按照提示原因，移步至相應系統調整，然後再點擊【重新上傳】即可修複問題數據",
        MIX_UBL_ALL_UBL_FE_LOC_202302201117: "重新上傳",
        MIX_UBL_ALL_UBL_FE_LOC_202302201118: "其他",
        MIX_UBL_ALL_UBL_FE_LOC_202302201119: "依賴數據錯誤",
        MIX_UBL_ALL_UBL_FE_LOC_202302201120: "查看依賴關系圖",
        MIX_UBL_ALL_UBL_FE_LOC_202302201121: "所有同步數據",
        MIX_UBL_ALL_UBL_FE_LOC_202302201122: "所有映射",
        MIX_UBL_ALL_UBL_FE_LOC_202302201123: "加載依賴",
        MIX_UBL_ALL_UBL_FE_LOC_202302201124: "根節點",
        MIX_UBL_ALL_UBL_FE_LOC_202302201125: "原因",
        MIX_UBL_ALL_UBL_FE_LOC_202302201126: "查看依賴數據",
        MIX_UBL_ALL_UBL_FE_LOC_202302201127: "依賴數據錯誤已查找完",
        MIX_UBL_ALL_UBL_FE_LOC_202302201128: "當前",

        MIX_UBL_ALL_UBL_FE_LOC_202304061040: "任務運行監控",

        MIX_UBL_ALL_UBL_FE_LOC_202304111723: "強制推送",
        MIX_UBL_ALL_UBL_FE_LOC_202304111724: "時間補錄",
        MIX_UBL_ALL_UBL_FE_LOC_202304111725: "增量時間",

        MIX_UBL_ALL_UBL_FE_LOC_202304191050: "定時任務將只支持增量查詢,不會全量查詢與指定條件查詢",
        MIX_UBL_ALL_UBL_FE_LOC_202304191051: "本方案支持指定時間段補錄數據",
        MIX_UBL_ALL_UBL_FE_LOC_202304191052: "本方案支持修改本次執行增量時間戳",

        MIX_UBL_ALL_UBL_FE_LOC_202305041935: "格式化",

        MIX_UBL_ALL_UBL_FE_LOC_2023053119585: "請選擇枚舉映射",

        MIX_UBL_ALL_UBL_FE_LOC_202307191532: "excel導入",
        MIX_UBL_ALL_UBL_FE_LOC_202307241000: "來源系統編碼",
        MIX_UBL_ALL_UBL_FE_LOC_202307241001: "目標系統編碼",

        MIX_UBL_ALL_UBL_FE_LOC_202308011500: "mdm與bip主數據",
        MIX_UBL_ALL_UBL_FE_LOC_202308011501: "mdm與erp關系",
        MIX_UBL_ALL_UBL_FE_LOC_202308011502: "導入成功",
    },
    enus: {
        MIX_UBL_ALL_UBL_FE_LOC_00050198: "Field name is required",
        MIX_UBL_ALL_UBL_FE_LOC_00050133: "New primary key mapping",
        MIX_UBL_ALL_UBL_FE_LOC_00050132: "Please enter view SQL",
        MIX_UBL_ALL_UBL_FE_LOC_00050196: "Set Time Expression",
        MIX_UBL_ALL_UBL_FE_LOC_00050131: "Affiliated organization",
        MIX_UBL_ALL_UBL_FE_LOC_00050195: "Gateway ID",
        MIX_UBL_ALL_UBL_FE_LOC_00050130: "Enter Time Expression",
        MIX_UBL_ALL_UBL_FE_LOC_00050194: "Not matched: ",
        MIX_UBL_ALL_UBL_FE_LOC_00050193: "PK Type",
        MIX_UBL_ALL_UBL_FE_LOC_00050192: "The matched：",
        MIX_UBL_ALL_UBL_FE_LOC_00050191: "Public File Association",
        MIX_UBL_ALL_UBL_FE_LOC_00050190: "ERP PK is required",
        MIX_UBL_ALL_UBL_FE_LOC_00050129: "External ERP Data Task",
        MIX_UBL_ALL_UBL_FE_LOC_00050128: "Gateway Connection Instance ID",
        MIX_UBL_ALL_UBL_FE_LOC_00050127: "ERP PK",
        MIX_UBL_ALL_UBL_FE_LOC_00050126: "View version",
        MIX_UBL_ALL_UBL_FE_LOC_00050125: "Task Management",
        MIX_UBL_ALL_UBL_FE_LOC_00050189: "Details of the data",
        MIX_UBL_ALL_UBL_FE_LOC_00050124: "SQL Condition",
        MIX_UBL_ALL_UBL_FE_LOC_00050188: "Initialize ERP Task",
        MIX_UBL_ALL_UBL_FE_LOC_00050187: "View labels",
        MIX_UBL_ALL_UBL_FE_LOC_00050122: "Running",
        MIX_UBL_ALL_UBL_FE_LOC_00050186: "Enable Increment",
        MIX_UBL_ALL_UBL_FE_LOC_00050121: "App of View",
        MIX_UBL_ALL_UBL_FE_LOC_00050185: "Field Column Name",
        MIX_UBL_ALL_UBL_FE_LOC_00050120: "Enter the view version or data view name",
        MIX_UBL_ALL_UBL_FE_LOC_00050184: "Not running",
        MIX_UBL_ALL_UBL_FE_LOC_00050025: "Archive data",
        MIX_UBL_ALL_UBL_FE_LOC_00050183:
            "Query criteria, no need to enter where keyword, example: code='2212' and ts>@lastupdatetime [@lastupdatetime is the last successful synchronization time]",
        MIX_UBL_ALL_UBL_FE_LOC_00050182: "Elapsed Time",
        MIX_UBL_ALL_UBL_FE_LOC_00050181: "This Task is not associated with a public file",
        MIX_UBL_ALL_UBL_FE_LOC_00050180: "\u003c%\u003d initName %\u003e task initialized",
        MIX_UBL_ALL_UBL_FE_LOC_00050083: "View Column Settings",
        MIX_UBL_ALL_UBL_FE_LOC_00050216: "View Task",
        MIX_UBL_ALL_UBL_FE_LOC_00050215: "PK Mapping",
        MIX_UBL_ALL_UBL_FE_LOC_00050214: "Conditional pull-out of data",
        MIX_UBL_ALL_UBL_FE_LOC_00050213: "Failed to run",
        MIX_UBL_ALL_UBL_FE_LOC_00050217: "Run conflict",
        MIX_UBL_ALL_UBL_FE_LOC_00050119: "Data synchronization task deleted",
        MIX_UBL_ALL_UBL_FE_LOC_00050212: "ERP Primary Key Name",
        MIX_UBL_ALL_UBL_FE_LOC_00050118: "Online",
        MIX_UBL_ALL_UBL_FE_LOC_00050211: "Enter view SQL",
        MIX_UBL_ALL_UBL_FE_LOC_00050117: "After successful switching，Synchronization tasks need to be triggered manually or created separately.",
        MIX_UBL_ALL_UBL_FE_LOC_00050210: "Edit Data View",
        MIX_UBL_ALL_UBL_FE_LOC_00050116: "Failed to initialize \u003c%\u003d initName %\u003e task",
        MIX_UBL_ALL_UBL_FE_LOC_00050115: "SQL statements",
        MIX_UBL_ALL_UBL_FE_LOC_00050114: "The synchronization task has no view label",
        MIX_UBL_ALL_UBL_FE_LOC_00050179: "Last Task End Time",
        MIX_UBL_ALL_UBL_FE_LOC_00050178: "Pagesize",
        MIX_UBL_ALL_UBL_FE_LOC_00050220: "Reviser",
        MIX_UBL_ALL_UBL_FE_LOC_00050219: "Revision time",
        MIX_UBL_ALL_UBL_FE_LOC_00050221: "Programme Management",

        MIX_UBL_ALL_UBL_FE_LOC_00050222: "Integration object properties",
        MIX_UBL_ALL_UBL_FE_LOC_00050223: "Mapping relation",
        MIX_UBL_ALL_UBL_FE_LOC_00050224: "Adjustment of startup scheme",
        MIX_UBL_ALL_UBL_FE_LOC_2021891451: "Adjustment of integration scheme",
        MIX_UBL_ALL_UBL_FE_LOC_00050177: "Character",
        MIX_UBL_ALL_UBL_FE_LOC_00050175: "Fuzzy matching",
        MIX_UBL_ALL_UBL_FE_LOC_00050174: "Displays unmatched data",
        MIX_UBL_ALL_UBL_FE_LOC_00050173: "Please Enter ERP PK name",
        MIX_UBL_ALL_UBL_FE_LOC_00050172: "View Details",
        MIX_UBL_ALL_UBL_FE_LOC_00050077: "View All",
        MIX_UBL_ALL_UBL_FE_LOC_00050171: "View Management",
        MIX_UBL_ALL_UBL_FE_LOC_00050072: "Field Type",
        MIX_UBL_ALL_UBL_FE_LOC_00050209: "Last Task Implementation Time",
        MIX_UBL_ALL_UBL_FE_LOC_00050208: "Reference",
        MIX_UBL_ALL_UBL_FE_LOC_00050207: "Enumeration",
        MIX_UBL_ALL_UBL_FE_LOC_00050206: "Switch to Scheduled Implementation",
        MIX_UBL_ALL_UBL_FE_LOC_00050205: "Refer to local",
        MIX_UBL_ALL_UBL_FE_LOC_00050204: "Translation reference",
        MIX_UBL_ALL_UBL_FE_LOC_00050203: "View Log",
        MIX_UBL_ALL_UBL_FE_LOC_00050202: "Execute",
        MIX_UBL_ALL_UBL_FE_LOC_216231735: "Problem data retransmission",
        MIX_UBL_ALL_UBL_FE_LOC_216231739: "retransmission",
        MIX_UBL_ALL_UBL_FE_LOC_00050201: "Data Source",
        MIX_UBL_ALL_UBL_FE_LOC_00050200: "Date",
        MIX_UBL_ALL_UBL_FE_LOC_00050169: "Reference File Type",
        MIX_UBL_ALL_UBL_FE_LOC_00050168: "Scheduled Task",
        MIX_UBL_ALL_UBL_FE_LOC_00050167: "Edit Data Condition",
        MIX_UBL_ALL_UBL_FE_LOC_00050166: "Success",
        MIX_UBL_ALL_UBL_FE_LOC_00050165: "Enumeration Mapping",
        MIX_UBL_ALL_UBL_FE_LOC_00050164: "Switch Task Gateway",
        MIX_UBL_ALL_UBL_FE_LOC_00050163: "Initialize \u003c%\u003d initName %\u003e task",
        MIX_UBL_ALL_UBL_FE_LOC_00050162:
            "After successful switching，Synchronization tasks are performed by tasks preset by the platform, and the synchronization time is between 1:00 and 5:00 a.m.",
        MIX_UBL_ALL_UBL_FE_LOC_00050161: "Saas PK is required",
        MIX_UBL_ALL_UBL_FE_LOC_00050160: "Gateway switched successfully",
        MIX_UBL_ALL_UBL_FE_LOC_00050159: "Integer",
        MIX_UBL_ALL_UBL_FE_LOC_00050158: "Float",
        MIX_UBL_ALL_UBL_FE_LOC_00050157: "Create Scheduled Tasks",
        MIX_UBL_ALL_UBL_FE_LOC_00050156: "A key to save",
        MIX_UBL_ALL_UBL_FE_LOC_00050155: "Field Name",
        MIX_UBL_ALL_UBL_FE_LOC_00050154: "Successful association",
        MIX_UBL_ALL_UBL_FE_LOC_00050153: "Select NC Version",
        MIX_UBL_ALL_UBL_FE_LOC_00050152: "Edit View Task",
        MIX_UBL_ALL_UBL_FE_LOC_00050151: "Task will stop after completing current pagination synchronization",
        MIX_UBL_ALL_UBL_FE_LOC_00050150: "Successfully",
        MIX_UBL_ALL_UBL_FE_LOC_00050149: "Failed to view",
        MIX_UBL_ALL_UBL_FE_LOC_00050148: "Manual match",
        MIX_UBL_ALL_UBL_FE_LOC_00050147: "Execute Now?",
        MIX_UBL_ALL_UBL_FE_LOC_00050146: "Matching results",
        MIX_UBL_ALL_UBL_FE_LOC_00050145: "Rely on",
        MIX_UBL_ALL_UBL_FE_LOC_00050144: "Gateway",
        MIX_UBL_ALL_UBL_FE_LOC_00050143: "Download Detailed Log",
        MIX_UBL_ALL_UBL_FE_LOC_00050142: "Whether MDM",
        MIX_UBL_ALL_UBL_FE_LOC_00050141: "Data Pushing",
        MIX_UBL_ALL_UBL_FE_LOC_00050140: "Alias",
        MIX_UBL_ALL_UBL_FE_LOC_00050139: "Data synchronization tasks to be performed：\u003c%\u003d viewNames %\u003e",
        MIX_UBL_ALL_UBL_FE_LOC_00050138: "Confirm switch？",
        MIX_UBL_ALL_UBL_FE_LOC_00050137: "Saas Pk",
        MIX_UBL_ALL_UBL_FE_LOC_00050136: "Last Status",
        MIX_UBL_ALL_UBL_FE_LOC_00050135: "View PK Mapping",
        MIX_UBL_ALL_UBL_FE_LOC_00050199: "Data View",
        MIX_UBL_ALL_UBL_FE_LOC_20217272024: "Start scheme code",
        MIX_UBL_ALL_UBL_FE_LOC_20217272025: "Startup scheme name",
        MIX_UBL_ALL_UBL_FE_LOC_20217272026: "startup type",
        MIX_UBL_ALL_UBL_FE_LOC_00050134: "Date & Time",
        MIX_UBL_ALL_UBL_FE_LOC_00050658: "Data Management",
        MIX_UBL_ALL_UBL_FE_LOC_00050692:
            "If the user uses the new gateway to synchronize data, please first set the default gateway (click Set Default in ERP connection configuration) and then fully perform human tasks and set the conditions for querying all data",
        MIX_UBL_ALL_UBL_FE_LOC_00050691: "Time consuming:",
        MIX_UBL_ALL_UBL_FE_LOC_00050690: "Status:",
        MIX_UBL_ALL_UBL_FE_LOC_00050726: "Mobile approval cannot obtain the correct NC/NCC user relationship",
        MIX_UBL_ALL_UBL_FE_LOC_00050725: "YHT relationship problem",
        MIX_UBL_ALL_UBL_FE_LOC_00050724: "View of execution",
        MIX_UBL_ALL_UBL_FE_LOC_00050723:
            "3.If the relationship is one, synchronize the data of the corresponding employee separately, and then determine the relationship",
        MIX_UBL_ALL_UBL_FE_LOC_00050722: "Run end time",
        MIX_UBL_ALL_UBL_FE_LOC_00050721: "Timing trigger",
        MIX_UBL_ALL_UBL_FE_LOC_00050720: "Problem description",
        MIX_UBL_ALL_UBL_FE_LOC_00050689: "Enable/Disable",
        MIX_UBL_ALL_UBL_FE_LOC_00050688: "Total",
        MIX_UBL_ALL_UBL_FE_LOC_00050687: "View execution details",
        MIX_UBL_ALL_UBL_FE_LOC_00050686: "Not Running:",
        MIX_UBL_ALL_UBL_FE_LOC_00050685: "Jump Over",
        MIX_UBL_ALL_UBL_FE_LOC_00050684: "Log details:",
        MIX_UBL_ALL_UBL_FE_LOC_00050683: "Trigger type",
        MIX_UBL_ALL_UBL_FE_LOC_00050682:
            "Mobile approval opens a white page or prompts that you cannot obtain the association relationship between YHT. In other cases, please find mobile approval",
        MIX_UBL_ALL_UBL_FE_LOC_00050681: "Repair type",
        MIX_UBL_ALL_UBL_FE_LOC_00050680: "Operating instructions:",
        MIX_UBL_ALL_UBL_FE_LOC_00050719: "Check the details",
        MIX_UBL_ALL_UBL_FE_LOC_00050718: "Note: The query statement is as follows (NC/NCC/U8C)",
        MIX_UBL_ALL_UBL_FE_LOC_00050717: "End Time",
        MIX_UBL_ALL_UBL_FE_LOC_00050716: "Run start time",
        MIX_UBL_ALL_UBL_FE_LOC_00050715: "Sync task execution log",
        MIX_UBL_ALL_UBL_FE_LOC_00050714: "View",
        MIX_UBL_ALL_UBL_FE_LOC_00050713: "Task execution status",
        MIX_UBL_ALL_UBL_FE_LOC_00050712: "Execution details",
        MIX_UBL_ALL_UBL_FE_LOC_00050711: "YHT ID",
        MIX_UBL_ALL_UBL_FE_LOC_00050710: "Data repair",
        MIX_UBL_ALL_UBL_FE_LOC_00050678: "Details",
        MIX_UBL_ALL_UBL_FE_LOC_00050677: "View factory SQL",
        MIX_UBL_ALL_UBL_FE_LOC_00050676: "Executing",
        MIX_UBL_ALL_UBL_FE_LOC_00050675: "Starting time",
        MIX_UBL_ALL_UBL_FE_LOC_00050674:
            "4. If there are multiple association relationships, confirm whether the association relationship data (ie the corresponding relationship between Youhutong and ERP user primary key) to be used for mobile approval is the latest data, if not, synchronize the corresponding employee data separately",
        MIX_UBL_ALL_UBL_FE_LOC_00050673: "Sync time",
        MIX_UBL_ALL_UBL_FE_LOC_00050709: "Running:",
        MIX_UBL_ALL_UBL_FE_LOC_00050708: "1. Enter user information to query user association details",
        MIX_UBL_ALL_UBL_FE_LOC_00050707: "Manual trigger",
        MIX_UBL_ALL_UBL_FE_LOC_00050706:
            "Query by user name: select cuserid as id, user_code, user_name from sm_user where user_name \u003d \u0027用户名\u0027",
        MIX_UBL_ALL_UBL_FE_LOC_00050705: "Execution status",
        MIX_UBL_ALL_UBL_FE_LOC_00050704: "User ID",
        MIX_UBL_ALL_UBL_FE_LOC_00050703: "Gateway ID:",
        MIX_UBL_ALL_UBL_FE_LOC_00050702: "View code",
        MIX_UBL_ALL_UBL_FE_LOC_00050701: "Search field type",
        MIX_UBL_ALL_UBL_FE_LOC_00050699: "Factory SQL",
        MIX_UBL_ALL_UBL_FE_LOC_00050698: "Number of tasks",
        MIX_UBL_ALL_UBL_FE_LOC_00050697: "2. If the associated relationship data is empty, synchronize the employee data separately",
        MIX_UBL_ALL_UBL_FE_LOC_00050696: "No.",
        MIX_UBL_ALL_UBL_FE_LOC_00050695: "Copy",
        MIX_UBL_ALL_UBL_FE_LOC_00050694: "Jump over:",
        MIX_UBL_ALL_UBL_FE_LOC_00050693: "End of execution",
        MIX_UBL_ALL_UBL_FE_LOC_00050879: "Partial success",
        MIX_UBL_ALL_UBL_FE_LOC_00050878: "Sync ID",
        MIX_UBL_ALL_UBL_FE_LOC_00050877: "Failure log",
        MIX_UBL_ALL_UBL_FE_LOC_00050876: "Not Enabled",
        MIX_UBL_ALL_UBL_FE_LOC_00050875: "View detailed log",
        MIX_UBL_ALL_UBL_FE_LOC_00050874: "Operating status",
        MIX_UBL_ALL_UBL_FE_LOC_00050873: "The current paged log is not written back",
        MIX_UBL_ALL_UBL_FE_LOC_00050872: "Number of pages",
        MIX_UBL_ALL_UBL_FE_LOC_00050871: "Details log",
        MIX_UBL_ALL_UBL_FE_LOC_00050870: "Activated",
        MIX_UBL_ALL_UBL_FE_LOC_00050882: "All logs",
        MIX_UBL_ALL_UBL_FE_LOC_00050881: "The amount of data",
        MIX_UBL_ALL_UBL_FE_LOC_00050880: "Download",
        MIX_UBL_ALL_UBL_FE_LOC_00050739: "All",
        MIX_UBL_ALL_UBL_FE_LOC_00050884: "Only show error log",
        MIX_UBL_ALL_UBL_FE_LOC_00050883: "Current log page number: ",

        MIX_UBL_ALL_UBL_FE_LOC_00050926: "View SQL",
        MIX_UBL_ALL_UBL_FE_LOC_2021891533: "type",
        MIX_UBL_ALL_UBL_FE_LOC_2021891534: "code",
        MIX_UBL_ALL_UBL_FE_LOC_2021891535: "name",
        MIX_UBL_ALL_UBL_FE_LOC_2021891536: "Primary key",
        MIX_UBL_ALL_UBL_FE_LOC_2021891537: "Candidate key",
        MIX_UBL_ALL_UBL_FE_LOC_2021891538: "Required",
        MIX_UBL_ALL_UBL_FE_LOC_2021891539: "Default value",
        MIX_UBL_ALL_UBL_FE_LOC_2021891540: "Primary key name",
        MIX_UBL_ALL_UBL_FE_LOC_2021891541: "preservation",
        MIX_UBL_ALL_UBL_FE_LOC_20218231439: "Update all",

        MIX_UBL_ALL_UBL_FE_LOC_20218261625: "Please process the data being edited first",
        MIX_UBL_ALL_UBL_FE_LOC_20219271106: "View historical SQL",
        MIX_UBL_ALL_UBL_FE_LOC_202112201500: "Historical SQL",
        MIX_UBL_ALL_UBL_FE_LOC_20219271107: "Modify data mapping",
        MIX_UBL_ALL_UBL_FE_LOC_20219271108: "Modify file",
        MIX_UBL_ALL_UBL_FE_LOC_20219271109: "Sass primary key",
        MIX_UBL_ALL_UBL_FE_LOC_20219271110: "code",
        MIX_UBL_ALL_UBL_FE_LOC_20219271111: "name",
        MIX_UBL_ALL_UBL_FE_LOC_20219271112: "cell-phone number",
        MIX_UBL_ALL_UBL_FE_LOC_20219271113: "mailbox",
        MIX_UBL_ALL_UBL_FE_LOC_20219271114: "query",
        MIX_UBL_ALL_UBL_FE_LOC_20219271115: "modify",

        MIX_UBL_ALL_UBL_FE_LOC_202111291525: "see",
        MIX_UBL_ALL_UBL_FE_LOC_20211211022: "Troubleshooting execution",
        MIX_UBL_ALL_UBL_FE_LOC_20211211023: "Troubleshooting log",
        MIX_UBL_ALL_UBL_FE_LOC_20211211024: "Condition",
        MIX_UBL_ALL_UBL_FE_LOC_20211211025: "Only 5 pieces of data are supported for troubleshooting, and more than 5 pieces are not supported",
        MIX_UBL_ALL_UBL_FE_LOC_20211211026: "Please click the troubleshooting log under troubleshooting execution to view it later",
        MIX_UBL_ALL_UBL_FE_LOC_20211211027: "testing",
        MIX_UBL_ALL_UBL_FE_LOC_20211211028: "implement",
        MIX_UBL_ALL_UBL_FE_LOC_20211211029: "Source data query log",
        MIX_UBL_ALL_UBL_FE_LOC_20211211030: "Target data query log",
        MIX_UBL_ALL_UBL_FE_LOC_20211211031: "Request body",
        MIX_UBL_ALL_UBL_FE_LOC_20211211032: "Return results",
        MIX_UBL_ALL_UBL_FE_LOC_20211211033: "Start time",
        MIX_UBL_ALL_UBL_FE_LOC_20211211034: "End time",
        MIX_UBL_ALL_UBL_FE_LOC_20211211035: "Request URL",
        MIX_UBL_ALL_UBL_FE_LOC_20211211036: "Request header",
        MIX_UBL_ALL_UBL_FE_LOC_20211211037: "Callback URL",
        MIX_UBL_ALL_UBL_FE_LOC_20211211038: "Execution failed",
        MIX_UBL_ALL_UBL_FE_LOC_20211261524: "Detection succeeded",

        MIX_UBL_ALL_UBL_FE_LOC_00050618: "Please enter the correct mailbox",
        MIX_UBL_ALL_UBL_FE_LOC_00050441: "Please enter the correct mobile phone number",

        MIX_UBL_ALL_UBL_FE_LOC_2022161700: "To modify SQL conditions, please uncheck",

        MIX_UBL_ALL_UBL_FE_LOC_2022761555: "Switch operation gateway",

        MIX_UBL_ALL_UBL_FE_LOC_2022781656: "Operation record",
        MIX_UBL_ALL_UBL_FE_LOC_2022781657: "Customize scheduled tasks",
        MIX_UBL_ALL_UBL_FE_LOC_2022781658: "system level scheduled tasks",
        MIX_UBL_ALL_UBL_FE_LOC_2022781659: "Timed execution",
        MIX_UBL_ALL_UBL_FE_LOC_2022781660: "Full re execution",
        MIX_UBL_ALL_UBL_FE_LOC_2022781661: "Scheduled execution list",
        MIX_UBL_ALL_UBL_FE_LOC_20227151435: "connection configuration",

        MIX_UBL_ALL_UBL_FE_LOC_20227191053: "Default or not",
        MIX_UBL_ALL_UBL_FE_LOC_2022781662: "Trigger method",
        MIX_UBL_ALL_UBL_FE_LOC_2022781663: "Passive execution",
        MIX_UBL_ALL_UBL_FE_LOC_2022781664: "Active execution",
        MIX_UBL_ALL_UBL_FE_LOC_20227131640: "Scheme code",
        MIX_UBL_ALL_UBL_FE_LOC_20227131641: "Scheme name",

        MIX_UBL_ALL_UBL_FE_LOC_20227261340: "Integration scheme configuration",

        MIX_UBL_ALL_UBL_FE_LOC_20227261614: "ERP integration scheme configuration",
        MIX_UBL_ALL_UBL_FE_LOC_20227261615: "Enable system level scheduled tasks",

        MIX_UBL_ALL_UBL_FE_LOC_20227271540: "The existing data will be completely overwritten. Please confirm to execute this task?",

        MIX_UBL_ALL_UBL_FE_LOC_20227281100: "Switch connections",

        MIX_UBL_ALL_UBL_FE_LOC_20227281101: "This scheme supports forced push, regardless of whether the target data exists or is enabled",
        MIX_UBL_ALL_UBL_FE_LOC_20227281102: "Execute",
        MIX_UBL_ALL_UBL_FE_LOC_20227281103: "All records",
        MIX_UBL_ALL_UBL_FE_LOC_20227281104: "Failure record",
        MIX_UBL_ALL_UBL_FE_LOC_20227281105: "Scheme",

        MIX_UBL_ALL_UBL_FE_LOC_2022851533: "Please select a connection",

        MIX_UBL_ALL_UBL_FE_LOC_2022891122: "View management is now moved to the scheme name, and click display",

        MIX_UBL_ALL_UBL_FE_LOC_20228111443: "waiting",

        MIX_UBL_ALL_UBL_FE_LOC_20228121003: "Source system primary key",
        MIX_UBL_ALL_UBL_FE_LOC_20228121004: "Target system primary key",

        MIX_UBL_ALL_UBL_FE_LOC_20228171609: "Connection switching succeeded",

        MIX_UBL_ALL_UBL_FE_LOC_20229161413: "Failure reason",

        MIX_UBL_ALL_UBL_FE_LOC_20229211546: "View execution sql",
        MIX_UBL_ALL_UBL_FE_LOC_20229211547: "Statistics sql",
        MIX_UBL_ALL_UBL_FE_LOC_20229211548: "Execute sql",
        MIX_UBL_ALL_UBL_FE_LOC_20229211549: "Data processing",

        MIX_UBL_ALL_UBL_FE_LOC_202210271514: "Incremental query time",

        MIX_UBL_ALL_UBL_FE_LOC_202211151751: "Reset succeeded",

        MIX_UBL_ALL_UBL_FE_LOC_202211221110: "Increment does not support setting conditions separately",

        MIX_UBL_ALL_UBL_FE_LOC_202302131100: "Target file data processing progress",
        MIX_UBL_ALL_UBL_FE_LOC_202302131101: "Not started",

        MIX_UBL_ALL_UBL_FE_LOC_202302201110: "Synchronize data details",
        MIX_UBL_ALL_UBL_FE_LOC_202302201111: "Problem data",
        MIX_UBL_ALL_UBL_FE_LOC_202302201112: "Synchronize data details",
        MIX_UBL_ALL_UBL_FE_LOC_202302201113: "Error type",
        MIX_UBL_ALL_UBL_FE_LOC_202302201114: "Error reason",
        MIX_UBL_ALL_UBL_FE_LOC_202302201115: "Last update",
        MIX_UBL_ALL_UBL_FE_LOC_202302201116:
            "Please follow the prompt reason, move to the corresponding system adjustment, and then click [Re-upload] to repair the problem data",
        MIX_UBL_ALL_UBL_FE_LOC_202302201117: "Re-upload",
        MIX_UBL_ALL_UBL_FE_LOC_202302201118: "Other",
        MIX_UBL_ALL_UBL_FE_LOC_202302201119: "Dependency data error",
        MIX_UBL_ALL_UBL_FE_LOC_202302201120: "View dependency graph",
        MIX_UBL_ALL_UBL_FE_LOC_202302201121: "All synchronized data",
        MIX_UBL_ALL_UBL_FE_LOC_202302201122: "All mappings",
        MIX_UBL_ALL_UBL_FE_LOC_202302201123: "Load Dependencies",
        MIX_UBL_ALL_UBL_FE_LOC_202302201124: "Root node",
        MIX_UBL_ALL_UBL_FE_LOC_202302201125: "Reason",
        MIX_UBL_ALL_UBL_FE_LOC_202302201126: "View dependency data",
        MIX_UBL_ALL_UBL_FE_LOC_202302201127: "Dependency data error has been found",
        MIX_UBL_ALL_UBL_FE_LOC_202302201128: "Now",

        MIX_UBL_ALL_UBL_FE_LOC_202304061040: "Task operation monitoring",

        MIX_UBL_ALL_UBL_FE_LOC_202304111723: "Force push",
        MIX_UBL_ALL_UBL_FE_LOC_202304111724: "Time period supplementary recording",
        MIX_UBL_ALL_UBL_FE_LOC_202304111725: "Incremental timestamp",

        MIX_UBL_ALL_UBL_FE_LOC_202304191050: "Timed tasks will only support incremental queries, not full queries and specified condition queries",
        MIX_UBL_ALL_UBL_FE_LOC_202304191051: "This solution supports supplementary data recording for a specified time period",
        MIX_UBL_ALL_UBL_FE_LOC_202304191052: "This solution supports modifying the incremental timestamp of this execution",

        MIX_UBL_ALL_UBL_FE_LOC_202305041935: "Format",

        MIX_UBL_ALL_UBL_FE_LOC_2023053119585: "Please select an enumeration mapping",

        MIX_UBL_ALL_UBL_FE_LOC_202307191532: "Excel Import",
        MIX_UBL_ALL_UBL_FE_LOC_202307241000: "Source System Code",
        MIX_UBL_ALL_UBL_FE_LOC_202307241001: "Target System Code",

        MIX_UBL_ALL_UBL_FE_LOC_202308011500: "MDM and BIP",
        MIX_UBL_ALL_UBL_FE_LOC_202308011501: "MDM and ERP",
        MIX_UBL_ALL_UBL_FE_LOC_202308011502: "Import was successful",
    },
};
