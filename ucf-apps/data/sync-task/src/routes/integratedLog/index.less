.ubl-intelog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 46px;
    // box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.1);
    // margin-bottom: 4px;

    .header-content {
        display: flex;
        align-items: center;

        &-back {
            height: 20px;
            padding: 0 4px;
            margin: 0 0 0 8px;
            line-height: 20px;
            font-size: 12px;
            color: #505766;
            border: 0;
            min-width: unset;

            :global(.wui-button-text-wrap) {
                display: flex;
                align-items: center;
            }
        }

        i {
            font-size: 14px;
            padding: 0;
        }
    }

    .app-search-con {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 15px;
    }
}

.ubl-intelog-content {
    height: calc(100% - 50px);
    overflow: auto;
    border-top: 1px solid #dfdfdf;
}

.ubl-intelog-header-left {
    padding-left: 8px;
}

.ubl-intelog-header-right {
    padding-right: 8px;
}

.ubl-intelog-title-h1 {
    font-size: 18px;
    font-family: PingFang-SC-Heavy, PingFang-SC;
    color: #333;
    margin-left: 8px;
    font-weight: 600;
    display: flex;
    align-items: center;
}
