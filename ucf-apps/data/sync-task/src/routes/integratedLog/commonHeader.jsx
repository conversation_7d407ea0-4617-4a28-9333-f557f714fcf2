/**
 * @description 通用Header
 * 可配置 【左侧：返回按钮】【右侧：搜索框 + 新增按钮】
 */
import React from "react";
import { Button, Icon } from "@tinper/next-ui";
import "./index.less";
import classNames from "classnames";

const BackBtn = (props) => {
    const { name, showIcon, goBack } = props;
    return name || showIcon ? (
        <div className={"header-content"}>
            {showIcon && (
                <Button fieldid="21881783-65a6-413f-99b1-c8e136b76c83" bordered={false} className="header-content-back" onClick={goBack}>
                    <Icon fieldid="8cedfc04-c687-46ea-9401-637ed74fd079" type="uf-xiangzuo" />
                    {lang.templateByUuid("UID:P_UBL-FE_1D96541205E00005", "返回") /* "返回" */}
                </Button>
            )}

            {name && <span className="ubl-intelog-title-h1">{name}</span>}
        </div>
    ) : (
        <div />
    );
};

const CommonHeader = function (props) {
    const { children, name, navigate } = props;

    const goBack = () => {
        if (navigate) {
            navigate(-1);
        }
    };

    return (
        <>
            <div className="ubl-intelog-header">
                <BackBtn name={name} showIcon goBack={goBack} />
            </div>

            <div className={classNames("ubl-intelog-content")}>{children}</div>
        </>
    );
};

export default CommonHeader;
