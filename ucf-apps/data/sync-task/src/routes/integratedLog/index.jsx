import React from "react";
import { useNavigate, useParams } from "react-router";
import DetailIcon from "./detail-header-icon.svg";
import CommonHeader from "./commonHeader";
const { MainApplication } = window.tnsSdk;

const IntegratedLog = () => {
    const params = useParams();
    const navigate = useNavigate();
    const app = MainApplication.parseUrl(
        `#/integrationDock!bootstrap/IntegratedData/${params.id}/${params.viewTag}/${params.startTime}/${params.endTime}?providerHost=/iuap-ipaas-base/ucf-wh/base-fe/`
    );
    return (
        <div style={{ height: "100%" }}>
            <CommonHeader
                visible
                titleStyle="h1"
                navigate={navigate}
                name={
                    <>
                        <img src={DetailIcon} style={{ marginRight: 8 }} />
                        详细日志
                    </>
                }
                showIcon
            >
                <div id="workbench_content" style={{ height: "calc(100% - 40px)" }}>
                    <MainApplication
                        providerHost={app.providerHost} //小应用提供方-host路径，可以为绝对地址或相对地址
                        providerName={app.providerName} //小应用提供方-服务名称
                        providerEntry={app.providerEntry} //小应用提供方-入口名
                        routePath={app.routePath} //小应用提供方-初始化路由路径
                        routeParam={app.routeParam} //小应用提供方-初始化路由参数
                        onSuccess={() => console.log("Small Application Run SUCCESS", app)} //小应用运行成功的回调事件
                        onError={() => console.log("Small Application Run ERROR", app)} //小应用运行失败的回调事件
                    />
                </div>
            </CommonHeader>
        </div>
    );
};

export default IntegratedLog;
