import { observable, computed, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { defaultListMap } from "utils/pageListUtils";
import { autoServiceMessage } from "utils/service";
import * as ownerService from "./service";

const initState = {
    queryParams: {
        taskId: "",
        dataTypeName: "",
    },
    dataSource: { ...defaultListMap },
    selectedLog: null,
    logStatus: "all", //fail|all,
    serviceCodeDiwork: "kfljsjtbrw",
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    setPageParams = (paramInfo) => {
        this.changeState(paramInfo);
    };

    init = () => {
        this.state = initState;
    };

    changeLogStatus = (status) => {
        this.state.logStatus = status;
        this.getDataSource({
            pageNo: 1,
        });
    };

    // 设置选中日志信息
    onLogGridSelect = (log = null) => {
        this.state.selectedLog = log;
    };

    getDataSource = (data) => {
        let logStatus = this.state.logStatus;
        let requestData = {
            status: logStatus === "fail" ? 0 : "",
            taskId: this.state.queryParams.taskId,
            ...data,
        };
        this.getPagesListFunc({
            service: ownerService.getTaskLogService,
            requestData,
            dataKey: "dataSource",
            header: { serviceCode: this.state.serviceCodeDiwork },
        });
    };
    handleResetStatus = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.resetStatusService(data, { serviceCode: this.state.serviceCodeDiwork }),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803E5", "重置成功", undefined, {
                returnStr: true,
            }),
        });
        if (res) {
            this.getDataSource();
        }
    };
    executeTask = async (taskInfo, continuation) => {
        let ids = [taskInfo.taskid];
        let res = await autoServiceMessage({
            service: ownerService.batchSyncTaskService(ids, continuation),
        });
        return res;
    };
}
/**
 * 根据具体模块修改 storeKey 为保证唯一性
 * storeKey 由 模块文件夹名称 + 页面名称 + 'Store' 小驼峰
 *
 * storeKey是作为当前 Store 的唯一键， storeKey与Store的实例会在 rootStore或者StoreMap中以 键值对的 形式存在
 * 1. 用于在模块的 storeMap（模块routes/store.js）中注册并初始化当前Store
 * 2. 用于在container的 inject 中关联当前Store的实例时，从模块的storeMap或者rootStore中获取对应store实例
 * */
export const storeKey = "dataSyncTaskSyncLogStore";

export default Store;
