import { getInvokeService } from "utils/service";

/**
 * 获取任务日志
 * @param {Object} data
 * @param {String} data.taskId
 * @param {String|Number=} data.status=[0失败1成功] -日志类型 不传查所有
 * @param {String|Number} data.pageNo
 * @param {String|Number} data.pageSize
 * @return {Promise<unknown>}
 */
export const getTaskLogService = function (data, header) {
    let { taskId, ..._data } = data;
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/erpdata/task/getLog/page/" + taskId,
            header,
        },
        _data
    );
};

export const resetStatusService = function (data, header) {
    let { id, ..._data } = data;
    return getInvokeService(
        {
            method: "POST",
            path: "/gwmanage/gwportal/diwork/erpdata/task/reSetTaskStatus/" + id,
            header,
        },
        _data
    );
};
export const batchSyncTaskService = function (taskIds, continuation, header) {
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/erpdata/task/syncdata/batch",
            timeout: 60000,
            header,
        },
        { taskIds },
        { continuation }
    );
};
