/*
 * @Author: your name
 * @Date: 2021-04-15 17:40:03
 * @LastEditTime: 2021-06-09 14:42:43
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \ublinker-fe\ucf-apps\data\sync-task\src\routes\sync-log\components\LogModal\index.js
 */
import React, { useEffect, useState } from "react";
import Modal from "components/TinperBee/Modal";
import { observer } from "mobx-react";
import { getLocalImg } from "utils/index";
import Store from "./store";
import { Switch } from "components/TinperBee";
import Pagination from "components/TinperBee/Pagination";
import "./index.less";

const logStore = new Store();

function LogModal(props) {
    const { selectedLog, onCancel } = props;
    const [selfShow, setSelfShow] = useState(false);

    useEffect(() => {
        setSelfShow(!!selectedLog);
        if (selectedLog) {
            logStore.setLogId(selectedLog.logid || selectedLog.id);
            //selectedLog.logid 是从同步任务执行日志——查看详情——查看日志   进来的
            //selectedLog.id    是从首页  查看日志——查看详细日志    进来的
        } else {
            logStore.init();
        }
    }, [selectedLog]);

    const { pages, pageIndex, onlyFail, logHasNotWrite, dataSource } = logStore.state;
    const pageTotal = pages.length;

    return (
        <Modal
            fieldid="ublinker-routes-sync-log-components-LogModal-index-475820-Modal"
            show={selfShow}
            cancelText={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803F0", "关闭") /* "关闭" */}
            title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803F2", "详情日志", undefined, {
                returnStr: true,
            })}
            okHide
            onCancel={onCancel}
            className="logModal"
            height={600}
        >
            <div className="log-content">
                <div className="log-success-checked">
                    <Switch
                        fieldid="ublinker-routes-sync-log-components-LogModal-index-5887040-Switch"
                        checked={onlyFail}
                        size="sm"
                        colors="blue"
                        onChange={logStore.changeOnlyFailAction}
                    />
                    <span className="ucg-mar-l-10">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803F1", "只显示错误日志") /* "只显示错误日志" */}</span>
                </div>
                {logHasNotWrite || pageTotal === 0 ? (
                    <div className="log-panel log-not-write">
                        <img fieldid="ublinker-routes-sync-log-components-LogModal-index-3393452-img" src={getLocalImg("data/xiaoyou.svg")} />
                        <p>{lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803F3", "当前分页日志未回写") /* "当前分页日志未回写" */}</p>
                    </div>
                ) : (
                    <div className="log-panel">
                        <p>
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803EF", "当前日志页码：") /* "当前日志页码：" */}
                            {dataSource.pageNo}
                        </p>
                        <div dangerouslySetInnerHTML={{ __html: dataSource.message }}></div>
                    </div>
                )}
                {pageTotal >= 1 ? (
                    <div className="log-pagination">
                        <Pagination
                            fieldid="ublinker-routes-sync-log-components-LogModal-index-1201677-Pagination"
                            prev
                            next
                            pageSizeOptions={[1]}
                            pageSize={1}
                            boundaryLinks
                            // items={pageTotal}
                            current={pageIndex}
                            onChange={logStore.setPageIndex}
                            total={pageTotal} //此处接口不返回总条数，所以把总页数赋值，一页一条
                        />
                    </div>
                ) : null}
            </div>
        </Modal>
    );
}

export default observer(LogModal);
