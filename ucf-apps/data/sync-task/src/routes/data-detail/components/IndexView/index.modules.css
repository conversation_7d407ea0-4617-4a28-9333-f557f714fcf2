.sync-task-data-detail {
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative;
    transform: scale(1);
}
.data-detail-header {
    height: 44px !important;
}
.page-header-tabs {
    /* margin: 0 auto; */
    /* display: inline-block; */
    /* vertical-align: middle !important; */
    /* margin-left: 30%; */
    /* position: relative; */
    /* max-width: 240px; */
    /* margin-top: -41px; */
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 6px;
    margin-left: 0;
    font-size: 14px;
    display: block;
}
.table-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    height: 43px;
}
.empty {
    flex: 1;
    flex-direction: column;
    display: flex;
    align-items: center;
    justify-content: center;
}
