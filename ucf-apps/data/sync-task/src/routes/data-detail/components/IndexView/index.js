import React, { Component, Fragment } from "react";
import { <PERSON><PERSON>, But<PERSON>, Tabs, Switch, Message, Checkbox } from "components/TinperBee";
import SearchPanel from "../SearchPanel/main";
import EditModal from "../EditModal";
import JsonModal from "../JsonModal";
import Grid from "components/TinperBee/Grid";
import { Header, Content, Footer } from "components/PageView";
import Pagination from "components/TinperBee/Pagination";
import styles from "./index.modules.css";
import { navTo, withIframeSize } from "decorator/index";
import DataMapModal from "../../../list/components/DataMapModal";
import DataMakeUpModal from "../../../list/components/DataMakeUpModal";
import { Table, Empty } from "@tinper/next-ui";
import withRouter from "decorator/withRouter";

const { filterColumn, multiSelect } = Table;
const ComplexTable = multiSelect(filterColumn(Table));
const { TabPane } = Tabs;

@withRouter
@withIframeSize
class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            gridColumns: [],
            showGrid: "loading",
            showDocModal: false,
            dataPk: "",
            editModalShow: false,
            showJsonModal: false,
            activeKey: "4",
            isPull: false,
            selectedRowKeys: [],
            selectedRows: [],
            isTranslate: false,
        };
    }

    async componentDidMount() {
        let { ownerStore } = this.props;

        let columnsRes = await ownerStore.getDataColumns();
        // debugger;
        if (columnsRes) {
            let gridColumns = this.getColumns(columnsRes.columns);
            this.setState({
                gridColumns,
                showGrid: gridColumns.length > 0 ? "visible" : "hidden",
            });
            // debugger;
            ownerStore.getDataList();
        }
    }

    componentWillUnmount() {
        // debugger;
        this.props.ownerStore.init();
    }
    handleDelOk = async (dataArr, isBatch) => {
        const res = await this.props.ownerStore.deleteData(dataArr);
        if (res && isBatch) {
            this.setState({
                selectedRows: [],
            });
        }
    };

    handleDelete = (dataArr, isBatch) => {
        Modal.confirm({
            fieldid: "************",
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418041A", "确认要删除吗？") /* "确认要删除吗？" */,
            onOk: () => this.handleDelOk(dataArr, isBatch),
        });
    };

    // 批量删除数据详情
    handleBatchDelData = (dataArr) => {
        const { selectedRows } = this.state;
        if (!selectedRows.length) {
            Message.destroy();
            Message.create({ content: lang.templateByUuid("UID:P_UBL-FE_1C2D42C20448000E", "请先选择数据") /* "请先选择数据" */, color: "danger" });
        } else {
            this.handleDelete(dataArr, true);
        }
    };

    handleEdit = (index, record) => {
        this.props.ownerStore.setEdit(index, record);
    };

    handleSaveEdit = (data, cb) => {
        this.props.ownerStore.saveEdit(data, cb);
    };

    selfCols = [];

    handleChange = (field, value) => {
        this.props.ownerStore.changeEditRowInfo(field, value);
    };

    renderCol = (field, value, record) => {
        let { isEdit = false } = record;
        return typeof value === "number" || (value && typeof value != "object") ? (
            value
        ) : typeof value === "object" && value ? (
            <div onClick={this.changeJsonModal.bind(null, field, value)} style={{ color: "#588ce9", cursor: "pointer" }}>
                {JSON.stringify(value)}
            </div>
        ) : (
            "-"
        );
    };

    getColumns = (columns) => {
        let _columns = [];
        for (let key in columns) {
            let columnName = columns[key];
            _columns.push({
                title: columnName,
                dataIndex: key,
                key: key,
                width: 200,
                render: this.renderCol.bind(null, key),
            });
        }
        return _columns.concat(this.selfCols);
    };
    changeJsonModal = (nowJsonKey, nowJsonValue) => {
        this.setState({
            showJsonModal: !this.state.showJsonModal,
            nowJsonValue,
            nowJsonKey,
        });
    };
    dataTrans = (list) => {
        let newList = [];
        for (let i = 0; i < list.length; i++) {
            let obj = {};
            for (let key in list[i]) {
                if (typeof list[i][key] === "boolean" && key !== "isEdit") {
                    obj[key] = list[i][key] + "";
                } else if (typeof list[i][key] === "object") {
                    if (list[i][key] == null) {
                        obj[key] = list[i][key];
                    } else {
                        // obj[key] = JSON.stringify(list[i][key]); //如果是对象，不处理了，columns.render处理
                        obj[key] = list[i][key];
                    }
                } else {
                    obj[key] = list[i][key];
                }
            }
            newList.push(obj);
        }
        return newList;
    };
    changeEditModalShow = (index, record) => {
        // this.props.ownerStore.onTaskGridSelect(task);
        this.handleEdit(index, record);
        this.setState({ editModalShow: !this.state.editModalShow });
    };
    handleEditModalOk = async (data) => {
        let { ownerStore } = this.props;
        this.handleSaveEdit(data, this.changeEditModalShow);
        // let res = await ownerStore.setSqlWhere(data);
        // if (res) {
        //   this.changeEditModalShow();
        // }
    };
    handleNavTo = (batchId) => {
        this.props.navigate({
            pathname: "/batch-detail",
            search: "?batchId=" + batchId,
        });
    };
    onChange = (activeKey) => {
        console.log(`onChange ${activeKey}o-^-o`);
        this.setState({
            activeKey,
            selectedRowKeys: [], // 因其他两个单独组件内部状态，和其他两个tab保持一致，切换tab清空selectedkeys
            selectedRows: [],
        });
    };
    handleReImExecute = async (taskInfo, executeType, errorDataIds, selectedErrData) => {
        let { ownerStore } = this.props;
        if (executeType === "now") {
            let res = await ownerStore.checkRunnable(taskInfo.pk_id);
            if (res && res.status == 1) {
                this.setState({ isPull: false }); //要求初始值永远是false，设置为true只在单次中
                //是否立即執行
                Modal.confirm({
                    fieldid: "************",
                    title: lang.templateByUuid("UID:P_UBL-FE_1F7D965C05600015", "是否立即执行") /* "是否立即执行" */,
                    content: (
                        <>
                            <div>
                                {lang.templateByUuid(
                                    "UID:P_UBL-FE_18D8CEF60418041B",
                                    "本方案支持强制推送，无视目标数据是否存在，是否启用" //@notranslate
                                )}
                            </div>
                            <Switch
                                fieldid="ublinker-routes-list-components-IndexView-index-4420293-Switch" //taskInfo.fromType==1 &&
                                className={styles["item-state-area-switch"]}
                                // checked={aa}
                                size="sm"
                                colors="blue"
                                onChange={(isPull) => {
                                    this.setState({ isPull });
                                }}
                            />
                        </>
                    ),
                    onOk: async () => {
                        let res;
                        if ([1, 2].includes(taskInfo.fromType)) {
                            res = await ownerStore.executeTask(
                                taskInfo,
                                errorDataIds,
                                this.state.isPull,
                                selectedErrData.lastExecTime,
                                selectedErrData.lastExecEnd
                            );
                        } else {
                            res = await ownerStore.executeTask(taskInfo, errorDataIds, this.state.isPull);
                        }
                        this.setState({ isPull: false });
                        if (res) {
                            Modal.success({
                                title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180411", "操作成功！") /* "操作成功！" */,
                                content: (
                                    <div>
                                        <p>
                                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180413", "将要执行的数据同步任务：", undefined, {
                                                returnStr: true,
                                            }) + res.data.viewNames || ""}
                                        </p>
                                    </div>
                                ),
                                confirmType: "one",
                                okText: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180416", "查看执行详情") /* "查看执行详情" */,
                                okCancel: true,
                                onOk: () => {
                                    this.props.navigate({
                                        pathname: "/batch-detail",
                                        search: "?batchId=" + res.data.batchId,
                                    });
                                },
                            });
                        }
                    },
                });
            } else {
                Modal.confirm({
                    fieldid: "************",
                    title: res.msg,
                });
            }
        } else if (executeType === "where") {
            ownerStore.executeTask(taskInfo, true);
        }
    };
    submitMakeUp = (task, errorDataIds, selectedErrData) => {
        this.handleReImExecute(task, "now", errorDataIds, selectedErrData);
    };
    hoverContent = (record, index, a, b, c) => {
        let {
            ownerState: { hasEdit },
        } = this.props;
        return (
            <Fragment>
                <Button
                    fieldid="UCG-FE-routes-data-detail-components-IndexView-index-6005888-Button"
                    {...Grid.hoverButtonPorps}
                    disabled={hasEdit}
                    onClick={this.changeEditModalShow.bind(null, index, record)}
                >
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180417", "编辑") /* "编辑" */}
                </Button>

                <Button
                    fieldid="UCG-FE-routes-data-detail-components-IndexView-index-8996761-Button"
                    {...Grid.hoverButtonPorps}
                    disabled={hasEdit}
                    onClick={this.handleDelete.bind(null, [record], false)}
                >
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180419", "删除") /* "删除" */}
                </Button>
            </Fragment>
        );
    };
    onDropBorder = (_e, _width, _column, columns) => {
        this.setState({
            gridColumns: columns,
        });
    };
    handleCheckTranslate = () => {
        this.setState(
            (prevState) => {
                const newIsTranslate = !prevState.isTranslate;
                this.props.ownerStore.setSearchParams({ isTranslate: newIsTranslate });
                return { isTranslate: newIsTranslate };
            },
            () => this.props.ownerStore.getDataList({ pageNo: 1 })
        );
    };
    safeParse = (str, defaultValue = {}) => {
        try {
            return JSON.parse(str);
        } catch (e) {
            return defaultValue;
        }
    };
    render() {
        let { gridColumns, showGrid, editModalShow, showJsonModal, nowJsonValue, nowJsonKey, activeKey } = this.state;
        let { ownerState, ownerStore, syncTaskListState } = this.props;
        let { dataSource, pkField, columns, resultArr2, queryCols, pagination, hasEdit, editRowInfo, queryParams } = ownerState;

        const { selectedTask } = syncTaskListState;
        if (pagination) {
            pagination.disabled = hasEdit;
        }
        let rowSelection = {
            type: "checkbox",
            selectedRowKeys: this.state.selectedRowKeys,
            onChange: (selectedRowKeys, selectedRows) => {
                this.setState({
                    selectedRowKeys,
                    selectedRows,
                });
            },
        };

        return (
            <div className={styles["sync-task-data-detail"]}>
                <Header
                    back={() => {
                        ownerStore.clearSearchParams();
                        this.props.navigate(-1);
                    }}
                    title={selectedTask.dataTypeName}
                    tabs={
                        <Tabs
                            fieldid="UCG-FE-routes-data-detail-components-IndexView-index-5763404-Tabs"
                            activeKey={this.state.activeKey}
                            type={this.state.tabType}
                            onChange={this.onChange}
                            tabBarStyle={{ marginBottom: 0 }}
                        >
                            <TabPane
                                fieldid="UCG-FE-routes-data-detail-components-IndexView-index-1243262-TabPane"
                                tab={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180415", "映射关系") /* "映射关系" */}
                                key="4"
                            ></TabPane>
                            {selectedTask.fromType !== 3 && (
                                <TabPane
                                    fieldid="UCG-FE-routes-data-detail-components-IndexView-index-6028463-TabPane"
                                    tab={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180412", "问题数据") /* "问题数据" */}
                                    key="3"
                                ></TabPane>
                            )}
                            {selectedTask.fromType !== 3 && (
                                <TabPane
                                    fieldid="UCG-FE-routes-data-detail-components-IndexView-index-3826981-TabPane"
                                    tab={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180410", "同步数据详情") /* "同步数据详情" */}
                                    key="2"
                                ></TabPane>
                            )}
                        </Tabs>
                    }
                />
                {activeKey == 2 ? (
                    <>
                        {showGrid == "loading" ? null : showGrid === "visible" ? (
                            <>
                                <SearchPanel
                                    hasEdit={hasEdit}
                                    columns={columns}
                                    mapresultArr={resultArr2}
                                    queryCols={[queryCols[0]]}
                                    ownerState={ownerState}
                                    ownerStore={ownerStore}
                                    handleNavTo={this.handleNavTo}
                                    onSearch={() => {
                                        ownerStore.getDataList({ pageNo: 1 });
                                    }}
                                />
                                <div className={styles["table-header"]}>
                                    <span
                                        style={{
                                            fontSize: "14px",
                                            fontWeight: "600",
                                            color: "#333333",
                                        }}
                                    >
                                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418041C", "所有同步数据") /* "所有同步数据" */}({dataSource.total})
                                    </span>
                                    <div style={{ display: "flex", alignItems: "center" }}>
                                        <Checkbox
                                            fieldid="2856d397-16e1-49e4-9140-ca2e61989818"
                                            style={{ float: "right", marginRight: "10px" }}
                                            checked={this.state.isTranslate}
                                            onChange={this.handleCheckTranslate}
                                        >
                                            {lang.templateByUuid("UID:P_UBL-FE_1AE83F7205C0001B", "显示翻译后数据") /* "显示翻译后数据" */}
                                        </Checkbox>
                                        <Button fieldid="baddab0e-141a-4180-9153-a08ef18a16bb" onClick={() => this.handleBatchDelData(this.state.selectedRows)}>
                                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180419", "删除") /* "删除" */}
                                        </Button>
                                    </div>
                                </div>
                                <Table
                                    fieldid="ublinker-routes-data-detail-components-IndexView-index-6283589-Grid"
                                    rowKey={pkField}
                                    columns={gridColumns}
                                    data={this.dataTrans(dataSource.list)}
                                    hoverContent={this.hoverContent}
                                    pagination={false}
                                    columnFilterAble
                                    dragborder={true}
                                    onDropBorder={this.onDropBorder}
                                    rowSelection={rowSelection}
                                    scroll={{ y: this.props.iframeSize.height - 218 }}
                                />
                                {pagination && (
                                    <Pagination
                                        fieldid="bccf9e18-4f8a-4309-96d5-f52ea5562ad1"
                                        current={pagination.activePage}
                                        onChange={(a, b) => pagination?.onPageChange({ pageSize: b, pageNo: a })}
                                        onPageSizeChange={(a, b) => pagination?.onPageChange({ pageSize: b, pageNo: 1 })}
                                        showSizeChanger
                                        total={pagination.total}
                                        pageSize={pagination.pageSize}
                                        pageSizeOptions={[...Pagination.dataNumSelect["page"]]}
                                        style={{ backgroundColor: "#fff", position: "fixed", bottom: 0, right: 0, width: "100%" }}
                                    />
                                )}
                            </>
                        ) : (
                            <Empty
                                fieldid="239a81af-7447-4b46-a27b-847c240c420b"
                                className={styles["empty"]}
                                description={
                                    <span>
                                        {
                                            lang.templateByUuid(
                                                "UID:P_UBL-FE_1EC2E0B20428000A",
                                                "当前无数据可展示，请前往方案设计中对字段进行数据标识，以便查看同步数据详情" //@notranslate
                                            ) /* "当前无数据可展示，请前往方案设计中对字段进行数据标识，以便查看同步数据详情" */
                                        }
                                    </span>
                                }
                            />
                        )}
                        <EditModal
                            taskInfo={editRowInfo}
                            show={editModalShow}
                            onCancel={this.changeEditModalShow}
                            onOk={this.handleEditModalOk}
                            ownerStore={ownerStore}
                            ownerState={ownerState}
                        />
                        <JsonModal
                            taskInfo={editRowInfo}
                            jsonValue={nowJsonValue}
                            jsonKey={nowJsonKey}
                            show={showJsonModal}
                            onCancel={this.changeJsonModal}
                            onOk={this.changeJsonModal}
                            ownerStore={ownerStore}
                            ownerState={ownerState}
                        />
                    </>
                ) : activeKey == 3 ? (
                    <DataMakeUpModal
                        taskInfo={selectedTask}
                        // taskInfo={aa}
                        onOk={this.submitMakeUp}
                    />
                ) : (
                    <DataMapModal taskInfo={selectedTask} storeKey="data-detail" />
                )}
            </div>
        );
    }
}

export default IndexView;
