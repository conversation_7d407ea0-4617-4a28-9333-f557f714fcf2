/*
 * @Author: your name
 * @Date: 2021-07-19 17:02:25
 * @LastEditTime: 2021-07-22 21:15:10
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \ublinker-fe\ucf-apps\start\start-config\src\routes\list\components\AddModal\index.js
 */
import React, { useState, useEffect } from "react";
import Modal from "components/TinperBee/Modal";
import { FormControl, FormList } from "components/TinperBee";
// import {Input as FormControl,Form as FormList,Modal} from '@tinper/next-ui';
const { TextArea } = FormControl;
import { Success, Error } from "utils/feedback";
import { codeReg } from "utils/regExp";
import "./index.less";
const FormItem = FormList.Item;

const AddAKSKModal = (props) => {
    const {
        show,
        onCancel,
        type,
        editRecord,
        sysList,
        taskInfo,
        ownerState: { columns },
    } = props;
    const [form] = FormList.useForm();
    // console.log(form);
    // console.log(taskInfo);
    // const [remark, setRemark] = useState(type === "create" ? "": editRecord.remark);
    const [file, setFile] = useState();
    // 表单字段验证方法
    const { validateFields, getFieldProps, setFieldsValue, getFieldError } = form;
    useEffect(() => {
        if (taskInfo) {
            setTimeout(() => {
                for (let key in taskInfo) {
                    if (typeof taskInfo[key] == "object") {
                        taskInfo[key] = JSON.stringify(taskInfo[key]);
                    }
                    if (taskInfo[key] == "null") {
                        taskInfo[key] = "";
                    }
                }
                console.log(taskInfo);
                setFieldsValue(taskInfo);
            });
        }
    }, [taskInfo]);
    const onOk = () => {
        validateFields()
            .then((values) => {
                console.log({ ...taskInfo, ...values });
                props.onOk({ ...taskInfo, ...values });
            })
            .catch((errorInfo) => {
                console.log(errorInfo);
            });
    };
    const formItemLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 12 },
    };
    return (
        <Modal
            fieldid="ublinker-routes-data-detail-components-EditModal-index-9022414-Modal"
            show={show}
            cancelText={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418014D", "取消") /* "取消" */}
            title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180544", "编辑", undefined, {
                returnStr: true,
            })}
            onOk={onOk}
            onCancel={onCancel}
            size={"md"}
        >
            <FormList
                fieldid="ublinker-routes-data-detail-components-EditModal-index-8765318-FormList"
                className=""
                form={form}
                name="form122"
                labelAlign="right"
                {...formItemLayout}
            >
                {Object.keys(columns).map((key) => {
                    return (
                        <FormItem fieldid="ublinker-routes-data-detail-components-EditModal-index-4474842-FormItem" label={columns[key]} name={key}>
                            <FormControl fieldid="ublinker-routes-data-detail-components-EditModal-index-6889134-FormControl" />
                        </FormItem>
                    );
                })}
            </FormList>
        </Modal>
    );
};

export default AddAKSKModal;
