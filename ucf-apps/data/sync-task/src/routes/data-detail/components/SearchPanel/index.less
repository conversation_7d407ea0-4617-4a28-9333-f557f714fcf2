.u-row {
    .u-input-group {
        height: 22px;
    }

    .u-button {
        padding: 0px;
        height: 22px !important;
    }

    .u-input-group-btn {
        height: 22px;
        font-size: 14px;

        button {
            width: 80px;
        }
    }
    .drop-form-contorl {
        height: 22px;
        margin-left: 10px;
    }
}
.u-dropdown {
    height: 300px;
    overflow: auto;
}
.data-detail-dropdown {
    width: auto !important;
}
// .data-detail-searchpanel .wui-dropdown{
//     height:300px;
//     overflow: auto;
// }

.data-detail-menu {
    height: 300px;
    overflow: auto;
}
.search-form {
    flex: 1;
    flex-wrap: nowrap;
    overflow-x: scroll;
}

.search-panel {
    .sp-clear-padding {
        .wui-col {
            padding-left: 0 !important;
            padding-right: 0 !important;
        }
    }
}
.sync-detail-searchForm {
    .as-dataForm-item {
        margin-left: 0;
        margin-right: 0;
    }
    .wui-form-item-control {
        max-width: 100% !important;
    }
}
