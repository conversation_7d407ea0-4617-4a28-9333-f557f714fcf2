import React, { useCallback, useEffect, useState } from "react";
import { SearchForm } from "tne-tinpernextpro-fe";
import { Error } from "utils/feedback";
import { Space, Row, Col, Form } from "@tinper/next-ui";
import { PRIVATE } from "utils/util";
import "./index.less";
import { toJS } from "mobx";
const SearchFormItem = SearchForm.Item;
const formItemLayout = {
    labelCol: {
        xs: { span: 8 },
        sm: { span: 8 },
    },
    wrapperCol: {
        xs: { span: 16 },
        sm: { span: 16 },
    },
};

const Search = (props) => {
    let { onSearch, columns, queryCols, hasEdit, mapresultArr, ownerStore } = props;
    // const form = SearchForm.useSearchFormInstance();
    const [form] = Form.useForm();
    const renderFormItem = useCallback(() =>
        queryCols.map((queryItem) => (
            <>
                {columns[queryItem] && (
                    <SearchFormItem
                        {...formItemLayout}
                        fieldid="892866e7-2045-4cd9-a26b-fe31e821b526"
                        inputType="input"
                        label={columns[queryItem]}
                        name={queryItem}
                    />
                )}
            </>
        ))
    );
    useEffect(() => {
        form.setFieldsValue(ownerStore.searchParams);
    }, []);
    const handleSearch = useCallback((values) => {
        if (hasEdit) {
            Error(lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418017E", "请先处理正在编辑的数据", undefined, { returnStr: true }));
            return;
        }
        props.ownerStore.setSearchParams({ ...values });
        onSearch && onSearch();
    });
    const handleReset = (values) => {
        Object.keys(values).forEach((key) => {
            values[key] = undefined;
        });
        handleSearch(values);
    };

    return (
        <div className="search-panel">
            <SearchForm
                form={form}
                fieldid="445cd7c0-5687-4a39-9f16-6c52ffa6d408"
                formLayout={3}
                style={{ marginBottom: "10px" }}
                onSearch={handleSearch}
                onReset={handleReset}
                key="submitter-demo-search-form1"
                submitter={{ searchConfig: { resetText: lang.templateByUuid("UID:P_UBL-FE_18D7622804180027", "重置") } }}
                locale={window.lang.lang || "zh_cn"}
                showSelected={false}
            >
                <Col className="sync-detail-searchForm" span={8}>
                    <SearchFormItem
                        fieldid="ee416e55-3b39-454d-9ea0-8e1202dd3a67"
                        inputType="select"
                        name="searchKey"
                        options={(mapresultArr.selectData || []).map((item) => ({ label: item.key, value: item.value })) || []}
                        initialValue={mapresultArr?.selectData?.[0]?.value}
                    />
                    <SearchFormItem fieldid="26209b0e-9a27-40c5-ac37-33a022854983" inputType="input" name="searchValue" />
                </Col>
                {!PRIVATE ? renderFormItem() : <></>}
                {/* <SearchFormItem inputType="checkbox" label="翻译参照" name="istranslate" /> */}
            </SearchForm>
        </div>
    );
};

export default Search;
