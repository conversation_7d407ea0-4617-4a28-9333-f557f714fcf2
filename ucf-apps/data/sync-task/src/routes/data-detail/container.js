/**
 * 数据同步任务-公共档案关联
 * location = window.location || reactHistory.location
 * location.search.taskId -任务ID taskItem.pk_id
 * */

import React, { Component } from "react";
import IndexView from "./components/IndexView";
import { inject, observer } from "mobx-react";
import { getPageParams } from "decorator";
import Store, { storeKey } from "./store";
import { storeKey as listStoreKey } from "../list/store";
import mixCore from "core";

mixCore.addStore({
    storeKey: storeKey,
    store: new Store(),
});

@inject((rootStore) => {
    let ownerStore = rootStore[storeKey];
    let syncTaskListStore = rootStore[listStoreKey];
    return {
        ownerState: ownerStore.toJS(),
        ownerStore: ownerStore,
        syncTaskListState: syncTaskListStore.toJS(),
    };
})
@observer
@getPageParams
class Container extends Component {
    constructor(props) {
        super(props);
        let { ownerStore } = props;
        console.log("xxxxx----", this.props.getPageParams());
        ownerStore.setPageParams(this.props.getPageParams());
    }

    render() {
        return <IndexView {...this.props} />;
    }
}
export default Container;
