import { getInvokeService, defineService, getServicePath } from "utils/service";
import * as J<PERSON><PERSON><PERSON><PERSON> from "json-bigint";

/**
 * 获取主页面列信息(dataview)
 * @param taskId
 * @return {*}
 */
export const getDataColumnsService = function (taskId, header) {
    return getInvokeService({
        method: "GET",
        path: "/diwork/erpdata/task/dataview/init/" + taskId,
        header,
    });
};

/**
 * 获取主页面列表(dataview)
 * @param {Object} data
 * @param {String} data.taskId
 * @param {String|Number} data.pageNo
 * @param {String|Number} data.pageSize
 * @param {Boolean} data.istranslate -是否翻译参照
 * * 以及columns接口中返回的 queryCols 中的参数
 * @return {*}
 */
export const getDataListService = function (data, header) {
    let { taskId, ..._data } = data;
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/erpdata/task/dataview/page/" + taskId,
            transformResponse: [
                function (data) {
                    try {
                        return JSONBig.parse(data);
                    } catch (err) {
                        // 如果转换失败，则包装为统一数据格式并返回
                        return data;
                    }
                },
            ],
            header,
        },
        _data
    );
};

/**
 * 批量删除数据详情
 */
export const deleteDataService = function (data, taskId, header) {
    return getInvokeService(
        {
            method: "POST",
            path: `/gwportal/diwork/erpdata/task/dataview/delete/${taskId}`,
            header,
        },
        data
    );
};

export const saveDataService = function (data, header) {
    let { taskId, ..._data } = data;
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/erpdata/task/dataview/update/" + taskId,
            header,
        },
        _data
    );
};
//检查接口是否可运行
export const checkRunnable = function (id, header) {
    // return getInvokeService({
    //   method: 'POST',
    //   path: '/diwork/erpdata/task/checkRunnable/' + id
    // })
    let service = defineService({
        method: "GET",
        path: "/diwork/erpdata/task/checkRunnable/" + id,
        header,
    });

    return service.invoke();
};
