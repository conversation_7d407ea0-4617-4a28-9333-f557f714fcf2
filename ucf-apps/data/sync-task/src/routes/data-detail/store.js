import { observable, action, toJS, reaction, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { defaultListMap } from "utils/pageListUtils";
import { autoServiceMessage } from "utils/service";
import commonText from "constants/commonText";
import { createEnum } from "constants/utils";
import { batchSyncTaskService } from "services/taskServices";
import * as ownerService from "./service";

const initState = {
    resultArr2: [],
    pkField: "",
    columns: {},
    refCols: [],
    queryCols: [],
    dataSource: { ...defaultListMap, pageSize: 20 },
    pagination: null,
    queryParams: {
        taskId: "",
        taskName: "",
        istranslate: false,
        taskInfo: {},
    },

    editRowInfo: null,
    editRowIndex: "",
    hasEdit: false,

    matchDataList: [],
    matchedSize: 0,
    unmatchedSize: 0,
    matching: false,
    // searchParams:{},
    serviceCodeDiwork: "kfljsjtbrw",
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    setPageParams = (paramInfo) => {
        this.changeState(paramInfo);
    };

    @observable searchParams = {};
    @action
    setSearchParams = (data) => {
        this.searchParams = { ...this.searchParams, ...data };
    };
    @action
    clearSearchParams = (data) => {
        this.searchParams = {};
    };

    init = () => {
        this.state = initState;
    };

    @action
    getDataColumns = async () => {
        let res = await autoServiceMessage({
            service: ownerService.getDataColumnsService(this.state.queryParams.taskId),
        });
        if (res) {
            let { pkField, columns, refCols, queryCols } = res;
            let resultArr2 = [];
            for (let key in columns) {
                let obj = {};
                obj.name = columns[key];
                obj.code = key;
                resultArr2.push(obj);
            }
            resultArr2 = createEnum(resultArr2);

            this.changeState({
                pkField,
                columns,
                refCols,
                queryCols,
                resultArr2,
            });
        }
        return res;
    };

    getDataList = async (reqData) => {
        console.log("5555----", this.state.queryParams.taskId);
        console.log("6666----", toJS(this.state.queryParams));
        let _reqData = {
            ...reqData,
            taskId: this.state.queryParams.taskId,
            ...this.searchParams,
        };
        this.getPagesListFunc({
            service: ownerService.getDataListService,
            requestData: _reqData,
            dataKey: "dataSource",
            paginationKey: "pagination",
            onPageChange: this.getDataList,
        });
    };

    deleteData = async (bodyData) => {
        const taskId = this.state.queryParams.taskId;
        let res = await autoServiceMessage({
            service: ownerService.deleteDataService(bodyData, taskId, { serviceCode: this.state.serviceCodeDiwork }),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418041D", "删除成功！", undefined, {
                returnStr: true,
            }) /* "删除成功！" */,
        });
        if (res) {
            this.getDataList();
            return res;
        }
    };

    /**
     * 设置某一数据行编辑状态 当index=''时表示取消编辑转态
     * @param index
     */
    setEdit = (index, record) => {
        let dataSource = this.toJS(this.state.dataSource);
        let editRowInfo = null;

        // editRowInfo = dataSource.list[index];
        editRowInfo = record;

        this.changeState({
            dataSource,
            editRowInfo,
            editRowIndex: index,
            hasEdit: !this.state.hasEdit,
        });
    };

    changeEditRowInfo = (field, value) => {
        this.state.editRowInfo[field] = value;
    };

    saveEdit = async (data, cb) => {
        let { editRowInfo, editRowIndex, queryParams } = this.state;
        // let _editRowInfo = this.toJS(editRowInfo);
        let requestData = {
            taskId: queryParams.taskId,
            ...data,
        };
        let res = await autoServiceMessage({
            service: ownerService.saveDataService(requestData, { serviceCode: this.state.serviceCodeDiwork }),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418041E", "更新成功！", undefined, {
                returnStr: true,
            }) /* "更新成功！" */,
        });
        if (res) {
            let dataSource = this.toJS(this.state.dataSource);
            dataSource.list[editRowIndex] = data;
            this.changeState({ dataSource });
            // this.setEdit('');
            cb();
        }
    };
    //检查接口是否可运行
    checkRunnable = async (param) => {
        return await autoServiceMessage({
            service: ownerService.checkRunnable(param, { serviceCode: this.state.serviceCodeDiwork }),
            // success: window.lang.template(commonText.operateSuccess)
        });
    };

    executeTask = async (taskInfo, errorDataIds, isPull, startTime, endTime) => {
        let ids = [taskInfo.pk_id];
        let res = await autoServiceMessage({
            service: batchSyncTaskService(ids, errorDataIds, isPull, startTime, endTime),
        });
        if (res) {
            // this.getDataSource();
        }
        return res;
    };
}

export const storeKey = "dataSyncTaskDataDetailStore";
export default Store;
