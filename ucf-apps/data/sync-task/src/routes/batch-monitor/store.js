import { observable, action, toJS, reaction, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { defaultListMap } from "utils/pageListUtils";
import { autoServiceMessage } from "utils/service";
import * as ownerService from "./service";
const initState = {
    pagination: null,
    serviceCodeDiwork: "kfljsjtbrw",
    monitorNav: [
        {
            traceId: "65098b1586b0e12e0a89ef50",
            stage: "query.data",
            code: "nc6",
            name: "nc6",
            status: 1,
            message: "",
        },
        {
            traceId: "65098b1586b0e12e0a89ef50",
            stage: "res.gateway",
            code: "9rlI7inIQOVbF4hLx8Qb_K",
            name: "gt009",
            status: 1,
            message: "",
        },
        {
            traceId: "65098b1586b0e12e0a89ef50",
            stage: "convert.data",
            code: "convert.data",
            name: lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00069", "数据转换") /* "数据转换" */,
            status: 1,
            message: "",
        },
        {
            traceId: "65098b1586b0e12e0a89ef50",
            stage: "save.data",
            code: "BIP",
            name: "BIP",
            status: 1,
            message: "",
        },
    ],
    monitorStage: {
        pages: {
            itemCount: 20,
            pageIndex: 1,
            pageSize: 10,
            pageCount: 2,
            hasPre: false,
            hasNext: true,
            items: [
                {
                    index: 1,
                    cost: 10, //耗时
                    status: 0,
                    dataSize: 100,
                    sucCount: 60,
                    failCount: 40,
                },
                {
                    index: 2,
                    cost: 10, //耗时
                    status: 0,
                    dataSize: 100,
                    sucCount: 60,
                    failCount: 40,
                },
                {
                    index: 3,
                    cost: 10, //耗时
                    status: 0,
                    dataSize: 100,
                    sucCount: 60,
                    failCount: 40,
                },
                {
                    index: 4,
                    cost: 10, //耗时
                    status: 0,
                    dataSize: 100,
                    sucCount: 60,
                    failCount: 40,
                },
                {
                    index: 5,
                    cost: 10, //耗时
                    status: 0,
                    dataSize: 100,
                    sucCount: 60,
                    failCount: 40,
                },
                {
                    index: 6,
                    cost: 10, //耗时
                    status: 0,
                    dataSize: 100,
                    sucCount: 60,
                    failCount: 40,
                },
                {
                    index: 7,
                    cost: 10, //耗时
                    status: 0,
                    dataSize: 100,
                    sucCount: 60,
                    failCount: 40,
                },
                {
                    index: 8,
                    cost: 10, //耗时
                    status: 0,
                    dataSize: 100,
                    sucCount: 60,
                    failCount: 40,
                },
                {
                    index: 9,
                    cost: 10, //耗时
                    status: 0,
                    dataSize: 100,
                    sucCount: 60,
                    failCount: 40,
                },
                {
                    index: 10,
                    cost: 10, //耗时
                    status: 0,
                    dataSize: 100,
                    sucCount: 60,
                    failCount: 40,
                },
            ],
        },
    },
    monitorOverview: [
        {
            code: "cost",
            name: lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00065", "耗时") /* "耗时" */,
            value: 0.0,
            order: 1,
        },
        {
            code: "status",
            name: lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00061", "状态") /* "状态" */,
            value: 2.0,
            order: 2,
        },
        {
            code: "count",
            name: lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00064", "数量") /* "数量" */,
            value: 0.0,
            order: 3,
        },
        {
            code: "config",
            name: lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00068", "连接配置") /* "连接配置" */,
            value: null,
            order: 4,
        },
        {
            code: "system",
            name: lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00062", "集成系统") /* "集成系统" */,
            value: "",
            order: 5,
        },
        {
            code: "api",
            name: "API",
            value: null,
            order: 6,
        },
        {
            code: "object",
            name: lang.templateByUuid("UID:P_UBL-FE_1D96540A05E0006A", "集成对象") /* "集成对象" */,
            value: "fangan001",
            order: 7,
        },
        {
            code: "code",
            name: lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00063", "错误类型") /* "错误类型" */,
            value: "10001",
            order: 8,
        },
        {
            code: "desc",
            name: lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00066", "错误描述") /* "错误描述" */,
            value: lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00067", "集成方案中来源操作未设置") /* "集成方案中来源操作未设置" */,
            order: 9,
        },
    ],

    monitorData: [],
    dataSource: {
        ...defaultListMap,
        pageSize: 20,
        list: [
            {
                index: 1,
                cost: 10, //耗时
                status: 0,
                dataSize: 100,
                sucCount: 60,
                failCount: 40,
            },
            {
                index: 2,
                cost: 10, //耗时
                status: 0,
                dataSize: 100,
                sucCount: 60,
                failCount: 40,
            },
            {
                index: 3,
                cost: 10, //耗时
                status: 0,
                dataSize: 100,
                sucCount: 60,
                failCount: 40,
            },
            {
                index: 4,
                cost: 10, //耗时
                status: 0,
                dataSize: 100,
                sucCount: 60,
                failCount: 40,
            },
            {
                index: 5,
                cost: 10, //耗时
                status: 0,
                dataSize: 100,
                sucCount: 60,
                failCount: 40,
            },
            {
                index: 6,
                cost: 10, //耗时
                status: 0,
                dataSize: 100,
                sucCount: 60,
                failCount: 40,
            },
            {
                index: 7,
                cost: 10, //耗时
                status: 0,
                dataSize: 100,
                sucCount: 60,
                failCount: 40,
            },
            {
                index: 8,
                cost: 10, //耗时
                status: 0,
                dataSize: 100,
                sucCount: 60,
                failCount: 40,
            },
            {
                index: 9,
                cost: 10, //耗时
                status: 0,
                dataSize: 100,
                sucCount: 60,
                failCount: 40,
            },
            {
                index: 10,
                cost: 10, //耗时
                status: 0,
                dataSize: 100,
                sucCount: 60,
                failCount: 40,
            },
        ],
    },
    selectedStage: "query.data",
    selectedNav: {},
    traceId: "",
    isErrOnly: true,
    pageIndex: 1,
    radioType: "table",
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    init = () => {
        this.state = initState;
    };

    getDataSource = async (data = {}) => {
        await this.getPagesListFunc({
            service: ownerService.getBatchLogService,
            requestData: data,
            dataKey: "dataSource",
            header: { serviceCode: this.state.serviceCodeDiwork },
        });
    };
    getMonitorNav = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.getMonitorNavService(data, { serviceCode: this.state.serviceCodeDiwork, zone: "gaofengy" }),
        });
        if (res) {
            this.changeState({
                monitorNav: res.data.list,
            });
            return res.data.list;
        }
    };
    getMonitorOverview = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.getMonitorOverviewService(data, { serviceCode: this.state.serviceCodeDiwork, zone: "gaofengy" }),
        });
        if (res) {
            this.changeState({
                monitorOverview: res.data.list,
            });
        }
    };
    getMonitorStage = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.getMonitorStageService(data, { serviceCode: this.state.serviceCodeDiwork, zone: "gaofengy" }),
        });
        if (res) {
            this.changeState({
                monitorStage: res.data.pages,
            });
        }

        // let requestData = {
        //   ...this.state.searchInfo, ...data
        // }
        // console.log(requestData)

        // this.getPagesListFunc({
        //   service: ownerService.getMonitorStageService,
        //   requestData,
        //   dataKey: 'dataSource',
        //   onPageChange: this.getDataSource,
        //   header: { serviceCode: this.state.serviceCodeDiwork,zone: 'gaofengy' }
        // })
    };
    getMonitorData = async (traceId) => {
        let res = await autoServiceMessage({
            service: ownerService.getMonitorDataService({ traceId }, { serviceCode: this.state.serviceCodeDiwork, zone: "gaofengy" }),
        });
        if (res) {
            this.changeState({
                monitorData: res.data,
            });
        }
    };
    // getDataSource = async (data) => {
    //   let requestData = {
    //     ...this.state.searchInfo, ...data
    //   }
    //   console.log(requestData)

    //   this.getPagesListFunc({
    //     service: ownerService.getApplicationListAllService,
    //     requestData,
    //     dataKey: 'dataSource',
    //     onPageChange: this.getDataSource,
    //     header: { serviceCode: this.state.serviceCodeDiwork }
    //   })
    // }
}
export const storeKey = "taskMonitoringMonitorStore";
export default Store;
