import { getInvokeService, getServicePath } from "utils/service";

export const getBatchLogService = function (data, header = {}) {
    return getInvokeService(
        {
            method: "GET",
            path: "/mygwapp/erpdata/task/batchexecutelog/list",
            header,
        },
        data
    );
};

export const getMonitorNavService = function (data, header = {}) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwportal/mygwapp/monitor/business/query/link",
            header,
        },
        data
    );
};
export const getMonitorStageService = function (data, header = {}) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwportal/mygwapp/monitor/business/query/stage",
            header,
        },
        data
    );
};

export const getMonitorOverviewService = function (data, header = {}) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwportal/mygwapp/monitor/business/query/overview",
            header,
        },
        data
    );
};

export const getMonitorDataService = function (data, header = {}) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwportal/mygwapp/monitor/business/query/data",
            header,
        },
        data
    );
};
