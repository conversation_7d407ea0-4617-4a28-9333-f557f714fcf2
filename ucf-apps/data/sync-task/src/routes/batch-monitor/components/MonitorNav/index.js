/*
 * @Author: your name
 * @Date: 2021-04-15 17:40:03
 * @LastEditTime: 2021-06-09 14:42:43
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \ublinker-fe\ucf-apps\data\sync-task\src\routes\sync-log\components\LogModal\index.js
 */
import React, { useEffect, useState } from "react";
import { Switch, FormList } from "components/TinperBee";
import { getLocalImg } from "utils/index";
import "./index.less";
const FormItem = FormList.Item;
const formItemLayout = {
    labelCol: { span: 12 },
    wrapperCol: { span: 12 },
};

function MonitorNav(props) {
    const {
        selectedLog,
        onCancel,
        monitorNav,
        selectedStage,
        ownerState,
        ownerState: { traceId, isErrOnly },
        ownerStore,
    } = props;
    const [selfShow, setSelfShow] = useState(false);
    const [form] = FormList.useForm();
    useEffect(() => {}, []);
    const handleChangeNav = (item) => {
        ownerStore.changeState({
            selectedStage: item.stage,
            selectedNav: item,
            pageIndex: 1,
        });
        ownerStore.getMonitorOverview({ traceId: "16d82644edd3d6f71ae7383f0584787a" || traceId, stage: item.stage });
        ownerStore.getMonitorStage({ traceId, stage: item.stage, pageIndex: 1, pageSize: 10, isErrOnly });
    };
    return (
        <div className="monitor-nav">
            {monitorNav.map((item, index) => {
                return (
                    <div
                        fieldid="UCG-FE-routes-taskMonitoring-BatchMonitor-components-MonitorNav-index-8330691-div"
                        className=""
                        key={index}
                        onClick={handleChangeNav.bind(null, item)}
                    >
                        <div className={`box ${selectedStage == item.stage ? "cur" : ""}`}>
                            {/* <i className={`ipaas iPS-liebiao monitor-nav-icon`}></i> */}
                            <img className="monitor-nav-icon" src={getLocalImg(`monitor/${item.stage}.svg`)} />
                            <div>
                                <div className="monitor-nav-title">{item.name}</div>
                                <div className="monitor-nav-tip">{item.desc}</div>
                            </div>
                            {
                                //最后一个元素不要线
                                index != monitorNav.length - 1 && (
                                    <>
                                        <div className="right-line"></div>
                                        <div className="left-line"></div>
                                    </>
                                )
                            }
                        </div>
                    </div>
                );
            })}
        </div>
    );
}

export default MonitorNav;
