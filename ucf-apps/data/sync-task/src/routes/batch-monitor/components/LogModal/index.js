/*
 * @Author: your name
 * @Date: 2021-04-15 17:40:03
 * @LastEditTime: 2021-06-09 14:42:43
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \ublinker-fe\ucf-apps\data\sync-task\src\routes\sync-log\components\LogModal\index.js
 */
import React, { useEffect, useState } from "react";
import Modal from "components/TinperBee/Modal";
import { observer } from "mobx-react";
import { getLocalImg } from "utils/index";
import { Switch, FormList } from "components/TinperBee";
import "./index.less";
import Grid from "components/TinperBee/Grid";
const FormItem = FormList.Item;
const formItemLayout = {
    labelCol: { span: 12 },
    wrapperCol: { span: 12 },
};

function LogModal(props) {
    const { selectedLog, onCancel, show, data } = props;
    const [selfShow, setSelfShow] = useState(false);
    const [form] = FormList.useForm();
    useEffect(() => {}, []);
    const gridColumns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00077", "页码") /* "页码" */,
            dataIndex: "taskname",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00071", "状态") /* "状态" */,
            dataIndex: "viewnum",
            width: 100,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00073", "错误类型") /* "错误类型" */,
            dataIndex: "triggertype",
            width: 100,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00074", "成功处理数据") /* "成功处理数据" */,
            dataIndex: "starttime",
            width: 150,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00070", "错误信息") /* "错误信息" */,
            dataIndex: "endtime",
            width: 150,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00072", "操作") /* "操作" */,
            dataIndex: "action",
            width: 170,
            fixed: "right",
        },
    ];
    return (
        <Modal
            fieldid="ublinker-routes-batch-detail-components-LogModal-index-8913359-Modal"
            show={show}
            cancelText={lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00076", "关闭") /* "关闭" */}
            title={lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00075", "详情日志") /* "详情日志" */}
            okHide
            onCancel={onCancel}
            className="logModal"
        >
            <Grid fieldid="ublinker-routes-batch-detail-components-IndexView-index-41505017-Grid" columns={gridColumns} data={data} />
        </Modal>
    );
}

export default observer(LogModal);
