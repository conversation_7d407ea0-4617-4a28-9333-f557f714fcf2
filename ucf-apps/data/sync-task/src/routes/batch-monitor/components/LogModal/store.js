import { observable, makeObservable } from "mobx";
import DefaultS<PERSON> from "utils/defaultStore";
import { autoServiceMessage } from "utils/service";
import * as ownerService from "./service";

const initState = {
    pages: [],

    pageIndex: 1,

    logId: "",

    onlyFail: false,

    // 当前分页日志未回写
    logHasNotWrite: false,
    dataSource: {
        dataenable: 0,
        dataversion: 0,
        message: "",
        pageNo: 1,
    },
    serviceCodeDiwork: "kfljsjtbrw",
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    init = () => {
        this.state = initState;
    };

    setLogId = (logId) => {
        this.state.logId = logId;
        this.getPages();
    };

    changeOnlyFailAction = (onlyFail) => {
        this.state.onlyFail = onlyFail;
        this.state.pageIndex = 1;
        this.state.pages = [];
        this.getPages();
    };

    getPages = async () => {
        const { onlyFail, logId } = this.state;
        const pageRes = await autoServiceMessage({
            service: ownerService.getLogPagesService(
                {
                    logId,
                    success: !onlyFail,
                    page: 1,
                },
                { serviceCode: this.state.serviceCodeDiwork }
            ),
        });
        if (pageRes) {
            const pages = pageRes.data || [];
            this.state.pages = pages;
            this.getMessage();
        }
    };

    setPageIndex = (pageIndex) => {
        this.state.pageIndex = pageIndex;
        this.getMessage();
    };

    // 获取日志信息
    getMessage = async () => {
        const { pageIndex, pages, logId } = this.state;
        const pageInfo = pages[pageIndex - 1];
        if (pageInfo) {
            let res = await autoServiceMessage({
                service: ownerService.getLogInfoService(
                    {
                        logId,
                        page: pageInfo.page,
                    },
                    { serviceCode: this.state.serviceCodeDiwork }
                ),
            });
            if (res) {
                if (res.data) {
                    this.state.dataSource = res.data;
                }
                this.state.logHasNotWrite = !res.data;
            }
        }
    };
}
/**
 * 根据具体模块修改 storeKey 为保证唯一性
 * storeKey 由 模块文件夹名称 + 页面名称 + 'Store' 小驼峰
 *
 * storeKey是作为当前 Store 的唯一键， storeKey与Store的实例会在 rootStore或者StoreMap中以 键值对的 形式存在
 * 1. 用于在模块的 storeMap（模块routes/store.js）中注册并初始化当前Store
 * 2. 用于在container的 inject 中关联当前Store的实例时，从模块的storeMap或者rootStore中获取对应store实例
 * */
export const storeKey = "dataSyncTaskLogListStore";

export default Store;
