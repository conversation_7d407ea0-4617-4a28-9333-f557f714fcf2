import { getInvokeService } from "utils/service";

/**
 * 获取日志分页列表
 * @param {Object} data
 * @param {String} data.logId
 * @param {String|Number} data.page
 * @param {String|Boolean} data.success
 * @return {*}
 */
export const getLogPagesService = function (data, header = {}) {
    const { logId, ..._data } = data;
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/erpdata/task/getEnablePageLog/" + logId,
            header,
        },
        _data
    );
};

/**
 * 获取任务详细日志
 * @param {Object} data
 * @param {String} data.logId
 * @param {String|Number} data.page -当前分页
 * @return {Promise<unknown>}
 */
export const getLogInfoService = function (data, header = {}) {
    let { logId, ..._data } = data;
    return getInvokeService(
        {
            method: "GET",
            path: "/gwmanage/gwportal/diwork/erpdata/task/getPageLog/" + logId,
            header,
        },
        _data
    );
};
