/*
 * @Author: your name
 * @Date: 2021-04-15 17:40:03
 * @LastEditTime: 2021-06-09 14:42:43
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \ublinker-fe\ucf-apps\data\sync-task\src\routes\sync-log\components\LogModal\index.js
 */
import React, { useEffect, useState } from "react";
import { Button, FormControl, FormList, Row, Col, Checkbox, Collapse, Pagination, InputGroup, Dropdown, Menu, Radio } from "components/TinperBee";
import Grid from "components/TinperBee/Grid";
import { GridActions, GridAction } from "components/TinperBee/Grid/GridActions";
import LogModal from "../LogModal";
const { Panel } = Collapse;
let MenuItem = Menu.Item;
import "./index.less";
const FormItem = FormList.Item;
const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 12 },
};

function MonitorData(props) {
    const {
        selectedLog,
        onCancel,
        selectedStage,
        ownerState,
        ownerState: { radioType, traceId, dataSource, selectedNav, monitorData, pageIndex, isErrOnly },
        ownerStore,
        monitorOverview,
    } = props;
    const [logModalShow, setLogModalShow] = useState(false);
    const [form1] = FormList.useForm();
    useEffect(() => {}, []);
    const menu1 = () => (
        // onSelect={() => {}}
        <Menu fieldid="1d6a0549-cae6-4af4-8fef-3f87f4b576d7" multiple>
            <MenuItem key="1">{lang.templateByUuid("UID:P_UBL-FE_1D96540A05E0004B", "借款合同") /* "借款合同" */}</MenuItem>
            <MenuItem key="2">{lang.templateByUuid("UID:P_UBL-FE_1D96540A05E0004D", "抵/质押合同") /* "抵/质押合同" */}</MenuItem>
            <MenuItem key="3">{lang.templateByUuid("UID:P_UBL-FE_1D96540A05E0004E", "担保合同") /* "担保合同" */}</MenuItem>
            <MenuItem key="4">{lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00052", "联保合同") /* "联保合同" */}</MenuItem>
            <MenuItem key="5">{lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00053", "合同审批") /* "合同审批" */}</MenuItem>
            <MenuItem key="6">{lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00055", "抵/质押合同跟踪") /* "抵/质押合同跟踪" */}</MenuItem>
        </Menu>
    );
    const onCheckBoxChange = (e) => {
        ownerStore.changeState({
            isErrOnly: e.target.checked,
            pageIndex: 1,
        });
        ownerStore.getMonitorStage({ traceId, stage: selectedStage, pageIndex: 1, pageSize: 10, isErrOnly: e.target.checked });
    };
    const handleTreeChange = (item) => {
        ownerStore.getMonitorData({
            traceId: "16d82644edd3d6f71ae7383f0584787a" || traceId,
            stage: selectedStage,
            pageIndex: 1,
            pageSize: 10,
            isErrOnly,
            index: item.index,
        });
    };
    const handlePagNationSelect = (e) => {
        ownerStore.changeState({ pageIndex: e });
        ownerStore.getMonitorStage({ traceId: "16d82644edd3d6f71ae7383f0584787a" || traceId, stage: selectedStage, pageIndex: e, pageSize: 10, isErrOnly });
    };
    const handleRadioChange = (value) => {
        ownerStore.changeState({ radioType: value });
    };
    const renderTriggerType = (value) => {
        let type = "";
        switch (value) {
            case "0":
                type = lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00054", "定时触发") /* "定时触发" */;
                break;
            case "1":
                type = lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00057", "手动触发") /* "手动触发" */;
                break;
        }
        return type;
    };
    const handleShowModal = () => {
        setLogModalShow(false);
    };
    const renderActions = (value, record) => {
        return (
            <GridActions>
                <GridAction fieldid="UCG-FE-routes-batch-log-components-IndexView-index-7054793-GridAction" onClick={handleShowModal}>
                    {lang.templateByUuid("UID:P_UBL-FE_1D96540A05E0005E", "查看详情") /* "查看详情" */}
                </GridAction>
            </GridActions>
        );
    };
    const gridColumns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180394", "序号") /* "序号" */,
            dataIndex: "$$index",
            width: 100,
            render: (value, record, index) => {
                return (dataSource.pageIndex - 1) * 10 + 1 + index;
            },
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_1D96540A05E0004F", "页码") /* "页码" */,
            dataIndex: "taskname",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00056", "状态") /* "状态" */,
            dataIndex: "viewnum",
            width: 100,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_1D96540A05E0005A", "错误类型") /* "错误类型" */,
            dataIndex: "triggertype",
            render: renderTriggerType,
            width: 100,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_1D96540A05E0005D", "成功处理数据") /* "成功处理数据" */,
            dataIndex: "starttime",
            render: Grid.renderTime,
            width: 150,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00060", "错误信息") /* "错误信息" */,
            dataIndex: "endtime",
            render: Grid.renderTime,
            width: 150,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_1D96540A05E0004A", "操作") /* "操作" */,
            dataIndex: "action",
            width: 170,
            fixed: "right",
            render: renderActions,
        },
    ];
    return (
        <div className="monitor-block">
            <Collapse fieldid="9825afa1-3189-46d8-a219-b11d11ada8a4" defaultActiveKey={"1"} ghost={false}>
                <Panel header={lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00058", "数据列表") /* "数据列表" */} key="2" type="card" defaultExpanded>
                    {/* <div style={{widhth:'100%',borderBottom:"1px solid black"}}></div> */}
                    <div className="monitor-block-data">
                        <div className="monitor-block-data-left">
                            <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                                <span>{lang.templateByUuid("UID:P_UBL-FE_1D96540A05E0005B", "全部") /* "全部" */}</span>
                                <Checkbox
                                    fieldid="71d63837-22a3-40d6-a2d1-57fc34950a4d"
                                    style={{ marginLeft: "20px" }}
                                    title="monitor-block-data-left-checkBox"
                                    className="test"
                                    checked={isErrOnly}
                                    onClick={(e) => onCheckBoxChange(e)}
                                >
                                    {lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00059", "仅错误") /* "仅错误" */}
                                </Checkbox>
                            </div>
                            <ul>
                                {dataSource.list &&
                                    dataSource.list.map((item, i) => {
                                        return (
                                            <li key={i} title="index" className="data-left-item" onClick={handleTreeChange.bind(null, item)}>
                                                {lang.templateByUuid("UID:P_UBL-FE_1D96540A05E0005F", "批次") /* "批次" */}
                                                {item.index}
                                            </li>
                                        );
                                    })}
                            </ul>
                            <Pagination
                                fieldid="c1ceee30-cf24-4afb-b2a9-15bb9c169892"
                                title="monitor-block-data-left-page"
                                simple
                                current={pageIndex}
                                onChange={handlePagNationSelect}
                                total={10000}
                                pageSize={10}
                            />
                        </div>
                        <div className="monitor-block-data-right">
                            <div className="data-right-header">
                                <div className="data-right-header-left">
                                    <div style={{ fontWeight: "bold" }}>{lang.templateByUuid("UID:P_UBL-FE_1D96540A05E0004C", "第一页") /* "第一页" */}</div>
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                    <div>
                                        {lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00051", "转换数据(条) 成功") /* "转换数据(条) 成功" */}
                                        <span style={{ fontWeight: "bold" }}> 100 </span>{" "}
                                        {lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00050", "| 失败") /* "| 失败" */}
                                        <span style={{ fontWeight: "bold" }}> 100 </span>{" "}
                                    </div>
                                </div>
                                <div className="data-right-header-right">
                                    <Checkbox fieldid="6e0dcd3e-d8aa-4e6c-9e04-7c5a100035d0" style={{ marginRight: "20px" }} title="1" className="test">
                                        {lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00059", "仅错误") /* "仅错误" */}
                                    </Checkbox>
                                    <InputGroup fieldid="0d652c60-7414-4872-92a8-5b4df144fbf1" style={{}}>
                                        <InputGroup.Button shape="border">
                                            <Dropdown fieldid="00fbe5d6-e2ea-4ce1-962f-80da5bde7b18" trigger={["click"]} overlay={menu1} animation="slide-up">
                                                <Button fieldid="52599d98-0145-47ae-bd12-51e25a17c20f" shape="border">
                                                    {lang.templateByUuid("UID:P_UBL-FE_1D96540A05E0005C", "带有分割线的下拉") /* "带有分割线的下拉" */}{" "}
                                                    <i fieldid="03cc57e2-6450-4ea9-8815-5c458a345584" className="uf uf-arrow-down">
                                                        {" "}
                                                    </i>
                                                </Button>
                                            </Dropdown>
                                        </InputGroup.Button>
                                        <FormControl type="" />
                                        <InputGroup.Button>
                                            <Button fieldid="338fe288-6f57-4df9-93f3-4c933cdd085f" icon="uf-search"></Button>
                                        </InputGroup.Button>
                                    </InputGroup>
                                    <Radio.Group
                                        title="data-right-header-right-radio"
                                        name="fruit"
                                        style={{ marginLeft: "10px" }}
                                        defaultValue="table"
                                        selectedValue={radioType}
                                        onChange={handleRadioChange}
                                    >
                                        <Radio.Button style={{ height: "28px" }} value="table">
                                            {lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00049", "列表") /* "列表" */}
                                        </Radio.Button>
                                        <Radio.Button style={{ height: "28px" }} value="JSON">
                                            JSON
                                        </Radio.Button>
                                    </Radio.Group>
                                </div>
                            </div>
                            <Grid
                                fieldid="ublinker-routes-batch-detail-components-IndexView-index-4150507-Grid"
                                // header={
                                // 	<div className="ucg-align-r ucg-pad-r-20">
                                // 		<Button fieldid="ublinker-routes-batch-detail-components-IndexView-index-2836126-Button" bordered onClick={this.HandleRefresh}>{lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802F9", "刷新")}</Button>
                                // 	</div>
                                // }
                                // rowKey="id"
                                columns={gridColumns}
                                data={monitorData}
                            />
                        </div>
                    </div>
                </Panel>
            </Collapse>
            <LogModal
                show={logModalShow}
                data={monitorData}
                // dataSource={logDetail}
                onCancel={() => {
                    setLogModalShow(false);
                }}
            />
        </div>
    );
}

export default MonitorData;
