/*
 * @Author: your name
 * @Date: 2021-04-15 17:40:03
 * @LastEditTime: 2021-06-09 14:42:43
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \ublinker-fe\ucf-apps\data\sync-task\src\routes\sync-log\components\LogModal\index.js
 */
import React, { useEffect, useState } from "react";
import { Button, FormControl, FormList, Row, Col, Checkbox, Collapse, Pagination, InputGroup, Dropdown, Menu, Radio } from "components/TinperBee";
import { getLocalImg } from "utils/index";
const { Panel } = Collapse;
import "./index.less";
const FormItem = FormList.Item;
const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 12 },
};

function MonitorAbstract(props) {
    const {
        selectedLog,
        onCancel,
        selectedStage,
        ownerState,
        ownerState: { selectedNav, monitorNav },
        ownerStore,
        monitorOverview,
    } = props;
    const [selfShow, setSelfShow] = useState(false);
    const [form1] = FormList.useForm();
    useEffect(() => {}, []);
    const getNameByValue = (item) => {
        if (item.code == "status") {
            return item.value == 1
                ? lang.templateByUuid("UID:P_UBL-FE_1D96540A05E0006E", "执行成功") /* "执行成功" */
                : item.value == 2
                  ? lang.templateByUuid("UID:P_UBL-FE_1D96540A05E0006D", "部分成功") /* "部分成功" */
                  : lang.templateByUuid("UID:P_UBL-FE_1D96540A05E0006F", "执行失败") /* "执行失败" */;
        }
        return item.value;
    };
    const renderBlockHeader = (monitorOverview, status = 1) => {
        let formStatus = monitorOverview.find((item) => item.code == "status") && monitorOverview.find((item) => item.code == "status").value;
        let node = (
            <div className="block-header-title">
                {selectedNav.desc || (monitorNav[0] && monitorNav[0].desc)}
                <span className={`block-header-title-staus ${formStatus == 1 ? "green" : formStatus == 2 ? "orange" : "red"}`}>
                    <i fieldid="e0c7026f-7ded-41a6-bcee-390f7f8fc84d" className={`ipaas iPS-yiwancheng`}></i>&nbsp;
                    <span className={`status-success`}>
                        {
                            formStatus == 1
                                ? lang.templateByUuid("UID:P_UBL-FE_1D96540A05E0006E", "执行成功") /* "执行成功" */
                                : formStatus == 2
                                  ? lang.templateByUuid("UID:P_UBL-FE_1D96540A05E0006D", "部分成功") /* "部分成功" */
                                  : lang.templateByUuid("UID:P_UBL-FE_1D96540A05E0006F", "执行失败") /* "执行失败" */
                        }
                    </span>
                </span>
            </div>
        );
        return node;
    };
    return (
        <div className="monitor-block">
            <Collapse fieldid="32ede56c-0163-4231-9056-8ff8015ebe90" defaultActiveKey={"1"} ghost={false}>
                <Panel header={renderBlockHeader(monitorOverview)} key="1" type="card" defaultExpanded>
                    <div style={{ padding: "7px 0" }}>
                        <FormList
                            fieldid="ublinker-routes-BasicInfo-components-IndexView-index-791995-FormList"
                            className="config-action-form"
                            ref={form1}
                            labelAlign={"right"}
                            style={{ display: "flex", alignItems: "center", flexWrap: "wrap", textAlign: "" }}
                            {...formItemLayout}
                        >
                            {monitorOverview.map((item, index) => {
                                return (
                                    <FormItem style={{ width: "25%" }} label={item.name} name={item.code}>
                                        <FormControl defaultValue={getNameByValue(item)} readOnly />
                                    </FormItem>
                                );
                            })}
                        </FormList>
                    </div>
                </Panel>
            </Collapse>
        </div>
    );
}

export default MonitorAbstract;
