import React, { Component, Fragment } from "react";
import { Header, Content, Footer } from "components/PageView";
import { navTo, initStore } from "decorator";
import { FormList, Collapse } from "components/TinperBee";
import "./index.less";
import { getPageParams } from "decorator/index";

import MonitorNav from "../MonitorNav";
import MonitorAbstract from "../MonitorAbstract";
import MonitorData from "../MonitorData";
const { Panel } = Collapse;
const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 12 },
};

@getPageParams
@initStore()
class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            editColumn: null,
            selectedStage: "query.data",
            isErrOnly: true,
            pageIndex: 1,
        };
        this.form1 = React.createRef();
    }

    async componentDidMount() {
        let pageParam = this.props.getPageParams(this.props);
        this.props.ownerStore.changeState({
            traceId: pageParam.queryParams.traceId,
        });
        let monitorNav = await this.props.ownerStore.getMonitorNav({ traceId: "16d82644edd3d6f71ae7383f0584787a" || pageParam.queryParams.traceId });
        this.props.ownerStore.getMonitorOverview({ traceId: "16d82644edd3d6f71ae7383f0584787a" || pageParam.queryParams.traceId, stage: monitorNav[0].stage });
        this.props.ownerStore.getMonitorStage({
            traceId: "16d82644edd3d6f71ae7383f0584787a" || pageParam.queryParams.traceId,
            stage: monitorNav[0].stage,
            pageIndex: 1,
            pageSize: 10,
            isErrOnly: this.state.isErrOnly,
        });
        this.props.ownerStore.getMonitorData({
            traceId: "16d82644edd3d6f71ae7383f0584787a" || pageParam.queryParams.traceId,
            stage: monitorNav[0].stage,
            pageIndex: 1,
            pageSize: 10,
            isErrOnly: this.state.isErrOnly,
            index: 1,
        });
    }

    // renderBatchDetail = (detail, record) => {
    // 	const { successful = 0, running = 0, failed = 0, skipped = 0, unexecuted = 0 } = detail || {};
    // 	return (
    // 		<Fragment>
    // 			<span className="task-status-success mix-transparent ucg-mar-r-10">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418038D", "成功：") /* "成功：" */}{successful}</span>
    // 			<span className="task-status-doing mix-transparent ucg-mar-r-10">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418038F", "运行中：") /* "运行中：" */}{running}</span>
    // 			<span className="task-status-fail mix-transparent ucg-mar-r-10">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180390", "失败：") /* "失败：" */}{failed}</span>
    // 			<span className="task-status-jump mix-transparent ucg-mar-r-10">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180392", "跳过：") /* "跳过：" */}{skipped}</span>
    // 			<span className="task-status-not mix-transparent">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180393", "未运行：") /* "未运行：" */}{unexecuted}</span>
    // 		</Fragment>
    // 	)
    // }

    options = {
        language: "json",
        minimap: { enabled: false },
        selectOnLineNumbers: true,
        automaticLayout: true,
        lineNumbersMinChars: 2,
        lineDecorationsWidth: 10,
        renderLineHighlight: "none",
        folding: false, // 是否允许折叠
        contextmenu: false, // 禁止右键的上下文菜单
        hideCursorInOverviewRuler: true, // 隐藏在滚动条中显示的光标
        overviewRulerLanes: 0,
        scrollbar: {
            vertical: "hidden",
            horizontal: "hidden",
        },
        renderIndentGuides: false,
        contextMenu: false,
        readOnly: true,
    };

    render() {
        let { ownerState, ownerStore } = this.props;
        let { monitorNav, monitorStage, monitorData, monitorOverview, selectedStage, isErrOnly } = ownerState;
        return (
            <div fieldid="UCG-FE-routes-taskMonitoring-BatchMonitor-components-IndexView-index-833069-div" className="batch-monitor">
                <Fragment>
                    <Header
                        back
                        fixed
                        title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802F4", "详情", undefined, {
                            returnStr: true,
                        })}
                        bordered
                    />
                    <Content style={{ padding: "10px", background: "#EBEFFB" }}>
                        <div className="monitor-cotainer">
                            <MonitorNav monitorNav={monitorNav} selectedStage={selectedStage} ownerState={ownerState} ownerStore={ownerStore} />
                        </div>
                        <div className="monitor-bottom">
                            <MonitorAbstract monitorOverview={monitorOverview} ownerState={ownerState} ownerStore={ownerStore} />
                            <MonitorData monitorOverview={monitorOverview} ownerState={ownerState} ownerStore={ownerStore} />
                            <div className="monitor-block">
                                <Collapse fieldid="dc2ce95e-d06b-4ca8-b586-e21645d88ec9" defaultActiveKey={"1"} ghost={false}>
                                    <Panel
                                        header={lang.templateByUuid("UID:P_UBL-FE_1D96540A05E00078", "请求参数") /* "请求参数" */}
                                        key="1"
                                        type="card"
                                        defaultExpanded
                                    >
                                        <div className={"preview-data"}>
                                            <div className={"source-data-con"}></div>
                                        </div>
                                    </Panel>
                                </Collapse>
                            </div>
                        </div>
                    </Content>
                </Fragment>
            </div>
        );
    }
}

export default IndexView;
