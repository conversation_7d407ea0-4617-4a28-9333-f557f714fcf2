.batch-monitor{
    .monitor-cotainer {
        display: flex;
        height: 120px;
        align-items: center;
        background: #EBEFFB;
    }
    
    .box {
        width: 350px;
    height: 103px;
    background: #D5DBED;
    border-radius: 3px;
    border: 1px solid rgba(255,255,255,0.46);
    
        padding-left: 50px;
        display: flex;
        align-items: center;
        // flex-direction: column;
        // justify-content: space-between;
        z-index: 99;
        position: relative;
        margin-right: 60px;
        box-sizing: border-box;
        position: relative;
    }
    .box.cur {
        background:#FFFFFF;
        position: relative;
    }
    .cur::before {
        position: absolute;
        content: '';
        width: 0;
        height: 0;
        border-top: 10px solid #fff;
        border-right: 10px solid transparent;
        border-left: 10px solid transparent;
        bottom:-10px;
        left:50%;
    }
    .cur::after {
        position: absolute;
        content: '';
        width: 100%;
        height: 4px;
        top:0;
        left:0;
    background: #EE2223;
    }
    
    .monitor-nav {
        display: flex;
        align-items: center;
        margin: 0 auto;
    }
    
    .box-errorTableName-title {
        text-align: center;
    }
    
    .monitor-nav-icon {
        width: 48px;
        height: 48px;
        margin-right:14px;
    }
    
    .monitor-nav-title {
        font-size: 14px;
        font-family: Alibaba-PuHuiTi-M, Alibaba-PuHuiTi;
        font-weight: bold;
        color: #090C3E;
        line-height: 20px;
        margin-bottom:9px;
    }
    
    .monitor-nav-tip {
        font-size: 12px;
        font-family: Alibaba-PuHuiTi-R, Alibaba-PuHuiTi;
        font-weight: normal;
        color: #090C3E;
    //     background: #C8C5EE;
    // border-radius: 3px;
    // opacity: 0.5;
    // padding:0px 6px;
    }
    
    .right-line {
        position: absolute;
        content: ' ';
        width: 140px;
        height: 1px;
        background: #505766;
        right: -100px;
        top: 50%;
    
    }
    
    .right-line {
        position: absolute;
        content: ' ';
        width: 60px;
        height: 1px;
        // background: #505766;
        border-bottom: 1px solid #505766;
        right: -60px;
        top: 36px;
    
    }
    
    .right-line:before {
        position: absolute;
        content: ' ';
        width: 8px;
        height: 8px;
        top: -4px;
        right: 0px;
        border-right: 2px solid #505766;
        border-bottom: 2px solid #505766;
        transform: rotate(-45deg);
    
    }
    
    .left-line {
        position: absolute;
        content: ' ';
        width: 57px;
        height: 1px;
        // background: #505766;
        right: -60px;
        top: 54px;
        border-bottom: 1px dashed #505766;
    
    }
    
    .left-line:before {
        position: absolute;
        content: ' ';
        width: 8px;
        height: 8px;
        top: -3px;
        right: 49px;
        border-right: 2px solid #505766;
        border-bottom: 2px solid #505766;
        transform: rotate(135deg);
    }
    
    .monitor-bottom {
        padding: 10px;
        background: #EBEFFB;
        padding-top:5px;
    }
    
    .monitor-bottom-status {
        width: 100%;
        background: #3A3C64;
        border-radius: 3px;
        padding: 7px 17px;
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }
    
    .monitor-bottom-status .status-success {
        font-size: 14px;
        font-family: Alibaba-PuHuiTi-M, Alibaba-PuHuiTi;
        font-weight: bold;
        color: #1AC88E;
        margin-right: 10px;
    }
    
    .monitor-bottom-status-total {
        background: rgba(216, 216, 216, 0.21);
        border-radius: 11px;
        padding: 2px 10px;
        font-size: 12px;
        font-family: Alibaba-PuHuiTi-R, Alibaba-PuHuiTi;
        font-weight: normal;
        color: #FFFFFF;
    }
    
    .monitor-block .wui-collapse-type-card{
        border-bottom: 1px solid #DBE0E5!important;
        height:46px!important;;
        line-height:46px!important;
        display: flex!important;
        align-items: center!important;
    
    }
    .monitor-block .wui-collapse-type-body-card{
        padding:0;
    }
    .block-header-title-staus{
        font-size: 14px;
    font-family: Alibaba-PuHuiTi-M, Alibaba-PuHuiTi;
    font-weight: normal;
    margin-left:10px;
    }
    .block-header-title-staus.green{
           color: #1AC88E;
    
    }
    .block-header-title-staus.orange{
        color: #FF8C39;
    
    }
    .block-header-title-staus.red{
        color: #FF3D3D;
    
    }
    .monitor-block {
        background: #FFFFFF;
        border-radius: 3px;
        // padding: 10px 0px;
        box-sizing: border-box;
        width: 100%;
        margin-bottom: 10px;
    }
    
    .monitor-block-data {
        display: flex;
        // align-items: center;
        width: 100%;
        // border-top:1px solid #DBE0E5;
        // padding-top:10px;
    }
    
    .monitor-block-data-left {
        flex: 0 0 200px;
        border-right: 1px solid #DBE0E5;
        padding: 10px 15px;
        box-sizing: border-box;
    }
    
    .data-left-item {
        padding: 5px 9px;
        background: #fff;
    }
    
    .data-left-item:hover {
    
        background: #E9F0FA;
    }
    
    .monitor-block-data-right {
        width:100%;
        // display: flex;
        // align-items: center;
    }
    
    .data-right-header {
        padding: 6px 10px;
        display: flex;
        justify-content: space-between;
        width:100%;
        // margin-bottom:10px;
    
    }
    
    .data-right-header-left {
        font-size: 14px;
        font-family: Alibaba-PuHuiTi-R, Alibaba-PuHuiTi;
        font-weight: normal;
        color: #333333;
        display: flex;
        align-items: center;
    }
    .data-right-header-right{
        font-size: 14px;
        font-family: Alibaba-PuHuiTi-R, Alibaba-PuHuiTi;
        font-weight: normal;
        color: #333333;
        display: flex;
        align-items: center;
    }
    // table {
    //     border-collapse: collapse;
    //     }
    //     th, td {
    //     padding: 10px;
    //     border: 1px solid black;
    //     }
    // .box:after {
    //     position: absolute;
    //     content: ' ';
    //     width: 140px;
    //     height: 1px;
    //     background: #505766;
    //     right: -100px;
    //     top: 50%;
    // }
    
    // .box:before {
    //     position: absolute;
    //     content: ' ';
    //     width: 1px;
    //     height: 1px;
    //     top: 50%;
    //     margin-top: -6px;
    //     right: -100px;
    //     border-left: 6px solid #505766;
    //     border-top: 6px solid transparent;
    //     border-bottom: 6px solid transparent;
    
    // }
    
    .monitor-block .wui-form-item{
        margin-bottom:7px;
    }
    .wui-form-item-label {
        // border:1px solid black;
        // text-align: right;
    }
    
    .wui-form-item-control {
        // border:1px solid black;
    }
    
    .wui-input {
        // text-align: center;
    }
    
    .monitor-title {
        margin-left: 20px;
    }
    .preview-data {
        display: flex;
        justify-content: space-between;
        height: 300px;
        margin: 0 16px;
        .source-data-con {
            flex: 1;
            overflow: hidden;
            border: 1px solid #d3d3d3;
            .source-text {
                min-height: 230px;
                width: 450px !important;
                height: 230px !important;
            }
            :global {
                .margin-view-overlays {
                    border-right: 1px solid #d3d3d3;
                }
                /* padding between the gutter and code */
                .monaco-editor .view-line {
                    padding-left: 5px;
                }
    
                /* fix cursor location */
                .monaco-editor .cursor {
                    margin-left: 5px;
                }
    
                /* fix highlighted text selection */
                .monaco-editor .selectionHighlight {
                    margin-left: 5px;
                }
    
                /* fix highlighted brackets */
                .monaco-editor .bracket-match {
                    margin-left: 5px;
                }
    
                /* fix multiline text selection */
                .monaco-editor .cslr.selected-text {
                    margin-left: 5px;
                }
    
                /* fix multiline text selection */
                .monaco-editor .bottom-left-radius,
                .monaco-editor .top-left-radius {
                    margin-left: 5px;
                }
            }
        }
    }
}
