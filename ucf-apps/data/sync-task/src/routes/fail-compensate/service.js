import { getInvokeService } from "utils/service";

/**
 * 查询NC用户关联关系
 * @param paramType  user_id 用户ID email 邮箱 mobile 手机号
 * @param param
 * @return {*}
 */
export const queryRelationshipsService = function (data, header) {
    return getInvokeService(
        {
            method: "GET",
            path: "/gwmanage/gwportal/diwork/erp/compensate/queryRelationships",
            header,
        },
        data
    );
};
