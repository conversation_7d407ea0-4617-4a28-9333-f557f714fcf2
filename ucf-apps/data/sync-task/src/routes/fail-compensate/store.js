import { observable, action, toJS, reaction, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { autoServiceMessage } from "utils/service";
import * as ownerService from "./service";

const initState = {
    dataSource: [],
    paramType: "userId",
    param: "",
    serviceCodeDiwork: "kfljsjtbrw",
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    init = () => {
        this.state = initState;
    };

    handleChange = (type, value) => {
        this.changeState({
            param: "",
            [type]: value,
        });
    };

    getDataSource = async () => {
        let { param, paramType } = this.state;
        let res = await autoServiceMessage({
            service: ownerService.queryRelationshipsService(
                {
                    paramType,
                    param,
                },
                { serviceCode: this.state.serviceCodeDiwork }
            ),
        });
        if (res) {
            this.changeState({
                dataSource: res.data,
            });
        } else {
            this.changeState({
                dataSource: [],
            });
        }
    };
}

export const storeKey = "dataSyncTaskFailCompensateStore";
export default Store;
