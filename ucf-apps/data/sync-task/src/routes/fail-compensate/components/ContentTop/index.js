import React from "react";
import SearchPanel from "components/SearchPanel";
import "./index.less";

const ContentTop = () => {
    return (
        <SearchPanel
            hideSearch
            advanced={
                <div className="fail-compensate-content-top">
                    <p>{lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180064", "操作说明：") /* "操作说明：" */}</p>
                    <p className="indent">
                        {
                            lang.templateByUuid(
                                "UID:P_UBL-FE_18D8CEF604180065",
                                "1、输入用户信息查询用户关联关系详情数据"//@notranslate
                            ) /* "1、输入用户信息查询用户关联关系详情数据" */
                        }
                    </p>
                    <p className="indent">
                        {
                            lang.templateByUuid(
                                "UID:P_UBL-FE_18D8CEF604180066",
                                "2、若关联关系数据若为空，则单独同步一下员工的数据"//@notranslate
                            ) /* "2、若关联关系数据若为空，则单独同步一下员工的数据" */
                        }
                    </p>
                    <p className="indent">
                        {
                            lang.templateByUuid(
                                "UID:P_UBL-FE_18D8CEF604180067",
                                "3、若关联关系为一条，同样单独同步一下对应员工的数据，再确定一下关联关系"//@notranslate
                            ) /* "3、若关联关系为一条，同样单独同步一下对应员工的数据，再确定一下关联关系" */
                        }
                    </p>
                    <p className="indent">
                        {
                            lang.templateByUuid(
                                "UID:P_UBL-FE_18D8CEF604180068",
                                "4、若干关联关系为多条，则确认移动审批所要使用的关联关系数据（即友户通和ERP用户主键对应关系）是否为最新的数据，若不为则单独同步一下对应员工的数据"//@notranslate
                            ) /* "4、若干关联关系为多条，则确认移动审批所要使用的关联关系数据（即友户通和ERP用户主键对应关系）是否为最新的数据，若不为则单独同步一下对应员工的数据" */
                        }
                    </p>
                    <p className="indent">
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180062", "注：查询语句如下（NC/NCC/U8C）") /* "注：查询语句如下（NC/NCC/U8C）" */}
                    </p>
                    {/* <p className="indent">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180063","按照用户名查询：select cuserid as id, user_code, user_name from sm_user where user_name = '用户名'") /* "按照用户名查询：select cuserid as id, user_code, user_name from sm_user where user_name = '用户名'" *}</p> */}
                </div>
            }
        />
    );
};

export default ContentTop;
