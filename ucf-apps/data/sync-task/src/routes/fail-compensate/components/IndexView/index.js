import React, { Component, Fragment } from "react";
import { Header, Content } from "components/PageView";
import { Select } from "components/TinperBee";
import ContentTop from "../ContentTop";
import SearchInput from "components/TinperBee/SearchInput";
import Grid from "components/TinperBee/Grid";
import yht from "static/images/data/yht.svg";
import { emailReg, phoneReg } from "utils/regExp";
import { Error } from "utils/feedback";
import commonText from "constants/commonText";

import "./index.less";

const Option = Select.Option;

class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            activeKey: 1, //补偿类型
        };

        this.column = [
            {
                title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418042F", "序号") /* "序号" */,
                dataIndex: "$$index",
                render: (text, record, index) => {
                    return index + 1;
                },
                width: 100,
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180433", "编码") /* "编码" */,
                dataIndex: "userCode",
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180420", "姓名") /* "姓名" */,
                dataIndex: "userName",
                width: 100,
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180423", "友户通ID") /* "友户通ID" */,
                dataIndex: "yhtUserId",
                width: 300,
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180425", "ERP主键") /* "ERP主键" */,
                dataIndex: "userId",
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180428", "手机号码") /* "手机号码" */,
                dataIndex: "userMobile",
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180429", "邮箱") /* "邮箱" */,
                dataIndex: "userEmail",
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418042C", "同步时间") /* "同步时间" */,
                dataIndex: "importDate",
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418042E", "是否默认") /* "是否默认" */,
                dataIndex: "isDefault",
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180431", "网关ID") /* "网关ID" */,
                dataIndex: "gatewayId",
            },
            {
                title: lang.templateByUuid("UID:P_UBL-FE_1F7D965C0560001E", "当前默认MaId") /* "当前默认MaId" */,
                dataIndex: "currentMaId",
            },
        ];
    }

    handleChange = (type, value) => {
        let { handleChange } = this.props.ownerStore;
        switch (type) {
            case "activeKey":
                this.setState({ [type]: value });
                break;
            case "paramType":
            case "param":
                handleChange(type, value);
        }
    };

    handleSearch = () => {
        const {
            ownerState: { paramType, param },
        } = this.props;
        let error = "";
        switch (paramType) {
            case "userId":
                break;
            case "email":
                if (!emailReg.test(param)) {
                    error = lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418042D", "请输入正确的邮箱"); /* "请输入正确的邮箱" */
                }
                break;
            case "mobile":
                if (!phoneReg.test(param)) {
                    error = lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180430", "请输入正确的手机号码"); /* "请输入正确的手机号码" */
                }
                break;
        }
        if (error) {
            Error(error);
            return;
        }
        this.props.ownerStore.getDataSource();
    };

    render() {
        let { activeKey } = this.state;
        let { ownerState } = this.props;
        let { dataSource, paramType, param } = ownerState;
        return (
            <div className="fail-compensate">
                <Header
                    title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180424", "数据修复", undefined, {
                        returnStr: true,
                    })}
                    back
                    fixed
                >
                    <Select
                        fieldid="ublinker-routes-fail-compensate-components-IndexView-index-4629369-Select"
                        placeholder={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180426", "修复类型", undefined, {
                            returnStr: true,
                        })}
                        className="fail-compensate-select"
                        value={activeKey}
                        onChange={this.handleChange.bind(null, "activeKey")}
                    >
                        <Option fieldid="UCG-FE-routes-fail-compensate-components-IndexView-index-666054-Option" value={1}>
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418042A", "友户通关联关系问题") /* "友户通关联关系问题" */}
                        </Option>
                    </Select>
                </Header>
                <Content className="fail-compensate-home-wrap" contentCls={"fail-compensate-home-content"} contentTop={activeKey == 0 ? null : ContentTop}>
                    {activeKey == "" ? (
                        <div className="fail-compensate-explain">
                            <div className="fail-compensate-explain-title">
                                {
                                    lang.templateByUuid(
                                        "UID:P_UBL-FE_18D8CEF604180432",
                                        "移动审批无法获取正确的NC/NCC用户关联关系"//@notranslate
                                    ) /* "移动审批无法获取正确的NC/NCC用户关联关系" */
                                }
                            </div>
                            <div className="fail-compensate-explain-subtitle">
                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418042A", "友户通关联关系问题") /* "友户通关联关系问题" */}
                            </div>
                            <div className="fail-compensate-explain-tip">
                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418041F", "问题说明") /* "问题说明" */}
                            </div>
                            <p className="fail-compensate-explain-text">
                                {
                                    lang.templateByUuid(
                                        "UID:P_UBL-FE_18D8CEF604180421",
                                        "若用户使用新网关同步数据，请先设置默认网关（ERP连接配置中点击设置默认）后全量执行人员任务,设置能查询到所有数据的条件"//@notranslate
                                    ) /* "若用户使用新网关同步数据，请先设置默认网关（ERP连接配置中点击设置默认）后全量执行人员任务,设置能查询到所有数据的条件" */
                                }
                            </p>
                            <p className="fail-compensate-explain-text">
                                {
                                    lang.templateByUuid(
                                        "UID:P_UBL-FE_18D8CEF604180422",
                                        "移动审批打开白页或者提示无法获取友户通关联关系，其他情况请找移动审批确认"//@notranslate
                                    ) /* "移动审批打开白页或者提示无法获取友户通关联关系，其他情况请找移动审批确认" */
                                }
                            </p>
                            <img
                                fieldid="ublinker-routes-fail-compensate-components-IndexView-index-1289276-img"
                                className="fail-compensate-explain-img"
                                src={yht}
                            />
                        </div>
                    ) : (
                        <Grid
                            fieldid="ublinker-routes-fail-compensate-components-IndexView-index-4657409-Grid"
                            header={
                                <div className="grid-header">
                                    <Select
                                        fieldid="ublinker-routes-fail-compensate-components-IndexView-index-2148309-Select"
                                        placeholder={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180427", "搜索字段类型", undefined, {
                                            returnStr: true,
                                        })}
                                        className="fail-compensate-select ucg-mar-l-20"
                                        value={paramType}
                                        onChange={this.handleChange.bind(null, "paramType")}
                                    >
                                        <Option fieldid="UCG-FE-routes-fail-compensate-components-IndexView-index-5964148-Option" value="userId">
                                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418042B", "用户ID") /* "用户ID" */}
                                        </Option>
                                        <Option fieldid="UCG-FE-routes-fail-compensate-components-IndexView-index-4928288-Option" value="email">
                                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180429", "邮箱") /* "邮箱" */}
                                        </Option>
                                        <Option fieldid="UCG-FE-routes-fail-compensate-components-IndexView-index-9356195-Option" value="mobile">
                                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180428", "手机号码") /* "手机号码" */}
                                        </Option>
                                    </Select>
                                    <SearchInput
                                        className="grid-header-input ucg-mar-l-20"
                                        value={param}
                                        onChange={this.handleChange.bind(null, "param")}
                                        onSearch={this.handleSearch}
                                        showClear
                                    />
                                </div>
                            }
                            rowKey={"id"}
                            data={dataSource}
                            columns={this.column}
                        ></Grid>
                    )}
                </Content>
            </div>
        );
    }
}

export default IndexView;
