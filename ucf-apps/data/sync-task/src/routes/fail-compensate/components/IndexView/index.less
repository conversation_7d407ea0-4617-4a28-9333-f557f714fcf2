.fail-compensate {
    box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);

    .page-header-content {
        display: inline-block;
        margin-left: 20px;
        float: none;
        position: relative;
    }

    &-select {
        width: 220px !important;
    }

    &-explain {
        position: relative;
        width: 1100px;
        height: 250px;
        background: #FFFFFF;
        border: 1px solid #DDDDDD;
        padding: 20px;
        margin-left: 32px;

        &-title {
            margin-top: 12px;
            font-size: 21px;
            color: #333333;
            line-height: 29px;
        }

        &-subtitle {
            margin-top: 6px;
            color: #E14C46;      
        }

        &-tip {
            margin-top: 25px;
            color: #999999;     
        }

        &-tip,&-subtitle{
            line-height: 20px;
            font-size: 14px;
        }

        &-text {
            margin-top: 6px;
            padding-left: 14px;
            font-size: 16px;
            color: #333333;
            line-height: 22px;
            position: relative;

            &::before{
                content: '·';
                position: absolute;
                left: 1px;
                font-weight: 600;
                color: #000;
            }
        }

        &-img{
            position: absolute;
            right: 20px;
            bottom: 20px;
        }
    }

    .fail-compensate-home-wrap {
        .u-select {
            overflow: visible !important;
        }
    }
}