/*
 * @Author: your name
 * @Date: 2021-06-10 16:07:21
 * @LastEditTime: 2021-07-21 17:39:53
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \ublinker-fe\ucf-apps\data\sync-task\src\routes\programme-Management\start\components\IndexView\index.js
 */
import React, { Component, Fragment } from "react";
import { Modal, Checkbox, Button, FormControl, FormList } from "components/TinperBee";
// import FormList from "components/TinperBee/Form";
import Grid from "components/TinperBee/Grid";
import { Header, Content, Footer } from "components/PageView";
import { GridActions, GridAction } from "components/TinperBee/Grid/GridActions";
// import EditModal from "../EditModal";
import Condition from "../../../../../../../../start/start-config/src/routes/info/components/condition";
import { columnsTypeEnum } from "../../constants";
import commonText from "constants/commonText";
import "./index.less";
import withRouter from "decorator/withRouter";
const FormItem = FormList.Item;
const labelCol = 120;
const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 12 },
};

@withRouter
class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            editColumn: null,
            taskId: "",
            startSchemeId: "",
            pkIntegratedId: "",
            afrom: "",
            increment: "",
        };
        this.form = React.createRef();
    }
    async componentDidMount() {
        let {
            ownerStore,
            location: { search },
        } = this.props;
        let afrom = search ? search.split("&")[0].split("=")[1] : "";
        this.setState({
            afrom,
        });
        if (afrom == "start") {
            let startSchemeId = search ? search.split("&")[2].split("=")[1] : "";
            let taskId = search ? search.split("&")[3].split("=")[1] : "";
            this.setState({
                taskId,
                startSchemeId,
            });
            ownerStore.getCaseInfoService(startSchemeId);
        } else {
            let pkIntegratedId = search ? search.split("&")[2].split("=")[1] : "";
            this.setState({
                pkIntegratedId,
            });
            let data = await ownerStore.getIntegrateScheme(pkIntegratedId);
            this.form.current.setFieldsValue({ datapkname: data.datapkname, pageSize: data.pageSize });
        }
    }
    inputChange = (fields, name) => {
        console.log(fields);
        const { submit } = this.props.ownerState;
        submit[name] = fields;
        this.props.ownerStore.changeState({
            submit,
        });
    };
    onChange = (increment) => {
        this.setState({
            increment,
        });
        const { submit } = this.props.ownerState;
        submit.increment = increment;
        this.props.ownerStore.changeState({
            submit,
        });
    };
    handSave = () => {
        let {
            ownerState: { submit },
            navigate,
        } = this.props;
        let { afrom } = this.state;
        if (afrom == "start") {
            this.props.ownerStore.editStartCase(
                { taskId: this.state.taskId, startSchemeId: this.state.startSchemeId, increment: this.state.increment || submit.increment },
                () => navigate(-1)
            );
        } else {
            this.props.ownerStore.editIntegrateScheme(this.state.pkIntegratedId, () => navigate(-1));
        }
    };
    handleInputChange = (value, name, record) => {
        let {
            ownerState: { submit, caseList },
            ownerStore,
        } = this.props;
        // let selectedCase, params

        // if(name=='sqlwhere'){
        //   value=btoa(encodeURIComponent(value))
        // }
        submit[name] = value;
        ownerStore.changeState({
            submit,
            sqlwhere: value,
        });
    };

    render() {
        let { afrom } = this.state;
        let {
            ownerState,
            ownerState: {
                submit,
                submit: { objectSrcType },
            },
            ownerStore,
        } = this.props;
        return (
            <Fragment>
                <Header
                    back
                    fixed
                    title={
                        afrom !== "start"
                            ? lang.templateByUuid("UID:P_UBL-FE_2009604004280449", "集成方案调整") /* "集成方案调整" */
                            : lang.templateByUuid("UID:P_UBL-FE_200960400428044A", "启动方案调整") /* "启动方案调整" */
                    }
                />
                <Content>
                    {objectSrcType == "mdd" || objectSrcType == "mddEntity" ? (
                        ownerState.submit.advanceCondition ? (
                            <Condition ownerState={ownerState} ownerStore={ownerStore} />
                        ) : (
                            ""
                        )
                    ) : objectSrcType == "sql" ? (
                        <div style={{ width: "1000px", margin: "0 auto" }}>
                            <div className="ucg-pad-20">
                                <p className="ucg-mar-b-5">{lang.templateByUuid("UID:P_UBL-FE_2009604004280448", "SQL条件") /* "SQL条件" */}</p>
                                <FormControl.TextArea
                                    rows={4}
                                    // componentClass="textarea"
                                    defaultValue={submit.sqlwhere || ""}
                                    className="ucg-mar-b-10"
                                    onChange={(value) => this.handleInputChange(value, "sqlwhere")}
                                    style={{ height: "auto" }}
                                    // autoSize={{
                                    //   minRows: 4
                                    // }}
                                />
                            </div>
                        </div>
                    ) : (
                        ""
                    )}
                    {ownerState.submit ? (
                        <div style={{ width: 1000, margin: "0 auto", textAlign: "center" }}>
                            {afrom == "start" ? (
                                <Checkbox
                                    fieldid="ublinker-programme-Management-start-components-IndexView-index-8725434-Checkbox"
                                    checked={ownerState.submit.increment}
                                    onChange={this.onChange}
                                >
                                    {lang.templateByUuid("UID:P_UBL-FE_200960400428044B", "是否启用增量") /* "是否启用增量" */}
                                </Checkbox>
                            ) : (
                                <div>
                                    <FormList
                                        fieldid="ublinker-programme-Management-start-components-IndexView-index-5441351-FormList"
                                        ref={this.form}
                                        {...formItemLayout}
                                        className="config-action-form"
                                    >
                                        {true && (
                                            <FormItem
                                                fieldid="ublinker-programme-Management-start-components-IndexView-index-813509-FormItem"
                                                label={lang.templateByUuid("UID:P_UBL-FE_2009604004280446", "主键名称") /* "主键名称" */}
                                                name="datapkname"
                                                // initialValue={ownerState.submit.datapkname}
                                            >
                                                <FormControl
                                                    fieldid="ublinker-programme-Management-start-components-IndexView-index-5675554-FormControl"
                                                    onChange={(value) => {
                                                        this.inputChange(value, "datapkname");
                                                    }}
                                                />
                                            </FormItem>
                                        )}
                                        {true && (
                                            <FormItem
                                                fieldid="ublinker-programme-Management-start-components-IndexView-index-620232-FormItem"
                                                label={lang.templateByUuid("UID:P_UBL-FE_2009604004280445", "分页大小") /* "分页大小" */}
                                                name="pageSize"
                                                // initialValue={ownerState.submit.pageSize}
                                            >
                                                <FormControl
                                                    fieldid="ublinker-programme-Management-start-components-IndexView-index-6904291-FormControl"
                                                    onChange={(value) => {
                                                        this.inputChange(value, "pageSize");
                                                    }}
                                                />
                                            </FormItem>
                                        )}
                                    </FormList>
                                </div>
                            )}

                            <Button
                                fieldid="ublinker-programme-Management-start-components-IndexView-index-2726786-Button"
                                className="ucg-mr-10"
                                colors="primary"
                                onClick={this.handSave}
                            >
                                {lang.templateByUuid("UID:P_UBL-FE_2009604004280447", "保存") /* "保存" */}
                            </Button>
                        </div>
                    ) : (
                        ""
                    )}
                </Content>
            </Fragment>
        );
    }
}

export default IndexView;
