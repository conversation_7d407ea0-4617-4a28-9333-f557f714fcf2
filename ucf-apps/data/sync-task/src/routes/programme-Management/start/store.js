/*
 * @Author: your name
 * @Date: 2021-06-10 16:07:21
 * @LastEditTime: 2021-07-23 13:44:11
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \ublinker-fe\ucf-apps\data\sync-task\src\routes\programme-Management\start\store.js
 */
import { observable, action, toJS, reaction, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { autoServiceMessage } from "utils/service";
import { loopApiCls } from "utils";
import * as ownerService from "./service";
import { Success, Error } from "utils/feedback";
import commonText from "constants/commonText";
const initState = {
    objectConditionTreeList: [],
    compareOpList: [],
    sqlExpression: "",
    submit: {},
};
const getUUID = () => {
    function S4() {
        return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
    }
    return S4() + S4() + S4() + S4() + S4() + S4() + S4() + S4();
};
class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    setPageParams = (paramInfo) => {
        this.changeState(paramInfo);
    };

    init = () => {
        this.state = initState;
    };
    getIntegrateScheme = async (id) => {
        let res = await autoServiceMessage({
            service: ownerService.getIntegrateScheme(id),
            // success: '获取成功'
        });
        if (res && res.status == 1) {
            if (!res.data.advanceCondition || JSON.stringify(res.data.advanceCondition) == "{}") {
                res.data.advanceCondition = {
                    logicOp: "and",
                    conditions: [
                        {
                            id: getUUID(),
                            field: "",
                            fieldType: "",
                            compareOp: "",
                            value1: "",
                            isLeaf: true,
                        },
                    ],
                };
            }
            // res.data.objectSrcType='dd'
            this.changeState({
                submit: res.data,
                sqlExpression: "",
            });
            this.getTreeList({ sysId: res.data.sourceSystemId, objectCode: res.data.sourceObjectCode });
            this.getCompareOpList();
            return res.data;
        }
    };
    editIntegrateScheme = async (pkIntegratedId, callBack, _id) => {
        let { submit } = this.state;
        let obj = { ...submit };
        let newSubmit = this.toJS(obj);
        let params = { conditionMap: {} };
        if (submit.objectSrcType == "mdd" || submit.objectSrcType == "mddEntity") {
            newSubmit = this.checkSubmitAdvanceCondition(newSubmit);
            if (!newSubmit.advanceCondition.conditions && JSON.stringify(newSubmit.advanceCondition) != "{}") {
                Error(lang.templateByUuid("UID:P_UBL-FE_20096040042802C3", "请输入父结点") /* "请输入父结点" */);
                return false;
            }
            params.conditionMap.advanceCondition = newSubmit.advanceCondition;
        } else if (submit.objectSrcType == "sql") {
            params.conditionMap.sqlView = btoa(encodeURIComponent(submit.sqlWhere));
        } else {
            params.conditionMap = {};
        }
        params.conditionMap.pageSize = newSubmit.pageSize;
        params.conditionMap.datapkname = newSubmit.datapkname;
        let res = await autoServiceMessage({
            service: ownerService.editTaskIntegrateScheme(pkIntegratedId, params),
            // success: "更新成功"
        });
        if (res && res.status) {
            callBack && callBack();
        }
    };
    getCaseInfoService = async (id) => {
        let res = await autoServiceMessage({
            service: ownerService.getCaseInfoService(id),
            // success: '获取成功'
        });
        if (res && res.status == 1) {
            if (!res.data.advanceCondition || JSON.stringify(res.data.advanceCondition) == "{}") {
                res.data.advanceCondition = {
                    logicOp: "and",
                    conditions: [
                        {
                            id: getUUID(),
                            field: "",
                            fieldType: "",
                            compareOp: "",
                            value1: "",
                            isLeaf: true,
                        },
                    ],
                };
            }
            // res.data.objectSrcType='dd'
            this.changeState({
                submit: res.data,
                sqlExpression: "",
            });
            this.getTreeList({ sysId: res.data.sysId, objectCode: res.data.objectCode });
            this.getCompareOpList();
        }
    };
    //更新启动方案
    editStartCase = async (data, callBack, _id) => {
        const { submit } = this.state;
        let obj = { ...submit };
        let newSubmit = this.toJS(obj);
        let params = { conditionMap: {} };
        if (submit.objectSrcType == "mdd" || submit.objectSrcType == "mddEntity") {
            newSubmit = this.checkSubmitAdvanceCondition(newSubmit);
            if (!newSubmit.advanceCondition.conditions && JSON.stringify(newSubmit.advanceCondition) != "{}") {
                Error(lang.templateByUuid("UID:P_UBL-FE_20096040042802C3", "请输入父结点") /* "请输入父结点" */);
                return false;
            }
            params.conditionMap.advanceCondition = newSubmit.advanceCondition;
        } else if (submit.objectSrcType == "sql") {
            params.conditionMap.sqlWhere = btoa(encodeURIComponent(submit.sqlWhere));
        } else {
            params = {};
        }

        let res = await autoServiceMessage({
            service: ownerService.editTaskStartCase(data, params),
            // success: "更新成功"
        });
        if (res && res.status) {
            callBack && callBack();
        }
    };
    checkSubmitAdvanceCondition = (submit) => {
        const { advanceCondition } = submit;
        if (advanceCondition.conditions.length == 1) {
            //不对表达式做任何操作时，提交一个空对象
            if (advanceCondition.conditions[0].field == "" && advanceCondition.conditions[0].compareOp == "") {
                submit.advanceCondition = {};
            }
        } else {
            let conditions = this.loop(advanceCondition.conditions);
            advanceCondition.conditions = conditions;
            submit.advanceCondition = advanceCondition;
        }
        return submit;
    };
    loop = (conditions, fn) => {
        for (var i = 0; i < conditions.length; i++) {
            if (conditions[i].conditions) {
                if (
                    !this.loop(conditions[i].conditions, function () {
                        conditions.splice(i, 1);
                        i = i - 1;
                    })
                ) {
                    return false;
                }
            } else {
                if (conditions[i].field == "" && conditions[i].compareOp == "") {
                    if (conditions.length == 1) {
                        //针对于再次进入Loop，只建了一个空组的情况，提交时需要过滤，从上个loop中的父结点删除该结点
                        fn();
                    } else {
                        if (conditions.length >= 2 && conditions[0].field == "" && conditions[0].compareOp == "" && !conditions[1].isLeaf) {
                            //这种情况针对于有且只有一个父结点并且是空的时候，就return false不让提交
                            return false;
                        } else {
                            conditions.splice(i, 1); //正常情况下，兄弟结点为空，提交时需要过滤，就删除
                            i = i - 1;
                        }
                    }
                }
            }
        }
        return conditions;
    };
    getTreeList = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.getTreeListService(data),
        });
        if (res && res.status) {
            //暂时不支持多级，只有一级
            let newRes = [];
            res.data.forEach((item) => {
                if (item.type !== "object") {
                    newRes.push(item);
                }
            });
            this.changeState({
                // objectTreeList: loopApiCls(newRes),
                objectConditionTreeList: loopApiCls(res.data),
            });

            // 临时给条件表达式下拉选赋初值
            // const {submit}=this.state
            // submit.advanceCondition.conditions[0].field=loopApiCls(res.data)[0].value
            // this.changeState({
            //   submit
            // })
            // 临时给条件表达式下拉选赋初值
        }
    };
    getTreeListAgain = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.getTreeListService(data),
        });
        if (res && res.status) {
            //暂时不支持多级，只有一级
            // let newRes=[]
            // res.data.forEach((item)=>{
            //   if(item.type!=='object'){
            //     newRes.push(item)
            //   }
            // })
            // return loopApiCls(newRes)
            return loopApiCls(res.data);
        }
    };
    getCompareOpList = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.getCompareOpList(data),
        });
        if (res && res.status) {
            this.changeState({
                compareOpList: res.data.COMPARE_OPERATE,
            });

            // 临时给条件表达式下拉选赋初值
            // const {submit}=this.state
            // submit.advanceCondition.conditions[0].compareOp=res.data.COMPARE_OPERATE[0].value
            // this.changeState({
            //   submit
            // })
            // 临时给条件表达式下拉选赋初值
        }
    };
    //新增组
    conditionAddGroup = (relationList, ownerState) => {
        const {
            submit,
            submit: {},
            objectTreeList,
            compareOpList,
        } = this.state;
        const {
            submit: { advanceCondition },
        } = ownerState;
        relationList.conditions.push({
            id: getUUID(),
            logicOp: "and",
            isLeaf: false,
            conditions: [
                {
                    id: getUUID(),
                    field: "",
                    fieldType: "",
                    compareOp: "",
                    value1: "",
                    isLeaf: true,
                },
            ],
        });
        submit.advanceCondition = { ...advanceCondition };
        this.changeState({
            submit,
        });
    };
    //新增行
    conditionAddRow = (relationList, ownerState, index) => {
        const { submit, objectTreeList, compareOpList } = this.state;
        const {
            submit: { advanceCondition },
        } = ownerState;
        if (!relationList.conditions) {
            relationList.conditions = [];
        }
        relationList.conditions.splice(index + 1, 0, {
            id: getUUID(),
            field: "",
            fieldType: "",
            compareOp: "",
            value1: "",
            isLeaf: true,
        });
        submit.advanceCondition = { ...advanceCondition };
        this.changeState({
            submit,
        });
    };
    //删除条件
    conditionDelete = (relationList, ownerState, index, type, activeIndex, groupChildrenCallBack) => {
        const { submit, objectTreeList, compareOpList } = this.state;
        let {
            submit: { advanceCondition },
        } = ownerState;
        if (relationList.conditions.length === 1 && type !== "children") {
            //一加载删第一个的效果
            relationList.conditions = [];
            relationList.conditions.push({
                id: getUUID(),
                field: "",
                fieldType: "",
                compareOp: "",
                value1: "",
                isLeaf: true,
            });
        } else if (relationList.conditions.length === 1 && type == "children") {
            groupChildrenCallBack();
        } else {
            relationList.conditions.splice(index, 1);
            console.log(relationList);
        }
        submit.advanceCondition = { ...advanceCondition };
        this.changeState({
            submit,
        });
    };
    conditionGroupCallBack = (index, relationList) => {
        const { objectTreeList, compareOpList } = this.state;
        if (relationList.conditions.length == 1) {
            relationList.conditions = [
                {
                    id: getUUID(),
                    field: "",
                    fieldType: "",
                    compareOp: "",
                    value1: "",
                    isLeaf: true,
                },
            ];
        } else {
            relationList.conditions.splice(index, 1);
        }
    };
    changeRelationType = (relationList, type, ownerState) => {
        const { submit } = this.state;
        let {
            submit: { advanceCondition },
        } = ownerState;
        relationList.logicOp = type;
        submit.advanceCondition = { ...advanceCondition };
        this.changeState({
            submit,
        });
    };
    conditionChangeField = (fieldName, field, activeItem, relationList, activeIndex, ownerState, secondName, secondValue) => {
        //1字段名2下拉框的值，3当条数据4一个组5当条数据在4中的位置6初始为一个组，后期为整个结构，为submit中的advanceCondition 7type的键，8type的值
        const { submit } = this.state;
        let {
            submit: { advanceCondition },
        } = ownerState;
        activeItem[fieldName] = field;
        activeItem[secondName] = secondValue;
        relationList.conditions[activeIndex] = activeItem;
        submit.advanceCondition = { ...advanceCondition };
        this.changeState({
            submit,
        });
    };
    getSqlExpression = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.getSqlExpression(data),
        });
        if (res && res.status) {
            this.changeState({
                sqlExpression: res.data,
            });
        }
    };
}

export const storeKey = "programmeManageStart";
export default Store;
