import { createEnum } from "constants/utils";

export const columnsTypeEnum = createEnum([
    {
        name: lang.templateByUuid("UID:P_UBL-FE_200960400428027F", "字符") /* "字符" */,
        code: 1,
    },
    {
        name: lang.templateByUuid("UID:P_UBL-FE_2009604004280282", "整数") /* "整数" */,
        code: 2,
    },
    {
        name: lang.templateByUuid("UID:P_UBL-FE_2009604004280284", "浮点数") /* "浮点数" */,
        code: 3,
    },
    {
        name: lang.templateByUuid("UID:P_UBL-FE_200960400428027D", "参照") /* "参照" */,
        code: 4,
    },
    {
        name: lang.templateByUuid("UID:P_UBL-FE_2009604004280280", "枚举") /* "枚举" */,
        code: 5,
    },
    {
        name: lang.templateByUuid("UID:P_UBL-FE_2009604004280283", "日期") /* "日期" */,
        code: 6,
    },
    {
        name: lang.templateByUuid("UID:P_UBL-FE_2009604004280285", "日期和时间") /* "日期和时间" */,
        code: 7,
    },
    {
        name: lang.templateByUuid("UID:P_UBL-FE_200960400428027E", "参照本地") /* "参照本地" */,
        code: 8,
    },
    {
        name: lang.templateByUuid("UID:P_UBL-FE_2009604004280281", "依赖") /* "依赖" */,
        code: 9,
    },
]);
