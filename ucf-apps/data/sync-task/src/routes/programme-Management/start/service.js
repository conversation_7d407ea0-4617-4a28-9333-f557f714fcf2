/*
 * @Author: your name
 * @Date: 2021-06-10 16:07:21
 * @LastEditTime: 2021-07-23 13:44:43
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \ublinker-fe\ucf-apps\data\sync-task\src\routes\programme-Management\start\service.js
 */
import { getInvokeService, defineService, getServicePath } from "utils/service";

/**
 * 列定义列表
 * @param {Object} data
 * @param {String} data.taskId
 * @param {String|Number} data.dataversion
 * @return {*}
 */
export const getViewColumnsService = function (data) {
    let { taskId, ..._data } = data;
    return getInvokeService(
        {
            method: "POST",
            path: `/diwork/erpdata/task/${taskId}/column`,
        },
        _data
    );
};

/**
 * 视图列编辑
 * @param {Object} data
 * @param {String} data.taskId
 * @param {String} data.action=[edit|delete]
 * @param {String} data.id -columnInfo.pk_id
 * @param {String} data.columnname -columnInfo.columnname
 * @param {String} data.columntype -columnInfo.columntype
 * @param {String} data.columnref -columnInfo.columnref
 * @param {String} data.enumtype -columnInfo.enumtype
 * @return {*}
 */
export const updateViewColummsService = function (data) {
    let { taskId, ..._data } = data;
    let formData = new FormData();
    for (let key in _data) {
        formData.append(key, _data[key]);
    }
    return getInvokeService(
        {
            method: "POST",
            path: `/diwork/erpdata/task/${taskId}/colupdate`,
            header: {
                "Content-Type": "application/x-www-form-urlencoded",
            },
        },
        formData
    );
};
export const getCaseInfoService = function (id) {
    return getInvokeService({
        method: "GET",
        path: "/gwmanage/gwportal/diwork/startscheme/get/" + id,
    });
};
export const getIntegrateScheme = function (id) {
    return getInvokeService({
        method: "GET",
        path: `/gwmanage/gwportal/diwork/integscheme/findSchemeSettingById/${id}`,
    });
};
export const getTreeListService = function (data) {
    let service = defineService({
        method: "GET",
        path: "/gwmanage/gwportal/diwork/integrated/object/attribute/listAllAttribute",
    });

    return service.invoke(data);
};
export const getCompareOpList = function (data) {
    let service = defineService({
        method: "GET",
        path: "/gwmanage/gwportal/diwork/startscheme//whereTypeList",
    });

    return service.invoke(data);
};
export const getSqlExpression = function (data) {
    let service = defineService({
        method: "POST",
        path: "/gwmanage/gwportal/diwork/startscheme/generateSQL",
    });

    return service.invoke(data);
};
export const editTaskStartCase = function (data, advanceCondition) {
    let { taskId, startSchemeId, increment } = data;
    let service = defineService({
        method: "POST",
        // path: '/gwmanage/gwportal/diwork/startscheme/update',
        path: `/diwork/startscheme/updateByTask/${taskId}/${startSchemeId}/${increment}`,
    });

    return service.invoke(advanceCondition);
};

export const editTaskIntegrateScheme = function (data, params) {
    let service = defineService({
        method: "POST",
        path: `/diwork/integscheme/updateSchemeSettingById/${data}`,
    });

    return service.invoke(params);
};
