import React, { Component } from "react";
import { FormControl, Select } from "components/TinperBee";
import Modal from "components/TinperBee/Modal";
import FormList from "components/TinperBee/Form";
import { codeReg, codeMessage } from "utils/regExp";
import { Error } from "utils/feedback";
import { columnsTypeEnum } from "../../constants";
import commonText from "constants/commonText";

const FormItem = FormList.Item;

const columnsTypes = columnsTypeEnum.selectData;

let includeRefs = [4, 8, 9];

class EditModal extends Component {
    constructor() {
        super();
        this.state = {
            tempType: 0,
        };
    }

    handleOk = () => {
        let {
            form: { validateFields },
            onOk,
            dataSource,
        } = this.props;
        validateFields((err, values) => {
            if (!err) {
                if (values.columnname) {
                    if (codeReg.test(values.columnname)) {
                        values.pk_id = dataSource.pk_id;
                        onOk(values);
                    } else {
                        Error(
                            lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418005C", "字段名称", undefined, {
                                returnStr: true,
                            }) + codeMessage
                        );
                    }
                } else {
                    Error(
                        lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418005D", "字段名称不能为空", undefined, {
                            returnStr: true,
                        }) /* "字段名称不能为空" */
                    );
                }
            }
        });
    };

    onColumnTypeChange = (value) => {
        let { form } = this.props;
        this.setState({
            tempType: value,
        });
        let values = {};
        if (value != 5) {
            values.enumtype = "";
        }

        if (!includeRefs.includes(value)) {
            values.columnref = "";
        }
        form.setFieldsValue(values);
    };

    labelCol = 120;

    render() {
        let { dataSource, onCancel } = this.props;
        if (dataSource) {
            let { enumTypes, refTypes, form } = this.props;
            let { getFieldProps, getFieldValue } = form;
            let columntype = getFieldValue("columntype") || dataSource.columntype || 1;
            return (
                <Modal
                    fieldid="ublinker-programme-Management-object-components-EditModal-index-598430-Modal"
                    title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418005E", "编辑", undefined, {
                        returnStr: true,
                    })}
                    show={!!dataSource}
                    onCancel={onCancel}
                    okDisabled={dataSource.columntype === 0 && this.state.tempType === 0}
                    onOk={this.handleOk}
                    size="md"
                >
                    <FormList
                        fieldid="ublinker-programme-Management-object-components-EditModal-index-8567926-FormList"
                        layoutOpt={{ md: 12 }}
                        className="ucg-pad-20-30"
                    >
                        <FormItem
                            fieldid="ublinker-programme-Management-object-components-EditModal-index-4779098-FormItem"
                            label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418005C", "字段名称") /* "字段名称" */}
                            labelCol={this.labelCol}
                        >
                            <FormControl
                                fieldid="ublinker-programme-Management-object-components-EditModal-index-7418786-FormControl"
                                {...getFieldProps("columnname", {
                                    initialValue: dataSource.columnname,
                                })}
                            />
                        </FormItem>

                        <FormItem
                            fieldid="ublinker-programme-Management-object-components-EditModal-index-2232248-FormItem"
                            label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180061", "字段类型") /* "字段类型" */}
                            labelCol={this.labelCol}
                        >
                            <Select
                                fieldid="ublinker-programme-Management-object-components-EditModal-index-7372221-Select"
                                data={columnsTypes}
                                {...getFieldProps("columntype", {
                                    initialValue: dataSource.columntype || "",
                                })}
                                onSelect={this.onColumnTypeChange}
                            />
                        </FormItem>

                        <FormItem
                            fieldid="ublinker-programme-Management-object-components-EditModal-index-5394763-FormItem"
                            label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180060", "枚举映射") /* "枚举映射" */}
                            labelCol={this.labelCol}
                        >
                            <Select
                                fieldid="ublinker-programme-Management-object-components-EditModal-index-115131-Select"
                                data={enumTypes}
                                disabled={columntype != 5}
                                {...getFieldProps("enumtype", {
                                    initialValue: dataSource.enumtype || "",
                                })}
                            />
                        </FormItem>

                        <FormItem
                            fieldid="ublinker-programme-Management-object-components-EditModal-index-5408021-FormItem"
                            label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418005F", "参照档案类型") /* "参照档案类型" */}
                            labelCol={this.labelCol}
                        >
                            <Select
                                fieldid="ublinker-programme-Management-object-components-EditModal-index-2211504-Select"
                                data={refTypes}
                                showSearch
                                optionFilterProp="children"
                                disabled={!includeRefs.includes(columntype)}
                                {...getFieldProps("columnref", {
                                    initialValue: dataSource.columnref || "",
                                })}
                            />
                        </FormItem>
                    </FormList>
                </Modal>
            );
        } else {
            return null;
        }
    }
}

export default FormList.createForm()(EditModal);
