import React, { Component, Fragment } from "react";
import { Modal, Switch } from "components/TinperBee";
import Grid from "components/TinperBee/Grid";
import { Header, Content, Footer } from "components/PageView";
import { GridActions, GridAction } from "components/TinperBee/Grid/GridActions";
// import EditModal from "../EditModal";
import { columnsTypeEnum } from "../../constants";
import commonText from "constants/commonText";
import "./index.less";

class IndexView extends Component {
    constructor(props) {
        super(props);
        console.log(props);
        this.state = {
            pkIntegratedId: "",
            editColumn: null,
        };
    }

    componentDidMount() {
        let {
            ownerStore,
            location: { search },
        } = this.props;
        let pkIntegratedId = search ? search.split("&")[1].split("=")[1] : "";
        ownerStore.changeState({
            integrateSchemeId: pkIntegratedId,
        });
        ownerStore.getDataSource();
    }
    // 渲染属性开关状态
    renderAttrStatus = (field, value, record) => {
        return (
            <Switch
                fieldid="ublinker-programme-Management-object-components-IndexView-index-2651262-Switch"
                size="sm"
                checked={record[field]}
                disabled
                colors="blue"
                // onChange={() => { this.props.ownerStore.updateAttrStatus(field, record) }}
            />
        );
    };
    gridColumns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180224", "集成对象属性") /* "集成对象属性" */,
            dataIndex: "$$index",
            width: 80,
            render: (value, record, index) => {
                return index + 1;
            },
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418021F", "类型") /* "类型" */,
            dataIndex: "type",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180221", "编码") /* "编码" */,
            dataIndex: "code",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180223", "名称") /* "名称" */,
            dataIndex: "name",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180225", "主键") /* "主键" */,
            dataIndex: "columnPrimary",
            render: this.renderAttrStatus.bind(null, "columnPrimary"),
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180226", "候选键") /* "候选键" */,
            dataIndex: "candidatePrimary",
            render: this.renderAttrStatus.bind(null, "candidatePrimary"),
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180220", "必填") /* "必填" */,
            dataIndex: "required",
            render: this.renderAttrStatus.bind(null, "required"),
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180222", "默认值") /* "默认值" */,
            dataIndex: "defaultValue",
        },
    ];

    render() {
        let { ownerState } = this.props;
        let { dataSource, pagination } = ownerState;
        return (
            <Fragment>
                <Header
                    back
                    fixed
                    title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180224", "集成对象属性", undefined, {
                        returnStr: true,
                    })}
                />
                <Content>
                    <Grid
                        fieldid="ublinker-programme-Management-object-components-IndexView-index-2787798-Grid"
                        rowKey="pk_id"
                        pagination={pagination}
                        columns={this.gridColumns}
                        data={dataSource.list}
                    />
                </Content>
                {/* {
                    !!editColumn ?
                        <EditModal
                            dataSource={editColumn}
                            refTypes={refTypes}
                            enumTypes={enumTypes}
                            onCancel={this.setEditColumn}
                            onOk={this.handleEditOk}
                        />
                        : null
                } */}
            </Fragment>
        );
    }
}

export default IndexView;
