/*
 * @Author: your name
 * @Date: 2021-06-10 16:06:56
 * @LastEditTime: 2021-06-11 10:42:50
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: \ublinker-fe\ucf-apps\data\sync-task\src\routes\programme-Management\object\store.js
 */
import { observable, action, toJS, reaction, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { autoServiceMessage } from "utils/service";
import { defaultListMap } from "utils/pageListUtils";
import * as ownerService from "./service";
import commonText from "constants/commonText";
const initState = {
    enumTypes: [], //枚举选择列表
    refTypes: [], //参照选择列表
    columnsData: [],
    istenant: false,
    queryParams: {
        taskId: "",
        dataversion: "",
    },
    dataSource: {
        ...defaultListMap,
    },
    searchKey: "",
    integrateSchemeId: "",
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    setPageParams = (paramInfo) => {
        this.changeState(paramInfo);
    };

    init = () => {
        this.state = initState;
    };
    //获取表格列表
    getDataSource = async (reqData, id) => {
        let requestData = {
            key: this.state.searchKey,
            integrateSchemeId: this.state.integrateSchemeId,
            ...reqData,
        };
        this.getPagesListFunc({
            service: ownerService.getObjectData,
            requestData,
            dataKey: "dataSource",
        });
        // this.state.searchKey = requestData.key
    };
}

export const storeKey = "programmeManageObject";
export default Store;
