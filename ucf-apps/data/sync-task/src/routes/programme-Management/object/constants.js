import { createEnum } from "constants/utils";

export const columnsTypeEnum = createEnum([
    {
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180353", "字符") /* "字符" */,
        code: 1,
    },
    {
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180355", "整数") /* "整数" */,
        code: 2,
    },
    {
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180356", "浮点数") /* "浮点数" */,
        code: 3,
    },
    {
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180357", "参照") /* "参照" */,
        code: 4,
    },
    {
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180358", "枚举") /* "枚举" */,
        code: 5,
    },
    {
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180359", "日期") /* "日期" */,
        code: 6,
    },
    {
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180351", "日期和时间") /* "日期和时间" */,
        code: 7,
    },
    {
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180352", "参照本地") /* "参照本地" */,
        code: 8,
    },
    {
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180354", "依赖") /* "依赖" */,
        code: 9,
    },
]);
