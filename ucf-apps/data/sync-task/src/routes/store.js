import { observable, makeObservable } from "mobx";
import DefaultS<PERSON> from "utils/defaultStore";
import { autoServiceMessage } from "utils/service";
import { appListService } from "services/common";

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = {
        appMap: {},
        appList: [],
    };

    getApp = async () => {
        let res = await autoServiceMessage({
            service: appListService(),
        });
        if (res) {
            let appList = res.data;
            let appMap = {};
            appList.forEach((item) => {
                appMap[item.id] = item.text;
            });
            this.changeState({ appMap, appList });
        }
    };
}

export const storeKey = "dataSyncTaskCommonStore";
export default Store;
