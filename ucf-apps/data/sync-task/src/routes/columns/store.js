import { observable, action, toJS, reaction, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { autoServiceMessage } from "utils/service";
import * as ownerService from "./service";
import commonText from "constants/commonText";
const initState = {
    enumTypes: [], //枚举选择列表
    refTypes: [], //参照选择列表
    columnsData: [],
    istenant: false,
    queryParams: {
        taskId: "",
        dataversion: "",
    },
    serviceCodeDiwork: "kfljsjtbrw",
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    setPageParams = (paramInfo) => {
        this.changeState(paramInfo);
    };

    init = () => {
        this.state = initState;
    };

    getDataSource = async () => {
        let queryParams = this.toJS(this.state.queryParams);
        let res = await autoServiceMessage({
            service: ownerService.getViewColumnsService(queryParams, { serviceCode: this.state.serviceCodeDiwork }),
        });
        if (res) {
            let { data, ref, enum: enums, istenant } = res;
            let refTypes = [],
                enumTypes = [];
            for (let key in ref) {
                let _key = ref[key];
                refTypes.push({
                    value: key,
                    key: _key,
                });
            }
            for (let key in enums) {
                let _key = enums[key];
                enumTypes.push({
                    value: key,
                    key: _key,
                });
            }
            this.changeState({
                columnsData: data,
                enumTypes,
                refTypes,
                istenant,
            });
        }
    };

    updateColumns = async (columnInfo, action) => {
        let columnsData = this.toJS(this.state.columnsData);
        let requestData = {
            id: columnInfo.pk_id,
            columnname: columnInfo.columnname,
            columntype: columnInfo.columntype,
            columnref: columnInfo.columnref,
            enumtype: columnInfo.enumtype,
            action: action,
            taskId: this.state.queryParams.taskId,
            saveDetail: columnInfo.saveDetail,
        };
        let columnIndex;
        let column = columnsData.find((item, index) => {
            if (item.pk_id === columnInfo.pk_id) {
                columnIndex = index;
                return true;
            } else {
                return false;
            }
        });
        let success = "",
            successFunc = null;
        switch (action) {
            case "edit":
                success = lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180397", "保存成功") /* "保存成功" */;
                if (columnInfo.columnref) {
                    const ref = this.state.refTypes.find((item) => item.value === columnInfo.columnref);
                    if (ref) {
                        columnInfo.refValue = ref.key;
                    }
                } else {
                    columnInfo.refValue = "";
                }
                if (columnInfo.enumtype) {
                    const en = this.state.enumTypes.find((item) => item.value === columnInfo.enumtype);
                    if (en) {
                        columnInfo.enumValue = en.key;
                    }
                } else {
                    columnInfo.enumValue = "";
                }
                successFunc = () => {
                    column = Object.assign(column, columnInfo);
                };
                break;
            case "delete":
                success = lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180398", "删除成功！") /* "删除成功！" */;
                successFunc = () => {
                    columnsData.splice(columnIndex, 1);
                };
                break;
        }
        let res = await autoServiceMessage({
            service: ownerService.updateViewColummsService(requestData, { serviceCode: this.state.serviceCodeDiwork }),
            success,
        });
        if (res) {
            successFunc && successFunc();
            this.state.columnsData = columnsData;
        }
        return res;
    };
}

export const storeKey = "dataSyncTaskViewColumnsStore";
export default Store;
