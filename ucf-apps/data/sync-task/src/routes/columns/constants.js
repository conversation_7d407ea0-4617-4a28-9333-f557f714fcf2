import { createEnum } from "constants/utils";

export const columnsTypeEnum = createEnum([
    {
        name: " ",
        code: 0,
    },
    {
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180090", "字符", undefined, {
            returnStr: true,
        }) /* "字符" */,
        code: 1,
    },
    {
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180092", "整数", undefined, {
            returnStr: true,
        }) /* "整数" */,
        code: 2,
    },
    {
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180093", "浮点数", undefined, {
            returnStr: true,
        }) /* "浮点数" */,
        code: 3,
    },
    {
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180094", "参照", undefined, {
            returnStr: true,
        }) /* "参照" */,
        code: 4,
    },
    {
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180095", "枚举", undefined, {
            returnStr: true,
        }) /* "枚举" */,
        code: 5,
    },
    {
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418008D", "日期", undefined, {
            returnStr: true,
        }) /* "日期" */,
        code: 6,
    },
    {
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418008E", "日期和时间", undefined, {
            returnStr: true,
        }) /* "日期和时间" */,
        code: 7,
    },
    {
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418008F", "参照本地", undefined, {
            returnStr: true,
        }) /* "参照本地" */,
        code: 8,
    },
    {
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180091", "依赖", undefined, {
            returnStr: true,
        }) /* "依赖" */,
        code: 9,
    },
]);
