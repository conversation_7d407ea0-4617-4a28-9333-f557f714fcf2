/**
 * 数据同步任务-数据列定义
 * location = window.location || reactHistory.location
 * location.search.taskId -任务ID taskItem.pk_id
 * location.search.dataversion -taskItem.dataversion
 * */

import React, { Component } from "react";
import IndexView from "./components/IndexView/index2";
import { inject, observer } from "mobx-react";
import { getPageParams } from "decorator";
import Store, { storeKey } from "./store";
import mixCore from "core";

mixCore.addStore({
    storeKey: storeKey,
    store: new Store(),
});

@inject((rootStore) => {
    let ownerStore = rootStore[storeKey];
    return {
        ownerState: ownerStore.toJS(),
        ownerStore: ownerStore,
    };
})
@observer
@getPageParams
class Container extends Component {
    constructor(props) {
        super(props);
        let { ownerStore, selectedTask } = props;
        ownerStore.setPageParams({ queryParams: { dataversion: selectedTask.dataversion, taskId: selectedTask.pk_id } });
    }

    render() {
        return <IndexView {...this.props} />;
    }
}
export default Container;
