import React, { Component, Fragment } from "react";
import { Modal } from "components/TinperBee";
import Grid from "components/TinperBee/Grid";
import { Header, Content, Footer } from "components/PageView";
import { GridActions, GridAction } from "components/TinperBee/Grid/GridActions";
import EditModal from "../EditModal";
import { columnsTypeEnum } from "../../constants";
import commonText from "constants/commonText";
import "./index.less";
import { Space, Button, Table } from "@tinper/next-ui";

class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            editColumn: null,
        };
    }

    componentDidMount() {
        let { ownerStore, selectedTask } = this.props;
        ownerStore.getDataSource();
    }

    componentWillUnmount() {
        this.props.ownerStore.init();
    }

    handleDelete = (record) => {
        let { ownerStore } = this.props;
        Modal.confirm({
            fieldid: "************",
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418022C", "确认要删除吗？") /* "确认要删除吗？" */,
            onOk: ownerStore.updateColumns.bind(null, record, "delete"),
        });
    };

    setEditColumn = (columnInfo = null) => {
        this.setState({ editColumn: columnInfo });
    };

    handleEditOk = async (columnInfo) => {
        let { ownerStore } = this.props;
        let res = await ownerStore.updateColumns(columnInfo, "edit");
        if (res) {
            this.setEditColumn();
        }
        return res;
    };

    renderActions = (value, record) => {
        let {
            ownerState: { istenant },
        } = this.props;
        if (istenant) {
            return (
                <GridActions>
                    <GridAction fieldid="UCG-FE-routes-columns-components-IndexView-index2-7403699-GridAction" onClick={this.setEditColumn.bind(null, record)}>
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180231", "编辑") /* "编辑" */}
                    </GridAction>
                    <GridAction fieldid="UCG-FE-routes-columns-components-IndexView-index2-6899793-GridAction" onClick={this.handleDelete.bind(null, record)}>
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180232", "删除") /* "删除" */}
                    </GridAction>
                </GridActions>
            );
        } else {
            return "-";
        }
    };
    renderStatus = (taskstatus) => {
        const taskStatus = columnsTypeEnum[taskstatus];
        return <span>{window.lang.template(taskStatus.tagName || taskStatus.name)}</span>;
    };
    hoverContent = (record) => {
        let {
            ownerState: { istenant },
        } = this.props;
        if (istenant) {
            return (
                <Space size={5}>
                    <Button
                        {...Grid.hoverButtonPorps}
                        fieldid="UCG-FE-routes-columns-components-IndexView-index-4313919-GridAction"
                        onClick={this.setEditColumn.bind(null, record)}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180306", "编辑") /* "编辑" */}
                    </Button>
                    <Button
                        {...Grid.hoverButtonPorps}
                        fieldid="UCG-FE-routes-columns-components-IndexView-index-2954894-GridAction"
                        onClick={this.handleDelete.bind(null, record)}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180307", "删除") /* "删除" */}
                    </Button>
                </Space>
            );
        }
    };
    gridColumns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418022D", "字段列名") /* "字段列名" */,
            dataIndex: "columncode",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418022F", "字段名称") /* "字段名称" */,
            dataIndex: "columnname",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180233", "字段类型") /* "字段类型" */,
            dataIndex: "columntype",
            render: this.renderStatus,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418022A", "参照档案类型") /* "参照档案类型" */,
            dataIndex: "refValue",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418022E", "枚举映射") /* "枚举映射" */,
            dataIndex: "enumValue",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180230", "主键类型") /* "主键类型" */,
            dataIndex: "pktype",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180234", "数据来源") /* "数据来源" */,
            dataIndex: "datasoure",
        },
        // {
        //     title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418022B", "操作") /* "操作" */,
        //     dataIndex: "action",
        //     width: 110,
        //     fixed: "right",
        //     render: this.renderActions,
        // },
    ];

    render() {
        let { editColumn } = this.state;
        let { ownerState } = this.props;
        let { columnsData, enumTypes, refTypes } = ownerState;
        return (
            <Fragment>
                <Table
                    dragborder
                    fieldid="ublinker-routes-columns-components-IndexView-index2-6239340-Grid"
                    rowKey="pk_id"
                    columns={this.gridColumns}
                    data={columnsData}
                    hoverContent={this.hoverContent}
                    scroll={{ y: 360 }}
                />
                {!!editColumn ? (
                    <EditModal dataSource={editColumn} refTypes={refTypes} enumTypes={enumTypes} onCancel={this.setEditColumn} onOk={this.handleEditOk} />
                ) : null}
            </Fragment>
        );
    }
}

export default IndexView;
