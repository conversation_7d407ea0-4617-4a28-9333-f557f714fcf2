import React, { useState, useEffect } from "react";
import { FormControl, Select, FormList, Checkbox, Col } from "components/TinperBee";
import Modal from "components/TinperBee/Modal";
// import FormList from "components/TinperBee/Form";
import { codeReg, codeMessage } from "utils/regExp";
import { Error } from "utils/feedback";
import { columnsTypeEnum } from "../../constants";
import commonText from "constants/commonText";

const FormItem = FormList.Item;

const columnsTypes = columnsTypeEnum.selectData;
console.log(columnsTypeEnum);
console.log(columnsTypes);
const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
};
let includeRefs = [4, 8, 9];

const EditModal = (props) => {
    const [form] = FormList.useForm();
    const [tempType, setTempType] = useState(0);
    let { enumTypes, refTypes, dataSource, onCancel } = props;
    useEffect(() => {
        setTimeout(() => {
            form.setFieldsValue(dataSource);
        });
    }, [dataSource]);
    const handleOk = () => {
        let { onOk, dataSource } = props;
        form.validateFields().then((values) => {
            if (values.columnname) {
                // if (codeReg.test(values.columnname)) {
                values.pk_id = dataSource.pk_id;
                onOk(values);
                // } else {
                //   Error("字段名称" + codeMessage)
                // }
            } else {
                Error(
                    lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801FB", "字段名称不能为空", undefined, {
                        returnStr: true,
                    }) /* "字段名称不能为空" */
                );
            }
        });
    };

    const onColumnTypeChange = (value) => {
        setTempType(value);
        let values = {};
        if (value != 5) {
            values.enumtype = "";
        }

        if (!includeRefs.includes(value)) {
            values.columnref = "";
        }
        form.setFieldsValue(values);
    };

    let columntype = form.getFieldValue("columntype") || dataSource.columntype;
    console.log(columntype);
    // let columntype = dataSource.columntype || 1;
    return (
        <Modal
            fieldid="ublinker-routes-columns-components-EditModal-index-9671184-Modal"
            title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801F8", "编辑", undefined, {
                returnStr: true,
            })}
            show={!!dataSource}
            onCancel={onCancel}
            okDisabled={dataSource.columntype === 0 && tempType === 0}
            onOk={handleOk}
            size="md"
        >
            <FormList fieldid="ublinker-routes-columns-components-EditModal-index-6800348-FormList" form={form} {...formItemLayout} className="ucg-pad-20-30">
                <FormItem
                    fieldid="ublinker-routes-columns-components-EditModal-index-8192752-FormItem"
                    label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801FE", "字段名称") /* "字段名称" */}
                    name="columnname"
                    // initialValue={dataSource.columnname}
                >
                    <FormControl fieldid="ublinker-routes-columns-components-EditModal-index-8965369-FormControl" />
                </FormItem>

                <FormItem
                    fieldid="ublinker-routes-columns-components-EditModal-index-2651615-FormItem"
                    label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801F9", "字段类型") /* "字段类型" */}
                    name="columntype"
                    initialValue={dataSource.columntype || ""}
                >
                    <Select
                        fieldid="ublinker-routes-columns-components-EditModal-index-9853151-Select"
                        // data={columnsTypes}
                        onSelect={onColumnTypeChange}
                    >
                        {columnsTypes.map((item) => {
                            return (
                                <Select.Option key={item.value} value={item.value}>
                                    {window.lang.template(item.key)}
                                </Select.Option>
                            );
                        })}
                    </Select>
                </FormItem>

                <FormItem
                    fieldid="ublinker-routes-columns-components-EditModal-index-1156178-FormItem"
                    label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801FA", "枚举映射") /* "枚举映射" */}
                    name="enumtype"
                    // initialValue={dataSource.enumtype || ''}
                    rules={[
                        {
                            required: columntype == 5 ? true : false,
                            message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801FC", "请选择模式") /* "请选择模式" */,
                        },
                    ]}
                >
                    {/* 等于5的时候让用  枚举 */}
                    <Select
                        fieldid="ublinker-routes-columns-components-EditModal-index-8685562-Select"
                        // data={enumTypes}
                        disabled={columntype != 5}
                    >
                        {enumTypes.map((item) => {
                            return (
                                <Select.Option key={item.value} value={item.value}>
                                    {item.key}
                                </Select.Option>
                            );
                        })}
                    </Select>
                </FormItem>

                <FormItem
                    fieldid="ublinker-routes-columns-components-EditModal-index-6601405-FormItem"
                    label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801FD", "参照档案类型") /* "参照档案类型" */}
                    name="columnref"
                    // initialValue={dataSource.columnref || ''}
                >
                    <Select
                        fieldid="ublinker-routes-columns-components-EditModal-index-4603735-Select"
                        // data={refTypes}
                        showSearch
                        optionFilterProp="children"
                        disabled={!includeRefs.includes(columntype)}
                        // 4 8 9 的时候让用
                    >
                        {refTypes.map((item) => {
                            return (
                                <Select.Option key={item.value} value={item.value}>
                                    {item.key}
                                </Select.Option>
                            );
                        })}
                    </Select>
                </FormItem>

                <FormItem fieldid="ublinker-routes-columns-components-EditModal-index-************-FormItem" label=" " name="saveDetail">
                    <Checkbox
                        fieldid="ublinker-routes-list-components-SqlWhereModal-index2-************-Checkbox"
                        disabled={dataSource.saveDetail}
                        defaultChecked={dataSource.saveDetail}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_1B3FCE8604100004", "记录同步数据") /* "记录同步数据" */}
                    </Checkbox>
                </FormItem>
            </FormList>
        </Modal>
    );
};

export default EditModal;
