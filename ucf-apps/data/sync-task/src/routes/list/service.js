import { getInvokeService, defineService, getServicePath } from "utils/service";
import { a_download } from "utils/index";
/**
 * 数据同步任务列表
 * @param {Object} data
 * @param {String} data.order
 * @param {String} data.key
 * @return {Promise<unknown>}
 */
export const viewTaskListService = function (data, header) {
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/erpdata/task/page",
            header,
        },
        data
    );
};
// 新同步任务列表
export const getTaskListService = function (data) {
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/erpdata/task/pageWithFlow",
        },
        data
    );
};

/**
 * 切换任务网关
 * @param {Object} data
 * @param {String} data.taskid  -任务pk_id
 * @param {String} data.gatewayid  -网关gatewayID
 * @param {String|Number} data.dataversion -任务dataversion
 * @return {*}
 */
export const switchUcgService = function (data, header) {
    return getInvokeService(
        {
            method: "POST",
            path: `/diwork/erpdata/task/switchgateway/${data.taskid}`,
            header,
        },
        data
    );
};

/**
 * 停止任务
 * @param {Object} data
 * @param {String} data.taskId
 * @return {*}
 */
export const stopTaskService = function (data, header) {
    return getInvokeService({
        method: "POST",
        path: `/diwork/erpdata/task/stop/${data.taskId}`,
        header,
    });
};

/**
 * 删除任务
 * @param {Object} data
 * @param {Array} data.ids
 * @return {*}
 */
export const deleteTaskService = function (data, header) {
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/erpdata/task/delete",
            header,
        },
        data
    );
};

/**
 *
 * @param {Object} data
 * @param {String} data.taskId
 * @param {String} data.dataversion
 * @param {String} data.sqlwhere
 * @param {String} data.increment
 * @return {*}
 */
export const setSqlWhereService = function (data, header) {
    let { taskId, ..._data } = data;
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/erpdata/task/update/" + taskId,
            header,
        },
        _data
    );
};

/**
 * 获取视图详情接口
 * @param {Object} data
 * @param {String} data.tableview -dataItem.dataview.tableview
 * @return {Promise<unknown>}
 */
export const getViewInfoService = function (data, header) {
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/erpdata/dataview/get",
            header,
        },
        data
    );
};

/**
 * 保存视图详情修改
 * @param {Object} data
 * @param {String} data.id -视图 pk_id
 * @param {String} data.dataversion -视图 dataversion
 * @param {String} data.tableview -视图 tableview
 * @param {String} data.sqlview -视图 sqlview
 * @param {String} data.datapkname -视图 datapkname
 * @param {String} data.pagesize -视图 pagesize
 * @return {Promise<unknown>}
 */
export const saveViewInfoService = function (data, header) {
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/erpdata/dataview/add",
            header,
        },
        data
    );
};

/**
 * 获取nc版本列表
 * @return {Promise<unknown>}
 */
export const getNcVersionsService = function (header) {
    return getInvokeService({
        method: "GET",
        path: "/diwork/erpdata/task/version/nc",
        header,
    });
};

/***
 * 初始化任务
 * @param {Object} data
 * @param {String} data.initType=[u9|u8|u8cloud|nc|nccloud|outsideerp|tplus]
 * @param {String=} data.viewversion -nc版本 code
 * @param {Boolean=} data.ifmdm -是否mdm
 * @return {Promise<unknown>}
 */
export const initTaskService = function (data, header) {
    let { initType, ..._data } = data;
    return getInvokeService(
        {
            method: "POST",
            path: `/diwork/erpdata/task/init/${initType}`,
            header,
        },
        _data
    );
};

/**
 * 执行任务同步（是否按条件）数据
 * @param {Object} data
 * @param {Boolean} data.enableWhere -是否按条件同步
 * @param {Array} data.ids -执行的任务id数组
 * @return {Promise<unknown>}
 */
export const executeTaskService = function (data, header) {
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/erpdata/task/syncdata/batch",
            header,
        },
        data.ids,
        { enableWhere: data.enableWhere }
    );
};

/**
 * 获取任务日志
 * @param {Object} data
 * @param {String} data.taskId
 * @param {String|Number=} data.status=[0失败1成功] -日志类型 不传查所有
 * @param {String|Number} data.pageNo
 * @param {String|Number} data.pageSize
 * @return {Promise<unknown>}
 */
export const getTaskLogService = function (data, header) {
    let { taskId, ..._data } = data;
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/erpdata/task/log/" + taskId,
            header,
        },
        _data
    );
};

/**
 * 创建定时任务
 * @param {Object} data
 * @param {String} data.taskId
 * @param {String} data.cronexpression
 * @return {Promise<unknown>}
 */
export const createTimingTaskService = function (data, header) {
    let { taskId, ..._data } = data;
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/erpdata/task/createjob/" + taskId,
            header,
        },
        _data
    );
};

/**
 * 切换任务同步机制
 * @param {Object} data
 * @param {String} data.taskId
 * @param {Boolean} data.excluded
 * @return {*}
 */
export const toggleTaskSyncModeService = function (data, header) {
    let { taskId, ..._data } = data;
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/erpdata/task/exclude/" + taskId,
            header,
        },
        _data
    );
};

/**
 * 推送失败数据
 * @param {Object} data
 * @param {String} data.tableview -taskItem.dataview.tableview
 * @return {*}
 */
export const pushFailDataService = function (data, header) {
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/erpdata/task/pushRecentData",
            header,
        },
        data
    );
};

/**
 * 获取数据映射列表
 * @param {Object} data
 * * @param {String} data.tableview -task.dataview.tableview
 * @param {String|Number} data.pageNo
 * @param {String|Number} data.pageSize
 * @param {String} data.searchKey=[saas|erp|code|name]
 * @param {String} data.value
 * @return {*}
 */
export const getDataMapListService = function (data, header) {
    let { tableview, ..._data } = data;
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/erpdata/datamapping/" + tableview,
            header,
        },
        _data
    );
};

export const geSeeSqlService = function (data, header) {
    let { tableview, ..._data } = data;
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/erpdata/queryAllVersions/" + tableview,
            header,
        },
        _data
    );
};

/**
 * 获取数据补偿列表
 * @param {Object} data
 * * @param {String} data.tableview -task.dataview.tableview
 * @param {String|Number} data.pageNo
 * @param {String|Number} data.pageSize
 * @param {String} data.searchKey=[saas|erp|code|name]
 * @param {String} data.value
 * @return {*}
 */
export const getDataMakeUpListService = function (data, header) {
    let { tableview, ..._data } = data;
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/erpdata/getErrorData/" + tableview,
            header,
        },
        _data
    );
};
/***
 * 保存数据主键映射
 * @param {Object} data
 * @param {String} data.tableview -task.dataview.tableview
 * @param {Array} data.dataMaps
 * @example
 [{
     "erppk": '',
                    "cloudpk": ''
                }]
 * @return {*}
 */
export const saveDataMapService = function (data, param) {
    let { tableview, dataMaps } = data;
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/erpdata/task/dataview/addIDMapping/" + tableview,
        },
        dataMaps,
        param
    );
};

/***
 * 查看出厂SQL
 * @param {Object} data
 * @param {String} data.tableview -task.dataview.tableview
 */
export const restoreSQLService = function (data, header) {
    let { tableview } = data;
    return getInvokeService({
        method: "GET",
        path: "/diwork/erpdata/dataview/revert?tableview=" + tableview,
        header,
    });
};

/***
 * 启用或停用任务
 * @param {Object} data
 * @param {String} data.id
 * @param {Boolean} data.enable
 */
export const changeEnableService = function (data, header) {
    let { id, enable } = data;
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/erpdata/task/changeEnable/" + id,
            header,
        },
        { enable }
    );
};
// /gwmanage/gwportal/diwork/erpdata/task/checkRunnable/{{id}}
//检查接口是否可运行
export const checkRunnable = function (id, header) {
    let service = defineService({
        method: "GET",
        path: "/diwork/erpdata/task/checkRunnable/" + id,
        header,
    });

    return service.invoke();
};

//排查执行  查询参数接口

export const getCheckConditionService = function (taskId, header) {
    let service = defineService({
        method: "GET",
        path: `/diwork/erpdata/task/getSchemeTaskParams/${taskId}`,
        header,
    });

    return service.invoke();
};

//排查执行  检测

export const checkService = function (data, header) {
    let service = defineService({
        method: "POST",
        path: `/diwork/erpdata/task/couldDebug`,
        header,
    });

    return service.invoke(data);
};
//排查执行  执行

export const checkOkService = function (data, header) {
    let service = defineService({
        method: "POST",
        path: `/diwork/erpdata/task/syncdata/batch?enableWhere=false&makeUp=false&debug=true`,
        header,
    });

    return service.invoke(data);
};
//排查执行  排查日志

export const getCheckLogService = function (id, header) {
    let service = defineService({
        method: "GET",
        path: `/diwork/erpdata/task/getDebugLog/${id}`,
        header,
    });

    return service.invoke();
};
export const getDataColumnsService = function (taskId, header) {
    return getInvokeService({
        method: "GET",
        path: "/diwork/erpdata/task/dataview/init/column/" + taskId,
        header,
    });
};
export const getRealColumnService = function (data, header) {
    let service = defineService({
        method: "POST",
        path: `/diwork/erpdata/task/getRealColumn`,
        header,
    });

    return service.invoke(data);
};

export const handleGetDiagramService = function (data, header) {
    let service = defineService({
        method: "POST",
        path: `/gwmanage/gwportal/diwork/errorReason/getErrorReason`,
        header,
    });

    return service.invoke(data);
};

export const primaryKeyMappingService = function (data, header) {
    const formdata = new FormData();
    formdata.append("file", data.file);
    formdata.append("taskCode", data.taskCode);
    formdata.append("callingMethod", data.callingMethod);
    let service = defineService({
        method: "POST",
        path: `/gwportal/diwork/integscheme/excel/import/primaryKeyMapping`,
        header: { "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundary6Jdq1PXI3LkQLVe0", ...header },
        timeout: 30000,
    });

    return service.invoke(formdata);
};

export const showImportBtnService = function (tableview, data) {
    let service = defineService({
        method: "GET",
        path: `/gwmanage/gwportal/diwork/erpdata/datamapping/exist/excelbutton/${tableview}`,
    });
    return service.invoke(data);
};

export const getConnectorListService = function (data, header) {
    let service = defineService({
        method: "GET",
        path: `/gwmanage/gwportal/diwork/erpdata/task/gateWayId`,
        header,
        showLoading: false,
    });

    return service.invoke();
};
export const deleteDataMapService = function (data, viewtag, systemData) {
    let service = defineService({
        method: "POST",
        path: `/gwportal/diwork/erpdata/datamapping/delete/${viewtag}`,
    });

    return service.invoke(data, systemData);
};
/**
 * 批量删除映射关系
 */
export const batchDelMapService = function (data, systemData) {
    let { taskCode, ids } = data;
    return getInvokeService(
        {
            method: "POST",
            path: "/gwportal/diwork/erpdata/datamapping/batch/delete/" + taskCode,
        },
        ids,
        systemData
    );
};
/**
 * 检测数据同步任务互斥状态
 */
export const batchCheckEnableService = function (data, header) {
    return getInvokeService(
        {
            method: "POST",
            path: "/gwportal/diwork/erpdata/task/batchCheckEnable",
            header,
        },
        data
    );
};
export const batchChangeEnableService = function (data, header) {
    return getInvokeService(
        {
            method: "POST",
            path: "/gwportal/diwork/erpdata/task/batchChangeEnable",
            header,
        },
        data
    );
};
export const getNewSqlService = function (data, header) {
    let service = defineService({
        method: "POST",
        path: `/gwmanage/gwportal/diwork/erpdata/dataview/origin/get`,
        header,
    });

    return service.invoke(data);
};
export const resetNewSqlService = function (data, header) {
    let service = defineService({
        method: "POST",
        path: `/gwmanage/gwportal/diwork/erpdata/dataview/tenant/reset`,
        header,
    });

    return service.invoke(data);
};
/**
 * 查询集成方案数据筛选条件参数：
 * @param {*} schemeCode
 * @param {*} header
 * @returns
 */
export const queryApiParamDefService = function (param, header) {
    let service = defineService({
        method: "GET",
        path: `/gwmanage/gwportal/diwork/integrated/object/operation/queryApiParamDef`,
        header,
    });
    return service.invoke({}, param);
};
/**
 * 立即执行
 * @param {*} data
 * @param {*} header
 * @returns
 */
export const immediateExecuteService = function (data, params, header) {
    let service = defineService({
        method: "POST",
        path: `/gwmanage/gwportal/diwork/erpdata/task/syncdata/batch`,
        header,
    });
    return service.invoke(data, params);
};
/**
 * 查询excel导入进度
 * @param {*} viewtag
 * @returns
 */
export const queryProgressService = function (viewtag) {
    let service = defineService({
        method: "POST",
        path: `/gwportal/diwork/erpdata/datamapping/excel/import/${viewtag}/progress`,
        showLoading: false,
    });
    return service.invoke();
};

export const downloadExcelTempService = function (params) {
    let service = defineService({
        method: "GET",
        path: `/gwportal/diwork/erpdata/datamapping/excel/download`,
        showLoading: false,
        responseType: "blob",
    });
    return service.invoke(params);
};
export const downloadExcelErrorService = function (schemeCode) {
    let service = defineService({
        method: "GET",
        path: `/gwportal/diwork/erpdata/datamapping/excel/import/downloadFailData?viewtag=${schemeCode}`,
        showLoading: false,
        responseType: "blob",
    });
    return service.invoke();
};
// 获取左侧树
export const getTreeDataService = async () => {
    return getInvokeService({
        method: "POST",
        path: `/iuap-ipaas-base/console/app/queryApps`,
        showLoading: false,
    });
};
// 来源和目标系统
export const getSystemListService = async () => {
    return getInvokeService({
        method: "POST",
        path: `/iuap-ipaas-base/baseconsole/integratesys/queryTenantSyslistWithConnectionConfig`,
        showLoading: false,
    });
};
