import React, { Component } from "react";
import IndexView from "./components/IndexView";
import { inject, observer } from "mobx-react";
import Store, { storeKey } from "./store";
import { storeKey as commonStoreKey } from "../store";
import mixCore from "core";
import { QueryContext } from "../index.js";

mixCore.addStore({
    storeKey: storeKey,
    store: new Store(),
});

@inject((rootStore) => {
    let ownerStore = rootStore[storeKey];
    let commonStore = rootStore[commonStoreKey];
    return {
        commonState: commonStore.toJS(),
        ownerState: ownerStore.toJS(),
        ownerStore: ownerStore,
        appContext: rootStore.appContext,
    };
})
@observer
class Container extends Component {
    constructor(props) {
        super(props);
    }

    componentDidMount() {}

    render() {
        return (
            <QueryContext.Consumer>
                {(context) => {
                    return <IndexView {...this.props} queryContext={context} />;
                }}
            </QueryContext.Consumer>
        );
    }
}
export default Container;
