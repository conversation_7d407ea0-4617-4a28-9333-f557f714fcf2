import { observable, computed, action, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { autoServiceMessage } from "utils/service";
import * as ownerService from "./service";
import { batchSyncTaskService } from "services/taskServices";
import { initTaskTypes } from "../../common/constants";
import { defaultPagination, selfPageChange } from "utils/pageListUtils";
import { createEnum } from "constants/utils";
import { Success, Error } from "utils/feedback";
import { defaultListMap } from "utils/pageListUtils";

export const initState = {
    taskList: [],
    getRealColumnContent: "",
    selectedTask: null,
    selectedTaskGateway: [],
    pagination: {
        ...defaultPagination,
        dataNumSelect: [10, 20, 50, 100, 200],
        pageSize: 20,
    },
    dataSource: { ...defaultListMap },
    columns: {},
    refCols: [],
    queryCols: [],
    resultArr2: {},
    serviceCodeDiwork: "kfljsjtbrw",
    tenantSupport: false,
    connectorsList: [],
    newSqlObj: "",
    selectedKeys: [],
    isRoot: undefined,
    isScheme: undefined,
    expandedKeys: [],
    autoExpandParent: false,
    keywords: '',
    treeLoading: false, 
    treeSource: [
        {
            id: "all",
            key: 'all',
            name: lang.templateByUuid("UID:P_UBL-FE_1F7D965C05600014","全部") /* "全部" */,
            children: [],
            parentId: "",
            level: 0,
        }]
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;
    // @observable tenantSuppor = false;

    @computed get actionDisabled() {
        return !!this.state.selectedTask;
    }

    @action
    revertState() {
        this.state = { ...initState };
    }
    // @action
    setTenantSupport(bol) {
        this.state.tenantSupport = bol;
    }


    onTaskGridSelect = (task) => {
        this.state.selectedTask = task;
    };
    onTaskGridSelectedGateway = (task) => {
        console.log(task.gatewayid.match(/\[(.+?)\]/gi));
        //如果连接配置有中括号，则取出中括号中的值，给弹窗中连接配置做选中回显，如果没有中括号，则不回显选中
        // let aa=this.toJS([{ code:task &&  (task.gatewayid.match(/\[(.+?)\]/gi)[0]).substring(1,(task.gatewayid.match(/\[(.+?)\]/gi)[0]).length-1), name: task && task.gatewayid }])
        this.state.selectedTaskGateway = [
            {
                code:
                    task &&
                    (task.gatewayid.match(/\[(.+?)\]/gi)
                        ? task.gatewayid.match(/\[(.+?)\]/gi)[0].substring(1, task.gatewayid.match(/\[(.+?)\]/gi)[0].length - 1)
                        : task.gatewayid),
                name: task && task.gatewayid,
            },
        ];
    };
    getDataSource = async (_query = {}) => {
        const {isScheme, isRoot, selectedKeys} = this.state
        let requestData = {
            ..._query,
            categoryId: selectedKeys[0] === "all" ? "" : selectedKeys[0],
            isScheme,
            isRoot,
        };
        this.getPagesListFunc({
            service: ownerService.getTaskListService,
            requestData,
            dataKey: "dataSource",
            paginationKey: "pagination",
            onPageChange: this.getDataSource,
        });
    };
    
    switchUcg = async (task, ucg, updateAll) => {
        let requestData = {
            taskid: task.pk_id,
            gatewayid: ucg.gatewayID,
            dataversion: task.dataversion,
            updateAll: updateAll,
        };
        let res = await autoServiceMessage({
            service: ownerService.switchUcgService(requestData, { serviceCode: this.state.serviceCodeDiwork }),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803E9", "切换连接成功", undefined, {
                returnStr: true,
            }) /* "切换连接成功" */,
        });
        if (res) {
            // this.getDataSource();
        }
    };
 

    stopTask = async (selectedTask) => {
        let res = await autoServiceMessage({
            service: ownerService.stopTaskService({ taskId: selectedTask.pk_id }, { serviceCode: this.state.serviceCodeDiwork }),
            success:
                selectedTask.taskstatus == 8
                    ? lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803E5", "重置成功", undefined, {
                          returnStr: true,
                      }) /* "重置成功" */
                    : lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803E4", "任务会在执行完本次分页同步后停止", undefined, {
                          returnStr: true,
                      }) /* "任务会在执行完本次分页同步后停止" */,
        });
        return res;
    };

    deleteTask = async (selectedTask) => {
        let ids = [selectedTask.pk_id];
        let res = await autoServiceMessage({
            service: ownerService.deleteTaskService({ ids }, { serviceCode: this.state.serviceCodeDiwork }),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803EE", "数据同步任务已删除", undefined, {
                returnStr: true,
            }) /* "数据同步任务已删除" */,
        });
        return res
    };
    // 任务列表查询连接配置列表
    getConnectorList = async (selectedTask) => {
        let res = await autoServiceMessage({
            service: ownerService.getConnectorListService({}, { serviceCode: this.state.serviceCodeDiwork }),
        });
        if (res) {
            this.state.connectorsList = res.data || [];
        }
    };

    setSqlWhere = async (data) => {
        let selectedTask = this.toJS(this.state.selectedTask);
        data.taskId = selectedTask.pk_id;
        data.dataversion = selectedTask.dataversion;
        let res = await autoServiceMessage({
            service: ownerService.setSqlWhereService(data, { serviceCode: this.state.serviceCodeDiwork }),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803EC", "保存成功", undefined, {
                returnStr: true,
            }) /* "保存成功" */,
        });
        return res;
    };

    initTask = async (data) => {
        let initName = initTaskTypes.getNameByCode(data.initType);
        let temParams = { initName };
        let res = await autoServiceMessage({
            service: ownerService.initTaskService(data, { serviceCode: this.state.serviceCodeDiwork }),
        });
        if (res) {
            this.getDataSource();
        }
        return res;
    };


    pushFailData = (selectedTask) => {
        if (selectedTask.dataview.tableview) {
            autoServiceMessage({
                service: ownerService.pushFailDataService(
                    {
                        tableview: selectedTask.dataview.tableview,
                    },
                    { serviceCode: this.state.serviceCodeDiwork }
                ),
                success: (res) => {
                    Success(res.msg);
                },
            });
        } else {
            Error(
                lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803ED", "该同步任务没有视图标签", undefined, {
                    returnStr: true,
                }) /* "该同步任务没有视图标签" */
            );
        }
    };

    //改变同步任务状态
    changeEnable = async (param) => {
        let res = await autoServiceMessage({
            service: ownerService.changeEnableService(
                {
                    id: param.id,
                    enable: param.enable,
                },
                { serviceCode: this.state.serviceCodeDiwork }
            ),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803E6", "操作成功！", undefined, {
                returnStr: true,
            }) /* "操作成功！" */,
        });

     return res
    };
    // 校验任务是否可执行
    checkRunnable = async (param) => {
        return await autoServiceMessage({
            service: ownerService.checkRunnable(param),
            // success: window.lang.template(commonText.operateSuccess)
        });
    };
    //排查执行  检测接口
    checkFunc = async (param, cb) => {
        let res = await autoServiceMessage({
            service: ownerService.checkService(param, { serviceCode: this.state.serviceCodeDiwork }),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803E7", "检测成功", undefined, {
                returnStr: true,
            }) /* "检测成功" */,
        });
        if (res && res.status == 1) {
            cb();
        }
    };
    //排查执行  执行接口
    checkOkFunc = async (param) => {
        return await autoServiceMessage({
            service: ownerService.checkOkService(param, { serviceCode: this.state.serviceCodeDiwork }),
            // success: window.lang.template(commonText.operateSuccess)
        });
    };
    getDataColumns = async (taskId, tableView) => {
        let res = await autoServiceMessage({
            service: ownerService.getDataColumnsService(taskId, { serviceCode: this.state.serviceCodeDiwork }),
        });
        if (res) {
            let { pkField, columns, refCols, queryCols } = res;
            let resultArr2 = [];
            for (let key in columns) {
                if (["cloudpk", "dataenable", "dataversion", "callbackid"].indexOf(key) == -1) {
                    let obj = {};
                    obj.name = columns[key];
                    obj.code = key;
                    resultArr2.push(obj);
                }
            }
            resultArr2 = createEnum(resultArr2);
            this.changeState({
                pkField,
                columns,
                refCols,
                queryCols,
                resultArr2,
            });
            resultArr2.selectData.length != 0 &&
                (await this.getRealColumn({
                    field: resultArr2.selectData[0].value || "",
                    tableView,
                }));
        }
        return res;
    };
    getRealColumn = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.getRealColumnService(data, { serviceCode: this.state.serviceCodeDiwork }),
        });
        if (res) {
            this.changeState({
                getRealColumnContent: res.data.content,
            });
        }
        return res;
    };
    getNewSql = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.getNewSqlService(data, { serviceCode: this.state.serviceCodeDiwork }),
        });
        if (res) {
            this.changeState({
                newSqlObj: res.data,
            });
        }
        return res;
    };
    resetNewSql = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.resetNewSqlService(data, { serviceCode: this.state.serviceCodeDiwork }),
        });
        if (res) {
        }
        return res;
    };
    loopGetExpandKeys = (keyList, sourceList) => {
        for (let i = 0; i < sourceList.length; i++) {
            keyList.push(sourceList[i].id);
            if (sourceList[i].children && sourceList[i].children.length > 0) {
                this.loopGetExpandKeys(keyList, sourceList[i].children);
            }
        }
    };
    setSelectedKeys = (value, isRoot, isScheme) => {
        this.changeState({
            selectedKeys: value,
            isRoot,
            isScheme
        });
    };
    // 递归处理树数据，为每个节点添加composeTitle属性（通过中划线拼接父节点名称）
    processTreeData = (treeNodes: any[], parentTitle = "", isSchemeApp = false) => {
        if (!treeNodes || !treeNodes.length) return [];

        return treeNodes.map((node) => {
            const currentTitle = parentTitle ? `${parentTitle}-${node.name}` : node.name;

            const children =
                node.children && node.children.length > 0
                    ? this.processTreeData(node.children, currentTitle, node.isSchemeApp)
                    : node.children;
            const isSchemeAppVar = typeof node.isSchemeApp === "undefined" ? isSchemeApp : node.isSchemeApp;
            return {
                ...node,
                children,
                composeTitle: currentTitle,
                isSchemeApp: isSchemeAppVar,
                key: isSchemeAppVar ? node.code : node.id,
            };
        });
    };
    // 左侧分类树
    getTreeData = async () => {
        this.changeState({
            treeLoading: true
        });
        
        const res = await autoServiceMessage({
            service: ownerService.getTreeDataService(),
        });
        
        if (res && res.data) {
            const processedData = this.processTreeData(res.data);
            this.changeState({
                treeSource: [{...this.state.treeSource[0], children: processedData}],
                treeLoading: false 
            });
            if (this.state.selectedKeys.length === 0) {
                const tempList = ["all"];
                // this.loopGetExpandKeys(tempList, res.data);
                this.changeState({
                    // expandedKeys: tempList,
                    selectedKeys: ["all"]
                });
            }
        } else {
            this.changeState({
                treeLoading: false
            });
        }
        return res
    };
    setKeywords = (value: string) => {
        const expandedTempKeys = [];
        const loop = (list, tempVal) => {
            for (let i = 0; i < list.length; i++) {
                if (list[i].name.indexOf(tempVal) > -1) {
                    expandedTempKeys.push(list[i].id);
                }
                if (list[i].children && list[i].children.length > 0) {
                    loop(list[i].children, tempVal);
                }
            }
        };
        loop(this.state.treeSource, value);
        const uniqueExpandedKeys = [];
        if (value !== "" && value !== undefined) {
            expandedTempKeys.forEach((item) => {
                if (item && uniqueExpandedKeys.indexOf(item) === -1) {
                    uniqueExpandedKeys.push(item);
                }
            });

            this.changeState({
                autoExpandParent: true,
                expandedKeys: uniqueExpandedKeys
            })
        } else {
            // 恢复成全部展开
            uniqueExpandedKeys.push("0");
            this.loopGetExpandKeys(uniqueExpandedKeys, this.state.treeSource);
            this.changeState({
                autoExpandParent: true,
                expandedKeys: uniqueExpandedKeys
            })
        }
        this.changeState({
            keywords: value,
        });
    };
}
/**
 * 根据具体模块修改 storeKey 为保证唯一性
 * storeKey 由 模块文件夹名称 + 页面名称 + 'Store' 小驼峰
 *
 * storeKey是作为当前 Store 的唯一键， storeKey与Store的实例会在 rootStore或者StoreMap中以 键值对的 形式存在
 * 1. 用于在模块的 storeMap（模块routes/store.js）中注册并初始化当前Store
 * 2. 用于在container的 inject 中关联当前Store的实例时，从模块的storeMap或者rootStore中获取对应store实例
 * */
export const storeKey = "dataSyncTaskListStore";

export default Store;
