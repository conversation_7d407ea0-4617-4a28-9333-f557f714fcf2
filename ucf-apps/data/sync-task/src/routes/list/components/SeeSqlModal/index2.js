import React, { Component, Fragment } from "react";
import { InputGroup, Dropdown, Button, Message, Modal as ModalCom } from "components/TinperBee";
import { Clipboard } from "@tinper/next-ui";
import Grid from "components/TinperBee/Grid";
import { inject, observer } from "mobx-react";
import Store from "./store";
import FieldWrap from "components/RowField/FieldWrap";
import commonText from "constants/commonText";

const DataMapStore = new Store();

@inject((rootStore) => {
    return {
        ownerState: DataMapStore.toJS(),
        ownerStore: DataMapStore,
    };
})
@observer
class DataMapModal extends Component {
    constructor(props) {
        super(props);
        this.state = {
            show: false,
        };
    }

    // componentWillReceiveProps (nextProps) {
    //   // console.log(nextProps)
    //   // console.log(this.props)
    //   if (nextProps.show !== this.props.show) {
    //     this.setState({show: nextProps.show})
    //     let { ownerStore } = nextProps;
    //     if (nextProps.show) {
    //       ownerStore.setTableView(nextProps.taskInfo.dataview.tableview);
    //       ownerStore.getDataSource()
    //     }else {
    //       ownerStore.init()
    //     }
    //   }
    // }
    componentDidMount(nextProps) {
        let { ownerStore, ownerState, selectedTask } = this.props;

        ownerStore.setTableView(selectedTask.dataview.tableview);
        ownerStore.getDataSource();

        //ownerStore.init()
    }

    restoreSQL = (sqlView) => {
        ModalCom.confirm({
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418040B", "历史SQL") /* "历史SQL" */,
            content: (
                <p className="copy-sql" style={{ wordBreak: "break-word" }}>
                    {sqlView}
                </p>
            ),
            okText: (
                <Clipboard
                    action="copy"
                    fieldid="ublinker-routes-list-components-SeeSqlModal-index2-4403084-Clipboard"
                    // target=".copy-sql"
                    text={sqlView}
                    success={() => {
                        Message.create({ content: lang.templateByUuid("UID:P_UBL-FE_1B55611A05A0000C", "复制成功") /* "复制成功" */, color: "success" });
                    }}
                >
                    <span style={{ color: "white" }}>{lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418043E", "复制") /* "复制" */}</span>
                </Clipboard>
            ),
            onOk: () => Promise.reject(),
            cancelText: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180407", "关闭") /* "关闭" */,
            confirmType: "one",
        });
        // }
    };
    columns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180408", "修改时间") /* "修改时间" */,
            dataIndex: "creationtime",
            width: 180,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418040A", "修改人") /* "修改人" */,
            dataIndex: "creator",
            width: 180,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418040D", "SQL语句") /* "SQL语句" */,
            dataIndex: "sqlView",
            width: 400,
        },
    ];
    hoverContent = (record) => {
        return (
            <Fragment>
                <Button
                    fieldid="ublinker-routes-list-components-SeeSqlModal-index2-9410921-Button"
                    {...Grid.hoverButtonPorps}
                    onClick={this.restoreSQL.bind(null, record.sqlView)}
                >
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180409", "查看") /* "查看" */}
                </Button>
                <Clipboard fieldid="8070e56f-6a51-4679-81d2-348f3461a1dd" action="copy" text={record.sqlView}>
                    <Button fieldid="24fd04ea-61ef-40ed-8a70-1e97c035e6cb" {...Grid.hoverButtonPorps}>
                        {lang.templateByUuid("UID:P_UBL-FE_1B55611A05A0000D", "复制sql") /* "复制sql" */}
                    </Button>
                </Clipboard>
            </Fragment>
        );
    };
    render() {
        let { ownerState, onCancel } = this.props;
        let { dataSource, pagination, hasEdit } = ownerState;
        if (pagination) {
            pagination.disabled = hasEdit;
        }
        return (
            <div className="">
                <Grid
                    fieldid="ublinker-routes-list-components-SeeSqlModal-index2-9844180-Grid"
                    columns={this.columns}
                    data={dataSource.list}
                    pagination={pagination}
                    dataKey={"pk_id"}
                    hoverContent={this.hoverContent}
                    scroll={{ y: 388 }}
                />
            </div>
        );
    }
}

export default DataMapModal;
