import { observable, computed, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { defaultListMap } from "utils/pageListUtils";
import * as ownerService from "../../service";
import { getUuid } from "utils/index";
import { autoServiceMessage } from "utils/service";
import { codeReg, codeMessage } from "utils/regExp";
import commonText from "constants/commonText";

const initState = {
    dataSource: { ...defaultListMap },
    tableview: "",
    searchKey: "saas",
    value: "",
    editRowInfo: null,
    editRowError: {
        pk_id: "",
        cloudpk: "",
    },
    hasEdit: false,
    serviceCodeDiwork: "kfljsjtbrw",
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }

    @observable state = initState;

    init = () => {
        this.state = initState;
    };

    setTableView = (tableview) => {
        this.state.tableview = tableview;
    };

    changeSearchKey = (key) => {
        this.state.searchKey = key;
    };

    changeSearchValue = (value) => {
        this.state.value = value;
    };

    getDataSource = (reqData) => {
        let { tableview, searchKey, value } = this.state;
        let requestData = {
            tableview,
            //  searchKey,
            //   value,
            ...reqData,
        };
        this.getPagesListFunc({
            service: ownerService.geSeeSqlService,
            requestData,
            dataKey: "dataSource",
            header: { serviceCode: this.state.serviceCodeDiwork },
        });
    };

    changeEditRowInfo = (field, value) => {
        this.state.editRowError[field] = "";
        this.state.editRowInfo[field] = value;
    };

    setEdit = (type) => {
        let editRowInfo = null,
            hasEdit = false;
        if (type === "add") {
            editRowInfo = {
                pk_id: "",
                cloudpk: "",
                pk_org: "",
                code: "",
                name: "",
                isEdit: true,
            };
            hasEdit = true;
            this.state.dataSource.list.unshift(editRowInfo);
        } else {
            this.state.dataSource.list.shift();
        }
        this.changeState({ editRowInfo, hasEdit, editRowError: initState.editRowError });
    };

    saveDataMap = async () => {
        let { tableview, editRowInfo } = this.state;
        let { pk_id, cloudpk } = editRowInfo;
        let hasError = false;
        if (pk_id === "") {
            hasError = true;
            this.state.editRowError.pk_id = lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180381", "请输入ERP主键") /* "请输入ERP主键" */;
        } else if (!codeReg.test(pk_id)) {
            hasError = true;
            this.state.editRowError.pk_id =
                lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418037D", "ERP主键", undefined, {
                    returnStr: true,
                }) + codeMessage;
        }

        if (cloudpk === "") {
            hasError = true;
            this.state.editRowError.cloudpk = lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418037E", "请输入Saas主键") /* "请输入Saas主键" */;
        } else if (!codeReg.test(cloudpk)) {
            hasError = true;
            this.state.editRowError.cloudpk =
                lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180380", "Saas主键", undefined, {
                    returnStr: true,
                }) + codeMessage;
        }

        if (!hasError) {
            let requestData = {
                tableview,
                dataMaps: [
                    {
                        erppk: editRowInfo.pk_id,
                        cloudpk: editRowInfo.cloudpk,
                    },
                ],
            };
            let res = await autoServiceMessage({
                service: ownerService.saveDataMapService(requestData, { serviceCode: this.state.serviceCodeDiwork }),
                success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418037F", "保存成功", undefined, {
                    returnStr: true,
                }) /* "保存成功" */,
            });
            if (res) {
                this.getDataSource();
                this.setEdit("edited");
            }
        }
    };
}

/**
 * 根据具体模块修改 storeKey 为保证唯一性
 * storeKey 由 模块文件夹名称 + 页面名称 + 'Store' 小驼峰
 *
 * storeKey是作为当前 Store 的唯一键， storeKey与Store的实例会在 rootStore或者StoreMap中以 键值对的 形式存在
 * 1. 用于在模块的 storeMap（模块routes/store.js）中注册并初始化当前Store
 * 2. 用于在container的 inject 中关联当前Store的实例时，从模块的storeMap或者rootStore中获取对应store实例
 * */
export const storeKey = "dataSyncTaskDataMapStore";

export default Store;
