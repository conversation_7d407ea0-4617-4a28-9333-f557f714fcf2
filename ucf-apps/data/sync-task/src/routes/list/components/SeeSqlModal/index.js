import React, { Component, Fragment } from "react";
import { InputGroup, Dropdown, Button, FormControl, Modal as ModalCom } from "components/TinperBee";
import Menu from "components/TinperBee/Menu";
import Modal from "components/TinperBee/Modal";
import Clipboard from "components/TinperBee/Clipboard";
import Grid from "components/TinperBee/Grid";
import { inject, observer } from "mobx-react";
import Store from "./store";
// import {dataMapSearchKeys} from '../../../../common/constants'
import { createEnum } from "constants/utils";
import FieldWrap from "components/RowField/FieldWrap";
import commonText from "constants/commonText";

const DataMapStore = new Store();

@inject((rootStore) => {
    return {
        ownerState: DataMapStore.toJS(),
        ownerStore: DataMapStore,
    };
})
@observer
class DataMapModal extends Component {
    constructor(props) {
        super(props);
        this.state = {
            show: false,
        };
    }

    componentDidUpdate(prevProps) {
        // console.log(prevProps)
        // console.log(this.props)
        if (this.props.show !== prevProps.show) {
            this.setState({ show: this.props.show });
            let { ownerStore } = this.props;
            if (this.props.show) {
                ownerStore.setTableView(this.props.taskInfo.dataview.tableview);
                ownerStore.getDataSource();
            } else {
                ownerStore.init();
            }
        }
    }
    dataMapSearchKeys = createEnum([
        {
            code: "erp",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804EA", "来源系统主键") /* "来源系统主键" */,
        },
        {
            code: "saas",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804EC", "目标系统主键") /* "目标系统主键" */,
        },
        {
            code: "code",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804ED", "编码") /* "编码" */,
        },
        {
            code: "name",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804EF", "名称") /* "名称" */,
        },
    ]);

    // handleSearchKeyChange = (node) => {
    //   let { key } = node;
    //   this.props.ownerStore.changeSearchKey(key)
    // }

    // getSearchKeyMenu = (searchKey) => {
    //   return (
    //     <Menu fieldid="ublinker-routes-list-components-SeeSqlModal-index-6060269-Menu"
    //       selectedKeys={[searchKey]}
    //       onSelect={this.handleSearchKeyChange}
    //     >
    //       {dataMapSearchKeys.selectData.map((item) => {
    //         let {key, value} = item;
    //         return (
    //           <Menu.Item fieldid="UCG-FE-routes-list-components-SeeSqlModal-index-4528872-Menu.Item"
    //             key={value}
    //           >{key}</Menu.Item>
    //         )
    //       })}
    //     </Menu>
    //   )
    // }

    // handleSearch = () => {
    //   this.props.ownerStore.getDataSource({
    //     pageNo: 1
    //   })
    // }

    // handleAddMap = () => {
    //   this.props.ownerStore.setEdit('add');
    // }

    renderHeader = () => {
        let {
            ownerState: { searchKey, hasEdit },
            ownerStore,
        } = this.props;
        let searchName = this.dataMapSearchKeys.getNameByCode(searchKey);
        return (
            <div className="ucg-pad-20 clearfix" ref={(node) => (this.getGridHeaderNode = node)}>
                <InputGroup fieldid="ublinker-routes-list-components-SeeSqlModal-index-3117638-InputGroup" style={{ width: 260, float: "left" }}>
                    <InputGroup.Button>
                        <Dropdown
                            fieldid="ublinker-routes-list-components-SeeSqlModal-index-4029030-Dropdown"
                            trigger={["click"]}
                            overlay={this.getSearchKeyMenu(searchKey)}
                            animation="slide-up"
                            getPopupContainer={() => this.getGridHeaderNode}
                            onSelect={this.changeSearchKey}
                        >
                            <Button fieldid="ublinker-routes-list-components-SeeSqlModal-index-5925521-Button" bordered>
                                {searchName} <i fieldid="ublinker-routes-list-components-SeeSqlModal-index-1499164-i" className="uf uf-arrow-down" />
                            </Button>
                        </Dropdown>
                    </InputGroup.Button>
                    <FormControl
                        fieldid="ublinker-routes-list-components-SeeSqlModal-index-5025563-FormControl"
                        type="search"
                        onChange={ownerStore.changeSearchValue}
                        onSearch={this.handleSearch}
                    />
                    <span></span>
                </InputGroup>

                <Button
                    fieldid="ublinker-routes-list-components-SeeSqlModal-index-2529847-Button"
                    className="ucg-float-r"
                    colors="primary"
                    disabled={hasEdit}
                    onClick={this.handleAddMap}
                >
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803E3", "新增主键映射") /* "新增主键映射" */}
                </Button>
            </div>
        );
    };

    handleChange = (field, value) => {
        this.props.ownerStore.changeEditRowInfo(field, value);
    };

    canEditFields = ["pk_id", "cloudpk"];

    renderCol = (field, value, record) => {
        let { isEdit = false } = record;
        if (isEdit) {
            let {
                ownerState: { editRowInfo, editRowError },
            } = this.props;
            let disabled = !this.canEditFields.includes(field);
            return (
                <FieldWrap message={editRowError[field]}>
                    <FormControl
                        fieldid="ublinker-routes-list-components-SeeSqlModal-index-2884287-FormControl"
                        value={editRowInfo[field]}
                        disabled={disabled}
                        onChange={this.handleChange.bind(null, field)}
                    />
                </FieldWrap>
            );
        } else {
            return value || "-";
        }
    };

    handleCancelEditMap = () => {
        this.props.ownerStore.setEdit("cancel");
    };

    handleSaveMap = () => {
        this.props.ownerStore.saveDataMap();
    };
    restoreSQL = (sqlView) => {
        // let { viewInfo } = this.state;
        // let res = await autoServiceMessage({
        //   service: restoreSQLService({
        //     tableview: viewInfo.tableview
        //   }),
        // });
        // if (res.data) {
        // let { sqlview } = res.data;
        ModalCom.confirm({
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803E2", "历史SQL") /* "历史SQL" */,
            content: (
                <p className="copy-sql" style={{ wordBreak: "break-word" }}>
                    {sqlView}
                </p>
            ),
            okText: (
                <Clipboard fieldid="ublinker-routes-list-components-SeeSqlModal-index-7624308-Clipboard" target=".copy-sql">
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803DB", "复制") /* "复制" */}
                </Clipboard>
            ),
            cancelText: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803DD", "关闭") /* "关闭" */,
            confirmType: "one",
        });
        // }
    };
    columns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803E0", "修改时间") /* "修改时间" */,
            dataIndex: "creationtime",
            width: 180,
            // render: this.renderCol.bind(null, 'pk_id')
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803E1", "修改人") /* "修改人" */,
            dataIndex: "creator",
            width: 180,
            // render: this.renderCol.bind(null, 'cloudpk')
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803DE", "SQL语句") /* "SQL语句" */,
            dataIndex: "sqlView",
            width: 400,
            // render: this.renderCol.bind(null, 'code')
        },
        {
            title: "",
            dataIndex: "$$editCol",
            render: (value, record, index) => {
                let { isEdit } = record;
                return (
                    <Fragment>
                        <Button
                            fieldid="ublinker-routes-list-components-SeeSqlModal-index-8091777-Button"
                            {...Grid.hoverButtonPorps}
                            onClick={this.restoreSQL.bind(null, record.sqlView)}
                        >
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803DC", "查看") /* "查看" */}
                        </Button>
                    </Fragment>
                );
            },
        },
    ];

    render() {
        let { show } = this.state;
        if (show) {
            let { ownerState, onCancel } = this.props;
            let { dataSource, pagination, hasEdit } = ownerState;
            if (pagination) {
                pagination.disabled = hasEdit;
            }
            return (
                <Modal
                    fieldid="ublinker-routes-list-components-SeeSqlModal-index-6188198-Modal"
                    show={show}
                    title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803DF", "查看历史SQL", undefined, {
                        returnStr: true,
                    })}
                    cancelText={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803DD", "关闭") /* "关闭" */}
                    size="xlg"
                    okHide
                    onCancel={onCancel}
                >
                    <div className="">
                        <Grid
                            fieldid="ublinker-routes-list-components-SeeSqlModal-index-3668446-Grid"
                            columns={this.columns}
                            data={dataSource.list}
                            pagination={pagination}
                            dataKey={"pk_id"}
                        />
                    </div>
                </Modal>
            );
        } else {
            return null;
        }
    }
}

export default DataMapModal;
