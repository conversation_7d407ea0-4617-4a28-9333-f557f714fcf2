/*
 * @Author: your name
 * @Date: 2021-04-15 17:40:03
 * @LastEditTime: 2021-06-10 14:32:52
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \ublinker-fe\ucf-apps\data\sync-task\src\routes\list\components\SqlWhereModal\index.js
 */
import React, { Component } from "react";
import Modal from "components/TinperBee/Modal";
import { autoServiceMessage } from "utils/service";
import { getCheckConditionService, getViewInfoService, saveViewInfoService, restoreSQLService } from "../../service";
import { Checkbox, FormControl, Button, InputGroup, Dropdown, Menu } from "components/TinperBee";

class SqlWhereModal extends Component {
    constructor() {
        super();
        this.state = {
            param: "",
            isDo: true,
            searchKey: "",
        };
    }
    async componentDidUpdate(prevProps) {
        // console.log(prevProps)
        // console.log(this.props)
        if (this.props.show !== prevProps.show) {
            this.setState({ isDo: true, searchKey: "" }); //每次打开弹框赋值执行按钮的状态
            if (this.props.show) {
                // console.log('dddddddddddddddddddddd')
                // console.log(this.props.taskInfo)
                let res = await autoServiceMessage({
                    service: getCheckConditionService(this.props.taskInfo.pk_id, { serviceCode: this.props.ownerState.serviceCodeDiwork }),
                });
                if (res) {
                    this.setState({
                        param: res.data,
                    });
                }
                this.props.ownerStore.getDataColumns(this.props.taskInfo.pk_id, this.props.taskInfo.dataview.tableview);
            }
        }
    }

    handleOk = () => {
        let obj = {
            param: btoa(encodeURIComponent(this.state.param)),
            taskIds: [this.props.taskInfo.pk_id],
        };
        this.props.onOk(obj);
    };
    handleCheck = () => {
        let obj = {
            param: btoa(encodeURIComponent(this.state.param)),
            taskId: this.props.taskInfo.pk_id,
        };
        this.props.onCheck(obj, () => {
            this.setState({ isDo: false });
        });
    };
    onChange = (value) => {
        // console.log(value)
        this.setState({
            param: value,
            isDo: true,
        });
    };
    handleChoseKey = async (data) => {
        this.setState({
            searchKey: data.key,
        });
        await this.props.ownerStore.getRealColumn({
            field: data.key,
            tableView: this.props.taskInfo.dataview.tableview,
        });
    };
    getSearchKeyMenu2 = (mapresultArr, searchKey) => {
        return (
            <Menu
                fieldid="ublinker-routes-list-components-CheckModal-index-6449677-Menu"
                selectedKeys={[searchKey]}
                onSelect={this.handleChoseKey}
                className="data-detail-menu"
            >
                {mapresultArr.selectData &&
                    mapresultArr.selectData.map((item) => {
                        let { key, value } = item;
                        return (
                            <Menu.Item fieldid="UCG-FE-routes-list-components-CheckModal-index-7159951-Menu.Item" key={value} title={key}>
                                {key}
                            </Menu.Item>
                        );
                    })}
            </Menu>
        );
    };
    render() {
        let { show, onCancel, taskInfo, ownerState = {} } = this.props;
        let { resultArr2 = {}, getRealColumnContent } = ownerState;
        let searchKey2 = resultArr2.selectData && resultArr2.selectData[0] && resultArr2.selectData[0].value;
        let { param, isDo, searchKey } = this.state;
        return (
            <Modal
                fieldid="ublinker-routes-list-components-CheckModal-index-7475298-Modal"
                show={show}
                title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180507", "排查执行", undefined, {
                    returnStr: true,
                })}
                okText={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180508", "检测") /* "检测" */}
                extendBtn={() => {
                    return (
                        <Button
                            fieldid="ublinker-routes-list-components-CheckModal-index-6858573-Button"
                            disabled={isDo}
                            colors="dark"
                            className="ucg-mr-10"
                            onClick={this.handleOk}
                        >
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180509", "执行") /* "执行" */}
                        </Button>
                    );
                }}
                extendBtnLocation="pre"
                onCancel={onCancel}
                onOk={this.handleCheck}
            >
                {show && taskInfo ? (
                    <div className="ucg-pad-20">
                        <p className="ucg-mar-b-5">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180506", "条件") /* "条件" */}</p>
                        <FormControl.TextArea
                            rows={4}
                            // componentClass="textarea"
                            value={param}
                            className="ucg-mar-b-10"
                            style={{ height: "auto" }}
                            // autoSize={{
                            //   minRows: 4
                            // }}
                            onChange={this.onChange}
                        />

                        <p className="ucg-mar-b-5">
                            {
                                lang.templateByUuid(
                                    "UID:P_UBL-FE_18D8CEF604180504",
                                    "排查执行仅支持5条数据，用于排查问题，超过5条不支持" //@notranslate
                                ) /* "排查执行仅支持5条数据，用于排查问题，超过5条不支持" */
                            }
                        </p>
                        <p className="ucg-mar-b-5">
                            {
                                lang.templateByUuid(
                                    "UID:P_UBL-FE_18D8CEF604180505",
                                    "请稍后点击排查执行下的排查日志查看" //@notranslate
                                ) /* "请稍后点击排查执行下的排查日志查看" */
                            }
                        </p>
                        {resultArr2.selectData && resultArr2.selectData.length != 0 && JSON.stringify(resultArr2) != "{}" ? (
                            <>
                                <InputGroup fieldid="ublinker-routes-list-components-CheckModal-index-8159337-InputGroup" style={{}}>
                                    <div style={{ width: "1px", height: "1px", display: "none" }}></div>

                                    <InputGroup.Button>
                                        <Dropdown
                                            fieldid="ublinker-routes-list-components-CheckModal-index-7508986-Dropdown"
                                            className="data-detail-dropdown"
                                            style={{ height: 300, overflow: "auto" }}
                                            trigger={["click"]}
                                            overlay={this.getSearchKeyMenu2(resultArr2, searchKey || searchKey2)}
                                            animation="slide-up"
                                            // getPopupContainer={() => headerContainer}
                                            // onSelect={setSearchKey}
                                        >
                                            <Button fieldid="ublinker-routes-list-components-CheckModal-index-4908602-Button" bordered style={{ width: 150 }}>
                                                {resultArr2.getNameByCode && resultArr2.getNameByCode(searchKey || searchKey2)}{" "}
                                                <i fieldid="ublinker-routes-list-components-CheckModal-index-7464496-i" className="uf uf-arrow-down" />
                                            </Button>
                                        </Dropdown>
                                    </InputGroup.Button>
                                    <div style={{ width: "1px", height: "1px", display: "none" }}></div>

                                    {/* <div>
                  <FormControl fieldid="ublinker-routes-list-components-CheckModal-index-9808093-FormControl" 
                    disabled
                    className="drop-form-contorl"
                    // onChange={setSearchValue}
                    value={getRealColumnContent}
                    style={{ width: 200 }}
                  />
                  </div> */}
                                </InputGroup>
                                <div>
                                    <FormControl.TextArea
                                        disabled
                                        className="drop-form-contorl"
                                        // onChange={setSearchValue}
                                        value={getRealColumnContent}
                                        // style={{ width: 200 }}
                                    />
                                </div>
                            </>
                        ) : null}
                    </div>
                ) : null}
            </Modal>
        );
    }
}

export default SqlWhereModal;
