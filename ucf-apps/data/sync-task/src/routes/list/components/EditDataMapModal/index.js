import React, { Component, Fragment } from "react";
import { InputGroup, Dropdown, Button, FormControl, Icon, Select, FormList } from "components/TinperBee";
import Menu from "components/TinperBee/Menu";
import Modal from "components/TinperBee/Modal";
import Grid from "components/TinperBee/Grid";
import { inject, observer } from "mobx-react";
import Store from "./store";
// import { dataMapSearchKeys } from '../../../../common/constants'
import { createEnum } from "constants/utils";
import FieldWrap from "components/RowField/FieldWrap";
import commonText from "constants/commonText";
const FormItem = FormList.Item;
const labelCol = 120;
const Option = Select.Option;
const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
};
const DataMapStore = new Store();

@inject((rootStore) => {
    return {
        ownerState: DataMapStore.toJS(),
        ownerStore: DataMapStore,
    };
})
@observer
class DataMapModal extends Component {
    constructor(props) {
        super(props);
        this.state = {
            show: false,
            isEditERP: false,
        };
        this.form = React.createRef();
    }

    componentDidUpdate(prevProps) {
        console.log(prevProps);
        console.log(this.props);
        if (this.props.show !== prevProps.show) {
            this.setState({ show: this.props.show });
            let { ownerStore } = this.props;
            if (this.props.show) {
                // ownerStore.setTableView(this.props.taskInfo.dataview.tableview);
                ownerStore.setTableView("nc65_staff_syn");

                ownerStore.getDataSource();
            } else {
                // ownerStore.init()
            }
        }
    }

    handleSearchKeyChange = (node) => {
        let { key } = node;
        this.props.ownerStore.changeSearchKey(key);
    };
    dataMapSearchKeys = createEnum([
        {
            code: "erp",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804EA", "来源系统主键") /* "来源系统主键" */,
        },
        {
            code: "saas",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804EC", "目标系统主键") /* "目标系统主键" */,
        },
        {
            code: "code",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804ED", "编码") /* "编码" */,
        },
        {
            code: "name",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804EF", "名称") /* "名称" */,
        },
    ]);
    getSearchKeyMenu = (searchKey) => {
        return (
            <Menu
                fieldid="ublinker-routes-list-components-EditDataMapModal-index-6570518-Menu"
                selectedKeys={[searchKey]}
                onSelect={this.handleSearchKeyChange}
            >
                {this.dataMapSearchKeys.selectData.map((item) => {
                    let { key, value } = item;
                    return (
                        <Menu.Item fieldid="UCG-FE-routes-list-components-EditDataMapModal-index-4867809-Menu.Item" key={value}>
                            {key}
                        </Menu.Item>
                    );
                })}
            </Menu>
        );
    };

    handleSearch = () => {
        this.props.ownerStore.getDataSource({
            pageNo: 1,
        });
    };

    handleAddMap = () => {
        this.props.ownerStore.setEdit("add");
    };
    inputChange = (value, name, record) => {
        const { caseList } = this.props;
        let selectedCase;
        // this.props.handleInputChange(value, name, record)
        // if(name=='pkIntegratedId'){
        //     selectedCase=caseList.find((item)=>{
        //         return item.id==value
        //     })
        //     this.props.ownerStore.getTreeList({objectCode:selectedCase.sourceObjectCode,sysId:selectedCase.sourceSystemId})
        //     this.props.ownerStore.getEventDataSource({objectCode:selectedCase.sourceObjectCode,sysId:selectedCase.sourceSystemId})
        // }
    };
    renderHeader = () => {
        let {
            ownerState: { searchKey, hasEdit },
            ownerStore,
            form: { validateFields, getFieldProps, getFieldError, setFieldsValue },
        } = this.props;
        let searchName = this.dataMapSearchKeys.getNameByCode(searchKey);
        return (
            <div className="ucg-pad-20 clearfix" ref={(node) => (this.getGridHeaderNode = node)}>
                {/* <InputGroup fieldid="ublinker-routes-list-components-EditDataMapModal-index-2862993-InputGroup" style={{width: 260, float: 'left'}}>
          <InputGroup.Button >
            <Dropdown fieldid="ublinker-routes-list-components-EditDataMapModal-index-414648-Dropdown" 
              trigger={['click']}
              overlay={this.getSearchKeyMenu(searchKey)}
              animation="slide-up"
              getPopupContainer={() => this.getGridHeaderNode}
              onSelect={this.changeSearchKey}
            >
              <Button fieldid="ublinker-routes-list-components-EditDataMapModal-index-3561742-Button" bordered>{searchName} <i fieldid="ublinker-routes-list-components-EditDataMapModal-index-2217548-i" className="uf uf-arrow-down"/></Button>
            </Dropdown>
          </InputGroup.Button>
          <FormControl fieldid="ublinker-routes-list-components-EditDataMapModal-index-3280393-FormControl" 
            type="search"
            onChange={ownerStore.changeSearchValue}
            onSearch={this.handleSearch}
          />
          <span></span>
        </InputGroup>

        <Button fieldid="ublinker-routes-list-components-EditDataMapModal-index-5179441-Button" 
          className="ucg-float-r"
          colors="primary"
          disabled={hasEdit}
          onClick={this.handleAddMap}
        >{"新增主鍵映射"}</Button> */}
                <FormList
                    fieldid="ublinker-routes-list-components-EditDataMapModal-index-4328810-FormList"
                    className=""
                    ref={this.form}
                    //  layout='inline'
                    {...formItemLayout}
                    layoutOpt={{ md: 6 }}
                >
                    <FormItem
                        fieldid="ublinker-routes-list-components-EditDataMapModal-index-6738324-FormItem"
                        label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801A9", "修改档案") /* "修改档案" */}
                        // required
                        // labelCol={labelCol}
                        name="code"
                    >
                        <Select
                            fieldid="ublinker-routes-list-components-EditDataMapModal-index-5740-Select"
                            size="sm"
                            onChange={(value, record) => {
                                this.inputChange(value, "code", record);
                            }}
                        >
                            {
                                // caseList && caseList.map((item, index) => {
                                //   return (<Option fieldid="UCG-FE-routes-list-components-EditDataMapModal-index-6655377-Option" key={item.id} value={item.id} objectSrcType={item.objectSrcType}>{item.schemeName}</Option>)
                                // })
                            }
                        </Select>
                    </FormItem>
                    <FormItem
                        fieldid="ublinker-routes-list-components-EditDataMapModal-index-5116679-FormItem"
                        label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801B0", "Sass主键") /* "Sass主键" */}
                        required
                        labelCol={labelCol}
                    >
                        <FormControl
                            fieldid="ublinker-routes-list-components-EditDataMapModal-index-6035703-FormControl"
                            {...getFieldProps("name", {
                                initialValue: "",
                                validateTrigger: "onBlur",
                                rules: [
                                    {
                                        required: true,
                                        // message: "请输入Sass主键"
                                    },
                                ],
                            })}
                        />
                        <span className="error">{getFieldError("name")}</span>
                    </FormItem>
                    <FormItem
                        fieldid="ublinker-routes-list-components-EditDataMapModal-index-7532442-FormItem"
                        label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801AC", "编码") /* "编码" */}
                        required
                        labelCol={labelCol}
                    >
                        <FormControl
                            fieldid="ublinker-routes-list-components-EditDataMapModal-index-3589425-FormControl"
                            {...getFieldProps("name", {
                                initialValue: "",
                                validateTrigger: "onBlur",
                                rules: [
                                    {
                                        required: true,
                                    },
                                ],
                            })}
                        />
                        <span className="error">{getFieldError("name")}</span>
                    </FormItem>
                    <FormItem
                        fieldid="ublinker-routes-list-components-EditDataMapModal-index-7262873-FormItem"
                        label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801A6", "名称") /* "名称" */}
                        required
                        labelCol={labelCol}
                    >
                        <FormControl
                            fieldid="ublinker-routes-list-components-EditDataMapModal-index-1944183-FormControl"
                            {...getFieldProps("name", {
                                initialValue: "",
                                validateTrigger: "onBlur",
                                rules: [
                                    {
                                        required: true,
                                    },
                                ],
                            })}
                        />
                        <span className="error">{getFieldError("name")}</span>
                    </FormItem>
                    <FormItem
                        fieldid="ublinker-routes-list-components-EditDataMapModal-index-5326580-FormItem"
                        label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801AE", "手机号") /* "手机号" */}
                        required
                        labelCol={labelCol}
                    >
                        <FormControl
                            fieldid="ublinker-routes-list-components-EditDataMapModal-index-529613-FormControl"
                            {...getFieldProps("name", {
                                initialValue: "",
                                validateTrigger: "onBlur",
                                rules: [
                                    {
                                        required: true,
                                    },
                                ],
                            })}
                        />
                        <span className="error">{getFieldError("name")}</span>
                    </FormItem>
                    <FormItem
                        fieldid="ublinker-routes-list-components-EditDataMapModal-index-4047609-FormItem"
                        label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801A8", "邮箱") /* "邮箱" */}
                        required
                        labelCol={labelCol}
                    >
                        <FormControl
                            fieldid="ublinker-routes-list-components-EditDataMapModal-index-9085029-FormControl"
                            {...getFieldProps("name", {
                                initialValue: "",
                                validateTrigger: "onBlur",
                                rules: [
                                    {
                                        required: true,
                                    },
                                ],
                            })}
                        />
                        <span className="error">{getFieldError("name")}</span>
                    </FormItem>
                </FormList>
                <Button
                    fieldid="ublinker-routes-list-components-EditDataMapModal-index-1314075-Button"
                    className="mgl"
                    colors="primary"
                    onClick={this.handleAdd}
                    size="sm"
                >
                    <Icon fieldid="ublinker-routes-list-components-EditDataMapModal-index-7343143-Icon" type="uf-plus" />
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801B1", "查询") /* "查询" */}
                </Button>
            </div>
        );
    };

    handleChange = (field, value) => {
        this.props.ownerStore.changeEditRowInfo(field, value);
    };

    canEditFields = ["pk_id", "cloudpk"];

    renderCol = (field, value, record) => {
        let { isEdit = false } = record;
        if (isEdit) {
            let {
                ownerState: { editRowInfo, editRowError },
            } = this.props;
            let disabled = !this.canEditFields.includes(field);
            return (
                <FieldWrap message={editRowError[field]}>
                    <FormControl
                        fieldid="ublinker-routes-list-components-EditDataMapModal-index-7805719-FormControl"
                        value={editRowInfo[field]}
                        disabled={disabled}
                        onChange={this.handleChange.bind(null, field)}
                    />
                </FieldWrap>
            );
        } else {
            return value || "-";
        }
    };

    handleCancelEditMap = () => {
        this.props.ownerStore.setEdit("cancel");
    };

    handleSaveMap = () => {
        this.setState({
            isEditERP: true,
        });
        // this.props.ownerStore.saveDataMap();
    };

    columns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801A7", "ERP主键") /* "ERP主键" */,
            dataIndex: "pk_id",
            width: 180,
            render: this.renderCol.bind(null, "pk_id"),
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801AA", "Saas主键") /* "Saas主键" */,
            dataIndex: "cloudpk",
            width: 180,
            render: this.renderCol.bind(null, "cloudpk"),
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801AE", "手机号") /* "手机号" */,
            dataIndex: "phone",
            width: 150,
            render: this.renderCol.bind(null, "phone"),
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801A8", "邮箱") /* "邮箱" */,
            dataIndex: "mail",
            width: 150,
            render: this.renderCol.bind(null, "mail"),
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801AC", "编码") /* "编码" */,
            dataIndex: "code",
            width: 150,
            render: this.renderCol.bind(null, "code"),
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801A6", "名称") /* "名称" */,
            dataIndex: "name",
            width: 150,
            render: this.renderCol.bind(null, "name"),
        },
        {
            title: "",
            dataIndex: "$$editCol",
            render: (value, record, index) => {
                let { isEdit } = record;
                // if (isEdit) {
                return (
                    <Fragment>
                        <Button
                            fieldid="ublinker-routes-list-components-EditDataMapModal-index-6281815-Button"
                            {...Grid.hoverButtonPorps}
                            onClick={this.handleSaveMap}
                        >
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180544", "编辑") /* "编辑" */}
                        </Button>
                    </Fragment>
                );
                // } else {
                //   return null
                // }
            },
        },
    ];
    changeERP = () => {
        this.setState({ isEditERP: false });
    };
    handleSaveERP = () => {};
    render() {
        let { show, isEditERP } = this.state;
        let {
            form: { validateFields, getFieldProps, getFieldError, setFieldsValue },
        } = this.props;
        if (show) {
            let { ownerState, onCancel } = this.props;
            let { dataSource, pagination, hasEdit } = ownerState;
            if (pagination) {
                pagination.disabled = hasEdit;
            }
            return (
                <>
                    <Modal
                        fieldid="ublinker-routes-list-components-EditDataMapModal-index-3619513-Modal"
                        show={show}
                        title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801AB", "数据映射", undefined, {
                            returnStr: true,
                        })}
                        cancelText={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801AD", "关闭") /* "关闭" */}
                        size="xlg"
                        okHide
                        onCancel={onCancel}
                    >
                        <div className="">
                            {this.renderHeader()}
                            <Grid
                                fieldid="ublinker-routes-list-components-EditDataMapModal-index-4251092-Grid"
                                columns={this.columns}
                                data={dataSource.list}
                                pagination={pagination}
                                dataKey={"pk_id"}
                            />
                        </div>
                    </Modal>
                    <Modal
                        fieldid="ublinker-routes-list-components-EditDataMapModal-index-2488918-Modal"
                        show={isEditERP}
                        title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801A7", "ERP主键", undefined, {
                            returnStr: true,
                        })}
                        // cancelText={"关闭"}
                        size="sm"
                        // okHide
                        onCancel={this.changeERP}
                        onOk={this.handleSaveERP}
                    >
                        <div className="" style={{ textAlign: "center", margin: 10 }}>
                            <FormControl
                                fieldid="ublinker-routes-list-components-EditDataMapModal-index-2168516-FormControl"
                                style={{ width: 200 }}
                                {...getFieldProps("erp", {
                                    initialValue: "",
                                    validateTrigger: "onBlur",
                                    rules: [
                                        {
                                            required: true,
                                        },
                                    ],
                                })}
                            />
                        </div>
                    </Modal>
                    {/* : null
        } */}
                </>
            );
        } else {
            return null;
        }
    }
}

export default FormList.createForm()(DataMapModal);
