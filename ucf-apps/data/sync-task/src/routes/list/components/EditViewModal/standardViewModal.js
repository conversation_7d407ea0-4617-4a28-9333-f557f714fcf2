import React, { Component, lazy, Suspense } from "react";
import { FormControl, Button, Modal as <PERSON><PERSON><PERSON><PERSON>, Input<PERSON><PERSON>ber, Form<PERSON>ist, <PERSON>bs, Icon } from "components/TinperBee";
// import Modal from "components/TinperBee/Modal";
import { Modal } from "@tinper/next-ui";
import "./index.less";
import { formatSqlWithVariables } from "utils/sqlFormat";
import DiffViewer from "iuap-ip-commonui-fe/react-diff-viewer";

class EditModal extends Component {
    constructor() {
        super();
        this.state = {
            activeKey: "",
        };
        this.form = React.createRef();
    }

    componentDidMount(nextProps) {
        this.props.ownerStore.getNewSql({ tableview: this.props.tableview });
    }

    componentWillUnmount() {
        this.props.ownerStore.state.newSqlObj = "";
    }

    labelCol = 120;

    handeApply = async () => {
        let {
            ownerState: { newSqlObj },
        } = this.props;
        const res = await this.props.ownerStore.resetNewSql({ tableview: this.props.tableview });
        if (res) {
            this.props.onOk(newSqlObj?.sqlview);
        }
    };
    render() {
        let {
            ownerState: { newSqlObj },
            show,
            onCancel,
            currentSql,
        } = this.props;
        return (
            <Modal
                maximize
                fieldid="ublinker-routes-list-components-ViewManageModal-index-2626539-Modal"
                title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418007B", "ERP集成方案配置", undefined, {
                    returnStr: true,
                })}
                className="standardViewModal"
                show={show}
                onCancel={onCancel}
                onOk={this.handeApply}
                okText={lang.templateByUuid("UID:P_UBL-FE_1C2D4A660448001E", "应用") /* "应用" */}
                size="xlg"
                height="540"
            >
                <div className="data-flag-tip">
                    <Icon fieldid="9babbdbd-69db-47bf-8c39-94a2299d94b0" type="uf-i-c-2" className="tip-icon" />
                    123
                </div>
                <div className="standardViewModal-content">
                    <div className="standardView-title">
                        <h2 className="standardView-t">{lang.templateByUuid("UID:P_UBL-FE_1C2D4A660448001C", "当前视图") /* "当前视图" */}</h2>
                        <h2 className="standardView-t">{lang.templateByUuid("UID:P_UBL-FE_1C2D4A660448001D", "最新视图") /* "最新视图" */}</h2>
                    </div>
                    {newSqlObj?.sqlview ? (
                        <Suspense fallback={<div>Loading diff editor...</div>}>
                            <div style={{ height: 350, overflow: "auto" }}>
                                <DiffViewer
                                    showDiffOnly={false}
                                    oldValue={newSqlObj?.sqlview ? currentSql : ""}
                                    newValue={newSqlObj?.sqlview ? formatSqlWithVariables(newSqlObj?.sqlview) : ""}
                                />
                            </div>
                        </Suspense>
                    ) : (
                        <></>
                    )}
                </div>
            </Modal>
        );
    }
}

export default EditModal;
