import React, { Component } from "react";
import { FormControl, Button, Modal as <PERSON><PERSON><PERSON><PERSON>, InputNumber, FormList } from "components/TinperBee";
import Clipboard from "components/TinperBee/Clipboard";
import Modal from "components/TinperBee/Modal";
import { codeReg, codeMessage } from "utils/regExp";
import { autoServiceMessage } from "utils/service";
import { getViewInfoService, saveViewInfoService, restoreSQLService } from "../../service";
import commonText from "constants/commonText";
import { formatSqlWithVariables } from "utils/sqlFormat";
import StandardViewModal from "./standardViewModal";
import "./index.less";
const FormItem = FormList.Item;
const formItemLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 16 },
};
class EditModal extends Component {
    constructor() {
        super();
        this.state = {
            show: false,
            viewInfo: null,
            tenantSupport: false,
            standardViewModalShow: false,
        };
        this.form = React.createRef();
    }
    componentDidMount(nextProps) {
        this.getViewInfo(this.props);
    }
    getViewInfo = async (nextProps) => {
        let newState = {};
        let { selectedTask } = nextProps.ownerState;
        let res = await getViewInfoService({ tableview: selectedTask.dataview.tableview }, { serviceCode: nextProps.ownerState.serviceCodeDiwork });
        if (res && res.data) {
            const tenantSupport = typeof res.data.tenantSupport === "undefined" ? true : res.data.tenantSupport;
            newState.tenantSupport = tenantSupport;
            this.props.ownerStore.setTenantSupport(tenantSupport);
            const sqlview = formatSqlWithVariables(res.data.sqlview || "");
            newState.viewInfo = { ...res.data, sqlview };
            this.setState(newState);
        }
    };

    saveViewInfo = async (data) => {
        let res = await autoServiceMessage({
            service: saveViewInfoService(data, { serviceCode: this.props.ownerState.serviceCodeDiwork }),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180021", "保存成功", undefined, {
                returnStr: true,
            }) /* "保存成功" */,
        });
        if (res) {
            this.getViewInfo(this.props);
        }
    };

    handleOk = () => {
        this.form.current.validateFields().then((values) => {
            let { viewInfo } = this.state;
            values.sqlview = "YonLinker" + btoa(encodeURIComponent(values.sqlview));
            values.id = viewInfo.pk_id;
            values.dataversion = viewInfo.dataversion;
            this.saveViewInfo(values);
        });
    };

    handleCancel = () => {
        this.setState({
            viewInfo: null,
            tenantSupport: false,
        });
        this.props.ownerStore.setTenantSuppor(false);
        // this.props.onCancel()
    };
    formatSQL = () => {
        let { viewInfo } = this.state;
        viewInfo.sqlview = formatSqlWithVariables(this.form.current.getFieldValue("sqlview"));
        //  this.setState({viewInfo})
        this.form.current.setFieldValue("sqlview", viewInfo.sqlview);
    };
    getStandardView = (newsqlview) => {
        this.setState({
            standardViewModalShow: !this.state.standardViewModalShow,
        });
        if (typeof newsqlview == "string" && newsqlview) {
            this.getViewInfo(this.props);
            this.form.current.setFieldValue("sqlview", formatSqlWithVariables(newsqlview || ""));
        }
    };
    restoreSQL = async () => {
        let { viewInfo } = this.state;
        let res = await autoServiceMessage({
            service: restoreSQLService(
                {
                    tableview: viewInfo.tableview,
                },
                { serviceCode: this.props.ownerState.serviceCodeDiwork }
            ),
        });
        if (res.data) {
            let { sqlview } = res.data;
            ModalCom.confirm({
                title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180026", "历史SQL") /* "历史SQL" */,
                content: (
                    <p className="copy-sql" style={{ wordBreak: "break-word" }}>
                        {sqlview}
                    </p>
                ),
                okText: (
                    <Clipboard fieldid="ublinker-routes-list-components-EditViewModal-index2-8815009-Clipboard" target=".copy-sql">
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180028", "复制") /* "复制" */}
                    </Clipboard>
                ),
                cancelText: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180029", "关闭") /* "关闭" */,
                confirmType: "one",
            });
        }
    };

    labelCol = 120;
    render() {
        let { viewInfo, show, tenantSupport, standardViewModalShow } = this.state;
        let { ownerStore, ownerState } = this.props;
        if (viewInfo) {
            return (
                <>
                    <FormList fieldid="ublinker-routes-list-components-EditViewModal-index2-2010910-FormList" ref={this.form} {...formItemLayout}>
                        <div className="sqlBox">
                            <FormItem
                                fieldid="ublinker-routes-list-components-EditViewModal-index2-8767306-FormItem"
                                label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180023", "视图标签") /* "视图标签" */}
                                name="tableview"
                                initialValue={viewInfo.tableview}
                            >
                                <FormControl fieldid="ublinker-routes-list-components-EditViewModal-index2-5573131-FormControl" disabled />
                            </FormItem>
                            <div className="sqlBox-btn2">
                                <Button
                                    fieldid="ublinker-routes-list-components-EditViewModal-index2-80639961-Button"
                                    onClick={this.getStandardView}
                                    style={{ verticalAlign: "top", marginLeft: "14px" }}
                                >
                                    {lang.templateByUuid("UID:P_UBL-FE_1C2D4A6604480020", "获取标准视图") /* "获取标准视图" */}
                                </Button>
                            </div>
                        </div>
                        <FormItem
                            fieldid="ublinker-routes-list-components-EditViewModal-index2-9639293-FormItem"
                            label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418002C", "视图版本") /* "视图版本" */}
                            name="viewversion"
                            initialValue={"V" + viewInfo.viewversion}
                        >
                            <FormControl fieldid="ublinker-routes-list-components-EditViewModal-index2-1401510-FormControl" disabled />
                        </FormItem>

                        <div className="sqlBox">
                            <FormItem
                                fieldid="ublinker-routes-list-components-EditViewModal-index2-551607-FormItem"
                                label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180022", "SQL语句") /* "SQL语句" */}
                                name="sqlview"
                                initialValue={viewInfo.sqlview}
                                rules={[
                                    {
                                        required: true,
                                        message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180025", "请输入视图SQL") /* "请输入视图SQL" */,
                                    },
                                ]}
                            >
                                <FormControl.TextArea
                                    placeholder={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418002A", "输入视图SQL", undefined, {
                                        returnStr: true,
                                    })}
                                    style={{ height: "340px" }}
                                    autoSize={false}
                                />
                            </FormItem>
                            <div className="sqlBox-btn2">
                                {/* <Button fieldid="ublinker-routes-list-components-EditViewModal-index2-8063996-Button" colors='dark' onClick={this.restoreSQL} style={{ verticalAlign: 'top', marginLeft: '14px' }}>{"查看历史SQL"}</Button> */}
                                <Button
                                    fieldid="ublinker-routes-list-components-EditViewModal-index2-8063996-Button"
                                    onClick={this.formatSQL}
                                    style={{ verticalAlign: "top", marginLeft: "14px" }}
                                >
                                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418001E", "格式化") /* "格式化" */}
                                </Button>
                            </div>
                        </div>
                        <FormItem
                            fieldid="ublinker-routes-list-components-EditViewModal-index2-1163390-FormItem"
                            label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180020", "ERP主键名称") /* "ERP主键名称" */}
                            name="datapkname"
                            initialValue={viewInfo.datapkname}
                            rules={[
                                {
                                    required: true,
                                    message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180024", "请输入ERP主键名称") /* "请输入ERP主键名称" */,
                                },
                                {
                                    pattern: codeReg,
                                    message:
                                        lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180020", "ERP主键名称", undefined, {
                                            returnStr: true,
                                        }) + codeMessage,
                                },
                            ]}
                        >
                            <FormControl fieldid="ublinker-routes-list-components-EditViewModal-index2-6694122-FormControl" disabled />
                        </FormItem>

                        <FormItem
                            fieldid="ublinker-routes-list-components-EditViewModal-index-52430361-FormItem"
                            label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180499", "增量字段") /* "增量字段" */}
                            name="incrementidentity"
                            initialValue={viewInfo.incrementidentity}
                            rules={[
                                {
                                    required: false,
                                    message:
                                        lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803CB", "请输入", undefined, {
                                            returnStr: true,
                                        }) +
                                        lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180499", "增量字段", undefined, {
                                            returnStr: true,
                                        }) /* "增量字段" */,
                                },
                            ]}
                        >
                            <FormControl fieldid="ublinker-routes-list-components-EditViewModal-index-70801041-FormControl" />
                        </FormItem>

                        <FormItem
                            fieldid="ublinker-routes-list-components-EditViewModal-index2-3820128-FormItem"
                            label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418001D", "分页大小") /* "分页大小" */}
                            name="pagesize"
                            initialValue={viewInfo.pagesize}
                        >
                            {/* <FormControl fieldid="ublinker-routes-list-components-EditViewModal-index2-4832910-FormControl" 
               {...getFieldProps('pagesize', {
                 initialValue: viewInfo.pagesize
               })}
             /> */}
                            <InputNumber
                                fieldid="ublinker-routes-list-components-EditViewModal-index2-3542555-InputNumber"
                                iconStyle="one"
                                min={1}
                                max={10000}
                            />
                        </FormItem>
                        <FormItem
                            fieldid="ublinker-routes-list-components-EditViewModal-index2-6316263-FormItem"
                            label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418002B", "修改人") /* "修改人" */}
                            name="modiefier"
                            initialValue={viewInfo.modifier}
                        >
                            <FormControl fieldid="ublinker-routes-list-components-EditViewModal-index2-1207245-FormControl" disabled />
                        </FormItem>
                        <FormItem
                            fieldid="ublinker-routes-list-components-EditViewModal-index2-1130610-FormItem"
                            label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418001F", "修改时间") /* "修改时间" */}
                            name="modifiedtime"
                            initialValue={viewInfo.modifiedtime}
                        >
                            <FormControl fieldid="ublinker-routes-list-components-EditViewModal-index2-1137823-FormControl" disabled />
                        </FormItem>
                    </FormList>
                    {standardViewModalShow ? (
                        <StandardViewModal
                            tableview={viewInfo.tableview}
                            currentSql={viewInfo.sqlview}
                            show={standardViewModalShow}
                            onCancel={this.getStandardView}
                            onOk={this.getStandardView}
                            ownerStore={ownerStore}
                            ownerState={ownerState}
                        ></StandardViewModal>
                    ) : (
                        <></>
                    )}
                </>
            );
        } else {
            return null;
        }
    }
}

export default EditModal;
