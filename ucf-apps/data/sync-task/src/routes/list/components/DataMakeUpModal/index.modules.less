.makeup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    height: 48px;
}

.makeup-header-left {
    display: flex;
    align-items: center;
}

.makeup-header-right {
    display: flex;
    align-items: center;
}

.makeup-header-time {
    font-size: 12px;
    font-family:
        PingFangSC-Regular,
        PingFang SC;
    font-weight: 400;
    color: #999999;
}

.makeup-header-tip {
    font-size: 12px;
    font-family:
        PingFangSC-Regular,
        PingFang SC;
    font-weight: 400;
    color: #ff7301;
    display: flex;
    align-items: center;
}

.fr {
    float: right;
}

.diagramModal .wui-modal-content {
    // transform: translate(12px, 23px);
    position: absolute;
    top: -20px;
    bottom: 0;
    left: 0;
    right: 0;
    // height: 94%;
}

.diagramModal .wui-modal-body {
    background: #eef1f7;
}
.modalTitle {
    font-size: 14px;
    font-family: PingFang-SC-Heavy, PingFang-SC;
    font-weight: 800;
    color: #333333;
}
