import React, { Component, Fragment } from "react";
import { InputGroup, Dropdown, Button, FormControl, Modal, Icon, Checkbox } from "components/TinperBee";
import { getLocalImg } from "utils/index";
import Menu from "components/TinperBee/Menu";
// import Modal from 'components/TinperBee/Modal'
import Grid from "components/TinperBee/Grid";
import { inject, observer } from "mobx-react";
import Store from "./store";
import FieldWrap from "components/RowField/FieldWrap";
import { getNewTreeData, getNewCardData, loopApiCls } from "utils";
import commonText from "constants/commonText";
import { getScreenInnerHeight } from "utils";
import styles from "./card.modules.less";
const ScreenInnerHeight = getScreenInnerHeight();

// const DataMakeUpStore = new Store()

// @inject((rootStore) => {
//   return {
//     ownerState: DataMakeUpStore.toJS(),
//     ownerStore: DataMakeUpStore
//   }
// })

// @observer
class Card extends Component {
    constructor(props) {
        super(props);
        this.state = {
            show: false,
            diagramModalShow: true,
        };
    }

    handleGetDiagram = (dataId) => {
        this.setState({
            diagramModalShow: true,
        });
        this.props.ownerStore.handleGetDiagram({
            tableView: this.props.taskInfo.dataview.tableview,
            dataId,
        });
    };
    getCardChild = (treeNode) => {
        console.log(treeNode);
        if (treeNode.errorTable && treeNode.errorFieldValue && !treeNode.noLeaf) {
            // console.log(treeNode)
            return new Promise(async (resolve) => {
                let {
                    ownerState: { diagramData },
                    ownerStore: { changeState },
                } = this.props;
                let params = {
                    tableView: treeNode.errorTable,
                    dataId: treeNode.errorFieldValue,
                };

                console.log(diagramData);
                let child = await this.props.ownerStore.handleGetDiagramAgain(params);
                console.log(child);
                getNewCardData(diagramData, treeNode, child);
                console.log(diagramData);
                changeState({
                    diagramData,
                });
                resolve();
            });
        }
    };

    // gao
    loopHeight = (list) => {
        if (!list || list.length === 0 || list.length === 1) {
            return 104;
        }
        let height = 0;
        for (let i = 0; i < list.length; i++) {
            let temp = this.loopHeight(list[i].children);
            height += temp;
        }
        height += 20 * (list.length - 1);
        return height;
    };

    // xian
    calculateHeight = (list) => {
        if (!list || list.length === 0 || list.length === 1) {
            return 104;
        }
        let height = 0;
        for (let i = 0; i < list.length; i++) {
            let temp = this.loopHeight(list[i].children);
            height += temp;
        }
        height += 20 * (list.length - 1);
        let fisrtHeight = this.loopHeight(list[0].children) / 2;
        let lastHeight = this.loopHeight(list[list.length - 1].children) / 2;
        height = height - fisrtHeight - lastHeight;
        return height;
    };

    // xian
    calculateTop = (list) => {
        console.log(list);
        if (!list || list.length === 0 || list.length === 1) {
            return 0;
        }

        let temp = this.loopHeight(list[0].children) / 2;
        return temp;
    };

    render() {
        let { ownerState, ownerStore, onCancel, onOk, taskInfo, diagramData } = this.props;
        // let {  diagramData } = ownerState;
        return (
            <div className={styles.cotainer}>
                {diagramData.map((item, index) => {
                    return (
                        <div className={styles.box1} key={index}>
                            <div className={styles["box1-left"]}>
                                <div className={styles.box} onClick={this.getCardChild.bind(null, item)}>
                                    <div className={styles["box-errorTableName"]} title={item.srcViewName}>
                                        <img
                                            src={getLocalImg(`syncTask/object-public-line${Math.floor(Math.random() * 3)}.svg`)}
                                            className={styles["box-errorTableName-img"]}
                                        />
                                        <div style={{ flex: 1 }}>
                                            <div className={styles["box-errorTableName-title"]}>{item.srcViewName}</div>
                                            <div className={styles["box-errorTableName-code"]}>{item.srcView}</div>
                                            {item.now && (
                                                <div className={styles["box-errorTableName-tag"]}>
                                                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418004D", "当前") /* "当前" */}
                                                </div>
                                            )}
                                        </div>
                                        {item.errorFieldValue && item.errorTable && !item.noLeaf && (
                                            <Button fieldid="ublinker-routes-home-components-ConnecterList-index-7218388-Button" type="text" bordered>
                                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418004C", "加载依赖") /* "加载依赖" */}
                                            </Button>
                                        )}
                                    </div>
                                    <div className={styles["box-errorReason"]} title={item.errorReason}>
                                        {item.errorReason}
                                    </div>
                                </div>
                            </div>
                            <div className={styles.right}>
                                {item.children ? (
                                    <Card
                                        fieldid="UCG-FE-routes-list-components-DataMakeUpModal-card-6849423-Card"
                                        diagramData={item.children}
                                        ownerState={ownerState}
                                        ownerStore={ownerStore}
                                    />
                                ) : null}
                            </div>
                            <div
                                className={styles["box1-line"]}
                                style={{ height: `${this.calculateHeight(item.children)}px`, top: `${this.calculateTop(item.children)}px` }}
                            ></div>
                        </div>
                    );
                })}
            </div>
        );
    }
}

export default Card;
