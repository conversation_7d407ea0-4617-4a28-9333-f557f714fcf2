import React, { Component, Fragment } from "react";
import { InputGroup, Dropdown, Button, FormControl, Modal, Icon, Checkbox, Table } from "components/TinperBee";
import Menu from "components/TinperBee/Menu";
import Grid from "components/TinperBee/Grid";
import { inject, observer } from "mobx-react";
import Store from "./store";
import FieldWrap from "components/RowField/FieldWrap";
import { getNewTreeData, loopApiCls } from "utils";
import { withIframeSize } from "decorator/index";
import styles from "./index.modules.less";
import { createEnum } from "constants/utils";
import Card from "./card";
const DataMakeUpStore = new Store();
import Pagination from "components/TinperBee/Pagination";
import { Warning } from "utils/feedback";

@withIframeSize
@inject((rootStore) => {
    return {
        ownerState: DataMakeUpStore.toJS(),
        ownerStore: DataMakeUpStore,
    };
})
@observer
class DataMakeUpModal extends Component {
    constructor(props) {
        super(props);
        this.state = {
            show: false,
            diagramModalShow: false,
            selectedRows: [],
        };
    }
    componentDidMount() {
        console.log(this.props.taskInfo);
        this.props.ownerStore.setTableView(this.props.taskInfo.dataview.tableview);
        this.props.ownerStore.changeState({ taskInfo: this.props.taskInfo });
        this.props.ownerStore.getDataSource();
    }

    componentWillUnmount() {
        this.props.ownerStore.changeSearchKey("erp");
        this.props.ownerStore.changeSearchValue("");
    }
    handleSearchKeyChange = (node) => {
        let { key } = node;
        this.props.ownerStore.changeSearchKey(key);
    };
    dataMapSearchKeys = createEnum([
        {
            code: "erp",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804EA", "来源系统主键") /* "来源系统主键" */,
        },
        {
            code: "saas",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804EC", "目标系统主键") /* "目标系统主键" */,
        },
        {
            code: "code",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804ED", "编码") /* "编码" */,
        },
        {
            code: "name",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804EF", "名称") /* "名称" */,
        },
    ]);
    getSearchKeyMenu = (searchKey) => {
        return (
            <Menu fieldid="ublinker-routes-list-components-DataMakeUpModal-index-9012571-Menu" selectedKeys={[searchKey]} onSelect={this.handleSearchKeyChange}>
                {this.dataMapSearchKeys.selectData.map((item) => {
                    let { key, value } = item;
                    return (
                        <Menu.Item fieldid="UCG-FE-routes-list-components-DataMakeUpModal-index-6906738-Menu.Item" key={value}>
                            {key}
                        </Menu.Item>
                    );
                })}
            </Menu>
        );
    };

    handleSearch = () => {
        this.props.ownerStore.getDataSource({
            pageNo: 1,
        });
    };

    handleAddMap = () => {
        this.props.ownerStore.setEdit("add");
    };

    renderHeader = () => {
        let {
            ownerState: { searchKey, hasEdit },
            ownerStore,
        } = this.props;
        let searchName = this.dataMapSearchKeys.getNameByCode(searchKey);
        return (
            <div className="ucg-pad-20 clearfix" ref={(node) => (this.getGridHeaderNode = node)}>
                <InputGroup fieldid="ublinker-routes-list-components-DataMakeUpModal-index-8980279-InputGroup" style={{}}>
                    <InputGroup.Button>
                        <Dropdown
                            fieldid="ublinker-routes-list-components-DataMakeUpModal-index-9169409-Dropdown"
                            trigger={["click"]}
                            overlay={this.getSearchKeyMenu(searchKey)}
                            animation="slide-up"
                            getPopupContainer={() => this.getGridHeaderNode}
                            onSelect={this.changeSearchKey}
                        >
                            <Button fieldid="ublinker-routes-list-components-DataMakeUpModal-index-4249688-Button" bordered>
                                {searchName} <i fieldid="ublinker-routes-list-components-DataMakeUpModal-index-7467727-i" className="uf uf-arrow-down" />
                            </Button>
                        </Dropdown>
                    </InputGroup.Button>
                    <FormControl
                        fieldid="ublinker-routes-list-components-DataMakeUpModal-index-6561677-FormControl"
                        type="search"
                        style={{ width: 200 }}
                        onChange={ownerStore.changeSearchValue}
                        onSearch={this.handleSearch}
                        showClose
                    />
                    <span></span>
                </InputGroup>
            </div>
        );
    };

    handleChange = (field, value) => {
        this.props.ownerStore.changeEditRowInfo(field, value);
    };

    canEditFields = ["pk_id", "cloudpk"];

    renderCol = (field, value, record) => {
        let { isEdit = false } = record;
        if (isEdit) {
            let {
                ownerState: { editRowInfo, editRowError },
            } = this.props;
            let disabled = !this.canEditFields.includes(field);
            return (
                <FieldWrap message={editRowError[field]}>
                    <FormControl
                        fieldid="ublinker-routes-list-components-DataMakeUpModal-index-9876092-FormControl"
                        value={editRowInfo[field]}
                        disabled={disabled}
                        onChange={this.handleChange.bind(null, field)}
                    />
                </FieldWrap>
            );
        } else {
            return value || "-";
        }
    };
    renderErrType = (field, value, record) => {
        return value == -1
            ? "-"
            : value == 1
              ? lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180285", "其他") /* "其他" */
              : lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180284", "依赖错误") /* "依赖错误" */;
    };

    handleCancelEditMap = () => {
        this.props.ownerStore.setEdit("cancel");
    };

    handleSaveMap = () => {
        this.props.ownerStore.saveDataMap();
    };
    columns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418027C", "来源系统主键") /* "来源系统主键" */,
            dataIndex: "pk_id",
            width: 180,
            render: this.renderCol.bind(null, "pk_id"),
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418027E", "目标系统主键") /* "目标系统主键" */,
            dataIndex: "cloudpk",
            width: 180,
            render: this.renderCol.bind(null, "cloudpk"),
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180281", "编码") /* "编码" */,
            dataIndex: "code",
            width: 150,
            render: this.renderCol.bind(null, "code"),
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180286", "名称") /* "名称" */,
            dataIndex: "name",
            width: 100,
            render: this.renderCol.bind(null, "name"),
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418027A", "同步ID") /* "同步ID" */,
            dataIndex: "callbackId",
            render: this.renderCol.bind(null, "callbackId"),
        },

        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418027D", "错误类型") /* "错误类型" */,
            dataIndex: "errorType",
            width: 100,
            render: this.renderErrType.bind(null, "errorType"),
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418027F", "错误原因") /* "错误原因" */,
            dataIndex: "errorReason",
            width: 100,
            render: this.renderCol.bind(null, "errorReason"),
        },
    ];
    schemaColumns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20076E5804280035", "任务开始时间") /* "任务开始时间" */,
            dataIndex: "lastExecTime",
            render: this.renderCol.bind(null, "lastExecTime"),
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_20076E5804280036", "任务结束时间") /* "任务结束时间" */,
            dataIndex: "lastExecEnd",
            render: this.renderCol.bind(null, "lastExecEnd"),
        },
    ];
    getSelectedDataFunc = (selectedList) => {
        this.setState({ selectedRows: selectedList });
    };
    handleGetDiagram = (dataId, name) => {
        this.props.ownerStore.changeState({ errorRecordName: name });
        this.setState({
            diagramModalShow: true,
        });
        this.props.ownerStore.handleGetDiagram({
            tableView: this.props.taskInfo.dataview.tableview,
            dataId,
        });
    };
    hoverContent = (record) => {
        if (record.errorType == 0) {
            return (
                <Fragment>
                    <Button
                        fieldid="ublinker-routes-list-components-IndexView-index-2398146-Button"
                        {...Grid.hoverButtonPorps}
                        onClick={() => {
                            this.handleGetDiagram(record.pk_id, record.name);
                        }}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180280", "查看依赖关系图") /* "查看依赖关系图" */}
                    </Button>
                </Fragment>
            );
        }
    };
    getCardChild = (treeNode) => {
        console.log(treeNode);
        return new Promise(async (resolve) => {
            let {
                ownerState: { diagramData },
                ownerStore: { changeState },
            } = this.props;
            let params = {
                mainCode: treeNode.props.eventKey,
            };
            let child = await this.props.ownerStore.handleGetDiagramAgain(params);
            console.log(child);
            getNewTreeData(diagramData, treeNode.props.id, child);
            changeState({
                diagramData,
            });
            resolve();
        });
    };
    render() {
        let { ownerState, onOk, taskInfo, ownerStore } = this.props;
        let { selectedRows } = this.state;
        let { dataSource, pagination, hasEdit, diagramData } = ownerState;
        if (pagination) {
            pagination.disabled = hasEdit;
        }
        return (
            <>
                <div className={styles["makeup-header"]}>
                    <div className={styles["makeup-header-left"]}>
                        <span style={{ fontSize: "14px", fontWeight: "600", color: "#333333" }}>
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180278", "问题数据") /* "问题数据" */}({dataSource.total})
                        </span>
                        &nbsp;&nbsp;&nbsp;
                        <div className={styles["makeup-header-tip"]}>
                            <Icon fieldid="UCG-FE-routes-list-components-DataMakeUpModal-index-4468988-Icon" type="uf-exc-c-o" />
                            {
                                lang.templateByUuid(
                                    "UID:P_UBL-FE_18D8CEF604180279",
                                    "请按照提示原因，移步至相应系统调整，然后再点击【重新上传】即可修复问题数据" //@notranslate
                                ) /* "请按照提示原因，移步至相应系统调整，然后再点击【重新上传】即可修复问题数据" */
                            }
                        </div>
                    </div>
                    <div className={styles["makeup-header-right"]}>
                        {this.renderHeader()}
                        <Button
                            fieldid="ublinker-routes-list-components-DataMakeUpModal-index-4627003-Button"
                            onClick={() => {
                                if ([1, 2].includes(this.props.taskInfo?.fromType)) {
                                    let isSameBatch = selectedRows.some((item) => item?.callbackId !== selectedRows[0]?.callbackId);
                                    if (isSameBatch) {
                                        Warning(lang.templateByUuid("UID:P_UBL-FE_20076E5804280037", "请选择同批次数据上传") /* "请选择同批次数据上传" */);
                                        return;
                                    }
                                }
                                let errorDataIds = selectedRows.map((obj, index) => {
                                    return obj["pk_id"];
                                });
                                onOk(taskInfo, errorDataIds, selectedRows[0]);
                            }}
                            disabled={dataSource.list.length == 0 ? true : false}
                            colors="primary"
                        >
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418027B", "重新上传") /* "重新上传" */}
                        </Button>
                    </div>
                </div>
                <Grid
                    fieldid="ublinker-routes-list-components-DataMakeUpModal-index-4897263-Grid"
                    multiSelect
                    selectedList={selectedRows}
                    getSelectedDataFunc={this.getSelectedDataFunc}
                    columns={[1, 2].includes(this.props.taskInfo.fromType) ? this.columns.concat(this.schemaColumns) : this.columns}
                    data={dataSource.list}
                    autoCheckedByClickRows={false}
                    pagination={false}
                    dataKey={"pk_id"}
                    hoverContent={this.hoverContent}
                    columnFilterAble
                    scroll={{ y: this.props.iframeSize.height - 155 }}
                />
                {pagination && (
                    <Pagination
                        fieldid="9ec0535f-e91f-4ead-9b98-f49c5ce46b25"
                        current={pagination.activePage}
                        onChange={(a, b) => pagination?.onPageChange({ pageSize: b, pageNo: a })}
                        onPageSizeChange={(a, b) => pagination?.onPageChange({ pageSize: b, pageNo: 1 })}
                        showSizeChanger
                        total={pagination.total}
                        pageSize={pagination.pageSize}
                        pageSizeOptions={[...Pagination.dataNumSelect["page"]]}
                        style={{ position: "fixed", bottom: "0", zIndex: "2", backgroundColor: "#fff", width: "100%" }}
                    />
                )}
                <Modal
                    fieldid="ublinker-routes-list-components-DataMapModal-index-7782356-Modal"
                    show={this.state.diagramModalShow}
                    title={
                        <div style={{ display: "flex" }}>
                            <div className={styles.modalTitle}>{lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180282", "查看依赖数据") /* "查看依赖数据" */}</div>
                            <div className={styles["makeup-header-tip"]}>
                                <Icon fieldid="UCG-FE-routes-list-components-DataMakeUpModal-index-5520793-Icon" type="uf-exc-c-o" />
                                {
                                    lang.templateByUuid(
                                        "UID:P_UBL-FE_18D8CEF604180279",
                                        "请按照提示原因，移步至相应系统调整，然后再点击【重新上传】即可修复问题数据" //@notranslate
                                    ) /* "请按照提示原因，移步至相应系统调整，然后再点击【重新上传】即可修复问题数据" */
                                }
                            </div>
                        </div>
                    }
                    cancelText={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180283", "关闭") /* "关闭" */}
                    //  size="xlg"
                    className={styles.diagramModal}
                    width={"96%"}
                    height={"88%"}
                    footer={null}
                    okHide
                    cancelHide
                    onCancel={() => {
                        this.setState({ diagramModalShow: false });
                    }}
                >
                    {/* <div style={{height:'100%'}}> */}
                    <Card
                        fieldid="UCG-FE-routes-list-components-DataMakeUpModal-index-1284-Card"
                        diagramData={diagramData}
                        ownerState={ownerState}
                        ownerStore={ownerStore}
                    />
                </Modal>
            </>
        );
    }
}

export default DataMakeUpModal;
