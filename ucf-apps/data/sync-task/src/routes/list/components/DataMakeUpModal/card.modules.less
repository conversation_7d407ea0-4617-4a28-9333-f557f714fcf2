.cotainer {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    height: auto;
    // padding-left:300px;
    // width:700px;
}

.box1 {
    display: flex;
    position: relative;
    align-items: center;
    // margin-bottom:20px;
    height: 100%;
    position: relative;
}

.box1-left {
    position: relative;
    width: 300px;
}

.box {
    width: 300px;
    height: 104px;
    background: #ffffff;
    border-radius: 3px;
    border: 1px solid #dbdee5;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    z-index: 99;
    position: relative;
}
.box-errorTableName {
    padding: 10px 10px 0 10px;

    display: flex;
    align-items: center;

    .box-errorTableName-load {
        font-size: 12px;
        font-family:
            PingFangSC-Regular,
            PingFang SC;
        font-weight: 400;
        color: #ee2233;
        line-height: 18px;
        cursor: pointer;
    }
}
.box-errorTableName-img {
    width: 17px;
    height: 20px;
    margin-right: 10px;
}
.box-errorTableName-title {
    font-size: 14px;
    font-family: PingFang-SC-Heavy, PingFang-SC;
    font-weight: 800;
    color: #333333;
    overflow: hidden;
    text-overflow: ellipsis;
    // display: -webkit-box;
    -webkit-box-orient: vertical;
    white-space: nowrap;
    width: 190px;
}
.box-errorTableName-code {
    font-size: 12px;
    font-family:
        PingFangSC-Regular,
        PingFang SC;
    font-weight: 400;
    color: #999999;
    overflow: hidden;
    text-overflow: ellipsis;
    // display: -webkit-box;
    -webkit-box-orient: vertical;
    white-space: nowrap;
    width: 190px;
}
.box-errorTableName-tag {
    width: 30px;
    height: 16px;
    background: rgba(88, 140, 233, 0.05);
    border-radius: 4px;
    border: 1px solid #588ce9;
    text-align: center;
    font-size: 11px;
    font-family:
        PingFangSC-Regular,
        PingFang SC;
    font-weight: 300;
    color: #588ce9;
    line-height: 16px;
}

.box-errorReason {
    background: #f5f9ff;

    padding: 10px 10px;
    box-sizing: border-box;

    font-size: 12px;
    font-family:
        PingFangSC-Regular,
        PingFang SC;
    font-weight: 400;
    color: #333333;
    line-height: 18px;
    height: 48px;

    overflow: hidden;
    -webkit-line-clamp: 2;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
}
.box1-line {
    position: absolute;
    width: 1px;
    left: 125px;
    top: 50px;
    background-color: #505766;
}
.right {
    height: auto;
    position: relative;
    .cotainer {
        justify-content: space-between;
    }
    .box1 {
        margin-top: 20px;
        margin-left: 20px;
        height: auto;
    }

    .box1:after {
        position: absolute;
        content: " ";
        width: 195px;
        height: 1px;
        background: #505766;
        left: -195px;
        top: 50%;
    }
    .box1:before {
        position: absolute;
        content: " ";
        width: 1px;
        height: 1px;
        top: 50%;
        margin-top: -6px;
        left: -5px;
        border-left: 6px solid #505766;
        border-top: 6px solid transparent;
        border-bottom: 6px solid transparent;
    }

    .box1:first-child {
        margin-top: 0px;

        border-top: none;
        padding-top: 0;
    }
}
