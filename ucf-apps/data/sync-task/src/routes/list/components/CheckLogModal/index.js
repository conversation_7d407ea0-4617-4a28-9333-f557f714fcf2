/*
 * @Author: your name
 * @Date: 2021-04-15 17:40:03
 * @LastEditTime: 2021-06-10 14:32:52
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \ublinker-fe\ucf-apps\data\sync-task\src\routes\list\components\SqlWhereModal\index.js
 */
import React, { Component } from "react";
import Modal from "components/TinperBee/Modal";
import { autoServiceMessage } from "utils/service";
import { getCheckConditionService, getViewInfoService, saveViewInfoService, getCheckLogService, restoreSQLService } from "../../service";
import { Checkbox, FormControl, Tabs, Button, FormList } from "components/TinperBee";
import "./index.less";
const FormItem = FormList.Item;
const labelCol = 100;

const TabPane = Tabs.TabPane;
class SqlWhereModal extends Component {
    constructor() {
        super();
        this.state = {
            logData: {},
        };
    }

    onChange = (value) => {
        this.setState({
            param: value,
        });
    };
    render() {
        let { show, onCancel, taskInfo, logData } = this.props;
        return (
            <Modal
                fieldid="ublinker-routes-list-components-CheckLogModal-index-377568-Modal"
                show={show}
                title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180535", "排查日志", undefined, {
                    returnStr: true,
                })}
                cancelText={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180537", "关闭") /* "关闭" */}
                onCancel={onCancel}
                okHide
            >
                {show && taskInfo ? (
                    <div className="">
                        <Tabs
                            fieldid="ublinker-routes-list-components-CheckLogModal-index-9834828-Tabs"
                            defaultActiveKey="1"
                            // onChange={this.handleTabChange}
                        >
                            <TabPane
                                fieldid="UCG-FE-routes-list-components-CheckLogModal-index-9823364-TabPane"
                                key="1"
                                tab={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418053C", "来源数据查询日志") /* "来源数据查询日志" */}
                            >
                                {logData.queryType == 2 ? (
                                    <div className="checkLog ucg-pad-20">
                                        <FormList
                                            fieldid="ublinker-routes-list-components-CheckLogModal-index-3278381-FormList"
                                            layoutOpt={{ md: 12 }}
                                            className="config-action-form2"
                                        >
                                            <FormItem
                                                fieldid="ublinker-routes-list-components-CheckLogModal-index-6807634-FormItem"
                                                label={lang.templateByUuid("UID:P_UBL-FE_18E7259004680048", "请求体") /* "请求体" */}
                                                labelCol={labelCol}
                                            >
                                                <span>{logData.queryParam}</span>
                                            </FormItem>
                                            <FormItem
                                                fieldid="ublinker-routes-list-components-CheckLogModal-index-9589853-FormItem"
                                                label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180533", "返回结果：") /* "返回结果：" */}
                                                labelCol={labelCol}
                                            >
                                                <span>{logData.queryResult}</span>
                                            </FormItem>
                                            <FormItem
                                                fieldid="ublinker-routes-list-components-CheckLogModal-index-6640788-FormItem"
                                                label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180530", "开始时间：") /* "开始时间：" */}
                                                labelCol={labelCol}
                                            >
                                                <span>{logData.queryStartTime}</span>
                                            </FormItem>
                                            <FormItem
                                                fieldid="ublinker-routes-list-components-CheckLogModal-index-3178675-FormItem"
                                                label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180531", "结束时间：") /* "结束时间：" */}
                                                labelCol={labelCol}
                                            >
                                                <span>{logData.queryEndTime}</span>
                                            </FormItem>
                                        </FormList>
                                    </div>
                                ) : (
                                    <div className="checkLog ucg-pad-20">
                                        <FormList
                                            fieldid="ublinker-routes-list-components-CheckLogModal-index-4301328-FormList"
                                            layoutOpt={{ md: 12 }}
                                            className="config-action-form2"
                                        >
                                            <FormItem
                                                fieldid="ublinker-routes-list-components-CheckLogModal-index-8222811-FormItem"
                                                label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418053A", "请求URL：") /* "请求URL：" */}
                                                labelCol={labelCol}
                                            >
                                                <span>{logData.queryUrl}</span>
                                            </FormItem>
                                            <FormItem
                                                fieldid="ublinker-routes-list-components-CheckLogModal-index-6171456-FormItem"
                                                label={lang.templateByUuid("UID:P_UBL-FE_18E7259004680049", "请求头") /* "请求头" */}
                                                labelCol={labelCol}
                                            >
                                                <span>{logData.queryHeader}</span>
                                            </FormItem>
                                            <FormItem
                                                fieldid="ublinker-routes-list-components-CheckLogModal-index-6195199-FormItem"
                                                label={lang.templateByUuid("UID:P_UBL-FE_18E7259004680048", "请求体") /* "请求体" */}
                                                labelCol={labelCol}
                                            >
                                                <span>{logData.queryParam}</span>
                                            </FormItem>
                                            <FormItem
                                                fieldid="ublinker-routes-list-components-CheckLogModal-index-1205859-FormItem"
                                                label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180533", "返回结果：") /* "返回结果：" */}
                                                labelCol={labelCol}
                                            >
                                                <span>{logData.queryResult}</span>
                                            </FormItem>
                                            <FormItem
                                                fieldid="ublinker-routes-list-components-CheckLogModal-index-4243622-FormItem"
                                                label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180530", "开始时间：") /* "开始时间：" */}
                                                labelCol={labelCol}
                                            >
                                                <span>{logData.queryStartTime}</span>
                                            </FormItem>
                                            <FormItem
                                                fieldid="ublinker-routes-list-components-CheckLogModal-index-8007709-FormItem"
                                                label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180531", "结束时间：") /* "结束时间：" */}
                                                labelCol={labelCol}
                                            >
                                                <span>{logData.queryEndTime}</span>
                                            </FormItem>
                                        </FormList>
                                    </div>
                                )}
                            </TabPane>

                            <TabPane
                                fieldid="UCG-FE-routes-list-components-CheckLogModal-index-7480585-TabPane"
                                key="2"
                                tab={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180536", "目标数据查询日志") /* "目标数据查询日志" */}
                            >
                                <div className="checkLog ucg-pad-20">
                                    <FormList
                                        fieldid="ublinker-routes-list-components-CheckLogModal-index-5353911-FormList"
                                        layoutOpt={{ md: 12 }}
                                        className="config-action-form2"
                                    >
                                        <FormItem
                                            fieldid="ublinker-routes-list-components-CheckLogModal-index-1038927-FormItem"
                                            label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180539", "回调URL：") /* "回调URL：" */}
                                            labelCol={labelCol}
                                        >
                                            <span>{logData.pushUrl}</span>
                                        </FormItem>
                                        <FormItem
                                            fieldid="ublinker-routes-list-components-CheckLogModal-index-7244101-FormItem"
                                            label={lang.templateByUuid("UID:P_UBL-FE_18E7259004680049", "请求头") /* "请求头" */}
                                            labelCol={labelCol}
                                        >
                                            <span>{logData.pushHeader}</span>
                                        </FormItem>
                                        <FormItem
                                            fieldid="ublinker-routes-list-components-CheckLogModal-index-6218165-FormItem"
                                            label={lang.templateByUuid("UID:P_UBL-FE_18E7259004680048", "请求体") /* "请求体" */}
                                            labelCol={labelCol}
                                        >
                                            <span>{logData.pushParam}</span>
                                        </FormItem>
                                        <FormItem
                                            fieldid="ublinker-routes-list-components-CheckLogModal-index-2087217-FormItem"
                                            label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180533", "返回结果：") /* "返回结果：" */}
                                            labelCol={labelCol}
                                        >
                                            <span>{logData.pushResult}</span>
                                        </FormItem>
                                        <FormItem
                                            fieldid="ublinker-routes-list-components-CheckLogModal-index-2312657-FormItem"
                                            label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180530", "开始时间：") /* "开始时间：" */}
                                            labelCol={labelCol}
                                        >
                                            <span>{logData.pushStartTime}</span>
                                        </FormItem>
                                        <FormItem
                                            fieldid="ublinker-routes-list-components-CheckLogModal-index-6435349-FormItem"
                                            label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180531", "结束时间：") /* "结束时间：" */}
                                            labelCol={labelCol}
                                        >
                                            <span>{logData.pushEndTime}</span>
                                        </FormItem>
                                    </FormList>
                                </div>
                            </TabPane>
                            {!logData.status && (
                                <TabPane
                                    fieldid="UCG-FE-routes-list-components-CheckLogModal-index-437993-TabPane"
                                    key="3"
                                    tab={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180534", "失败原因") /* "失败原因" */}
                                >
                                    <div className="checkLog ucg-pad-20">
                                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180538", "执行失败：") /* "执行失败：" */}
                                        <div className="ucg-pad-20 scrollX">{logData.errorMessage}</div>
                                    </div>
                                </TabPane>
                            )}
                        </Tabs>
                    </div>
                ) : null}
            </Modal>
        );
    }
}

export default SqlWhereModal;
