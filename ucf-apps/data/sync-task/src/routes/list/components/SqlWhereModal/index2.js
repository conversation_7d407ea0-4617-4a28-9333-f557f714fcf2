/*
 * @Author: your name
 * @Date: 2021-04-15 17:40:03
 * @LastEditTime: 2021-06-10 14:32:52
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \ublinker-fe\ucf-apps\data\sync-task\src\routes\list\components\SqlWhereModal\index.js
 */
import React, { Component } from "react";
import { Input } from "@tinper/next-ui";
import { Checkbox, FormControl, FormList, Row, Col, InputGroup, Dropdown, Menu, Button, DatePicker } from "components/TinperBee";
import { Success, Error } from "utils/feedback";
// import lang from 'tne-core-fe/i18n';
const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 12 },
};
const placeholders = {
    en_US: {
        DatePicker: "DatePicker",
        MonthPicker: "MonthPicker",
        QuarterPicker: "QuarterPicker",
        WeekPicker: "WeekPicker",
        HalfYearPicker: "HalfYearPicker",
        YearPicker: "YearPicker",
    },
    zh_CN: {
        DatePicker: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418019D", "日期") /* "日期" */,
        MonthPicker: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180197", "月") /* "月" */,
        WeekPicker: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180198", "周") /* "周" */,
        QuarterPicker: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180199", "季度") /* "季度" */,
        HalfYearPicker: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418019A", "半年") /* "半年" */,
        YearPicker: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418019B", "年") /* "年" */,
    },
    zh_TW: {
        DatePicker: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418019D", "日期") /* "日期" */,
    },
    id_ID: {
        DatePicker: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418019D", "日期") /* "日期" */,
    },
};
class SqlWhereModal extends Component {
    constructor() {
        super();
        this.state = {
            areaDisable: false,
            inputValue: "",
            show: false,
            searchKey: "",
            locale: lang.lang,
            placeholders: placeholders[lang.lang],
        };
        this.form = React.createRef();
    }
    // async componentWillReceiveProps(nextProps) {
    //   if (nextProps.show !== this.props.show) {
    //     this.setState({ areaDisable: nextProps.taskInfo ? nextProps.taskInfo.increment : false })
    //     this.setState({ show: nextProps.show,searchKey: '' })
    //     if (nextProps.show) {
    //       let res = await nextProps.ownerStore.getDataColumns(nextProps.taskInfo.pk_id, nextProps.taskInfo.dataview.tableview);
    //     }

    //   }

    // }
    async componentDidMount() {
        const {
            ownerStore,
            ownerState: { selectedTask },
        } = this.props;
        this.setState({ areaDisable: selectedTask ? selectedTask.increment : false, searchKey: "" });
        ownerStore.getDataColumns(selectedTask.pk_id, selectedTask.dataview.tableview);
    }
    handleOk = () => {
        let data = {
            sqlwhere: btoa(encodeURIComponent(this.sqlwhereInput?.input?.state?.value)),
            // sqlwhere: btoa(encodeURIComponent(this.state.inputValue)),
            increment: this.incrementCheckBox.state.checked,
            incrementTime: this.state.incrementTime || this.props.ownerState.selectedTask.lastsuctime,
        };
        // console.log(data,)
        this.props.ownerStore.setSqlWhere(data);
    };
    change = (e) => {
        let selectedTask = this.props.ownerState.selectedTask;
        if (e && this.sqlwhereInput?.input?.state?.value) {
            //勾选的时候,并且文本框里有值的时候,提示,并且不让选
            Error(
                lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418019E", "增量不支持单独设置条件", undefined, {
                    returnStr: true,
                }) /* "增量不支持单独设置条件" */
            );
            selectedTask.increment = false;
            this.props.ownerStore.changeState({
                selectedTask,
            });
            return;
        }
        selectedTask.increment = !selectedTask.increment;
        this.props.ownerStore.changeState({
            selectedTask,
        });
        this.setState({
            areaDisable: e,
        });
    };
    changeDate = (e, b) => {
        this.setState({
            incrementTime: b,
        });
    };
    inputChange = (value) => {
        this.setState({
            inputValue: value,
        });
        let task = this.props.ownerState.selectedTask;
        task.sqlwhere = value;
        this.props.ownerStore.changeState({
            selectedTask: task,
        });
    };
    handleChoseKey = async (data) => {
        this.setState({
            searchKey: data.key,
        });
        await this.props.ownerStore.getRealColumn({
            field: data.key,
            tableView: this.props.ownerState.selectedTask.dataview.tableview,
        });
    };
    getSearchKeyMenu2 = (mapresultArr, searchKey) => {
        return (
            <Menu
                fieldid="ublinker-routes-list-components-SqlWhereModal-index2-2546676-Menu"
                selectedKeys={[searchKey]}
                onSelect={this.handleChoseKey}
                className="data-detail-menu"
            >
                {mapresultArr.selectData &&
                    mapresultArr.selectData.map((item) => {
                        let { key, value } = item;
                        return (
                            <Menu.Item fieldid="UCG-FE-routes-list-components-SqlWhereModal-index2-1505095-Menu.Item" key={value}>
                                {key}
                            </Menu.Item>
                        );
                    })}
            </Menu>
        );
    };
    render() {
        let { show: show2, searchKey } = this.state;

        let {
            show,
            onCancel,
            taskInfo,
            ownerState,
            ownerState: { selectedTask },
        } = this.props;
        let { dataSource, pkField, columns, resultArr2, getRealColumnContent, queryCols, pagination, hasEdit } = ownerState;
        let searchKey2 = resultArr2.selectData && resultArr2.selectData[0] && resultArr2.selectData[0].value;
        let { areaDisable } = this.state;
        return (
            <div className="">
                <p className="ucg-mar-b-5">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801A5", "SQL条件") /* "SQL条件" */}</p>
                <Input
                    type="textarea"
                    // componentClass="textarea"
                    rows={4}
                    defaultValue={(selectedTask && selectedTask.sqlwhere) || ""}
                    className="ucg-mar-b-10"
                    // placeholder={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418019C","查询条件，无需输入where关键词，示例：code='2212' and ts>@lastupdatetime[@lastupdatetime为最后一次成功同步时间") /* "查询条件，无需输入where关键词，示例：code='2212' and ts>@lastupdatetime[@lastupdatetime为最后一次成功同步时间" */}
                    ref={(node) => (this.sqlwhereInput = node)}
                    style={{ height: "auto" }}
                    disabled={areaDisable}
                    onChange={this.inputChange}
                    // autoSize={{
                    //   minRows: 4
                    // }}
                />
                <div style={{ marginBottom: 10, display: "flex", alignItems: "center" }}>
                    {/* <Row> */}
                    <Col md={15}>
                        <Checkbox
                            fieldid="ublinker-routes-list-components-SqlWhereModal-index2-63628-Checkbox"
                            // defaultChecked={selectedTask && selectedTask.increment}
                            checked={selectedTask && selectedTask.increment}
                            ref={(node) => (this.incrementCheckBox = node)}
                            onChange={this.change}
                        >
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801A1", "是否启用增量") /* "是否启用增量" */}
                            &nbsp;&nbsp; ({" "}
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801A3", "如需修改sql条件，请取消选中") /* "如需修改sql条件，请取消选中" */} )
                        </Checkbox>
                    </Col>
                    <Col md={8} style={{ display: "flex", alignItems: "center" }}>
                        <div style={{ minWidth: "124px" }}>{lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801A4", "增量查询时间") /* "增量查询时间" */}</div>
                        <DatePicker
                            fieldid="UCG-FE-routes-list-components-SqlWhereModal-index2-8647587-DatePicker"
                            defaultValue={selectedTask && selectedTask.lastsuctime}
                            format="YYYY-MM-DD HH:mm:ss"
                            onChange={this.changeDate}
                            onSelect={this.select}
                            showTime
                        />
                        {/* locale={this.state.locale} placeholder={this.state.placeholders.DatePicker} */}
                    </Col>
                    {/* </Row> */}
                </div>

                {/* {queryCols.length != 0 && JSON.stringify(resultArr2) != "{}" ? */}
                {resultArr2.selectData && resultArr2.selectData.length != 0 && JSON.stringify(resultArr2) != "{}" ? (
                    <>
                        <InputGroup fieldid="ublinker-routes-list-components-SqlWhereModal-index2-385750-InputGroup" style={{ marginLeft: 1 }}>
                            <div style={{ width: "1px", height: "1px", display: "none" }}></div>

                            <InputGroup.Button>
                                <Dropdown
                                    fieldid="ublinker-routes-list-components-SqlWhereModal-index2-4645652-Dropdown"
                                    className="data-detail-dropdown"
                                    style={{ height: 300, overflow: "auto" }}
                                    trigger={["click"]}
                                    overlay={this.getSearchKeyMenu2(resultArr2, searchKey || searchKey2)}
                                    animation="slide-up"
                                    // getPopupContainer={() => headerContainer}
                                    // onSelect={setSearchKey}
                                >
                                    <Button fieldid="ublinker-routes-list-components-SqlWhereModal-index2-3079038-Button" bordered style={{ width: 150 }}>
                                        {resultArr2.getNameByCode && resultArr2.getNameByCode(searchKey || searchKey2)}{" "}
                                        <i fieldid="ublinker-routes-list-components-SqlWhereModal-index2-9848291-i" className="uf uf-arrow-down" />
                                    </Button>
                                </Dropdown>
                            </InputGroup.Button>
                            <div style={{ width: "1px", height: "1px", display: "none" }}></div>

                            {/* <div>
                  <FormControl fieldid="ublinker-routes-list-components-SqlWhereModal-index2-8583637-FormControl" 
                    disabled
                    className="drop-form-contorl"
                    // onChange={setSearchValue}
                    value={getRealColumnContent}
                    style={{ width: 200 }}
                  />
                  </div> */}
                        </InputGroup>
                        <div>
                            <FormControl.TextArea
                                disabled
                                className="drop-form-contorl"
                                // onChange={setSearchValue}
                                value={getRealColumnContent}
                                // style={{ width: 200 }}
                            />
                        </div>
                    </>
                ) : null}

                <div style={{ margin: 20 }}></div>
                <p className="ucg-mar-b-5">
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418019F", "修改人") /* "修改人" */} : {selectedTask && selectedTask.modifior}
                </p>
                <div style={{ margin: 10 }}></div>
                <p className="ucg-mar-b-5">
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801A0", "修改时间") /* "修改时间" */} : {selectedTask && selectedTask.modifiedTime}
                </p>
            </div>
        );
    }
}

export default SqlWhereModal;
