/*
 * @Author: your name
 * @Date: 2021-04-15 17:40:03
 * @LastEditTime: 2021-06-10 14:32:52
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \ublinker-fe\ucf-apps\data\sync-task\src\routes\list\components\SqlWhereModal\index.js
 */
import React, { Component } from "react";
import Modal from "components/TinperBee/Modal";
import SearchPanel from "../../../data-detail/components/SearchPanel";
import { Checkbox, FormControl, FormList, Row, Col, InputGroup, Dropdown, Menu, Button } from "components/TinperBee";
const FormItem = FormList.Item;
const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 12 },
};
class SqlWhereModal extends Component {
    constructor() {
        super();
        this.state = {
            areaDisable: false,
            inputValue: "",
            show: false,
            searchKey: "",
        };
        this.form = React.createRef();
    }
    componentDidUpdate(prevProps) {
        if (this.props.show !== prevProps.show) {
            this.setState({ areaDisable: this.props.taskInfo ? this.props.taskInfo.increment : false });
            this.setState({ show: this.props.show, searchKey: "" });
            if (this.props.show) {
                this.props.ownerStore.getDataColumns(this.props.taskInfo.pk_id, this.props.taskInfo.dataview.tableview);
            }
        }
    }
    handleOk = () => {
        let data = {
            sqlwhere: btoa(encodeURIComponent(this.sqlwhereInput.state.value)),
            // sqlwhere: btoa(encodeURIComponent(this.state.inputValue)),
            increment: this.incrementCheckBox.state.checked,
        };
        // console.log(this.sqlwhereInput.state.value)
        this.props.onOk(data);
    };
    change = (e) => {
        this.setState({
            areaDisable: e,
        });
    };
    inputChange = (value) => {
        this.setState({
            inputValue: value,
        });
    };
    handleChoseKey = async (data) => {
        this.setState({
            searchKey: data.key,
        });
        await this.props.ownerStore.getRealColumn({
            field: data.key,
            tableView: this.props.taskInfo.dataview.tableview,
        });
    };
    getSearchKeyMenu2 = (mapresultArr, searchKey) => {
        return (
            <Menu
                fieldid="ublinker-routes-list-components-SqlWhereModal-index-4120548-Menu"
                selectedKeys={[searchKey]}
                onSelect={this.handleChoseKey}
                className="data-detail-menu"
            >
                {mapresultArr.selectData &&
                    mapresultArr.selectData.map((item) => {
                        let { key, value } = item;
                        return (
                            <Menu.Item fieldid="UCG-FE-routes-list-components-SqlWhereModal-index-6288446-Menu.Item" key={value}>
                                {key}
                            </Menu.Item>
                        );
                    })}
            </Menu>
        );
    };
    render() {
        let { show: show2, searchKey } = this.state;
        if (show2) {
            let { show, onCancel, taskInfo, ownerState } = this.props;
            let { dataSource, pkField, columns, resultArr2, getRealColumnContent, queryCols, pagination, hasEdit } = ownerState;
            let searchKey2 = resultArr2.selectData && resultArr2.selectData[0] && resultArr2.selectData[0].value;
            let { areaDisable } = this.state;
            return (
                <Modal
                    fieldid="ublinker-routes-list-components-SqlWhereModal-index-8379965-Modal"
                    show={show}
                    title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180448", "视图任务编辑", undefined, {
                        returnStr: true,
                    })}
                    onCancel={onCancel}
                    onOk={this.handleOk}
                >
                    {show && taskInfo ? (
                        <div className="ucg-pad-20">
                            <p className="ucg-mar-b-5">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180449", "SQL条件") /* "SQL条件" */}</p>
                            <FormControl.TextArea
                                // componentClass="textarea"
                                rows={4}
                                defaultValue={taskInfo.sqlwhere || ""}
                                className="ucg-mar-b-10"
                                // placeholder={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418044B","查询条件，无需输入where关键词，示例：code='2212' and ts>@lastupdatetime[@lastupdatetime为最后一次成功同步时间") /* "查询条件，无需输入where关键词，示例：code='2212' and ts>@lastupdatetime[@lastupdatetime为最后一次成功同步时间" */}
                                ref={(node) => (this.sqlwhereInput = node)}
                                style={{ height: "auto" }}
                                disabled={areaDisable}
                                onChange={this.inputChange}
                                // autoSize={{
                                //   minRows: 4
                                // }}
                            />
                            <div style={{ marginLeft: -24, marginBottom: 10 }}>
                                <Checkbox
                                    fieldid="ublinker-routes-list-components-SqlWhereModal-index-8427973-Checkbox"
                                    defaultChecked={taskInfo.increment}
                                    ref={(node) => (this.incrementCheckBox = node)}
                                    onChange={this.change}
                                >
                                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418044A", "是否启用增量") /* "是否启用增量" */}
                                    &nbsp;&nbsp; ({" "}
                                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418044C", "如需修改sql条件，请取消选中") /* "如需修改sql条件，请取消选中" */} )
                                </Checkbox>
                            </div>

                            {/* {queryCols.length != 0 && JSON.stringify(resultArr2) != "{}" ? */}
                            {resultArr2.selectData && resultArr2.selectData.length != 0 && JSON.stringify(resultArr2) != "{}" ? (
                                <>
                                    <InputGroup fieldid="ublinker-routes-list-components-SqlWhereModal-index-9474412-InputGroup" style={{}}>
                                        <InputGroup.Button>
                                            <Dropdown
                                                fieldid="ublinker-routes-list-components-SqlWhereModal-index-7910970-Dropdown"
                                                className="data-detail-dropdown"
                                                style={{ height: 300, overflow: "auto" }}
                                                trigger={["click"]}
                                                overlay={this.getSearchKeyMenu2(resultArr2, searchKey || searchKey2)}
                                                animation="slide-up"
                                                // getPopupContainer={() => headerContainer}
                                                // onSelect={setSearchKey}
                                            >
                                                <Button
                                                    fieldid="ublinker-routes-list-components-SqlWhereModal-index-6720738-Button"
                                                    bordered
                                                    style={{ width: 150 }}
                                                >
                                                    {resultArr2.getNameByCode && resultArr2.getNameByCode(searchKey || searchKey2)}{" "}
                                                    <i fieldid="ublinker-routes-list-components-SqlWhereModal-index-4993745-i" className="uf uf-arrow-down" />
                                                </Button>
                                            </Dropdown>
                                        </InputGroup.Button>
                                        {/* <div>
                  <FormControl fieldid="ublinker-routes-list-components-SqlWhereModal-index-9656014-FormControl" 
                    disabled
                    className="drop-form-contorl"
                    // onChange={setSearchValue}
                    value={getRealColumnContent}
                    style={{ width: 200 }}
                  />
                  </div> */}
                                    </InputGroup>
                                    <div>
                                        <FormControl.TextArea
                                            disabled
                                            className="drop-form-contorl"
                                            // onChange={setSearchValue}
                                            value={getRealColumnContent}
                                            // style={{ width: 200 }}
                                        />
                                    </div>
                                </>
                            ) : null}

                            <div style={{ margin: 20 }}></div>
                            <p className="ucg-mar-b-5">
                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418044D", "修改人") /* "修改人" */} : {taskInfo.modifior}
                            </p>
                            <div style={{ margin: 10 }}></div>
                            <p className="ucg-mar-b-5">
                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418044E", "修改时间") /* "修改时间" */} : {taskInfo.modifiedTime}
                            </p>
                        </div>
                    ) : null}
                </Modal>
            );
        } else {
            return null;
        }
    }
}

export default SqlWhereModal;
