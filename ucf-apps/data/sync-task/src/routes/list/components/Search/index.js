import React, { useCallback, useEffect } from "react";
// import FormList from "components/TinperBee/Form";
import { FormControl, Select, FormList, Row, Col } from "components/TinperBee";
// import { taskStatusTypes } from "../../../../common/constants";
import SearchPanel from "components/SearchPanel";
import { initState } from "../../store";
import { createEnum } from "constants/utils";

import "./index.less";

const FormItem = FormList.Item;

const layoutOpt = { md: 4 };
const searchLabelCol = 100;

const Search = (props) => {
    const [form] = FormList.useForm();
    const formItemLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 14 },
    };
    const { setFieldsValue, validateFields, getFieldProps } = form;
    useEffect(() => {}, []);
    const taskStatusTypes = createEnum([
        {
            code: "0",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804F5", "未运行") /* "未运行" */,
            cls: "task-status-not",
        },
        {
            code: "1",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804E9", "运行中") /* "运行中" */,
            cls: "task-status-doing",
        },
        {
            code: "2",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804EB", "运行成功") /* "运行成功" */,
            cls: "task-status-success",
        },
        {
            code: "3",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804EE", "运行失败") /* "运行失败" */,
            cls: "task-status-fail",
        },
        {
            code: "4",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804F0", "部分成功") /* "部分成功" */,
            // name: "运行成功(含异常数据)",
            // tagName: "部分成功",
            cls: "task-status-success-second",
        },
        {
            code: "5",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804F1", "运行冲突") /* "运行冲突" */,
            cls: "task-status-fail",
        },
        {
            code: "6",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804F3", "跳过") /* "跳过" */,
            cls: "task-status-jump",
        },
        {
            code: "7",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804F4", "等待") /* "等待" */,
            cls: "task-status-success-second",
        },
        {
            code: "8",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804E8", "数据处理中") /* "数据处理中" */,
            cls: "task-status-success-second",
        },
    ]);
    const { query, onSearch } = props;

    const handleSearch = useCallback(async () => {
        const values = await validateFields();
        onSearch && onSearch(values);
    }, []);
    const handleKeyDown = (e) => {
        if (e.keyCode == 13) {
            handleSearch();
        }
    };
    const handleReset = useCallback(() => {
        setFieldsValue(initState.query);
        handleSearch();
    }, []);

    return (
        <SearchPanel className={"sync-task-search"} reset={handleReset} search={handleSearch}>
            <FormList
                fieldid="ublinker-routes-list-components-Search-index-2913495-FormList"
                size="sm"
                // layout='inline'
                // labelCol={searchLabelCol}
                form={form}
                name="form122"
                labelAlign="right"
                {...formItemLayout}
            >
                <Row>
                    <Col span={7}>
                        <FormItem
                            fieldid="ublinker-routes-list-components-Search-index-3504938-FormItem"
                            label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803F4", "方案") /* "方案" */}
                            name="key"
                            initialValue={query.key}
                        >
                            <FormControl
                                fieldid="ublinker-routes-list-components-Search-index-7526568-FormControl"
                                className="ucg-mar-r-20"
                                size="sm"
                                onKeyDown={handleKeyDown}
                            />
                        </FormItem>
                    </Col>
                    <Col span={7}>
                        <FormItem
                            fieldid="ublinker-routes-list-components-Search-index-2414201-FormItem"
                            label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803F9", "启用/停用") /* "启用/停用" */}
                            name="enable"
                            initialValue={query.enable}
                        >
                            <Select
                                fieldid="ublinker-routes-list-components-Search-index-7378781-Select"
                                size="sm"
                                // {...getFieldProps('enable', {
                                // 	initialValue: query.enable
                                // })}
                            >
                                <Select.Option value="">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803F6", "全部") /* "全部" */}</Select.Option>
                                <Select.Option value="1">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803F7", "已启用") /* "已启用" */}</Select.Option>
                                <Select.Option value="0">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803F8", "未启用") /* "未启用" */}</Select.Option>
                            </Select>
                        </FormItem>
                    </Col>
                    <Col span={7}>
                        <FormItem
                            fieldid="ublinker-routes-list-components-Search-index-2524197-FormItem"
                            label={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803F5", "运行状态") /* "运行状态" */}
                            name="taskstatus"
                            initialValue={query.taskstatus}
                        >
                            <Select
                                fieldid="ublinker-routes-list-components-Search-index-2246887-Select"
                                size="sm"
                                // {...getFieldProps('taskstatus', {
                                // 	initialValue: query.taskstatus
                                // })}
                            >
                                <Select.Option value="">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803F6", "全部") /* "全部" */}</Select.Option>
                                {taskStatusTypes.selectData.map(({ key, value }) => {
                                    return (
                                        <Select.Option value={value} key={value}>
                                            {key}
                                        </Select.Option>
                                    );
                                })}
                            </Select>
                        </FormItem>
                    </Col>
                </Row>
            </FormList>
        </SearchPanel>
    );
};

export default Search;
