import React, { useEffect, useState } from "react";
import { Modal, Form, Switch, Input, DatePicker } from "@tinper/next-ui";
// import { mockData } from "./data";
import styles from "./index.modules.css";
import { autoServiceMessage } from "utils/service";
import { initTable } from "utils";
import * as service from "../../service";
// import DynamicForm from "components/DynamicForm";
import DynamicFormItem from "./DynamicFormItem";
import { CustomRangePicker, CustomMonacoEditor, CustomDateItem } from "./CustomFormItem";

const layout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
};

const ConditionFilterModal = (props) => {
    const [form] = Form.useForm();
    const [conditionData, setConditionData] = useState([]);
    const filterConditionRef = React.createRef(); // 数据过滤条件表单
    const { visible, onOk, onCancel, data = {}, ownerStore, ownerState } = props;
    const handleOk = async () => {
        const formData = await form.validateFields();
        onOk({ ...formData });
    };

    // useEffect(() => {
    //     // api触发或定时触发的集成流
    //     if ([5, 6].includes(data?.fromType)) {
    //         setConditionData([]);
    //     }
    // }, []);
    const handleSwitchAdditional = async (bool, ownerStore, ownerState) => {
        return;
        const data = props.data;
        if (bool) {
            //开关打开
            let res = await autoServiceMessage({
                service: service.queryApiParamDefService(
                    {
                        schemeCode: data?.startSchemeCode,
                        linkerCode: data?.integrateScheme?.sourceConnectorCode,
                        apiCode: data?.integrateScheme?.sourceOpreation?.[0],
                        objectCode: data?.integrateScheme?.sourceObjectCode,
                    },
                    { serviceCode: ownerState.serviceCodeDiwork }
                ),
            }).catch((err) => console.log(err));
            if (res?.data && Array.isArray(res?.data)) {
                setConditionData(res.data);
            }
            return;
        }
    };
    const TimeFilterDom = () => {
        return (
            <>
                <Form.Item
                    label={lang.templateByUuid("UID:P_UBL-FE_1C38306804F8001D", "增量时间戳") /* "增量时间戳" */}
                    name="lastsuctime"
                    tooltip={lang.templateByUuid("UID:P_UBL-FE_1C38306804F8001E", "按指定时间过滤数据") /* "按指定时间过滤数据" */}
                >
                    <CustomDateItem format="YYYY-MM-DD HH:mm:ss" showTime inputReadOnly />
                </Form.Item>
                <Form.Item
                    label={lang.templateByUuid("UID:P_UBL-FE_1C38306804F80020", "时间区间") /* "时间区间" */}
                    name="rangePicker"
                    tooltip={lang.templateByUuid("UID:P_UBL-FE_1C38306804F80021", "按一段时间范围过滤数据") /* "按一段时间范围过滤数据" */}
                >
                    <CustomRangePicker />
                </Form.Item>
            </>
        );
    };
    return (
        <Modal
            fieldid="d074cc42-fd48-407b-aa86-bb562660e15b"
            className={styles["condition-filter-modal"]}
            title={`${lang.templateByUuid("UID:P_UBL-FE_1C38306804F80022", "立即执行")}-${data?.dataTypeName || ""}${lang.templateByUuid("UID:P_UBL-FE_1C38306804F80023", "方案")}`}
            visible={visible}
            onOk={handleOk}
            onCancel={onCancel}
            bodyStyle={{ maxHeight: "510px" }}
        >
            <Form fieldid="8f6a9a76-8333-41c1-9c9b-324956cd4fdc" form={form} {...layout}>
                <Form.Item label={lang.templateByUuid("UID:P_UBL-FE_1C4A04A80590000E", "重复数据推送") /* "重复数据推送" */}>
                    <div style={{ display: "flex", alignItems: "center" }}>
                        <Form.Item noStyle name="isPull" valuePropName="checked" initialValue={false}>
                            <Switch fieldid="08721f05-56ca-48f8-acbb-918b83d43910" />
                        </Form.Item>
                        <span style={{ marginLeft: "10px" }}>
                            {lang.templateByUuid("UID:P_UBL-FE_1C91C14C04380009", "开启后，将不校验数据重复，直接推送下游")}
                        </span>
                    </div>
                </Form.Item>
                {/* api触发 */}

                {/* 定时触发 */}

                {[2, 3].includes(data?.fromType) ? (
                    <>
                        <Form.Item
                            name="additional"
                            label={lang.templateByUuid("UID:P_UBL-FE_1C38306804F80024", "按数据条件执行") /* "按数据条件执行" */}
                            valuePropName="checked"
                        >
                            <Switch fieldid="3a0faeef-753b-4c95-a5a5-209ccc584738" onChange={handleSwitchAdditional} />
                        </Form.Item>
                        <Form.Item noStyle dependencies={["additional"]}>
                            {({ getFieldValue }) => {
                                if (!getFieldValue("additional")) {
                                    return;
                                }
                                // return (
                                //     <>
                                //         {TimeFilterDom()}
                                //         <DynamicForm ref={filterConditionRef.current} data={data} items={conditionData} />
                                //     </>
                                // );
                                return (
                                    <>
                                        {TimeFilterDom()}
                                        {conditionData?.map((item = {}) => (
                                            <Form.Item
                                                key={item.code}
                                                label={item.name}
                                                name={item.code}
                                                tooltip={item.name}
                                                required={Boolean(item.required)}
                                                initialValue={item.value || item.defaultValue}
                                                rules={[
                                                    {
                                                        required: Boolean(item.required),
                                                        message: <span>{lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803CB", "请输入")}</span>,
                                                    },
                                                ]}
                                            >
                                                {item.paramType === "object" ? (
                                                    <CustomMonacoEditor />
                                                ) : (
                                                    <DynamicFormItem type={(item?.paramType || "").toLowerCase()} />
                                                )}
                                            </Form.Item>
                                        ))}
                                    </>
                                );
                            }}
                        </Form.Item>
                    </>
                ) : null}
            </Form>
        </Modal>
    );
};
export default ConditionFilterModal;
