import React from "react";
import { Switch, Input, InputNumber } from "@tinper/next-ui";
import { CustomDateItem, CustomNumberItem } from "./CustomFormItem";

const DynamicFormItem = ({ type, ...props }) => {
    // 用于显示不同的Content组件
    switch (type) {
        case "int":
        case "integer":
        case "short":
        case "byte":
        case "float":
        case "double":
        case "decimal":
        case "bigdecimal":
        case "number":
            return <InputNumber fieldid="1746d91a-b3d5-4b45-95fe-edd978e42f3e" {...props} placeholder={type} />;
        case "long":
            return <CustomNumberItem {...props} type={type} />;
        case "boolean":
            return <Switch fieldid="0b94e15e-4612-4788-bc4c-256d957cecba" {...props} placeholder={type} />;
        case "datetime":
            return <CustomDateItem {...props} format="YYYY-MM-DD HH:mm:ss" type={type} showTime inputReadOnly />;
        case "date":
            return <CustomDateItem {...props} format="YYYY-MM-DD" type={type} inputReadOnly />;

        default: //string
            return <Input fieldid="fad3a4a6-1d79-4f1d-8126-121f54db0af0" {...props} placeholder={type} />;
    }
};
export default DynamicFormItem;
