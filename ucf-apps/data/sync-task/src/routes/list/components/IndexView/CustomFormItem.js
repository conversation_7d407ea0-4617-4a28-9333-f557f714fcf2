import React, { useEffect, useRef } from "react";
import { DatePicker, InputNumber } from "@tinper/next-ui";
import ReactCodemirror from "iuap-ip-commonui-fe/react-codemirror";
import moment from "moment";
const { RangePicker } = DatePicker;

const CustomDateItem = ({ value, onChange, type, ...props }) => {
    const handleChange = (value, b) => {
        onChange(b);
    };
    return (
        <DatePicker fieldid="07faac5c-53e2-4f39-8530-5855351030dd" placeholder={type} {...props} value={moment(value, props.format)} onChange={handleChange} />
    );
};

const CustomRangePicker = ({ onChange, value, ...props }) => {
    const momentArr = value; //useMemo(() => value?.map((str) => moment(str, "YYYY-MM-DD HH:mm:ss")), [value]);
    const handleChange = (value, formatString, dateStringArr) => {
        onChange(dateStringArr);
    };
    return (
        <RangePicker
            fieldid="cffdf733-9d96-4340-be5d-1b95b3039288"
            picker="date"
            showTime
            value={momentArr}
            format="YYYY-MM-DD HH:mm:ss"
            onChange={handleChange}
            {...props}
        />
    );
};

const CustomMonacoEditor = ({ value, onChange }) => {
    return (
        <ReactCodemirror
            value={JSON.stringify(value, null, 2)}
            onChange={(jsonData) => {
                try {
                    onChange(JSON.parse(jsonData));
                } catch (error) {
                    onChange("");
                }
            }}
            language={"json"}
            width={"100%"}
            height={"300px"}
            style={{
                width: "100%",
                height: "300px",
            }}
        />
    );
};

const CustomNumberItem = ({ value, onChange, type }) => {
    const isLong = useRef(-1);
    useEffect(() => {
        if (value && value.indexOf) {
            isLong.current = value?.indexOf("longAPIIntSpecialStr");
        }
    }, [value]);

    const handleChange = (value) => {
        const outValue = isLong.current > -1 ? `longAPIIntSpecialStr${value}` : value;
        onChange(outValue);
    };
    return (
        <InputNumber
            fieldid="eb3fd0ae-5e9f-439d-af17-3c2c4db875b7"
            placeholder={type}
            value={isLong.current > -1 ? value.substr(20, value.length) : value}
            onChange={handleChange}
        />
    );
};

export { CustomDateItem, CustomMonacoEditor, CustomNumberItem, CustomRangePicker };
