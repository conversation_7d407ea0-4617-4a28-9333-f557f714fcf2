import React, { useState, useCallback, useMemo, useEffect, forwardRef, useImperativeHandle } from "react";
import { Modal, Button, Dropdown, Icon, Switch, DatePicker } from "components/TinperBee";
import classnames from "classnames";
const { RangePicker } = DatePicker;
import "./index.less";
// import lang from 'tne-core-fe/i18n';
// console.log('lang', lang);

const Self = (props, ref) => {
    const { taskInfo } = props;
    const [isPull, setIsPull] = useState(false);

    const [isShowTime, setIsShowTime] = useState(false); //taskInfo.lastsuctime
    const [isShowTimeRange, setIsShowTimeRange] = useState(false);
    const [startTime, setStartTime] = useState("");
    const [lastsuctime, setLastsuctime] = useState(taskInfo.lastsuctime);
    const [endTime, setEndTime] = useState("");

    const [locale, setLocale] = useState(lang.lang);

    const firstChange = (value) => {
        console.log(value);
        setIsPull(value);
    };

    const timeRangeSwitchChange = (isShowTimeRange) => {
        console.log(isShowTimeRange);
        setIsShowTimeRange(isShowTimeRange);
        if (!isShowTimeRange) {
            setStartTime("");
            setEndTime("");
        }
    };

    const timeSwitchChange = (isShowTime) => {
        console.log(isShowTime);
        setIsShowTime(isShowTime);
        if (!isShowTime) {
            // setStartTime('')
            setLastsuctime("");
        }
    };
    const timeRangeChange = (time) => {
        const [startTime = null, endTime = null] = time;
        console.log(startTime.format("YYYY-MM-DD HH:mm:ss"));
        setStartTime(startTime.format("YYYY-MM-DD HH:mm:ss"));
        setEndTime(endTime.format("YYYY-MM-DD HH:mm:ss"));
    };
    const timeChange = (time, b) => {
        setLastsuctime(b);
    };

    useImperativeHandle(ref, () => {
        return {
            isPull,
            setIsPull,
            startTime,
            endTime,
            lastsuctime,
        };
    }, [isPull, startTime, endTime, lastsuctime]);
    return (
        <>
            <div style={{ margin: "10px 0" }}>
                <span className="confirm-title">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180191", "强制推送") /* "强制推送" */}</span>&nbsp;&nbsp;&nbsp;
                <Switch
                    fieldid="ublinker-routes-list-components-IndexView-index-4420293-Switch"
                    className="item-state-area-switch"
                    // checked={aa}
                    size="sm"
                    colors="blue"
                    onChange={firstChange}
                />
                &nbsp;&nbsp;&nbsp;
                <span className="confirm-tip">
                    {
                        lang.templateByUuid(
                            "UID:P_UBL-FE_18D8CEF604180195",
                            "本方案支持强制推送，无视目标数据是否存在，是否启用" //@notranslate
                        ) /* "本方案支持强制推送，无视目标数据是否存在，是否启用" */
                    }
                </span>
            </div>
            {taskInfo.fromType == 2 ? (
                <div>
                    <div style={{ margin: "10px 0" }}>
                        <span className="confirm-title">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180196", "时间段补录") /* "时间段补录" */}</span>
                        &nbsp;&nbsp;&nbsp;
                        <Switch
                            fieldid="UCG-FE-routes-list-components-IndexView-modalConfimContent-7489277-Switch"
                            className="item-state-area-switch"
                            size="sm"
                            colors="blue"
                            onChange={timeRangeSwitchChange}
                        />{" "}
                        &nbsp;&nbsp;&nbsp;
                        <span className="confirm-tip">
                            {
                                lang.templateByUuid(
                                    "UID:P_UBL-FE_18D8CEF604180192",
                                    "本方案支持强制推送与指定时间段补录数据" //@notranslate
                                ) /* "本方案支持强制推送与指定时间段补录数据" */
                            }
                        </span>
                    </div>
                    {isShowTimeRange && (
                        <div style={{}}>
                            <RangePicker
                                fieldid="UCG-FE-routes-list-components-IndexView-modalConfimContent-6427369-RangePicker"
                                picker="date"
                                showTime
                                format="YYYY-MM-DD HH:mm:ss"
                                locale={locale}
                                onChange={timeRangeChange}
                            />
                        </div>
                    )}
                    <div style={{ margin: "10px 0" }}>
                        <span className="confirm-title">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180193", "增量时间戳") /* "增量时间戳" */}</span>
                        &nbsp;&nbsp;&nbsp;
                        <Switch
                            fieldid="UCG-FE-routes-list-components-IndexView-modalConfimContent-6067046-Switch"
                            className="item-state-area-switch"
                            size="sm"
                            colors="blue"
                            onChange={timeSwitchChange}
                        />{" "}
                        &nbsp;&nbsp;&nbsp;
                        {/* checked={isShowTime} */}
                        <span className="confirm-tip">
                            {
                                lang.templateByUuid(
                                    "UID:P_UBL-FE_18D8CEF604180194",
                                    "本方案支持强制推送与修改本次执行增量时间戳" //@notranslate
                                ) /* "本方案支持强制推送与修改本次执行增量时间戳" */
                            }
                        </span>
                    </div>
                    {isShowTime && (
                        <div style={{}}>
                            <DatePicker
                                fieldid="UCG-FE-routes-list-components-IndexView-modalConfimContent-8810144-DatePicker"
                                picker="date"
                                showTime
                                value={lastsuctime}
                                defaultValue={taskInfo.lastsuctime}
                                format="YYYY-MM-DD HH:mm:ss"
                                locale={locale}
                                onChange={timeChange}
                            />
                        </div>
                    )}
                </div>
            ) : taskInfo.fromType == 1 ? ( //1的情况已作废
                <div style={{ margin: "10px 0" }}>
                    <div>
                        <span className="confirm-title">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180193", "增量时间戳") /* "增量时间戳" */}</span>
                        &nbsp;&nbsp;&nbsp;
                        <Switch
                            fieldid="UCG-FE-routes-list-components-IndexView-modalConfimContent-6067046-Switch"
                            className="item-state-area-switch"
                            size="sm"
                            colors="blue"
                            onChange={timeSwitchChange}
                        />{" "}
                        &nbsp;&nbsp;&nbsp;
                        <span className="confirm-tip">
                            {
                                lang.templateByUuid(
                                    "UID:P_UBL-FE_18D8CEF604180194",
                                    "本方案支持强制推送与修改本次执行增量时间戳" //@notranslate
                                ) /* "本方案支持强制推送与修改本次执行增量时间戳" */
                            }
                        </span>
                    </div>
                    {isShowTime && (
                        <div style={{}}>
                            <DatePicker
                                fieldid="UCG-FE-routes-list-components-IndexView-modalConfimContent-8810144-DatePicker"
                                picker="date"
                                showTime
                                format="YYYY-MM-DD HH:mm:ss"
                                locale={locale}
                                onChange={timeChange}
                            />
                        </div>
                    )}
                </div>
            ) : null}
        </>
    );
};
export default forwardRef(Self);
