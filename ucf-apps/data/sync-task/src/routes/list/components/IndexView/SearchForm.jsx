import React, { useImperativeHandle, useEffect } from "react";
import { SearchForm } from "tne-tinpernextpro-fe";
import { triggerModeMap } from "./index";
import RadioTag from "components/CustomFormItem/RadioTag";

const Item = SearchForm.Item;
const formItemLayout = {
    labelCol: {
        xs: { span: 8 },
        sm: { span: 8 },
    },
    wrapperCol: {
        xs: { span: 16 },
        sm: { span: 16 },
    },
};
const SubmitterSearchForm = React.forwardRef(({ onSearch, onReset, connectorsList = [], data, ...props }, ref) => {
    const formRef = React.useRef(null);

    useEffect(() => {
        formRef.current?.setFieldsValue(data);
        // handleSearch(initialValues);
    }, []);
    useImperativeHandle(ref, () => ({
        setFieldsValue: (data) => {
            formRef.current?.setFieldsValue(data);
        },
    }));
    const taskStatusTypes = [
        {
            value: 0,
            label: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804F5", "未运行") /* "未运行" */,
        },
        {
            value: 1,
            label: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804E9", "运行中") /* "运行中" */,
        },
        {
            value: 2,
            label: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804EB", "运行成功") /* "运行成功" */,
        },
        {
            value: 3,
            label: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804EE", "运行失败") /* "运行失败" */,
        },
        {
            value: 4,
            label: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804F0", "部分成功") /* "部分成功" */,
        },
        {
            value: 5,
            label: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804F1", "运行冲突") /* "运行冲突" */,
        },
        {
            value: 6,
            label: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804F3", "跳过") /* "跳过" */,
        },
        {
            value: 7,
            label: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804F4", "等待") /* "等待" */,
        },
        {
            value: 8,
            label: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804E8", "数据处理中") /* "数据处理中" */,
        },
    ];
    const enableStatusTypes = [
        {
            value: "1",
            label: lang.templateByUuid("UID:P_UBL-FE_1C382F9404F8000A", "启用") /* "启用" */,
        },
        {
            value: "0",
            label: lang.templateByUuid("UID:P_UBL-FE_1C382F9404F8000B", "停用") /* "停用" */,
        },
    ];
    return (
        <SearchForm
            style={{ background: "#FFFFFF" }}
            fieldid="06f256eb-e735-47ac-b139-30401002b691"
            formLayout={3}
            ref={formRef}
            onSearch={(values) => onSearch(values)}
            onReset={() => {
                onReset(props.initData);
                formRef.current.resetFields();
            }}
            key="submitter-demo-search-form1"
            // initialValues={initialValues}
            locale={window.lang.lang || "zh_cn"}
            submitter={{ searchConfig: { resetText: lang.templateByUuid("UID:P_UBL-FE_18D7622804180027", "重置") } }}
            {...props}
        >
            <Item
                label={lang.templateByUuid("UID:P_UBL-FE_1FD9FD2205980009", "集成流") /* "集成流" */}
                fieldid="760f9208-d340-41a9-82b0-da105a1ea852"
                {...formItemLayout}
                allowClear
                inputType="input"
                name="key"
                placeholder={lang.templateByUuid("UID:P_UBL-FE_1C3D237E0520000B", "请输入编码/名称")}
            />
            <Item
                {...formItemLayout}
                fieldid="85dcbf37-47a3-4484-8fec-ef25dc864413"
                allowClear
                inputType="select"
                label={lang.templateByUuid("UID:P_UBL-FE_1AFE6E4404400008", "启用/停用", undefined, {
                    returnStr: true,
                })}
                name="enable"
                options={enableStatusTypes}
            />
            <Item
                fieldid="1214d8b6-5623-4e72-a709-0981c865c0a9"
                {...formItemLayout}
                allowClear
                inputType="select"
                label={lang.templateByUuid("UID:P_UBL-FE_1AFE6E4404400009", "运行状态") /* "运行状态" */}
                name="taskstatus"
                options={taskStatusTypes}
                listHeight
            />
            <Item
                fieldid="1214d8b6-5623-4e72-a709-0981c865c0a9"
                {...formItemLayout}
                allowClear
                inputType="select"
                label={lang.templateByUuid("UID:P_UBL-FE_1C382F9404F80009", "触发方式") /* "触发方式" */}
                name="startTypeInt"
                options={Object.entries(triggerModeMap).map(([value, label]) => ({
                    value: parseInt(value),
                    label,
                }))}
            />
            <Item
                fieldid="b1cdeecf-8170-493c-84c7-e22741c037b5"
                {...formItemLayout}
                allowClear
                inputType="select"
                label={lang.templateByUuid("UID:P_UBL-FE_1C382F9404F80008", "连接配置") /* "连接配置" */}
                name="gateWayId"
                // options={connectorsList}
                options={connectorsList.map((item) => ({ label: item?.value, value: item?.key }))}
            />
        </SearchForm>
    );
});

export default SubmitterSearchForm;
