// .sync-task-content .wui-table-fixed-right{
//     max-width: 400px;
//     overflow-x: auto!important;
//     width:400px;
// }
// .sync-task-content .wui-table-fixed-right .wui-table-tbody td{
//     max-width: none !important
// }
// .sync-task-content .wui-table-content .wui-table-scroll > div{
//     margin-right: 400px!important;
// }
// .sync-task-content  .wui-table-fixed-right .wui-table-body-outer  .wui-table-body-inner{  //针对火狐
//     overflow-x: scroll !important;
//     padding-bottom:0 !important;
// }
.iuapIpaasDataintegrationFe-sync{

  .panel-mask{
      position: fixed;
          left: 0;
          right: 0;
          bottom: 0;
          top: 0;
          background-color: rgba(40, 44, 52, 0.7);
          z-index: 9;
  }
  .test-guid {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      height: 100%;
      z-index: 99;
      // background-color: rgba(0, 0, 0, 0.5);
      &-step0 {
          width: 394px;
          height: 105px;
          position: absolute;
          top: 136px;
          left: 202px;
      }
      &-step1 {
          position: absolute;
          width: 459px;
          height: 108px;
          top: 138px;
          /* left: 872px; */
          right: 151px;
      }
      &-step2 {
          position: absolute;
          width: 423px;
          height: 157px;
          top: 133px;
          /* left: 705px; */
          right: -4px;
      }
      &-step3 {
          position: absolute;
          width: 236px;
          height: 110px;
          top: 135px;
          /* left: 41px; */
          right: 82px;
      }
      &-step4 {
        position: absolute;
        width: 433px;
        height: 108px;
        top: 61px;
      right: 108px;
      }
    }
    .self-container .wui-modal-content{
      width: 620px !important;
      height:230px;
    }
    .confirm-title{
      font-weight: bold;
      color:black;
      font-size:15px;
    }
    .confirm-tip{
      font-size:12px;
    }
    .dataMapModal-header-right .wui-upload-wrapper {
      float: right;
  }
}