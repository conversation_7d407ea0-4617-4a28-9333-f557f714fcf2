export const mockData = [
    {
        name: "header-token",
        requestParamType: "HeaderParam",
        code: "token",
        type: "string",
        array: false,
        required: 0,
    },
    {
        name: "query-id",
        requestParamType: "QueryParam",
        code: "id",
        type: "string",
        array: false,
        required: 0,
    },
    {
        name: "对象属性a", //@notranslate
        requestParamType: "BodyParam",
        code: "person",
        type: "object",
        array: false,
        required: 0,
        children: [
            {
                name: "对象数组属性a-1", //@notranslate
                requestParamType: "BodyParam",
                code: "objectArray2",
                type: "object",
                required: 0,
                array: true,
                //数组的情况下，同级的paramType和子集的类型保持一致
                // 数组的子集是简单类型不能有code
                // 数组的类型是对象类型
                children: [
                    {
                        name: "",
                        requestParamType: "BodyParam",
                        code: "",
                        type: "object",
                        required: 0,
                        array: false,
                        children: [
                            {
                                name: "姓名", //@notranslate
                                requestParamType: "BodyParam",
                                code: "a",
                                type: "string",
                                required: 1,
                                array: false,
                                value: "a1",
                            },
                            {
                                name: "性别", //@notranslate
                                requestParamType: "BodyParam",
                                code: "b",
                                type: "string",
                                required: 0,
                                array: false,
                                value: "b1",
                            },
                        ],
                    },
                ],
            },
            {
                name: "字符串数组属性a-2", //@notranslate
                requestParamType: "BodyParam",
                code: "simpleArray1",
                type: "string",
                required: 1,
                array: true,
                children: [
                    {
                        name: "",
                        requestParamType: "BodyParam",
                        code: "",
                        type: "int",
                        required: 0,
                        array: false,
                        value: "1",
                    },
                ],
            },
        ],
    },
    {
        name: "数组属性2", //@notranslate
        requestParamType: "BodyParam",
        code: "outArray",
        type: "string",
        required: 0,
        array: true,
        children: [
            {
                name: "cici",
                requestParamType: "BodyParam",
                code: "c",
                type: "string",
                required: 1,
                array: false,
                value: "",
            },
            {
                name: "d",
                requestParamType: "BodyParam",
                code: "d",
                type: "string",
                required: 0,
                array: false,
                value: "",
            },
        ],
    },
    {
        name: "日期属性3", //@notranslate
        requestParamType: "BodyParam",
        code: "ageRange",
        type: "datetime",
        array: false,
        children: null,
        required: 1,
        value: "2023-07-28 12:00:00",
    },
];
