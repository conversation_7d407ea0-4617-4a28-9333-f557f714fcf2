.code-render {
    display: flex;
    align-items: center;
}
.code-copy {
    padding-left: 4px;
    display: none;
    cursor: pointer;
}
.sync-task-content {
    :global(.wui-table-row-hover) {
        .code-copy {
            display: block;
        }
    }
    background-color: #fff;
    display: flex;
    flex-direction: column;
}
.sync-data-table {
    flex: 1;
    overflow: hidden;
}
.condition-filter-modal {
    :global {
        .wui-form-item {
            margin-bottom: 8px !important;
        }
        .monaco-editor .minimap {
            display: none;
        }
    }
}
.integrate-task {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
}
.integrate-task-content {
    flex: 1;
}
.application-tree {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.application-tree-search {
    height: 51px;
    width: 100%;
    padding-left: 16px;
    padding-right: 22px;
    padding-top: 15px;
}
.application-tree-data {
    flex: 1;
    overflow: auto;
}
