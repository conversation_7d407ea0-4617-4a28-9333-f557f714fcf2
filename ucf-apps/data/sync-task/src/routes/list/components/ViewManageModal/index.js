import React, { Component } from "react";
import { FormControl, Button, Modal as <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ber, FormList, Tabs } from "components/TinperBee";
import { Modal } from "@tinper/next-ui";
import SqlWhere from "../SqlWhereModal/index2";
import { Warning } from "utils/feedback";
import EditView from "../EditViewModal/index2";
import SeeSql from "../SeeSqlModal/index2";
import Column from "../../../columns/container2";
import PubDoc from "../../../public-doc/container2";
import GateWay from "../UcgRefer/ReferListModal";
import tipIcon from "static/images/tip.svg";
import "./index.less";
import styles from "./index.modules.css";

const FormItem = FormList.Item;
const { TabPane } = Tabs;

const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
};
class EditModal extends Component {
    constructor() {
        super();
        this.state = {
            show: false,
            activeKey: "",
        };
        this.form = React.createRef();

        this.tab2 = React.createRef();
        this.tab3 = React.createRef();
        this.tab5 = React.createRef();
    }

    static getDerivedStateFromProps(nextProps, prevState) {
        if (nextProps.show !== prevState.show) {
            return { show: nextProps.show };
        }
        return null;
    }

    onChange = (activeKey) => {
        this.setState({
            activeKey,
        });
        if (activeKey == 6) {
            let {
                ownerState: { selectedTask },
            } = this.props;
            if (!selectedTask.dataview.commondoccode) {
                Warning(lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180078", "此任务未关联公共档案") /* "此任务未关联公共档案" */);
            }
        }
    };
    handleCancel = (activeKey) => {
        this.setState({
            activeKey: "",
        });
        this.props.onCancel();
    };

    labelCol = 120;

    handleOK = () => {
        let { activeKey } = this.state;
        let { ownerState } = this.props;
        if (activeKey === "2") {
            this.tab2.current.handleOk();
        }
        if (activeKey === "3") {
            this.tab3.current.handleOk();
        }
        if (activeKey === "5") {
            this.tab5.current.handleOk();
        }
    };
    handleNavToTrans = (record) => {
        if (record.voChangeRegId) {
            window.jDiwork?.openService(
                "kfljsjjhdy",
                {
                    providerHost: "/iuap-ipaas-dataintegration/ucf-wh/ublinker-fe/",
                    routePath: "/columns",
                    dataExId: record.voChangeRegId,
                    dataSrc: record.fromBillType,
                    dataTar: record.toBillType,
                },
                {
                    serviceType: "tns", // 打开tns去iframe节点
                    providerName: "iuapIpaasDataintegrationFe",
                    providerEntry: "exchange",
                }
            );
        } else {
            window.jDiwork?.openService("kfljsjjhdy");
        }
    };
    render() {
        let { show, activeKey } = this.state;
        let {
            ownerStore,
            ownerState,
            ownerState: { selectedTask },
            res,
            oldDataParam,
            selectedList,
        } = this.props;
        if (show) {
            return (
                <Modal
                    maximize
                    fieldid="ublinker-routes-list-components-ViewManageModal-index-2626539-Modal"
                    title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418007B", "ERP集成方案配置", undefined, {
                        returnStr: true,
                    })}
                    className="view-modal"
                    show={true}
                    onCancel={this.handleCancel}
                    size="xlg"
                    height="540"
                    footer={
                        activeKey === "2" || (activeKey === "3" && ownerState.tenantSupport) || activeKey === "5"
                            ? [
                                  <Button fieldid="34d1a57d-5962-4e23-93df-c4db83d8acc2" key="back" type="primary" onClick={this.handleOK}>
                                      {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180027", "确定") /* "确定" */}
                                  </Button>,
                              ]
                            : []
                    }
                    // container={document.getElementById("app")}
                >
                    <Tabs
                        fieldid="ublinker-routes-list-components-ViewManageModal-index-6821225-Tabs"
                        // activeKey={activeKey}
                        destroyInactiveTabPane={true}
                        tabBarPosition={"left"}
                        onChange={this.onChange}
                        defaultActiveKey="1"
                        className={styles["demo4-tabs"]}
                    >
                        <TabPane
                            fieldid="UCG-FE-routes-list-components-ViewManageModal-index-8370255-TabPane"
                            tab={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418007F", "视图列定义") /* "视图列定义" */}
                            key="1"
                        >
                            <div className={styles["exchange-tip"]}>
                                <img style={{ width: "22px", height: "22px" }} src={tipIcon} />
                                {
                                    lang.templateByUuid(
                                        "UID:P_UBL-FE_1C2AF14805800008",
                                        "视图可以使用数据转换规则进行字段特殊逻辑处理，如不存在可以手动新建。"//@notranslate
                                    ) /* "视图可以使用数据转换规则进行字段特殊逻辑处理，如不存在可以手动新建。" */
                                }
                                <Button fieldid="fdb071bc-97d3-4eb3-9425-284e0d586d2d" type="link" onClick={() => this.handleNavToTrans(selectedTask)}>
                                    {lang.templateByUuid("UID:P_UBL-FE_1C2AF14805800009", "查看数据转换规则") /* "查看数据转换规则" */}
                                </Button>
                            </div>
                            <Column ownerStore={ownerStore} ownerState={ownerState} selectedTask={selectedTask} />
                        </TabPane>
                        <TabPane
                            fieldid="UCG-FE-routes-list-components-ViewManageModal-index-4225151-TabPane"
                            tab={lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180080", "视图条件定义") /* "视图条件定义" */}
                            key="2"
                        >
                            <SqlWhere ref={this.tab2} ownerStore={ownerStore} ownerState={ownerState} />
                        </TabPane>
                        <TabPane
                            fieldid="UCG-FE-routes-list-components-ViewManageModal-index-4295950-TabPane"
                            tab={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041800E2", "编辑") /* "编辑" */}
                            key="3"
                        >
                            <EditView ref={this.tab3} ownerStore={ownerStore} ownerState={ownerState} />
                        </TabPane>
                        <TabPane
                            fieldid="UCG-FE-routes-list-components-ViewManageModal-index-1866276-TabPane"
                            tab={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418007A", "查看历史SQL") /* "查看历史SQL" */}
                            key="4"
                        >
                            <div>
                                <SeeSql ownerStore={ownerStore} ownerState={ownerState} selectedTask={selectedTask} aa={1} />
                            </div>
                        </TabPane>
                        {selectedTask && selectedTask.changeConnect && (
                            <TabPane
                                fieldid="UCG-FE-routes-list-components-ViewManageModal-index-2278118-TabPane"
                                tab={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418007C", "连接配置") /* "连接配置" */}
                                key="5"
                                style={{ overflow: "hidden" }}
                            >
                                {" "}
                                {/* u8不展示 */}
                                <GateWay
                                    ref={this.tab5}
                                    ownerStore={ownerStore}
                                    ownerState={ownerState}
                                    selectedTask={selectedTask}
                                    res={res}
                                    oldDataParam={oldDataParam}
                                    selectedList={selectedList}
                                />
                            </TabPane>
                        )}
                        <TabPane
                            fieldid="UCG-FE-routes-list-components-ViewManageModal-index-6945484-TabPane"
                            tab={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418007D", "公共档案关联") /* "公共档案关联" */}
                            key="6"
                        >
                            {((selectedTask || {}).dataview || {}).commondoccode ? (
                                <PubDoc ownerStore={ownerStore} ownerState={ownerState} selectedTask={selectedTask} />
                            ) : null}
                        </TabPane>
                    </Tabs>
                </Modal>
            );
        } else {
            return null;
        }
    }
}

export default EditModal;
