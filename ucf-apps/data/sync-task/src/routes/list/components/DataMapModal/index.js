import React, { Component, Fragment, useState } from "react";
import { InputGroup, Button, FormControl, Modal as ModalConfirm } from "components/TinperBee";
import { Space, Message, Modal, Menu, Upload, Notification, Progress, Dropdown, Table, Input, Form, Select } from "@tinper/next-ui";
import Grid from "components/TinperBee/Grid";
import { inject, observer } from "mobx-react";
import Store from "./store";
import { createEnum } from "constants/utils";
import FieldWrap from "components/RowField/FieldWrap";
import { withIframeSize } from "decorator/index";
import "../IndexView/index.less";
import { autoServiceMessage } from "utils/service";
import { downloadFile } from "utils";
import { Error } from "utils/feedback";
import * as service from "../../service";
import styles from "./index.modules.css";
import Pagination from "components/TinperBee/Pagination";
import EditModal from "./editModal";

let newNotification = null;
Notification.newInstance({ placement: "BottomRight" }, (n) => (newNotification = n));

const DataMapStore = new Store();

const formItemLayout = {
    labelCol: { span: 5 },
    wrapperCol: { span: 14 },
};

const CustomInput = ({ onChange, value, ...rest }) => {
    const [state, setState] = useState(value);
    const handleSearch = () => {
        onChange(state);
    };
    return (
        <Input
            style={{ width: 150 }}
            fieldid="ublinker-routes-list-components-DataMapModal-index-2904489-FormControl"
            type="search"
            value={state}
            onChange={(val) => setState(val.trim())}
            onSearch={handleSearch}
            {...rest}
        />
    );
};
@withIframeSize
@inject((rootStore) => {
    return {
        ownerState: DataMapStore.toJS(),
        ownerStore: DataMapStore,
    };
})
@observer
class DataMapModal extends Component {
    constructor(props) {
        super(props);
        this.state = {
            show: false,
            selectedRows: [],
            importProgress: 0,
            editModalVisible: false,
        };
        let _this = this;
        this.uploadData = {
            name: "file",
            // action: '/upload.do',
            headers: {
                authorization: "authorization-text",
            },
            beforeUpload(zipFile, list) {
                console.log(zipFile, list);
                _this.setState({ file: zipFile });
                _this.props.ownerStore.importExcel({ file: zipFile, callingMethod: "1" });
                return false; // 禁止上传
            },
        };
        this.uploadData2 = {
            name: "file",
            // action: '/upload.do',
            headers: {
                authorization: "authorization-text",
            },
            beforeUpload(zipFile, list) {
                console.log(zipFile, list);
                _this.setState({ file: zipFile });
                _this.props.ownerStore.importExcel({ file: zipFile, callingMethod: "2" });
                return false; // 禁止上传
            },
        };
    }
    searchFormRef = React.createRef();

    pollingImportProgressTimer;
    noticeKey = 0;

    async componentDidMount() {
        this.props.ownerStore.init();
        this.props.ownerStore.setRecord(this.props.taskInfo);
        await this.props.ownerStore.getShowImportBtn();
        this.props.ownerStore.getDataSource();
    }
    componentWillUnmount() {
        this.props.ownerStore.setSearchData({});
        this.props.ownerStore.changeSearchKey("erp");
        this.props.ownerStore.changeSearchValue("");
        this.props.ownerStore.changeState({ hasEdit: false, searchSystemKeys: [] });
        if (this.pollingImportProgressTimer) {
            clearTimeout(this.pollingImportProgressTimer);
        }
    }

    dataMapSearchKeys = createEnum([
        {
            code: "erp",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804EA", "来源系统主键") /* "来源系统主键" */,
        },
        {
            code: "saas",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804EC", "目标系统主键") /* "目标系统主键" */,
        },
        {
            code: "code",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804ED", "编码") /* "编码" */,
        },
        {
            code: "name",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804EF", "名称") /* "名称" */,
        },
    ]);

    handleSearch = () => {
        this.props.ownerStore.getDataSource({
            pageNo: 1,
        });
    };

    handleDownload = ({ key }) => {
        // this.props.ownerStore.setEdit('add');
        console.log(key);
    };
    menuOnCard = () => {
        return (
            <Menu fieldid="ublinker-routes-list-components-GateWayCard-index-2577861-Menu" onClick={this.handleDownload} className="dataMapModal-download">
                <Menu.Item fieldid="UCG-FE-routes-list-components-GateWayCard-index-2936552-Menu.Item" key={"1"}>
                    <Upload
                        fieldid="UCG-FE-routes-MappingRelation-components-AddModal-index-84417141-Upload"
                        style={{ float: "right", marginRight: 10 }}
                        {...this.uploadData}
                    >
                        <Button fieldid="81c17ac2-0b22-4b22-8628-40ac82a7a9a6" type="text" style={{ color: "#111" }}>
                            {lang.templateByUuid("UID:P_UBL-FE_191DEC3004500042", "mdm主数据与bip关系") /* "mdm主数据与bip关系" */}
                        </Button>
                    </Upload>
                </Menu.Item>
                <Menu.Item fieldid="UCG-FE-routes-list-components-GateWayCard-index-1906118-Menu.Item" key={"2"}>
                    <Upload
                        fieldid="UCG-FE-routes-MappingRelation-components-AddModal-index-84417142-Upload"
                        style={{ float: "right", marginRight: 10 }}
                        {...this.uploadData2}
                    >
                        <Button fieldid="f8679b19-5aef-472d-8b9b-0625d6246597" type="text" style={{ color: "#111" }}>
                            {lang.templateByUuid("UID:P_UBL-FE_191DEC3004500041", "mdm主数据与erp关系") /* "mdm主数据与erp关系" */}
                        </Button>
                    </Upload>
                </Menu.Item>
            </Menu>
        );
    };
    handleValueChange = (changedValues, allValues) => {
        const { searchSystemKeys } = this.props.ownerState;
        if (allValues["systemCode"]) {
            allValues = {
                ...allValues,
                systemCode: undefined,
                [searchSystemKeys[0]]: allValues["systemCode"].split("~")[0],
                [searchSystemKeys[1]]: allValues["systemCode"].split("~")[1],
            };
        }
        this.props.ownerStore.setSearchData(allValues);
        this.props.ownerStore.getDataSource({
            pageNo: 1,
        });
    };
    renderHeader = () => {
        let {
            ownerState: { hasEdit, checkExist },
        } = this.props;
        return (
            <div className={styles["operation-content"]} ref={(node) => (this.getGridHeaderNode = node)}>
                <Form fieldid="0d1f232a-b69e-44ba-a90f-064b742c2d49" layout="inline" onValuesChange={this.handleValueChange} ref={this.searchFormRef}>
                    {checkExist?.systemCode?.length > 0 ? (
                        <Form.Item
                            label={lang.templateByUuid("UID:P_UBL-FE_1F7D965C05600017", "系统") /* "系统" */}
                            name="systemCode"
                            colon
                            {...formItemLayout}
                            initialValue={Object.values(checkExist?.defaultSystemCode || {}).join("~")}
                        >
                            <Select fieldid="5b9171a3-427e-4013-95f1-6e67e87ab261" onChange={this.onGenderChange} allowClear style={{ width: 180 }}>
                                {checkExist?.systemCode?.map((item) => (
                                    <Option key={Object.values(item).join("~")} value={Object.values(item).join("~")}>
                                        {Object.values(item).join("~")}
                                    </Option>
                                ))}
                            </Select>
                        </Form.Item>
                    ) : null}

                    <Form.Item label="">
                        <div style={{ display: "flex", alignItems: "center" }}>
                            <Form.Item noStyle name="searchKey" initialValue="erp">
                                <Select fieldid="ublinker-routes-list-components-DataMapModal-index-141012-Menu" style={{ width: 120 }}>
                                    {this.dataMapSearchKeys.selectData.map((item) => {
                                        let { key, value } = item;
                                        return (
                                            <Option fieldid="UCG-FE-routes-list-components-DataMapModal-index-6168580-Menu.Item" key={value} value={value}>
                                                {key}
                                            </Option>
                                        );
                                    })}
                                </Select>
                            </Form.Item>
                            <Form.Item noStyle name="value">
                                <CustomInput disabled={hasEdit} style={{ width: 150 }} />
                            </Form.Item>
                        </div>
                    </Form.Item>
                </Form>
                <Space siza={6}>
                    <Button
                        fieldid="ublinker-routes-list-components-DataMapModal-index-1645555-Button"
                        className=""
                        colors="primary"
                        disabled={hasEdit}
                        onClick={this.handleAdd}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180543", "新增")}
                    </Button>

                    {checkExist?.existExcel ? (
                        <Dropdown fieldid="ublinker-routes-list-components-GateWayCard-index-1521848-Dropdown" overlay={this.menuOnCard()} placement="bottom">
                            <Button
                                fieldid="ublinker-routes-list-components-DataMapModal-index-16455551-Button"
                                className=""
                                colors=""
                                disabled={hasEdit}
                                // icon='uf-cloud-o-down'
                            >
                                {lang.templateByUuid("UID:P_UBL-FE_1C4A939C05900006", "Excel导入") /* "Excel导入" */}
                            </Button>
                        </Dropdown>
                    ) : (
                        <Dropdown.Button overlay={this.importMenu}>{lang.templateByUuid("UID:P_UBL-FE_1CA6430A04780014", "导入") /* "导入" */}</Dropdown.Button>
                    )}

                    <Button fieldid="178810d6-985e-423e-af57-0d0753ddd7eb" onClick={this.handleBatchDelMap}>
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180419", "删除") /* "删除" */}
                    </Button>
                </Space>
            </div>
        );
    };

    handleChange = (field, value) => {
        this.props.ownerStore.changeEditRowInfo(field, value);
    };

    canEditFields = ["pk_id", "cloudpk"];

    renderCol = (field, value, record) => {
        let { isEdit = false } = record;
        if (isEdit) {
            let {
                ownerState: { editRowInfo, editRowError },
            } = this.props;
            let disabled = !this.canEditFields.includes(field);
            return (
                <FieldWrap message={editRowError[field]}>
                    <FormControl
                        fieldid="ublinker-routes-list-components-DataMapModal-index-3735212-FormControl"
                        style={{ width: "90%" }}
                        value={editRowInfo && editRowInfo[field]}
                        disabled={disabled}
                        onChange={this.handleChange.bind(null, field)}
                    />
                </FieldWrap>
            );
        } else {
            return value || "-";
        }
    };

    columns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_1F7D965C05600016", "主键") /* "主键" */,
            dataIndex: "_id",
            width: 180,
            // render: this.renderCol.bind(null, "_id"),
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418018F", "来源系统主键", undefined, {
                returnStr: true,
            }) /* "来源系统主键" */,
            dataIndex: "pk_id",
            width: 180,
            // render: this.renderCol.bind(null, "pk_id"),
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180186", "目标系统主键", undefined, {
                returnStr: true,
            }) /* "目标系统主键" */,
            dataIndex: "cloudpk",
            width: 180,
            // render: this.renderCol.bind(null, "cloudpk"),
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180189", "编码", undefined, {
                returnStr: true,
            }) /* "编码" */,
            dataIndex: "code",
            width: 150,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418018C", "名称", undefined, {
                returnStr: true,
            }) /* "名称" */,
            dataIndex: "name",
            width: 150,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418018E", "来源系统编码", undefined, {
                returnStr: true,
            }) /* "来源系统编码" */,
            dataIndex: "sourceSystemCode",
            width: 150,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180190", "目标系统编码", undefined, {
                returnStr: true,
            }) /* "目标系统编码" */,
            dataIndex: "targetSystemCode",
            width: 150,
        },
        {
            title: "",
            dataIndex: "$$editCol",
            render: (value, record, index) => {
                let { isEdit } = record;
                if (isEdit) {
                    return (
                        <Fragment>
                            <Button
                                fieldid="ublinker-routes-list-components-DataMapModal-index-5103478-Button"
                                {...Grid.hoverButtonPorps}
                                onClick={this.handleSaveMap}
                            >
                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180188", "确定") /* "确定" */}
                            </Button>
                            <Button
                                fieldid="ublinker-routes-list-components-DataMapModal-index-7981743-Button"
                                {...Grid.hoverButtonPorps}
                                colors="default"
                                onClick={this.handleCancelEditMap}
                            >
                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418018B", "取消") /* "取消" */}
                            </Button>
                        </Fragment>
                    );
                } else {
                    return null;
                }
            },
        },
    ];
    handleDelete = (record) => {
        let { ownerStore } = this.props;
        ModalConfirm.confirm({
            fieldid: "************1",
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418041A", "确认要删除吗？", undefined, {
                returnStr: true,
            }) /* "确认要删除吗？" */,
            onOk: async () => {
                const res = await ownerStore.deleteData({ id: record._id }, this.props.taskInfo?.startSchemeCode);
                if (res) {
                    this.setState({ selectedRows: [] });
                }
            },
        });
    };
    // 批量删除映射关系
    handleBatchDelMap = () => {
        const { selectedRows } = this.state;
        if (!selectedRows.length) {
            Message.destroy();
            Message.create({ content: lang.templateByUuid("UID:P_UBL-FE_1C4A939C05900005", "请先选择数据") /* "请先选择数据" */, color: "danger" });
        } else {
            Modal.info({
                fieldid: "************",
                title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418041A", "确认要删除吗？"),
                onOk: async () => {
                    const res = await this.props.ownerStore.batchDelMap({ taskCode: this.props.taskInfo?.startSchemeCode, ids: selectedRows });
                    if (res) {
                        this.setState({ selectedRows: [] });
                    }
                },
            });
        }
    };
    handleAdd = () => {
        this.props.ownerStore.changeState({ editRowInfo: {} });
        this.setState({ editModalVisible: true });
        // this.props.ownerStore.setEdit("add");
    };
    handleEdit = (record) => {
        this.props.ownerStore.changeState({ editRowInfo: record });
        this.setState({ editModalVisible: true });
    };
    handleUpdate = async (data) => {
        const {
            ownerStore: { saveDataMap, getShowImportBtn, getDataSource },
            ownerState,
        } = this.props;
        const res = await saveDataMap({ ...data, dataType: this.props.taskInfo?.datatype });
        if (res) {
            // 刷新系统的查询条件
            const res = await getShowImportBtn();
            if (res?.data) {
                // 更新表单中systemCode的值
                if (this.searchFormRef.current) {
                    const systemCodeValue = Object.values(res?.data?.defaultSystemCode || {}).join("~");
                    this.searchFormRef.current.setFieldsValue({
                        systemCode: systemCodeValue,
                    });
                }
            }
            getDataSource();
            this.handleClose();
        }
    };
    handleClose = () => {
        this.setState({ editModalVisible: false });
    };
    hoverContent = (record) => {
        return (
            <Fragment>
                <Button
                    fieldid="UCG-FE-routes-data-detail-components-IndexView-index-89967611-Button"
                    {...Grid.hoverButtonPorps}
                    onClick={this.handleEdit.bind(null, record)}
                >
                    {lang.templateByUuid("UID:P_UBL-FE_2045E98C04D00007", "编辑") /* "编辑" */}
                </Button>
                <Button
                    fieldid="UCG-FE-routes-data-detail-components-IndexView-index-89967611-Button"
                    {...Grid.hoverButtonPorps}
                    onClick={this.handleDelete.bind(null, record)}
                >
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180419", "删除") /* "删除" */}
                </Button>
            </Fragment>
        );
    };

    handleShowProgressNotice = () => {
        const contentNode = (
            <Space size={12} align="center">
                <Progress fieldid="e6d472a8-ae32-45d0-9b6b-4afd628eea5f" type="circle" width={40} percent={this.state.importProgress} />
                <span>{lang.templateByUuid("UID:P_UBL-FE_1CA6430A04780016", "正在处理") /* "正在处理" */}</span>
            </Space>
        );
        this.noticeKey = Date.now();
        newNotification.notice({
            description: contentNode,
            key: this.noticeKey,
            duration: null,
            closable: false,
        });
        return;
    };

    handlePollingProgress = async () => {
        const res = await autoServiceMessage({
            service: service.queryProgressService(this.props.taskInfo?.startSchemeCode),
        });
        if (res && res.data) {
            const resData = res.data;
            this.setState({ importProgress: res.data.progress });
            // x秒轮询一次
            if (res.data?.progress === 100) {
                newNotification.removeNotice(this.noticeKey);
                newNotification.notice({
                    color: "successlight",
                    message: lang.templateByUuid("UID:P_UBL-FE_1D76EF8804F00017", "导入请求已全部执行完成") /* "导入请求已全部执行完成" */,
                    description: (
                        <>
                            <div>
                                {lang.templateByUuid("UID:P_UBL-FE_1D866D6A0508000B", "数据导入：成功") /* "数据导入：成功" */}
                                {res.data?.successCount || 0}&nbsp;
                                <span>
                                    {lang.templateByUuid("UID:P_UBL-FE_1D866D6A0508000C", "失败") /* "失败" */}
                                    {res.data?.failCount || 0}
                                </span>
                            </div>
                            {resData.failCount ? (
                                <Button fieldid="87988e8f-93d6-434a-9426-3e70a7d22c8d" type="text" onClick={this.handleLoadError} style={{ padding: 0 }}>
                                    {lang.templateByUuid("UID:P_UBL-FE_1D76EF8804F00016", "下载失败数据") /* "下载失败数据" */}
                                </Button>
                            ) : null}
                        </>
                    ),
                    key: 10,
                    closable: true,
                    duration: resData.failCount ? null : 6,
                });
                clearTimeout(this.pollingImportProgressTimer);
                this.props.ownerStore.getDataSource();
            } else {
                this.pollingImportProgressTimer = setTimeout(() => {
                    this.handlePollingProgress();
                }, 1000);
            }
        } else {
            newNotification.removeNotice(this.noticeKey);
            // Message.destroy(this.noticeKey);
        }
    };

    uploadImport = {
        name: "file",
        action: `/iuap-ipaas-dataintegration/gwmanage/gwportal/diwork/erpdata/datamapping/excel/import`,
        data: { viewtag: this.props.taskInfo?.startSchemeCode, dataType: this.props.taskInfo?.datatype },
        xscf: true,
        accept: ".xls,.xlsx",
        withCredentials: true,
        showUploadList: false,
        beforeUpload: () => {
            this.setState({ importProgress: 0 }, () => this.handleShowProgressNotice());
            return true;
        },
        onChange: (_info) => {
            if (_info.file?.status === "done") {
                console.log("info ---", _info);
                if (_info.file?.response?.status === 0) {
                    Error(_info.file.response.msg);
                    newNotification.removeNotice(this.noticeKey);
                } else {
                    this.handlePollingProgress();
                }
            }
        },
    };
    handleExportExcelTemp = async () => {
        const res = await autoServiceMessage({
            service: service.downloadExcelTempService({ dataType: this.props.taskInfo?.datatype }),
            success: lang.templateByUuid("UID:P_UBL-FE_1CF09D180458000A", "下载成功") /* "下载成功" */,
        });
        if (res) {
            downloadFile(res);
        }
    };
    // 下载excel导入失败数据
    handleLoadError = async () => {
        const res = await autoServiceMessage({
            service: service.downloadExcelErrorService(this.props.taskInfo?.startSchemeCode),
            success: lang.templateByUuid("UID:P_UBL-FE_1CF09D180458000A", "下载成功") /* "下载成功" */,
        });
        if (res) {
            downloadFile(res);
        }
    };
    importMenu = (
        <Menu fieldid="40096395-68de-4876-aaed-715013cf2319">
            <Menu.Item key="1">
                <Upload {...this.uploadImport}>
                    <div>{lang.templateByUuid("UID:P_UBL-FE_1CA6430A04780013", "导入Excel")}</div>
                </Upload>
            </Menu.Item>
            <Menu.Item key="2" onClick={this.handleExportExcelTemp}>
                {lang.templateByUuid("UID:P_UBL-FE_1CA6430A04780015", "下载Excel模板") /* "下载Excel模板" */}
            </Menu.Item>
        </Menu>
    );

    render() {
        let { ownerState, onCancel } = this.props;
        let { editModalVisible } = this.state;
        let { dataSource, pagination, hasEdit } = ownerState;
        if (pagination) {
            pagination.disabled = hasEdit;
        }
        let rowSelection = {
            type: "checkbox",
            selectedRowKeys: this.state.selectedRows.map((item) => item._id),
            onChange: (selectedRowKeys, selectedRows) => {
                this.setState({
                    selectedRows,
                });
            },
        };
        return (
            <>
                <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", padding: "0 20px", height: "48px" }}>
                    <span style={{ fontSize: "14px", fontWeight: "600", color: "#333333" }}>
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418018A", "所有映射") /* "所有映射" */}({dataSource.total})
                    </span>
                    {this.renderHeader()}
                </div>
                <Table
                    dragborder
                    rowKey="_id"
                    fieldid="ublinker-routes-list-components-DataMapModal-index-4363318-Grid"
                    columns={this.columns}
                    data={dataSource.list}
                    rowSelection={rowSelection}
                    scroll={{ y: this.props.iframeSize.height - 153 }}
                    hoverContent={this.hoverContent}
                    // dataKey={"id"}
                    // selectedList={this.state.selectedList}
                    // getSelectedDataFunc={this.getSelectedDataFunc}
                />
                {pagination && (
                    <Pagination
                        fieldid="c5442881-5a59-4ccf-93c8-ad1ac1c05e15"
                        current={pagination.activePage}
                        onChange={(a, b) => pagination?.onPageChange({ pageSize: b, pageNo: a })}
                        onPageSizeChange={(a, b) => pagination?.onPageChange({ pageSize: b, pageNo: 1 })}
                        showSizeChanger
                        total={pagination.total}
                        pageSize={pagination.pageSize}
                        pageSizeOptions={[...Pagination.dataNumSelect["page"]]}
                        style={{ position: "fixed", bottom: "0", zIndex: "2", backgroundColor: "#fff", width: "100%" }}
                    />
                )}
                {editModalVisible && (
                    <EditModal
                        visible={editModalVisible}
                        dataType={this.props.taskInfo?.datatype}
                        onCancel={this.handleClose}
                        onOk={this.handleUpdate}
                        data={ownerState.editRowInfo || {}}
                        searchSystemKeys={ownerState.searchSystemKeys}
                        defaultSystemCode={ownerState.checkExist?.defaultSystemCode}
                    />
                )}
            </>
        );
    }
}

export default DataMapModal;
