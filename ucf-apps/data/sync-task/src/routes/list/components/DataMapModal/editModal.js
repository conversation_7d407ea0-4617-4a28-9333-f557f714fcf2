import React, { useEffect, useState } from "react";
import { Modal, Form, Input, Select } from "@tinper/next-ui";
import PropTypes from "prop-types";
import * as ownerService from "../../service";
import { autoServiceMessage } from "utils/service";

const FormItem = Form.Item;

const AddModal = (props) => {
    const { visible, onCancel, onOk, data = {}, title, dataType, defaultSystemCode, searchSystemKeys } = props;
    const [form] = Form.useForm();
    const [systemList, setSystemList] = useState([]);

    useEffect(() => {
        (async () => {
            let res = await autoServiceMessage({
                service: ownerService.getSystemListService(),
            });
            if (res) {
                setSystemList(res?.data || []);
            }
        })();

        if (visible) {
            form.setFieldsValue({
                ...data,
                [searchSystemKeys[0]]: data?.sourceSystemCode || defaultSystemCode?.[searchSystemKeys[0]],
                [searchSystemKeys[1]]: data?.targetSystemCode || defaultSystemCode?.[searchSystemKeys[1]],
            });
        }
        return () => {
            form.resetFields();
        };
    }, [visible, data, form]);

    // 处理确认按钮点击
    const handleOk = async () => {
        try {
            const values = await form.validateFields();
            onOk({ ...data, ...values });
        } catch (errInfo) {
            console.log("验证失败:", errInfo);
        }
    };

    // 表单布局配置
    const formItemLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 14 },
    };

    return (
        <Modal
            title={
                title ||
                (data && data._id
                    ? lang.templateByUuid("UID:P_UBL-FE_20076E5804280047", "编辑") /* "编辑" */
                    : lang.templateByUuid("UID:P_UBL-FE_20076E580428004A", "新增")) /* "新增" */
            }
            visible={visible}
            onCancel={onCancel}
            onOk={handleOk}
            okText={lang.templateByUuid("UID:P_UBL-FE_20076E5804280048", "确定") /* "确定" */}
            cancelText={lang.templateByUuid("UID:P_UBL-FE_20076E5804280049", "取消") /* "取消" */}
            width={600}
            destroyOnClose
        >
            <Form form={form} {...formItemLayout}>
                <FormItem
                    label={lang.templateByUuid("UID:P_UBL-FE_2045E86405E00008", "来源系统主键") /* "来源系统主键" */}
                    name="pk_id"
                    rules={[{ required: true, message: lang.templateByUuid("UID:P_UBL-FE_20076E5804280045", "请输入") /* "请输入" */ }]}
                >
                    <Input placeholder={lang.templateByUuid("UID:P_UBL-FE_20076E5804280045", "请输入") /* "请输入" */} />
                </FormItem>

                <FormItem
                    label={lang.templateByUuid("UID:P_UBL-FE_2045E86405E0000A", "目标系统主键") /* "目标系统主键" */}
                    name="cloudpk"
                    rules={[{ required: true, message: lang.templateByUuid("UID:P_UBL-FE_20076E5804280045", "请输入") /* "请输入" */ }]}
                >
                    <Input placeholder={lang.templateByUuid("UID:P_UBL-FE_20076E5804280045", "请输入") /* "请输入" */} />
                </FormItem>
                {dataType == "1" ? (
                    <>
                        <FormItem
                            label={lang.templateByUuid("UID:P_UBL-FE_2045E86405E00009", "来源系统") /* "来源系统" */}
                            name={searchSystemKeys[0]}
                            rules={[{ required: true, message: "" }]}
                        >
                            <Select
                                disabled={data.sourceSystemCode}
                                showSearch
                                optionFilterProp="sysName"
                                options={systemList}
                                fieldNames={{ label: "sysName", value: "sysCode" }}
                            />
                        </FormItem>
                        <FormItem
                            label={lang.templateByUuid("UID:P_UBL-FE_2045E86405E0000B", "目标系统") /* "目标系统" */}
                            name={searchSystemKeys[1]}
                            rules={[{ required: true, message: "" }]}
                        >
                            <Select
                                disabled={data.targetSystemCode}
                                showSearch
                                optionFilterProp="sysName"
                                options={systemList}
                                fieldNames={{ label: "sysName", value: "sysCode" }}
                            />
                        </FormItem>
                    </>
                ) : null}
            </Form>
        </Modal>
    );
};

AddModal.propTypes = {
    visible: PropTypes.bool,
    onCancel: PropTypes.func,
    onOk: PropTypes.func,
    data: PropTypes.object,
    title: PropTypes.string,
};

export default AddModal;
