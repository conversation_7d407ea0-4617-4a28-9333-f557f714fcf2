import { observable, computed, action, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { defaultListMap } from "utils/pageListUtils";
import * as ownerService from "../../service";
import { autoServiceMessage } from "utils/service";

const initState = {
    dataSource: { ...defaultListMap, pageSize: 20 },
    tableview: "",
    startSchemeCode: "",
    searchKey: "erp",
    value: "",
    editRowInfo: null, // 映射关系 行数据
    editRowError: {
        pk_id: "",
        cloudpk: "",
    },
    hasEdit: false,
    isShowImportBtn: false,
    serviceCodeDiwork: "kfljsjtbrw",
    checkExist: {
        existExcel: false,
        systemCode: [],
        defaultSystemCode: { systemCode1: "", systemCode2: "" },
    },
    searchSystemKeys: [],
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }

    @observable state = initState;
    @observable searchData = {};

    @observable record = {};

    init = () => {
        this.state = initState;
    };

    @action
    setRecord = (record) => {
        this.record = record;
        this.changeState({
            tableview: record?.dataview?.tableview || record.tableview,
            startSchemeCode: record?.startSchemeCode,
        });
    };
    @computed
    get dataTypeParam() {
        // dataType 为了适配新菜单映射关系： 1是走新的模型查询，为空时为数据集成老的模式查询
        return this.record?.datatype !== undefined ? { dataType: this.record?.datatype } : {};
    }

    @action
    setSearchData = (data) => {
        this.searchData = data;
    };
    changeSearchKey = (key) => {
        this.state.searchKey = key;
    };

    changeSearchValue = (value) => {
        this.state.value = value;
    };

    getDataSource = (reqData) => {
        let { tableview } = this.state;
        let requestData = {
            tableview,
            ...this.searchData,
            ...reqData,
            ...this.dataTypeParam,
        };
        this.getPagesListFunc({
            service: ownerService.getDataMapListService,
            requestData,
            dataKey: "dataSource",
        });
    };

    changeEditRowInfo = (field, value) => {
        this.state.editRowError[field] = "";

        this.state.editRowInfo[field] = value.trim();
    };

    setEdit = (type) => {
        let editRowInfo = null,
            hasEdit = false;
        if (type === "add") {
            editRowInfo = {
                pk_id: "",
                cloudpk: "",
                pk_org: "",
                code: "",
                name: "",
                isEdit: true,
            };
            hasEdit = true;
            this.state.dataSource.list.unshift(editRowInfo);
        } else {
            this.state.dataSource.list.shift();
        }
        this.changeState({ editRowInfo, hasEdit, editRowError: initState.editRowError });
    };

    saveDataMap = async (editRowInfo) => {
        let { tableview, searchSystemKeys } = this.state;
        let requestData = {
            tableview,
            dataMaps: [
                {
                    erppk: editRowInfo.pk_id,
                    cloudpk: editRowInfo.cloudpk,
                },
            ],
        };
        let res = await autoServiceMessage({
            service: ownerService.saveDataMapService(requestData, {
                dataType: editRowInfo.dataType,
                [searchSystemKeys[0]]: editRowInfo[searchSystemKeys[0]],
                [searchSystemKeys[1]]: editRowInfo[searchSystemKeys[1]],
            }),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180238", "保存成功", undefined, {
                returnStr: true,
            }) /* "保存成功" */,
        });
        if (res) {
            this.changeState({ hasEdit: false });
            return res;
        }
    };

    importExcel = async (data) => {
        let param = { taskCode: this.state.startSchemeCode, ...data };
        let res = await this.dispatchService({
            service: ownerService.primaryKeyMappingService(param, { serviceCode: this.state.serviceCodeDiwork }),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180236", "导入成功", undefined, {
                returnStr: true,
            }) /* "导入成功" */,
        });
        if (res) {
            this.getDataSource();
        }
    };
    deleteData = async (data, viewtag) => {
        const { searchSystemKeys } = this.state;
        let res = await autoServiceMessage({
            service: ownerService.deleteDataMapService(
                data,
                viewtag,
                searchSystemKeys.length
                    ? {
                          [searchSystemKeys[0]]: this.searchData[searchSystemKeys[0]],
                          [searchSystemKeys[1]]: this.searchData[searchSystemKeys[1]],
                          ...this.dataTypeParam,
                      }
                    : null
            ),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418041D", "删除成功！", undefined, {
                returnStr: true,
            }) /* "删除成功！" */,
        });
        if (res) {
            this.changeState({ hasEdit: false });
            this.getDataSource();
            return res;
        }
    };
    // 批量删除映射关系
    batchDelMap = async (data) => {
        const { searchSystemKeys } = this.state;
        let res = await autoServiceMessage({
            service: ownerService.batchDelMapService(
                data,
                searchSystemKeys.length
                    ? {
                          [searchSystemKeys[0]]: this.searchData[searchSystemKeys[0]],
                          [searchSystemKeys[1]]: this.searchData[searchSystemKeys[1]],
                          ...this.dataTypeParam,
                      }
                    : null
            ),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418041D", "删除成功！", undefined, {
                returnStr: true,
            }) /* "删除成功！" */,
        });
        if (res) {
            this.changeState({ hasEdit: false });
            this.getDataSource();
            return res;
        }
    };
    getShowImportBtn = async () => {
        let { tableview } = this.state;

        let res = await autoServiceMessage({
            service: ownerService.showImportBtnService(tableview, this.dataTypeParam),
        });

        if (res?.data) {
            this.changeState({ checkExist: res?.data, searchSystemKeys: Object.keys(res?.data?.defaultSystemCode || {}) || [] });
            const aa = res.data.systemCode?.length > 0 ? res?.data?.defaultSystemCode || {} : {};
            this.searchData = { ...this.searchData, ...aa };
        }
    };
}

/**
 * 根据具体模块修改 storeKey 为保证唯一性
 * storeKey 由 模块文件夹名称 + 页面名称 + 'Store' 小驼峰
 *
 * storeKey是作为当前 Store 的唯一键， storeKey与Store的实例会在 rootStore或者StoreMap中以 键值对的 形式存在
 * 1. 用于在模块的 storeMap（模块routes/store.js）中注册并初始化当前Store
 * 2. 用于在container的 inject 中关联当前Store的实例时，从模块的storeMap或者rootStore中获取对应store实例
 * */
export const storeKey = "dataSyncTaskDataMapStore";

export default Store;
