import React, { useEffect } from "react";
import { ucgListService } from "./service";
import { WithButton as WithButtonBase, WithInput as WithInputBase } from "components/Refer";
import commonText from "constants/commonText";

const columns = [
    {
        title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180401", "网关连接实例ID") /* "网关连接实例ID" */,
        dataIndex: "gatewayID",
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180402", "别名") /* "别名" */,
        dataIndex: "name",
    },
    {
        title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803FB", "是否在线") /* "是否在线" */,
        dataIndex: "state",
        render: (value) =>
            value === "ONLINE"
                ? lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803FF", "是") /* "是" */
                : lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803FE", "否") /* "否" */,
    },
];

let referProps = {
    columns: columns,
    hasPage: false,
    title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180400", "切换任务网关") /* "切换任务网关" */,
    service: ucgListService,
    pkKey: "gatewayID",
};

export const UcfReferButton = (props) => {
    return <WithButtonBase {...props} {...referProps} />;
};

export const UcfReferInput = (props) => {
    const { res } = props;
    useEffect(() => {
        let init = () => {
            if (res && res.data && res.data.status === 1) {
                referProps.columns.push({
                    title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803FA", "网关所属") /* "网关所属" */,
                    dataIndex: "fromMainTenant",
                    render: (value) =>
                        value
                            ? lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803FD", "主租户") /* "主租户" */
                            : lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803FC", "当前租户") /* "当前租户" */,
                });
            }
        };
        init();
    }, []);

    return <WithInputBase {...props} {...referProps} />;
};
