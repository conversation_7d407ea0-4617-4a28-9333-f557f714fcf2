import { getInvokeService } from "utils/service";

export function ucgListService(data, header) {
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/gateway/list",
            header,
        },
        data
    );
}

export function ucgListService2(data, header) {
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/connect/connect/list",
            header,
        },
        data
    );
}

export function getIsTenantService(header) {
    return getInvokeService({
        method: "GET",
        path: "/gwmanage/gwportal/diwork/isTenantGroup",
        header,
    });
}
