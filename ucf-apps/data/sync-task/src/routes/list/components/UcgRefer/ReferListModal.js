import React, { Component } from "react";
import propTypes from "prop-types";
// import Modal from '../TinperBee/Modal';
// import SearchInput from '../TinperBee/SearchInput';
import Grid from "components/TinperBee/Grid";
import { Checkbox, Button } from "components/TinperBee";
import { ucgListService2 } from "./service";
import { autoServiceMessage, getInvokeService } from "utils/service";
import _set from "lodash/set";
import commonText from "constants/commonText";
import { Success, Error } from "utils/feedback";
class AddModal extends Component {
    constructor(props) {
        super(props);
        this.state = {
            show: false,
            selectedList: [],
            selectedRowIndex: "",
            tableData: [],
            dataSource: [],
            pagination: null,
            requestParam: "",
            // 是否更新所有
            updateAll: false,
        };
        this.listService = this.getService();
    }

    getService = () => {
        let { service } = this.referProps;
        let serviceType = typeof service;
        if (serviceType === "function") {
            return service;
        } else if (serviceType === "string") {
            let { method } = this.props;
            return function (data) {
                return getInvokeService(
                    {
                        method: method,
                        path: service,
                        header: { serviceCode: this.props.ownerState.serviceCodeDiwork },
                    },
                    data
                );
            };
        }
    };
    getDataSource = async (reqData = {}) => {
        let res = await autoServiceMessage({
            service: this.listService({ serviceCode: this.props.ownerState.serviceCodeDiwork }),
        });
        if (res) {
            this.setState({
                tableData: res.data,
            });
        }
    };
    componentDidMount() {
        // this.setState({
        //     selectedList: this.props.selectedList
        // }, () => {
        // })
        this.getDataSource();
    }
    componentWillUnmount() {
        this.setState({
            selectedList: [],
            dataSource: [],
            tableData: [],
            pagination: null,
        });
    }
    getSelectedDataFunc = (selectedList, data) => {
        this.setState({ selectedList });
        this.props.ownerStore.changeState({
            selectedTaskGateway: selectedList,
        });
    };

    handleCancel = () => {
        this.props.onCancel();
        this.setState({ updateAll: false });
    };
    handleOk = () => {
        let { selectedList, updateAll } = this.state;
        let ucg = selectedList[0] || this.props.ownerState.selectedTaskGateway[0];
        console.log(ucg);
        if (!ucg) {
            Error(
                lang.templateByUuid("UID:P_UBL-FE_18D9147C05300013", "请选择连接", undefined, {
                    returnStr: true,
                }) /* "请选择连接" */
            );
        }
        let item = this.state.tableData.find((item) => {
            return item.code == ucg.code;
        });
        this.props.ownerStore.switchUcg(this.props.selectedTask, item, updateAll);
    };

    rowClassNameFun = (record) => {
        let { pkKey } = this.props;
        let { selectedList } = this.state;
        let selected = selectedList[0];
        if (selected && record[pkKey] === selected[pkKey]) {
            return "selected";
        } else {
            return "";
        }
    };

    handleSearch = (value) => {
        let { searchField } = this.props;
        this.setState({ requestParam: value });
        this.getDataSource({
            [searchField]: value,
            pageNo: 1,
        });
    };

    renderHeader = () => {
        let { searchField, onSearch } = this.props;
        let handleSearch = null;
        if (onSearch) {
            handleSearch = (v) => {
                onSearch(v);
                this.handleSearch(v);
            };
        } else {
            handleSearch = (v) => {
                this.handleSearch(v);
            };
        }
        if (searchField) {
            return (
                <SearchInput
                    className="ucg-ma-header-search"
                    size="md"
                    showClear
                    placeholder={this.props.searchPlaceholder || ""}
                    onSearch={(v) => handleSearch(v)}
                />
            );
        } else {
            return null;
        }
    };

    handleUpdateAllChange = (value) => {
        this.setState({
            updateAll: value,
        });
    };
    columns = [
        //     {
        //     title: "网关连接实例ID",
        //     dataIndex: 'gatewayID',
        // },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180501", "编码") /* "编码" */,
            dataIndex: "code",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180502", "名称") /* "名称" */,
            dataIndex: "name",
        },
    ];
    referProps = {
        service: ucgListService2,
    };
    render() {
        let { isMulti, title, modalWidth } = this.props;
        let { tableData, show, selectedList, pagination, requestParam } = this.state;
        return (
            <>
                <Grid
                    fieldid="ublinker-routes-list-components-UcgRefer-ReferListModal-5166461-Grid"
                    tableType="modal"
                    requestParam={requestParam}
                    size="sm"
                    header={this.renderHeader()}
                    data={tableData}
                    columns={this.columns}
                    rowKey="gatewayID"
                    selectedList={selectedList.length == 0 ? this.props.ownerState.selectedTaskGateway : selectedList}
                    multiSelect={isMulti}
                    radioSelect={!isMulti}
                    // rowClassName={(!isMulti && this.rowClassNameFun) || undefined}
                    getSelectedDataFunc={this.getSelectedDataFunc}
                    pagination={pagination}
                    style={{ y: 351 }}
                />

                <div style={{ marginTop: 20 }}>
                    <Checkbox
                        fieldid="ublinker-routes-list-components-UcgRefer-ReferListModal-1142464-Checkbox"
                        checked={this.state.updateAll}
                        onChange={this.handleUpdateAllChange}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180503", "是否更新所有") /* "是否更新所有" */}
                    </Checkbox>
                </div>
            </>
            // </Modal>
        );
    }
}

export default AddModal;
