import React, { Component } from "react";
import Modal from "components/TinperBee/Modal";
import OwnerCornEditor from "components/CronEditor";
import { Radio, Switch, Message } from "components/TinperBee";

class SqlWhereModal extends Component {
    constructor() {
        super();
        this.state = {
            cornValue: "0 6 * * * ?",
            show: "",
            val: "1",
            switchState: false,
        };
    }

    componentDidUpdate(prevProps) {
        if (this.props.show !== prevProps.show) {
            this.setState({ show: this.props.show, val: "1", switchState: this.props.taskInfo ? this.props.taskInfo.excluded : false });
            if (!this.props.show) {
                this.setState({ cornValue: "0 6 * * * ?" });
            }
        }
    }

    onChange = (value) => {
        this.setState({ cornValue: value });
    };

    handleOk = () => {
        const cornValue = this.state.cornValue;
        const cornArr = cornValue.split(" ");
        for (let index = 0; index < cornArr.length; index++) {
            if (cornArr[index].includes("-")) {
                const start = cornArr[index].split("-")[0];
                const end = cornArr[index].split("-")[1];
                if (start > end) {
                    Message.destroy();
                    Message.create({ content: lang.templateByUuid("UID:P_UBL-FE_1C081F0E05200027", "周期结束时间不能小于开始时间"), color: "danger" });
                    return false;
                }
            }
        }
        const lastCorn = cornArr[cornArr.length - 1];
        if (cornArr.length === 7 && lastCorn.includes("-")) {
            const start = lastCorn.split("-")[0];
            const end = lastCorn.split("-")[1];
            const currentYear = new Date().getFullYear();
            if (end < currentYear) {
                Message.destroy();
                Message.create({ content: lang.templateByUuid("UID:P_UBL-FE_1C081F0E05200026", "结束年份不能小于当前年"), color: "danger" });
                return false;
            }
        }
        this.props.onOk(cornValue, this.state.val, this.props.taskInfo, this.state.switchState);
    };
    handleChange = (e) => {
        this.setState({ val: e.target.value });
    };
    handleStateChange = (e) => {
        console.log(e);
        this.setState({ switchState: !e });
    };
    render() {
        let { show, cornValue, switchState } = this.state;
        if (show) {
            let { onCancel, taskInfo } = this.props;

            return (
                <Modal
                    fieldid="ublinker-routes-list-components-CreateTimingModal-index-1293918-Modal"
                    show={show}
                    title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418014F", "设置时间表达式", undefined, {
                        returnStr: true,
                    })}
                    onCancel={onCancel}
                    onOk={this.handleOk}
                >
                    <Radio.Group antd value={this.state.val} onChange={this.handleChange}>
                        <Radio fieldid="ublinker-routes-list-components-CreateTimingModal-index-6176994-Radio" value="1">
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180151", "自定义定时任务") /* "自定义定时任务" */}
                        </Radio>
                        <Radio fieldid="ublinker-routes-list-components-CreateTimingModal-index-468684-Radio" value="2">
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180152", "加入系统级定时任务") /* "加入系统级定时任务" */}
                        </Radio>
                    </Radio.Group>
                    <div style={{ marginTop: "20px" }}>
                        {
                            lang.templateByUuid(
                                "UID:P_UBL-FE_18D8CEF604180153",
                                "定时任务将只支持增量查询,不会全量查询与指定条件查询" //@notranslate
                            ) /* "定时任务将只支持增量查询,不会全量查询与指定条件查询" */
                        }
                    </div>
                    {this.state.val == "1" && (
                        <>
                            {show && taskInfo ? (
                                <div className="ucg-pad-20">
                                    <p className="ucg-mar-b-5">
                                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180150", "输入时间表达式") /* "输入时间表达式" */}
                                    </p>
                                    <OwnerCornEditor hides={["second"]} value={cornValue} onChange={this.onChange} showRunTime />
                                </div>
                            ) : null}
                        </>
                    )}
                    {this.state.val == "2" && (
                        <div style={{ display: "flex", alignItems: "center", paddingLeft: "20px", paddingTop: "30px" }}>
                            <div style={{ marginRight: "10px" }}>
                                {" "}
                                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180154", "启用系统级定时任务") /* "启用系统级定时任务" */}
                            </div>
                            <Switch
                                fieldid="ublinker-routes-list-components-CreateTimingModal-index-4379477-Switch"
                                className="item-state-area-switch"
                                // checked={!taskInfo.excluded}
                                checked={!switchState}
                                size="sm"
                                colors="blue"
                                onChange={this.handleStateChange}
                            />
                        </div>
                    )}
                </Modal>
            );
        } else {
            return null;
        }
    }
}

export default SqlWhereModal;
