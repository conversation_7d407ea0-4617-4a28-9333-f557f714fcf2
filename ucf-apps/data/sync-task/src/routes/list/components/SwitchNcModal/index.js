import React, { Component } from "react";
import { Checkbox, Icon } from "components/TinperBee";
import Modal from "components/TinperBee/Modal";
import classnames from "classnames";
import { autoServiceMessage } from "utils/service";
import { getNcVersionsService } from "../../service";

import "./index.less";

class EditModal extends Component {
    constructor() {
        super();
        this.state = {
            show: false,
            ncVersions: [],
            selectedVersion: null,
        };
    }

    componentDidUpdate(prevProps) {
        if (this.props.show !== prevProps.show) {
            this.setState({ show: this.props.show });
            if (this.props.show) {
                this.getNcVersions();
            }
        }
    }

    getNcVersions = async () => {
        let res = await autoServiceMessage({
            service: getNcVersionsService(),
            error: () => {},
        });
        if (res) {
            let versions = res.data || [];
            this.setState({ ncVersions: versions });
        }
    };

    selectVersion = (version) => {
        this.setState({ selectedVersion: version });
    };

    handleOk = () => {
        let { selectedVersion } = this.state;
        let data = {
            viewversion: selectedVersion ? selectedVersion.code : "",
            ifmdm: this.ifmdmCheckBox.state.checked,
        };
        this.props.onOk(data);
    };

    handleCancel = () => {
        this.setState({
            ncVersions: [],
            selectedVersion: null,
        });
        this.props.onCancel();
    };

    render() {
        let { show } = this.state;
        if (show) {
            let { selectedVersion, ncVersions } = this.state;
            return (
                <Modal
                    fieldid="ublinker-routes-list-components-SwitchNcModal-index-5037944-Modal"
                    title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804D0", "选择NC版本", undefined, {
                        returnStr: true,
                    })}
                    show={show}
                    onCancel={this.handleCancel}
                    onOk={this.handleOk}
                    size="md"
                >
                    <div className="ucg-pad-20">
                        <Checkbox
                            fieldid="ublinker-routes-list-components-SwitchNcModal-index-2340790-Checkbox"
                            defaultChecked={false}
                            ref={(node) => (this.ifmdmCheckBox = node)}
                        >
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804D1", "是否mdm") /* "是否mdm" */}
                        </Checkbox>
                        <ul className="data-sync-nc-vers">
                            {ncVersions.map((item) => {
                                let { code, name } = item;
                                let ncNum = name.toLowerCase().replace("nc", "");
                                let isActive = selectedVersion && selectedVersion.code === code;
                                let cls = classnames("vers-item", {
                                    active: isActive,
                                });
                                return (
                                    <li className={cls} onClick={this.selectVersion.bind(null, item)}>
                                        <span className="nc_tap">NC</span>
                                        <span>{ncNum}</span>
                                        {isActive ? (
                                            <span className="active_tag">
                                                <Icon fieldid="ublinker-routes-list-components-SwitchNcModal-index-9123298-Icon" type="uf-correct-2" />
                                            </span>
                                        ) : null}
                                    </li>
                                );
                            })}
                        </ul>
                    </div>
                </Modal>
            );
        } else {
            return null;
        }
    }
}

export default EditModal;
