import { observable, action, toJS, reaction, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { defaultListMap } from "utils/pageListUtils";
import * as ownerService from "./service";
const initState = {
    dataSource: defaultListMap,
    pagination: null,
    serviceCodeDiwork: "kfljsjtbrw",
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    init = () => {
        this.state = initState;
    };

    getDataSource = async (data = {}) => {
        await this.getPagesListFunc({
            service: ownerService.getBatchLogService,
            requestData: data,
            dataKey: "dataSource",
            header: { serviceCode: this.state.serviceCodeDiwork },
        });
    };
}

export const storeKey = "taskMonitoringLogStore";
export default Store;
