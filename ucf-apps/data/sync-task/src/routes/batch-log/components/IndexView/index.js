import React, { Component, Fragment } from "react";
import Grid from "components/TinperBee/Grid";
import { Header } from "components/PageView";
import { navTo, initStore } from "decorator";
import query from "query-string";
import { Button } from "components/TinperBee";
import CustomTag from "components/CustomTag";
import styles from "./index.modules.css";
import ResizeObserver from "resize-observer-polyfill";
import withRouter from "decorator/withRouter";

@withRouter
@initStore()
class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            editColumn: null,
            size: { width: 0, height: 0 },
        };
    }
    bodyRef = React.createRef();

    componentDidMount() {
        const { ownerStore } = this.props;
        ownerStore.getDataSource();

        this.resizeObserver = new ResizeObserver((entries) => {
            entries.forEach((entry) => {
                const { clientWidth, clientHeight } = entry.target;
                this.setState({ size: { width: clientWidth, height: clientHeight } });
            });
        });
        this.resizeObserver.observe(this.bodyRef.current);
    }
    componentWillUnmount() {
        this.resizeObserver?.disconnect();
    }

    HandleRefresh = () => {
        const { ownerStore } = this.props;
        ownerStore.getDataSource({ pageNo: 1 });
    };

    navToLogDetail = (record) => {
        this.props.navigate({
            pathname: "/batch-detail",
            search: "?batchId=" + record.id,
        });
    };

    renderBatchDetail = (detail, record) => {
        const { successful = 0, running = 0, failed = 0, skipped = 0, unexecuted = 0 } = detail || {};
        return (
            <div size={0} className={styles["implement-details"]}>
                <span>{lang.templateByUuid("UID:P_UBL-FE_1CF2C41004580003", "成功") /* "成功" */}</span>
                <span style={{ color: "#18b681" }}>{successful} </span>
                <span className={styles["implement-divide"]} />
                <span>{lang.templateByUuid("UID:P_UBL-FE_1CF2C41004580004", "运行中") /* "运行中" */}</span>
                <span style={{ color: "#588ce9" }}>{running}</span>
                <span className={styles["implement-divide"]} />
                <span>{lang.templateByUuid("UID:P_UBL-FE_1CF2C41004580005", "失败") /* "失败" */}</span>
                <span style={{ color: "#ee2223" }}>{failed}</span>
                <span className={styles["implement-divide"]} />
                <span>{lang.templateByUuid("UID:P_UBL-FE_1CF2C41004580001", "跳过") /* "跳过" */}</span>
                <span style={{ color: "#a878f6" }}>{skipped}</span>
                <span className={styles["implement-divide"]} />
                <span>{lang.templateByUuid("UID:P_UBL-FE_1CF2C41004580002", "未运行") /* "未运行" */}</span>
                <span style={{ color: "#708091" }}>{unexecuted}</span>
            </div>
        );
    };

    renderTriggerType = (value) => {
        let type = "";
        switch (value) {
            case 0:
                type = lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180382", "定时触发") /* "定时触发" */;
                break;
            case 1:
                type = lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180384", "手动触发") /* "手动触发" */;
                break;
            case 2:
                type = lang.templateByUuid("UID:P_UBL-FE_1F7D965C05600012", "API触发") /* "API触发" */;
                break;
            case 3:
                type = lang.templateByUuid("UID:P_UBL-FE_1F7D965C05600013", "YonBIP事件触发") /* "YonBIP事件触发" */;
                break;
            case 10:
                type = lang.templateByUuid("UID:P_UBL-FE_1F7D965C05600012", "API触发") /* "API触发" */;
                break;
        }
        return type;
    };

    renderStatus = (status) => {
        let text = "",
            cls = "";
        switch (status) {
            case 0:
                text = lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180388", "执行中") /* "执行中" */;
                cls = "pending";
                break;
            case 1:
                text = lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418038B", "执行结束") /* "执行结束" */;
                cls = "success";
                break;
        }
        return <CustomTag tagName={text} status={cls} />;
    };

    gridColumns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180394", "序号") /* "序号" */,
            dataIndex: "$$index",
            width: 60,
            render: (value, record, index) => {
                const {
                    ownerState: { dataSource },
                } = this.props;
                return (dataSource.pageIndex - 1) * dataSource.pageSize + 1 + index;
            },
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418006E", "方案名称") /* "方案名称" */,
            dataIndex: "taskname",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180385", "任务数量") /* "任务数量" */,
            dataIndex: "viewnum",
            width: 70,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180387", "触发类型") /* "触发类型" */,
            dataIndex: "triggertype",
            render: this.renderTriggerType,
            width: 75,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418038A", "开始时间") /* "开始时间" */,
            dataIndex: "starttime",
            render: Grid.renderTime,
            width: 160,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418038C", "结束时间") /* "结束时间" */,
            dataIndex: "endtime",
            render: Grid.renderTime,
            width: 160,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180391", "执行状态") /* "执行状态" */,
            dataIndex: "status",
            width: 85,
            render: this.renderStatus,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180395", "执行详情") /* "执行详情" */,
            dataIndex: "detail",
            render: this.renderBatchDetail,
            width: 260,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180396", "备注") /* "备注" */,
            dataIndex: "comments",
            render: Grid.renderText,
            width: 280,
        },
    ];
    hoverContent = (record = {}) => {
        return (
            <Button
                fieldid="UCG-FE-routes-batch-log-components-IndexView-index-7054793-GridAction"
                {...Grid.hoverButtonPorps}
                onClick={this.navToLogDetail.bind(null, record)}
            >
                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180389", "查看详情") /* "查看详情" */}
            </Button>
        );
    };
    back = () => {
        let _this = this;
        _this.props.navigate({
            pathname: "/list",
            search:
                "?" +
                query.stringify({
                    back: "back",
                }),
            type: "back",
        });
    };
    render() {
        let { ownerState } = this.props;
        let { dataSource, pagination } = ownerState;

        return (
            <div className={styles["batch-log"]}>
                <Header
                    back={this.back}
                    fixed
                    title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801D1", "任务运行监控", undefined, {
                        returnStr: true,
                    })}
                >
                    <Button fieldid="ublinker-routes-batch-log-components-IndexView-index-4371254-Button" bordered onClick={this.HandleRefresh}>
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418038E", "刷新") /* "刷新" */}
                    </Button>
                </Header>
                <div ref={this.bodyRef} style={{ flex: 1, overflow: "hidden" }}>
                    <Grid
                        fieldid="ublinker-routes-batch-log-components-IndexView-index-18222-Grid"
                        rowKey="id"
                        columns={this.gridColumns}
                        data={dataSource.list}
                        pagination={pagination}
                        hoverContent={this.hoverContent}
                        scroll={{ y: this.state.size.height - 62 }}
                        bodyStyle={{ minHeight: this.state.size.height - 62 }}
                    />
                </div>
            </div>
        );
    }
}

export default IndexView;
