import React, { Component, Fragment } from "react";
import Grid from "components/TinperBee/Grid";
import { Header, Content, Footer } from "components/PageView";
import { Button } from "components/TinperBee";
import LogModal from "../../../sync-log/components/LogModal";
import LogTotal from "../LogTotal";
import CustomTag from "components/CustomTag";
import { createEnum } from "constants/utils";
import { initStore } from "decorator";
import { navTo } from "decorator";
import styles from "./index.modules.css";

import ResizeObserver from "resize-observer-polyfill";

@initStore()
class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            editColumn: null,
            size: {},
        };
    }
    bodyRef = React.createRef();

    componentDidMount() {
        let { ownerStore } = this.props;
        ownerStore.getDataSource();
        this.resizeObserver = new ResizeObserver((entries) => {
            entries.forEach((entry) => {
                const { clientWidth, clientHeight } = entry.target;
                this.setState({ size: { width: clientWidth, height: clientHeight } });
            });
        });
        this.resizeObserver.observe(this.bodyRef.current);
    }
    componentWillUnmount() {
        this.resizeObserver?.disconnect();
    }

    handleLogDetail = (record) => {
        this.props.ownerStore.getLogDetail(record.logid);
    };
    navToMonitor = (record) => {
        this.props.navigate({
            pathname: "/batch-monitor",
            search: "?traceId=" + record.logid,
        });
    };

    taskStatusTypes = createEnum([
        {
            code: "0",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801C0", "未运行") /* "未运行" */,
            cls: "default",
        },
        {
            code: "1",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801B4", "运行中") /* "运行中" */,
            cls: "pending",
        },
        {
            code: "2",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801B6", "运行成功") /* "运行成功" */,
            cls: "success",
        },
        {
            code: "3",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801B9", "运行失败") /* "运行失败" */,
            cls: "fail",
        },
        {
            code: "4",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801BB", "部分成功") /* "部分成功" */,
            // name: "运行成功(含异常数据)",
            // tagName: "部分成功",
            cls: "warning",
        },
        {
            code: "5",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801BC", "运行冲突") /* "运行冲突" */,
            cls: "warning",
        },
        {
            code: "6",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801BE", "跳过") /* "跳过" */,
            cls: "default",
        },
        {
            code: "7",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801BF", "等待") /* "等待" */,
            cls: "default",
        },
        {
            code: "8",
            name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801B3", "数据处理中") /* "数据处理中" */,
            cls: "default",
        },
    ]);
    renderStatus = (status, record) => {
        const taskStatus = this.taskStatusTypes[status];
        return <CustomTag tagName={taskStatus.tagName || taskStatus.name} status={taskStatus?.cls} />;
    };

    gridColumns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802FC", "序号") /* "序号" */,
            dataIndex: "$$index",
            width: 80,
            render: (value, record, index) => {
                return index + 1;
            },
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418006E", "方案名称") /* "方案名称" */,
            dataIndex: "view",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180071", "方案编码") /* "方案编码" */,
            dataIndex: "viewtag",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802FB", "任务执行状态") /* "任务执行状态" */,
            dataIndex: "status",
            render: this.renderStatus,
            width: 140,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802FE", "运行开始时间") /* "运行开始时间" */,
            dataIndex: "starttime",
            render: Grid.renderTime,
            width: 160,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802F6", "运行结束时间") /* "运行结束时间" */,
            dataIndex: "endtime",
            render: Grid.renderTime,
            width: 160,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802FA", "备注") /* "备注" */,
            dataIndex: "comments",
            width: 280,
        },
    ];
    // 集成日志
    handleLogModalCancel = (record) => {
        this.props.ownerStore.setLogDetail(record);
        return;
        if (record?.fromType === 3) {
            // TODO：后端新增fromType
            // 集成流-跳转到集成日志定位到该条记录，并打开日志详情
            window.jDiwork?.openService("kflj_Taskmonitoring", { filter: { requestId: "5a66a28b-b976-4a15-96f8-8721c232d2be" }, expandDetail: "true" });
        } else {
            this.props.ownerStore.setLogDetail(record);
        }
    };
    // 链路追踪
    handleLinkTrack = (record) => {
        debugger;
        if (record?.fromType === 3) {
            // window.jDiwork?.openService("kflj_Taskmonitoring", { filter: JSON.stringify({ traceId: record.traceId }) });
            window.jDiwork?.openService(
                this.props.otherSource?.source === 3 ? "YBD_LOWCODE" : "kflj_Taskmonitoring",
                {
                    routePath: "/IntegratedRuntimeBus",
                    providerHost: "/iuap-ipaas-base/ucf-wh/base-fe/",
                    from: "diwork",
                    locale: lang.lang,
                    flowId: record.flowId,
                    traceId: record.traceId,
                    status: record.taskStatus,
                    timestamp: record.startTime,
                    flowName: record.view,
                },
                {
                    providerName: "integrationDock",
                    providerEntry: "bootstrap",
                    multiTitle: {
                        zh_CN: `链路追踪-${record.view}-${record.traceId}`, //@notranslate
                        en_US: `Link tracking-${record.view}-${record.traceId}`,
                        zh_TW: `鏈路追蹤-${record.view}-${record.traceId}`, //@notranslate
                    },
                    code: record.traceId,
                }
            );
        } else {
            window.jDiwork?.openService(
                "kflj_Taskmonitoring",
                {
                    routePath: `/IntegratedData/${record.traceId}/${record.taskCode}/${record.starttime}/${record.endtime}`, // 项目路由
                    providerHost: "/iuap-ipaas-base/ucf-wh/base-fe/",
                    from: "diwork",
                    locale: lang.lang,
                },
                {
                    providerName: "integrationDock",
                    providerEntry: "bootstrap",
                    multiTitle: {
                        zh_CN: "链路追踪", //@notranslate
                        en_US: "Link tracking",
                        zh_TW: "鏈路追蹤", //@notranslate
                    },
                    code: "LinkTracking",
                }
            );
        }
    };

    hoverContent = (record = {}) => {
        if (record.status == 0) return null;

        const disabled = !record.logid;
        return (
            <>
                <Button
                    fieldid="UCG-FE-routes-batch-detail-components-IndexView-index-4205279-GridAction"
                    {...Grid.hoverButtonPorps}
                    disabled={disabled}
                    onClick={() => {
                        this.handleLinkTrack(record);
                    }}
                >
                    {lang.templateByUuid("UID:P_UBL-FE_1CE33AE804B00001", "链路追踪") /* "链路追踪" */}
                </Button>

                <Button
                    fieldid="UCG-FE-routes-batch-detail-components-IndexView-index-4205279-GridAction"
                    {...Grid.hoverButtonPorps}
                    disabled={disabled}
                    onClick={this.handleLogModalCancel.bind(null, record)}
                >
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802F7", "查看日志") /* "查看日志" */}
                </Button>
            </>
        );
    };

    HandleRefresh = () => {
        this.props.ownerStore.getDataSource();
    };

    render() {
        let { ownerState } = this.props;
        let { statistics, tasks, logDetail } = ownerState;
        return (
            <div className={styles["im-execute-detail"]}>
                <Header
                    back
                    fixed
                    title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802F4", "详情", undefined, {
                        returnStr: true,
                    })}
                    bordered
                />
                <div className={styles["im-execute-detail-content"]}>
                    <LogTotal dataSource={statistics} />
                    <div ref={this.bodyRef} style={{ flex: 1, overflow: "hidden" }}>
                        <Grid
                            fieldid="ublinker-routes-batch-detail-components-IndexView-index-4150507-Grid"
                            header={
                                <div className="ucg-align-r ucg-pad-r-20">
                                    <Button
                                        fieldid="ublinker-routes-batch-detail-components-IndexView-index-2836126-Button"
                                        bordered
                                        onClick={this.HandleRefresh}
                                    >
                                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041802F9", "刷新") /* "刷新" */}
                                    </Button>
                                </div>
                            }
                            rowKey="id"
                            columns={this.gridColumns}
                            data={tasks}
                            hoverContent={this.hoverContent}
                            scroll={{ y: this.state.size.height - 83 }}
                            bodyStyle={{ minHeight: this.state.size.height - 83 }}
                        />
                    </div>
                </div>
                <LogModal selectedLog={logDetail} onCancel={this.handleLogModalCancel} />
            </div>
        );
    }
}

export default IndexView;
