import React from "react";

import totalIcon from "./icon/total.png";
import successIcon from "./icon/success.png";
import failIcon from "./icon/fail.png";
import doingIcon from "./icon/doing.png";
import jumpIcon from "./icon/jump.png";
import notIcon from "./icon/not.png";

import "./index.less";

const LogTotal = ({ dataSource }) => {
    const { total, successful, running, failed, skipped, unexecuted } = dataSource;
    return (
        <ul className="batch-log-total">
            <li className="batch-log-total-item">
                <div className="icon-box">
                    <img fieldid="ublinker-routes-batch-detail-components-LogTotal-index-122361-img" src={totalIcon} alt="" />
                </div>
                <div className="content-box">
                    <p className="total">{total}</p>
                    <p className="name">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180241", "总数量") /* "总数量" */}</p>
                </div>
            </li>

            <li className="batch-log-total-item">
                <div className="icon-box">
                    <img fieldid="ublinker-routes-batch-detail-components-LogTotal-index-6721811-img" src={successIcon} alt="" />
                </div>
                <div className="content-box">
                    <p className="total">{successful}</p>
                    <p className="name">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418023F", "运行成功") /* "运行成功" */}</p>
                </div>
            </li>

            <li className="batch-log-total-item">
                <div className="icon-box">
                    <img fieldid="ublinker-routes-batch-detail-components-LogTotal-index-720988-img" src={doingIcon} alt="" />
                </div>
                <div className="content-box">
                    <p className="total">{running}</p>
                    <p className="name">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180243", "运行中") /* "运行中" */}</p>
                </div>
            </li>

            <li className="batch-log-total-item">
                <div className="icon-box">
                    <img fieldid="ublinker-routes-batch-detail-components-LogTotal-index-1932039-img" src={failIcon} alt="" />
                </div>
                <div className="content-box">
                    <p className="total">{failed}</p>
                    <p className="name">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180240", "运行失败") /* "运行失败" */}</p>
                </div>
            </li>

            <li className="batch-log-total-item">
                <div className="icon-box">
                    <img fieldid="ublinker-routes-batch-detail-components-LogTotal-index-4311466-img" src={jumpIcon} alt="" />
                </div>
                <div className="content-box">
                    <p className="total">{skipped}</p>
                    <p className="name">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180244", "跳过") /* "跳过" */}</p>
                </div>
            </li>

            <li className="batch-log-total-item">
                <div className="icon-box">
                    <img fieldid="ublinker-routes-batch-detail-components-LogTotal-index-2254919-img" src={notIcon} alt="" />
                </div>
                <div className="content-box">
                    <p className="total">{unexecuted}</p>
                    <p className="name">{lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180242", "未运行") /* "未运行" */}</p>
                </div>
            </li>
        </ul>
    );
};

export default LogTotal;
