import { observable, makeObservable } from "mobx";
import De<PERSON>ultS<PERSON> from "utils/defaultStore";
import { autoServiceMessage } from "utils/service";
import * as ownerService from "./service";

const initState = {
    statistics: {
        total: 0,
        successful: 0,
        running: 0,
        failed: 0,
        skipped: 0,
        unexecuted: 0,
    },
    tasks: [],
    logDetail: null,
    serviceCodeDiwork: "kfljsjtbrw",
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    setPageParams = (paramInfo) => {
        this.changeState(paramInfo);
    };

    init = () => {
        this.state = initState;
    };

    getDataSource = async () => {
        let queryParams = this.toJS(this.state.queryParams);
        let res = await autoServiceMessage({
            service: ownerService.getBatchDetailService(queryParams, { serviceCode: this.state.serviceCodeDiwork }),
        });
        if (res) {
            let { statistics, tasks = [] } = res.data || {};
            this.changeState({
                statistics: statistics || initState.statistics,
                tasks: tasks || [],
            });
        }
    };

    setLogDetail = (logDetail = null) => {
        this.state.logDetail = logDetail;
    };

    getLogDetail = async (logId) => {
        let res = await autoServiceMessage({
            service: ownerService.getBatchLogDetailService({ logId }, { serviceCode: this.state.serviceCodeDiwork }),
        });
        if (res) {
            this.setLogDetail(res.data);
        }
    };
}

export const storeKey = "taskMonitoringDetailStore";
export default Store;
