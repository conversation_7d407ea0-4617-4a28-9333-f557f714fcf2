import { getInvokeService, getServicePath } from "utils/service";

/***
 * 查询同步任务执行详情
 * @param {Object} data
 * @param {String} data.batchId -取自同步任务日志列表中的id字段
 * @returns {Promise | Promise<unknown>}
 */
export const getBatchDetailService = function (data, header = {}) {
    return getInvokeService(
        {
            method: "GET",
            path: "/mygwapp/erpdata/task/batchexecutelog/getById",
            header,
        },
        data
    );
};

/**
 * 查询同步任务日志详情-具体日志详情
 * @param {Object} data
 * @param {String} data.logId
 * @returns {Promise | Promise<unknown>}
 */
export const getBatchLogDetailService = function (data, header = {}) {
    return getInvokeService(
        {
            method: "GET",
            path: "/mygwapp/erpdata/task/executelog/getDetailById",
            header,
        },
        data
    );
};
