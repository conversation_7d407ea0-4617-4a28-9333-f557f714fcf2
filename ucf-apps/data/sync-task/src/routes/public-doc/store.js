import { observable, action, toJS, reaction, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { defaultListMap } from "utils/pageListUtils";
import { autoServiceMessage } from "utils/service";
import * as ownerService from "./service";
import { Success } from "utils/feedback";
const initState = {
    pkField: "",
    columns: {},
    refCols: [],
    queryCols: [],
    dataSource: { ...defaultListMap },
    pagination: null,
    queryParams: {
        taskId: "",
        showUnmatched: false,
    },

    matchDataList: [],
    matchedSize: 0,
    unmatchedSize: 0,
    matching: false,
    serviceCodeDiwork: "kfljsjtbrw",
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    setPageParams = (paramInfo) => {
        this.changeState(paramInfo);
    };

    init = () => {
        this.state = initState;
    };

    getDataColumns = async () => {
        let res = await autoServiceMessage({
            service: ownerService.getDataColumnsService(this.state.queryParams.taskId, { serviceCode: this.state.serviceCodeDiwork }),
        });
        if (res) {
            let { pkField, columns, refCols, queryCols } = res;
            this.changeState({
                pkField,
                columns,
                refCols,
                queryCols,
            });
        }
        return res;
    };

    getDataList = async (reqData) => {
        let queryParams = this.toJS(this.state.queryParams);
        let _reqData = {
            ...queryParams,
            ...reqData,
        };

        this.state.queryParams.showUnmatched = typeof reqData !== "undefined" ? reqData.showUnmatched : queryParams.showUnmatched;
        this.getPagesListFunc({
            service: ownerService.getDataListService,
            requestData: _reqData,
            dataKey: "dataSource",
            paginationKey: "pagination",
            onPageChange: this.getDataList,
            header: { serviceCode: this.state.serviceCodeDiwork },
        });
    };

    cancelMatch = () => {
        this.changeState({
            matchDataList: [],
            matchedSize: 0,
            unmatchedSize: 0,
            matching: false,
        });
    };

    getFuzzyMatchData = async () => {
        let { dataSource, pkField } = this.state;
        let pks = dataSource.list.map((item) => item[pkField]);
        let res = await autoServiceMessage({
            service: ownerService.fuzzyMatchDataService(
                {
                    taskId: this.state.queryParams.taskId,
                    pks,
                },
                { serviceCode: this.state.serviceCodeDiwork }
            ),
        });

        if (res) {
            let { data: matchDataList, matchedSize, unmatchedSize } = res;
            dataSource.list.forEach((item) => {
                let pkFieldValue = item[pkField];
                let matchData = matchDataList.find((item) => item[pkField] === pkFieldValue);
                item.matchData = matchData || null;
            });
            this.changeState({
                matchDataList,
                matchedSize,
                unmatchedSize,
                matching: true,
            });
        }

        return res;
    };

    saveMatch = async () => {
        let { queryParams, pkField, dataSource } = this.state;
        let requestData = {
            taskId: queryParams.taskId,
        };
        dataSource.list.forEach((item) => {
            let pkFieldValue = item[pkField];
            let matchData = item.matchData;
            requestData[pkFieldValue] = matchData ? matchData.cloudpk : "";
        });
        let res = await autoServiceMessage({
            service: ownerService.saveMatchDataService(requestData, { serviceCode: this.state.serviceCodeDiwork }),
        });
        if (res) {
            Success(res.msg);
            this.getDataList();
            this.cancelMatch();
        }
    };
}

export const storeKey = "dataSyncTaskPublicDocStore";
export default Store;
