import { getInvokeService, getServicePath } from "utils/service";

/**
 * 获取主页面列信息(dataview)
 * @param taskId
 * @return {*}
 */
export const getDataColumnsService = function (taskId, header) {
    return getInvokeService({
        method: "GET",
        path: "/diwork/erpdata/task/dataview/init/social/" + taskId,
        header,
    });
};

/**
 * 获取主页面列表(dataview)
 * @param {Object} data
 * @param {String} data.taskId
 * @param {String|Number} data.pageNo
 * @param {String|Number} data.pageSize
 * @param {Boolean} data.showUnmatched -true未匹配|已匹配
 * @return {*}
 */
export const getDataListService = function (data, header) {
    let { taskId, ..._data } = data;
    return getInvokeService(
        {
            method: "GET",
            path: "/gwmanage/gwportal/diwork/erpdata/task/dataview/page/social/" + taskId,
            header,
        },
        _data
    );
};

/**
 * 获取模糊匹配数据
 * @param {Object} data
 * @param {String} data.taskId
 * @param {String} data.pks -当前页 dataview pk列表
 * @return {*}
 */
export const fuzzyMatchDataService = function (data, header) {
    let { taskId, ..._data } = data;
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/commondoc/fuzzyMatch/" + taskId,
            header,
        },
        _data
    );
};

/**
 * 保存模糊匹配数据
 * @param {Object} data
 * @param {String} data.taskId
   属于参数为 key=pkField value=cloudpk
 * @param {Boolean} data.showUnmatched -true未匹配|已匹配
 * @return {*}
 */
export const saveMatchDataService = function (data, header) {
    let { taskId, ..._data } = data;
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/commondoc/batchsetpkmapping/" + taskId,
            header,
        },
        _data
    );
};
