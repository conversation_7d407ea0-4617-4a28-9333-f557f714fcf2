import { getInvokeService, getServicePath } from "utils/service";

/**
 * 手动关联档案时，查询档案列表
 * @param {String} data.taskId
 * @param {String|Number} data.pageNo
 * @param {String|Number} data.pageSize
 * @param {Boolean} data.showUnmatched -true未匹配|已匹配
 * @param {String} data.searchkey -column.key
 * @param {String} data.searchvalue
 * @return {*}
 */
export const getDocListService = function (data) {
    let { taskId, ..._data } = data;
    return getInvokeService(
        {
            method: "POST",
            path: "/diwork/commondoc/pageDocData/" + taskId,
        },
        _data
    );
};

/**
 * 手动关联档案保存
 * @param {Object} data
 * @param {String} data.taskId
 * @param {String} data.taskdatapk
 * @param {String} data.docdatapk
 * @return {*}
 */
export const docConnectService = function (data) {
    let { taskId, ..._data } = data;
    return getInvokeService(
        {
            method: "GET",
            path: "/diwork/commondoc/setpkmapping/" + taskId,
        },
        _data
    );
};
