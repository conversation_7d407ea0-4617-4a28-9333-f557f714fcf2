import React, { Component } from "react";
import Modal from "components/TinperBee/Modal";
import { FormControl, InputGroup, Dropdown, Button } from "components/TinperBee";
import Menu from "components/TinperBee/Menu";
import Grid from "components/TinperBee/Grid";
import { autoServiceMessage } from "utils/service";
import { getDocListService, docConnectService } from "./service";

class AddModal extends Component {
    constructor(props) {
        super(props);
        this.state = {
            show: false,
            selectedList: [],
            selectedRowIndex: "",
            dataSource: {
                list: [],
                pageNo: 1,
                pageSize: 10,
                total: 0,
            },
            pagination: null,
            columns: {},
            gridColumns: [],
            searchKey: "",
            searchValue: "",
            gridShow: false,
            pkfield: "",
        };
    }

    getDataSource = async (reqData = {}) => {
        let { dataSource, searchKey, searchValue } = this.state;
        let { showUnmatched, taskId } = this.props;
        let _reqData = {
            taskId: taskId,
            pageNo: dataSource.pageNo,
            pageSize: dataSource.pageSize,
            showUnmatched,
            searchkey: searchKey,
            searchvalue: searchValue,
            ...reqData,
        };

        let res = await autoServiceMessage({
            service: getDocListService(_reqData),
        });
        if (res) {
            let resData = res.data || {};
            let _dataSource = {
                list: resData.items || [],
                total: resData.itemCount,
                totalPages: resData.pageCount,
                pageNo: _reqData.pageNo || 1,
                pageSize: _reqData.pageSize || 10,
            };

            Object.assign(dataSource, _dataSource);

            let pagination = null;

            if (_dataSource.list.length > 0) {
                pagination = {
                    total: resData.itemCount,
                    items: Number(resData.pageCount),
                    pageSize: _dataSource.pageSize,
                    activePage: _dataSource.pageNo,
                    onPageChange: this.getDataSource,
                };
            }

            let columns = res.columns;
            let gridColumns = [],
                initSearchKey = "";

            for (let key in columns) {
                let columnName = columns[key];
                gridColumns.push({ title: columnName, dataIndex: key });
                if (!initSearchKey) {
                    initSearchKey = key;
                }
            }

            let _state = {
                dataSource,
                pagination,
                gridColumns,
                columns,
                searchKey: initSearchKey,
                gridShow: gridColumns.length > 0,
                pkfield: res.pkfield,
            };

            this.setState(_state);
        }
    };

    componentWillReceiveProps = (nextProps) => {
        if (nextProps.show && !this.state.show) {
            this.setState(
                {
                    show: nextProps.show,
                    selectedList: nextProps.selectedList || [],
                },
                () => {
                    this.getDataSource();
                }
            );
        }

        if (!nextProps.show && this.state.show) {
            this.setState({
                show: nextProps.show,
                selectedList: [],
                dataSource: {
                    list: [],
                    pageNo: 1,
                    pageSize: 10,
                    total: 0,
                },
                pagination: null,
                gridColumns: [],
                columns: [],
                searchKeys: [],
                searchKey: "",
                searchValue: "",
                gridShow: false,
            });
        }
    };

    getSelectedDataFunc = (selectedList, data) => {
        this.setState({ selectedList });
    };

    handleCancel = () => {
        this.props.onCancel();
    };

    handleOk = async () => {
        let { selectedList, pkfield, dataPkField } = this.state;
        let selectedDoc = selectedList[0];
        let { dataPk, taskId } = this.props;
        let res = await autoServiceMessage({
            service: docConnectService({
                taskId,
                taskdatapk: dataPk,
                docdatapk: selectedDoc[pkfield],
            }),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418010C", "关联成功", undefined, {
                returnStr: true,
            }) /* "关联成功" */,
        });
        if (res) {
            this.handleCancel();
            this.props.onRefresh();
        }
    };

    onRowClick = (record) => {
        this.setState({ selectedList: [record] });
    };
    rowClassNameFun = (record) => {
        let { pkKey } = this.props;
        let { selectedList } = this.state;
        let selected = selectedList[0];
        if (selected && record[pkKey] === selected[pkKey]) {
            return "selected";
        } else {
            return "";
        }
    };

    changeSearchKey = (node) => {
        let { key } = node;
        this.setState({ searchKey: key });
    };

    changeSearchValue = (value) => {
        this.setState({ searchValue: value });
    };

    handleSearch = () => {
        this.getDataSource({ pageNo: 1 });
    };

    getSearchKeyMenu = (gridColumns, searchKey) => {
        return (
            <Menu fieldid="ublinker-routes-public-doc-components-DocRefer-index-3910679-Menu" selectedKeys={[searchKey]} onSelect={this.changeSearchKey}>
                {gridColumns.map((item) => {
                    let { title, dataIndex } = item;
                    return (
                        <Menu.Item fieldid="UCG-FE-routes-public-doc-components-DocRefer-index-6820995-Menu.Item" key={dataIndex}>
                            {title}
                        </Menu.Item>
                    );
                })}
            </Menu>
        );
    };

    renderHeader = () => {
        let { gridColumns, searchKey, columns } = this.state;
        return (
            <div className="ucg-pad-l-20 ucg-pad-t-10" ref={(node) => (this.getGridHeaderNode = node)}>
                <InputGroup fieldid="ublinker-routes-public-doc-components-DocRefer-index-5232445-InputGroup" style={{ width: 280 }}>
                    <InputGroup.Button shape="border">
                        <Dropdown
                            fieldid="ublinker-routes-public-doc-components-DocRefer-index-134570-Dropdown"
                            trigger={["click"]}
                            overlay={this.getSearchKeyMenu(gridColumns, searchKey)}
                            animation="slide-up"
                            getPopupContainer={() => this.getGridHeaderNode}
                        >
                            <Button fieldid="ublinker-routes-public-doc-components-DocRefer-index-6963281-Button" shape="border">
                                {columns[searchKey]} <span className="uf uf-arrow-down"> </span>
                            </Button>
                        </Dropdown>
                    </InputGroup.Button>
                    <FormControl
                        fieldid="ublinker-routes-public-doc-components-DocRefer-index-846147-FormControl"
                        type="search"
                        onChange={this.changeSearchValue}
                        onSearch={this.handleSearch}
                    />
                </InputGroup>
            </div>
        );
    };

    render() {
        let { dataSource, show, selectedList, pagination, gridColumns, gridShow } = this.state;
        return (
            <Modal
                fieldid="ublinker-routes-public-doc-components-DocRefer-index-3416924-Modal"
                title={lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418010D", "档案数据", undefined, {
                    returnStr: true,
                })}
                show={show}
                onCancel={this.handleCancel}
                onOk={this.handleOk}
                okDisabled={selectedList.length <= 0}
            >
                {gridShow ? (
                    <Grid
                        fieldid="ublinker-routes-public-doc-components-DocRefer-index-9329140-Grid"
                        size="sm"
                        header={this.renderHeader()}
                        data={dataSource.list}
                        columns={gridColumns}
                        rowKey="id"
                        selectedList={selectedList}
                        radioSelect
                        rowClassName={this.rowClassNameFun}
                        getSelectedDataFunc={this.getSelectedDataFunc}
                        pagination={pagination}
                    />
                ) : null}
            </Modal>
        );
    }
}

export default AddModal;
