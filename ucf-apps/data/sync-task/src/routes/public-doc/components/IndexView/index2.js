import React, { Component, Fragment } from "react";
import { FormControl, Icon, Button, Modal, Checkbox } from "components/TinperBee";
import Grid from "components/TinperBee/Grid";
import DocRefer from "../DocRefer";
import { Header, Content, Footer } from "components/PageView";
import { GridActions, GridAction } from "components/TinperBee/Grid/GridActions";
import "./index.less";
import commonText from "constants/commonText";

class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            gridColumns: [],
            showGrid: false,
            showDocModal: false,
            dataPk: "",
        };
    }

    async componentDidMount() {
        let { ownerStore } = this.props;
        let columnsRes = await ownerStore.getDataColumns();
        if (columnsRes) {
            let gridColumns = this.getColumns(columnsRes.columns);
            this.setState({
                gridColumns,
                showGrid: gridColumns.length > 0,
            });
            await ownerStore.getDataList();
        }
    }

    componentWillUnmount() {
        this.props.ownerStore.init();
    }

    changeDocModalShow = (record = null) => {
        let {
            ownerState: { pkField },
        } = this.props;
        this.setState({
            showDocModal: !this.state.showDocModal,
            dataPk: record ? record[pkField] : "",
        });
    };

    actionCol = {
        title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804A2", "操作") /* "操作" */,
        dataIndex: "action",
        fixed: "right",
        render: (value, record, index) => {
            let {
                ownerState: { matching },
            } = this.props;
            return (
                <GridActions>
                    <GridAction
                        fieldid="UCG-FE-routes-public-doc-components-IndexView-index2-5630329-GridAction"
                        disabled={matching}
                        onClick={this.changeDocModalShow.bind(null, record)}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804A6", "手动匹配") /* "手动匹配" */}
                    </GridAction>
                </GridActions>
            );
        },
    };
    hoverContent = (record) => {
        let {
            ownerState: { matching },
        } = this.props;
        return (
            <Button
                {...Grid.hoverButtonPorps}
                fieldid="UCG-FE-routes-public-doc-components-IndexView-index2-5630329-GridAction"
                disabled={matching}
                onClick={this.changeDocModalShow.bind(null, record)}
            >
                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804A6", "手动匹配") /* "手动匹配" */}
            </Button>
        );
    };
    getColumns = (columns) => {
        let _columns = [];
        for (let key in columns) {
            let columnName = columns[key];
            _columns.push({
                title: columnName,
                dataIndex: key,
            });
        }
        // _columns.push(this.actionCol);
        return _columns;
    };

    handleUnmatchChange = (checked) => {
        let { ownerStore } = this.props;
        ownerStore.getDataList({
            pageNo: 1,
            showUnmatched: checked,
        });
    };

    handleFuzzyMatch = async () => {
        let res = await this.props.ownerStore.getFuzzyMatchData();
        if (res) {
            let { unmatchedSize, matchedSize } = res;
            Modal.info({
                title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804A7", "匹配结果") /* "匹配结果" */,
                content: (
                    <p>
                        <span style={{ color: "#19be6b", padding: "0 70px 0 0" }}>
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804A0", "已匹配：") /* "已匹配：" */}
                            {matchedSize}条
                        </span>
                        <span style={{ color: "#ed4014" }}>
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804A1", "未匹配：") /* "未匹配：" */}
                            {unmatchedSize}条
                        </span>
                    </p>
                ),
            });
        }
    };

    rowClassName = (record) => {
        let {
            ownerState: { matching },
        } = this.props;
        let { matchData } = record;
        return matching && matchData && matchData.score == 1 ? "selected" : "";
    };

    render() {
        let { gridColumns, showGrid, showDocModal, dataPk } = this.state;
        let { ownerState, ownerStore } = this.props;
        let { dataSource, pkField, queryParams, pagination, matching } = ownerState;
        if (pagination) {
            pagination.disabled = matching;
        }
        return (
            <Fragment>
                <Content>
                    <div style={{ paddingBottom: 20 }}>
                        <Checkbox fieldid="ublinker-routes-public-doc-components-IndexView-index2-9312350-Checkbox" onChange={this.handleUnmatchChange}>
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804A5", "显示未匹配的数据") /* "显示未匹配的数据" */}
                        </Checkbox>
                        <div className="ucg-float-r">
                            {matching ? (
                                <Fragment>
                                    <Button
                                        fieldid="ublinker-routes-public-doc-components-IndexView-index2-2795020-Button"
                                        className="ucg-mar-r-10"
                                        colors="primary"
                                        onClick={ownerStore.saveMatch}
                                    >
                                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804A3", "一键保存") /* "一键保存" */}
                                    </Button>
                                    <Button
                                        fieldid="ublinker-routes-public-doc-components-IndexView-index2-8200382-Button"
                                        bordered
                                        onClick={ownerStore.cancelMatch}
                                    >
                                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804A4", "取消") /* "取消" */}
                                    </Button>
                                </Fragment>
                            ) : (
                                <Button
                                    fieldid="ublinker-routes-public-doc-components-IndexView-index2-3102634-Button"
                                    className="ucg-mar-r-10"
                                    colors="primary"
                                    disabled={dataSource.list.length <= 0}
                                    onClick={this.handleFuzzyMatch}
                                >
                                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804A8", "模糊匹配") /* "模糊匹配" */}
                                </Button>
                            )}
                        </div>
                    </div>
                    {showGrid ? (
                        <Grid
                            fieldid="ublinker-routes-public-doc-components-IndexView-index2-1226290-Grid"
                            rowKey={pkField}
                            columns={gridColumns}
                            data={dataSource.list}
                            pagination={pagination}
                            rowClassName={this.rowClassName}
                            scroll={{ y: 500 }}
                            hoverContent={this.hoverContent}
                        />
                    ) : null}
                </Content>

                <DocRefer show={showDocModal} dataPk={dataPk} {...queryParams} onCancel={this.changeDocModalShow} onRefresh={ownerStore.getDataList} />
            </Fragment>
        );
    }
}

export default IndexView;
