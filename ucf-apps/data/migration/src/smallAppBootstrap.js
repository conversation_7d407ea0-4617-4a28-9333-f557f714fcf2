import React from "react";
import { Provider } from "core";
import { MemoryRouter as Router } from "react-router";
import Routes from "./routes";
import lang from "tne-core-fe/i18n";
import withMultiLang from "components/AsyncComponent/withMultiLang";
const MultiLangRouter = withMultiLang(Routes, ["YS_IPAAS_UBL-FE"], { serviceCode: "kfljsjjhdy" });
lang.init({ zhcn: {}, zhtw: {}, enus: {} }, null);
import "./app.less";
import "static/cl/iconfont.css";
import "static/ipaas/iconfont.css";
import "static/icon";
import { handleEntriesParams } from "utils/entriesParams";

import { configure } from "mobx";
configure({ isolatedGlobalState: true });
const { SmallApplication } = window.tnsSdk;

export default function SmallAppBootstrap() {
    return (
        <SmallApplication style={{ transform: "translate(0)" }}>
            {(appContext) => {
                const initialEntries = handleEntriesParams(appContext);
                return (
                    <Provider serviceCodeDiwork="kfljsjjhdy">
                        <Router initialEntries={initialEntries}>
                            <MultiLangRouter />
                        </Router>
                    </Provider>
                );
            }}
        </SmallApplication>
    );
}
