import React from "react";
import { RoutesRender } from "core";

import ListContainer from "./list/container";

import getConfigProvider from "components/ConfigProvider";
import config from "../config";

const ConfigProvider = getConfigProvider(config);

const routes = [
    {
        path: "/",
        exact: true,
        component: ListContainer,
    },
];

const Routes = () => {
    return (
        <ConfigProvider>
            <RoutesRender routes={routes} />
        </ConfigProvider>
    );
};
export default Routes;
