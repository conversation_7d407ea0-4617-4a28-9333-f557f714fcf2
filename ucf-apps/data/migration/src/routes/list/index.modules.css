.migrate-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
}
.migrate-list {
    flex: 1;
    overflow: hidden;
}
.migrate-pagination {
    height: 40px;
}
.code-render {
    display: flex;
    align-items: center;
}
.code-copy {
    padding-left: 4px;
    display: none;
    cursor: pointer;
}
.sync-task-content {
    :global(.wui-table-row-hover) {
        .code-copy {
            display: block;
        }
    }
    background-color: #fff;
    display: flex;
    flex-direction: column;
}
.sync-data-table {
    flex: 1;
    overflow: hidden;
}
.condition-filter-modal {
    :global {
        .wui-form-item {
            margin-bottom: 8px !important;
        }
        .monaco-editor .minimap {
            display: none;
        }
    }
}
.migrate-steps {
    :global {
        .wui-steps-item-icon {
            width: 34px;
        }
        .wui-steps-item {
            min-height: 100px;
        }
        .wui-steps-item-title {
            font-weight: 600;
            font-size: 14px;
        }
    }
}
