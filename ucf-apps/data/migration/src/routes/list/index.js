import React, { useRef, useEffect, useState } from "react";
import { Space, Table, Pagination, Button, Drawer, Steps, Row, Tag, Col, Icon } from "@tinper/next-ui";
import { SearchForm } from "tne-tinpernextpro-fe";
import Grid from "components/TinperBee/Grid";
import ResizeObserver from "resize-observer-polyfill";
import { autoServiceMessage } from "utils/service";
import * as ownerService from "./service";
import { downloadFileByIframe } from "utils/index";
import { Warning } from "utils/feedback";

const { multiSelect } = Table;
const DragColumnTable = multiSelect(Table);
const { Step } = Steps;
import styles from "./index.modules.css";

const Item = SearchForm.Item;

const formItemLayout = {
    labelCol: {
        xs: { span: 8 },
        sm: { span: 8 },
    },
    wrapperCol: {
        xs: { span: 16 },
        sm: { span: 16 },
    },
};

function IndexView(props) {
    const { ownerStore, ownerState } = props;
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [size, setSize] = useState({});
    const [currentTenant, setCurrentTenant] = useState({});
    const [drawerVisible, setDrawerVisible] = useState(false);
    const detailTimer = React.createRef();

    const bodyRef = React.createRef();
    const { pagination } = ownerState;
    const migrateStatus = [
        lang.templateByUuid("UID:P_UBL-FE_1E96520204900030", "未迁移") /* "未迁移" */,
        lang.templateByUuid("UID:P_UBL-FE_1E9652020490002E", "等待迁移") /* "等待迁移" */,
        lang.templateByUuid("UID:P_UBL-FE_1E9652020490002D", "正在迁移") /* "正在迁移" */,
        lang.templateByUuid("UID:P_UBL-FE_1E9652020490002B", "迁移完成") /* "迁移完成" */,
        lang.templateByUuid("UID:P_UBL-FE_1E9652020490002C", "迁移失败") /* "迁移失败" */,
        lang.templateByUuid("UID:P_UBL-FE_1E9652020490002F", "已忽略") /* "已忽略" */,
        "已回滚",//@notranslate
    ];
    const tagStatusType = { 1: "info", 2: "danger", 3: "success", 4: "warning" }; // 0：未开始   1：执行中  2：失败  3：成功 4：跳过
    const stepStatusEnum = {
        0: "wait", // 未执行
        1: "process", // 执行中
        2: "finish", // 迁移成功
        3: "error", // 迁移失败
        4: "wait", // 执行跳过
        5: "wait", // 执行取消
    };

    const steps = ownerState.migrateDetail || [];

    useEffect(() => {
        ownerStore.getMigrationList({ pageNo: 1 });
        const resizeObserver = new ResizeObserver((entries) => {
            entries.forEach((entry) => {
                const { clientWidth, clientHeight } = entry.target;
                setSize({ width: clientWidth, height: clientHeight });
            });
        });
        if (bodyRef.current) {
            resizeObserver.observe(bodyRef.current);
        }
        return () => {
            resizeObserver.disconnect();
        };
    }, []);

    //  使用ref存储最新状态避免闭包问题
    const drawerVisibleRef = useRef(drawerVisible);
    const currentTenantRef = useRef(currentTenant);

    useEffect(() => {
        drawerVisibleRef.current = drawerVisible;
        currentTenantRef.current = currentTenant;
    }, [drawerVisible, currentTenant]);

    const getMigrateDetailTimer = () => {
        if (detailTimer.current) {
            clearTimeout(detailTimer.current);
            detailTimer.current = null;
        }

        detailTimer.current = setTimeout(async () => {
            // 使用ref获取最新状态
            if (!drawerVisibleRef.current) {
                return;
            }

            try {
                const migrateDetail = await ownerStore.getMigrationDetail(currentTenantRef.current.tenant_id, false);

                // 再次检查drawer状态，可能在等待API期间已更改
                if (!drawerVisibleRef.current) return;

                const lastStep = migrateDetail?.[migrateDetail.length - 1];
                if (drawerVisibleRef.current && (lastStep?.step_status === 0 || lastStep?.step_status === 1)) {
                    getMigrateDetailTimer();
                }
            } catch (error) {
                console.log("获取迁移详情失败:", error);
            }
        }, 3000);
    };

    useEffect(() => {
        if (drawerVisible && currentTenant?.tenant_id) {
            // 延迟启动轮询，避免和初始加载的API冲突
            const startTimer = setTimeout(() => {
                getMigrateDetailTimer();
            }, 500);

            return () => {
                clearTimeout(startTimer);
                if (detailTimer.current) {
                    clearTimeout(detailTimer.current);
                    detailTimer.current = null;
                }
            };
        } else if (!drawerVisible) {
            if (detailTimer.current) {
                clearTimeout(detailTimer.current);
                detailTimer.current = null;
            }
            ownerStore.getMigrationList();
        }

        return () => {
            if (detailTimer.current) {
                clearTimeout(detailTimer.current);
                detailTimer.current = null;
            }
        };
    }, [drawerVisible, currentTenant]);

    const rowSelection = {
        selectedRowKeys: selectedRowKeys,
        onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRowKeys(selectedRowKeys);
        },
    };

    const hoverContent = (record = {}) => {
        const { ownerStore } = props;

        return (
            <Space fieldid="6beb7b5f-e0fd-418a-ba55-c29a0f468e57" size={3}>
                {[0, 4, 6].includes(record.migrate_status) && ( // 未迁移 迁移失败 易忽略
                    <Button
                        fieldid="468bbd50-7efe-4759-8cfc-e8d68aa0e1a1"
                        {...Grid.hoverButtonPorps}
                        onClick={() => ownerStore.markTenant({ tenant_id: record.tenant_id, mark_type: 0 })}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_1E9652020490003E", "忽略") /* "忽略" */}
                    </Button>
                )}
                {record.migrate_status === 5 && (
                    <Button
                        fieldid="b89dca91-0bb8-42a0-8743-9d1f17e3d35a"
                        {...Grid.hoverButtonPorps}
                        onClick={() => ownerStore.markTenant({ tenant_id: record.tenant_id, mark_type: 1 })}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_1E96520204900033", "取消忽略") /* "取消忽略" */}
                    </Button>
                )}
                {[2, 3, 4].includes(record.migrate_status) && (
                    <Button
                        fieldid="a260e307-2be9-43a5-a1d3-c26614627cae"
                        {...Grid.hoverButtonPorps}
                        onClick={() => {
                            setCurrentTenant(record);
                            setDrawerVisible(true);
                            ownerStore.getMigrationDetail(record.tenant_id, true);
                        }}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_1E96520204900038", "查看迁移详情") /* "查看迁移详情" */}
                    </Button>
                )}
                {record.migrate_status === 3 && ( // 成功
                    <Button {...Grid.hoverButtonPorps} onClick={() => ownerStore.rollbackMigrate({ tenant_id: record.tenant_id })}>
                        {lang.templateByUuid("UID:P_UBL-FE_20076E580428004D", "回滚") /* "回滚" */}
                    </Button>
                )}
            </Space>
        );
    };
    const columnsVar = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_1E9652020490003A", "租户名称") /* "租户名称" */,
            dataIndex: "tenant_name",
            key: "tenant_name",
            width: 300,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_1E9652020490003B", "租户id") /* "租户id" */,
            dataIndex: "tenant_id",
            key: "tenant_id",
            width: 300,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_1E96520204900029", "数据总条数") /* "数据总条数" */,
            dataIndex: "total_data_size",
            key: "total_data_size",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_1E96520204900032", "迁移状态") /* "迁移状态" */,
            dataIndex: "migrate_status",
            key: "migrate_status",
            render: (text) => migrateStatus[text],
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_1E96520204900035", "迁移开始时间") /* "迁移开始时间" */,
            dataIndex: "migrate_starttime",
            key: "migrate_starttime",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_1E96520204900037", "迁移结束时间") /* "迁移结束时间" */,
            dataIndex: "migrate_endtime",
            key: "migrate_endtime",
        },
    ];
    const renderStepDesc = (item) => {
        const stepContent = item.step_content || {};
        return (
            <div>
                {stepContent["tables"]?.length > 0 && (
                    <Row gutter={16}>
                        {stepContent["tables"]?.map((table, index) => (
                            <Col key={index}>
                                <Tag fieldid="9946e20f-7805-4bf4-865c-afef279935fa" type="filled" color={tagStatusType[table.status]} style={{ margin: "3px" }}>
                                    {table.status === 0 ? <Icon type="uf-loadingstate" /> : null}
                                    {table?.name}
                                    {table.data_size || table.data_size === 0 ? `(${table.data_size})` : ""}
                                </Tag>
                            </Col>
                        ))}
                    </Row>
                )}

                <div style={[3, 5].indexOf(item.step_status) > -1 ? { color: "red" } : null}>{stepContent.text || ""}</div>
            </div>
        );
    };

    const handleSearch = (values) => {
        const {
            ownerStore: { changeState, getMigrationList },
        } = props;
        changeState({
            searchData: values,
        });
        getMigrationList({ pageNo: 1 });
    };
    const handleExport = async () => {
        const { tenant_name = "", tenant_id = "", migrate_status = "" } = props.ownerState.searchData;
        let url =
            window.location.origin +
            `/iuap-ipaas-dataintegration/gwmanage/migration/export?tenant_name=${tenant_name}&tenant_id=${tenant_id}&migrate_status=${migrate_status}`;
        downloadFileByIframe(url);
    };
    const handleReMigrate = async (type) => {
        if (selectedRowKeys.length === 0) {
            Warning(lang.templateByUuid("UID:P_UBL-FE_1C2D42C20448000E", "请先选择数据") /* "请先选择数据" */);
            return;
        }
        const res = await props.ownerStore.reMigrate({ tenant_ids: selectedRowKeys });
        if (res) {
            setSelectedRowKeys([]);
        }
    };
    const handleRefreshTenantList = async () => {
        await autoServiceMessage({
            service: ownerService.refreshTenantListService(),
        });
    };
    return (
        <div className={styles["migrate-container"]}>
            <SearchForm
                style={{ background: "#FFFFFF" }}
                formLayout={3}
                key="submitter-demo-search-form1"
                initialValues={{}}
                locale={window.lang.lang || "zh_cn"}
                showSelected={false}
                submitter={{
                    onReset: (values, formIns) => {
                        console.log("submitter onReset", values, formIns);
                        handleSearch({});
                    },
                    onSearch: (values) => {
                        handleSearch(values);
                    },
                    spaceProps: {
                        size: "small",
                    },
                    submitButtonProps: {
                        type: "default",
                    },
                    render(_props, dom) {
                        return [
                            ...dom,
                            <Button fieldid="fc04a9c6-f140-4eb4-a270-91f20854553d" key="custom0" onClick={() => handleReMigrate(0)}>
                                {lang.templateByUuid("UID:P_UBL-FE_1E9652020490002A", "迁移") /* "迁移" */}
                            </Button>,
                            // <Button fieldid="534ed294-9383-48f3-bbbb-1621eac420b0" key="custom1" onClick={() => handleReMigrate(1)}>
                            //     {lang.templateByUuid("UID:P_UBL-FE_1E96520204900031", "重新迁移") /* "重新迁移" */}
                            // </Button>,
                            <Button fieldid="8ad093b2-a8a5-4204-baba-4c3293fe76ad" key="custom2" onClick={handleExport}>
                                {lang.templateByUuid("UID:P_UBL-FE_1E96520204900034", "导出") /* "导出" */}
                            </Button>,
                            <Button fieldid="d58193ea-69b7-481a-a185-7504975e9b6e" key="custom3" onClick={handleRefreshTenantList}>
                                {lang.templateByUuid("UID:P_UBL-FE_1E96520204900036", "更新租户列表") /* "更新租户列表" */}
                            </Button>,
                        ];
                    },
                }}
            >
                <Item
                    {...formItemLayout}
                    allowClear
                    inputType="input"
                    label={lang.templateByUuid("UID:P_UBL-FE_1E9652020490003A", "租户名称") /* "租户名称" */}
                    name="tenant_name"
                    placeholder={lang.templateByUuid("UID:P_UBL-FE_1E96520204900039", "请输入租户") /* "请输入租户" */}
                />
                <Item
                    {...formItemLayout}
                    allowClear
                    inputType="input"
                    label={lang.templateByUuid("UID:P_UBL-FE_1E9652020490003B", "租户id") /* "租户id" */}
                    name="tenant_id"
                    placeholder={lang.templateByUuid("UID:P_UBL-FE_1E96520204900039", "请输入租户") /* "请输入租户" */}
                />
                <Item
                    {...formItemLayout}
                    allowClear
                    inputType="select"
                    label={lang.templateByUuid("UID:P_UBL-FE_1E96520204900032", "迁移状态") /* "迁移状态" */}
                    name="migrate_status"
                    options={migrateStatus.map((item, index) => ({ label: item, value: index }))}
                />
            </SearchForm>
            <div className={styles["migrate-list"]} ref={bodyRef}>
                <DragColumnTable
                    dragborder="fixed"
                    rowKey="tenant_id"
                    columns={columnsVar}
                    catchId="migrate-table"
                    data={ownerState.dataSource?.list || []}
                    pagination={false}
                    hoverContent={hoverContent}
                    scroll={{ y: size.height - 32 }}
                    bodyStyle={{ minHeight: size.height - 32 }}
                    rowSelection={rowSelection}
                    autoCheckedByClickRows={false}
                />
            </div>
            {pagination && (
                <Pagination
                    fieldid="2d4faec2-f34a-48d1-be30-c868b138f5f8"
                    className={styles["migrate-pagination"]}
                    current={pagination.activePage}
                    onChange={(a, b) => pagination?.onPageChange({ pageSize: b, pageNo: a })}
                    onPageSizeChange={(a, b) => pagination?.onPageChange({ pageSize: b, pageNo: 1 })}
                    showSizeChanger
                    total={pagination.total}
                    pageSize={pagination.pageSize}
                />
            )}
            <Drawer
                fieldid="3509ae74-74a9-4c6b-91a9-df5f55232d7f"
                title={lang.templateByUuid("UID:P_UBL-FE_1E9652020490003C", "迁移详情") /* "迁移详情" */}
                width={800}
                visible={drawerVisible}
                onClose={() => {
                    setDrawerVisible(false);
                    ownerStore.closeDrawer();
                }}
                showClose
            >
                <Steps
                    fieldid="72740b5e-1a17-4550-9d3c-a1aa903e4e9a"
                    direction="vertical"
                    className={styles["migrate-steps"]}
                    current={steps.findIndex((item) => item.step_status === 1)}
                >
                    {steps.map((item, index) => (
                        <Step
                            key={index}
                            title={item.step_name}
                            description={renderStepDesc(item)}
                            status={stepStatusEnum[item.step_status]}
                            icon={item.step_status === 1 ? <Icon type="uf-loadingstate" /> : null}
                        />
                    ))}
                </Steps>
            </Drawer>
        </div>
    );
}
export default IndexView;
