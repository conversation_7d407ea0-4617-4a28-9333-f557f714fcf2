import { getInvokeService } from "utils/service";

export const getMigrationListService = function (data, header = {}) {
    return getInvokeService(
        {
            method: "POST",
            path: "/migration/tenant_list",
            header,
        },
        data
    );
};
export const refreshTenantListService = function (data, header = {}) {
    return getInvokeService(
        {
            method: "POST",
            path: "/migration/refresh_tenant_list",
            header,
        },
        data
    );
};
export const exportDataService = function (data, header = {}) {
    return getInvokeService(
        {
            method: "POST",
            path: "/migration/export",
            header,
            loadingTip: lang.templateByUuid("UID:P_UBL-FE_20076E580428002F", "导出中...") /* "导出中..." */,
        },
        data
    );
};
export const reMigrateService = function (data, header = {}) {
    return getInvokeService(
        {
            method: "POST",
            path: "/migration/tenant_migrate",
            header,
            loadingTip: lang.templateByUuid("UID:P_UBL-FE_20076E580428002B", "迁移中...") /* "迁移中..." */,
        },
        data
    );
};
export const rollbackMigrateService = function (data, header = {}) {
    return getInvokeService({
        method: "POST",
        path: `/migration/rollback/${data.tenant_id}`,
        header,
    });
};

export const markTenantService = function (data) {
    return getInvokeService(
        {
            method: "PUT",
            path: "/migration/mark_tenant",
        },
        data
    );
};
// 获取迁移详情
export const getMigrationDetailService = function (tenantId, showLoading) {
    return getInvokeService({
        method: "GET",
        path: `/migration/tenant_migrate_detail/${tenantId}`,
        showLoading: showLoading ?? true,
    });
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            resolve({
                code: 0,
                msg: "success",
                data: {
                    steps: [
                        {
                            step_seq: 0,

                            step_name: lang.templateByUuid("UID:P_UBL-FE_20076E5804280031", "租户预检") /* "租户预检" */,

                            step_status: 2,

                            step_starttime: "2025-02-01 11:00:00",

                            step_endtime: "2025-02-01 11:20:00",

                            step_content: {
                                //待迁移的表集合（表名和表中的数据条数）

                                tables: [
                                    {
                                        name: "xerwerwe",

                                        table_data_size: 3000,
                                    },

                                    {
                                        name: "xerwerwe",

                                        table_data_size: 3000,
                                    },
                                ],

                                total_data_size: 4343434,
                            },
                        },

                        {
                            step_seq: 1,

                            step_name: lang.templateByUuid("UID:P_UBL-FE_20076E5804280032", "关闭定时任务") /* "关闭定时任务" */,

                            step_status: 4,

                            step_starttime: "2025-02-01 11:00:00",

                            step_endtime: "2025-02-01 11:20:00",

                            step_content: {
                                //待迁移的表集合（表名和表中的数据条数）

                                tables: [
                                    {
                                        task_id: "xerwerwe",

                                        name: lang.templateByUuid("UID:P_UBL-FE_20076E580428002D", "学历证书") /* "学历证书" */,

                                        task_code: "xodocwe|diwork|}||demo",
                                    },
                                ],
                            },
                        },

                        {
                            step_seq: 2,
                            step_name: lang.templateByUuid("UID:P_UBL-FE_20076E580428002C", "数据迁移") /* "数据迁移" */,
                            step_status: 5,
                            step_starttime: "2025-02-01 11:00:00",
                            step_endtime: "2025-02-01 11:20:00",
                            step_content: {
                                //待迁移的表
                                tables: [
                                    {
                                        name: "bip_to_nc65",
                                        migrate_data_size: 10000,
                                        status: 1,
                                    },
                                    {
                                        name: "u8_to_nc",
                                        migrate_data_size: 200,
                                        status: 2,
                                    },
                                    {
                                        name: "u8_to_ncc",
                                        migrate_data_size: 200,
                                        status: 0,
                                    },
                                    {
                                        name: "u8_to_ncc",
                                        migrate_data_size: 200,
                                        status: 4,
                                    },
                                    {
                                        name: "u8_to_ncc",
                                        migrate_data_size: 200,
                                        status: 4,
                                    },
                                    {
                                        name: "u8_to_ncc",
                                        migrate_data_size: 200,
                                        status: 4,
                                    },
                                    {
                                        name: "u8_to_ncc",
                                        migrate_data_size: 200,
                                        status: 4,
                                    },
                                    {
                                        name: "u8_to_ncc",
                                        migrate_data_size: 200,
                                        status: 4,
                                    },
                                ],
                            },
                        },

                        {
                            step_seq: 3,

                            step_name: lang.templateByUuid("UID:P_UBL-FE_20076E5804280030", "切换存储") /* "切换存储" */,

                            step_status: 1,

                            step_starttime: "2025-02-01 11:00:00",

                            step_endtime: "2025-02-01 11:20:00",

                            step_content: { text: "123" },
                        },

                        {
                            step_seq: 4,

                            step_name: lang.templateByUuid("UID:P_UBL-FE_20076E580428002E", "启用定时任务") /* "启用定时任务" */,

                            step_status: 0,

                            step_starttime: "2025-02-01 11:00:00",

                            step_endtime: "2025-02-01 11:20:00",

                            step_content: {
                                //待迁移的表集合（表名和表中的数据条数）

                                tables: [
                                    {
                                        task_id: "xerwerwe",

                                        name: lang.templateByUuid("UID:P_UBL-FE_20076E580428002D", "学历证书") /* "学历证书" */,

                                        task_code: "xodocwe|diwork|}||demo",

                                        is_success: true,
                                    },
                                ],
                            },
                        },
                    ],
                },
            });
        }, 1000);
    });
};
