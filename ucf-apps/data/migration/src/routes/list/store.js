import { observable, action, toJS, reaction, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { defaultListMap } from "utils/pageListUtils";
import { autoServiceMessage } from "utils/service";
import * as ownerService from "./service";

const initState = {
    dataSource: { ...defaultListMap },
    migrateDetail: [],
    loadingDetail: false,
    drawerVisible: false,
    searchData: {},
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    getMigrationList = async (reqData) => {
        let _reqData = {
            ...this.state.searchData,
            ...reqData,
        };
        this.getPagesListFunc({
            service: ownerService.getMigrationListService,
            requestData: _reqData,
            dataKey: "dataSource",
            paginationKey: "pagination",
            onPageChange: this.getMigrationList,
        });
    };

    @action
    getMigrationDetail = async (tenantId, showLoading) => {
        this.changeState({
            loadingDetail: true,
            drawerVisible: true,
        });
        let res = await autoServiceMessage({
            service: ownerService.getMigrationDetailService(tenantId, showLoading),
        });

        this.changeState({
            loadingDetail: false,
        });
        if (res?.data) {
            this.changeState({
                migrateDetail: res.data,
            });
            return res.data;
        }
    };

    @action
    reMigrate = async (data) => {
        const res = await autoServiceMessage({
            service: ownerService.reMigrateService(data),
        });
        if (res?.data) {
            this.getMigrationList({ pageNo: 1 });
        }
        return res;
    };
    @action
    markTenant = async (data) => {
        const res = await autoServiceMessage({
            service: ownerService.markTenantService(data),
        });
        if (res) {
            this.getMigrationList();
        }
    };
    @action
    rollbackMigrate = async (data) => {
        const res = await autoServiceMessage({
            service: ownerService.rollbackMigrateService(data),
        });
        if (res) {
            this.getMigrationList({ pageNo: 1 });
        }
    };

    @action
    closeDrawer = () => {
        this.changeState({
            drawerVisible: false,
            migrateDetail: [],
        });
    };
}

export const storeKey = "migrationListStore";

export default Store;
