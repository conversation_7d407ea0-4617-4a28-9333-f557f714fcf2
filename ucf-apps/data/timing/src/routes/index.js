import React from "react";
import { RoutesRender } from "core";

import ListContainer from "./list/container";

import getConfigProvider from "components/ConfigProvider";
import config from "../config";
const ConfigProvider = getConfigProvider(config);

const routes = [
    {
        path: "/",
        exact: true,
        component: ListContainer,
    },
    {
        path: "/list",
        component: ListContainer,
    },
];

const Routes = () => {
    return (
        <ConfigProvider fieldid="UCG-FE-data-timing-src-routes-index-9695656-ConfigProvider">
            <RoutesRender routes={routes} />
        </ConfigProvider>
    );
};
export default Routes;
