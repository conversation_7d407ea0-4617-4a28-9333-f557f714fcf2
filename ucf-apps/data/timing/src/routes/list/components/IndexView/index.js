import React, { Component } from "react";
import { Header } from "components/PageView";
import Grid from "components/TinperBee/Grid";
import EditModal from "../EditModal";
import withRouter from "decorator/withRouter";
import PageLayout from "components/PageLayout";
import { Pagination, Modal, Button, Space, Tag, Table } from "@tinper/next-ui";
import "./index.less";
import CustomTag from "components/CustomTag";
import ResizeObserver from "resize-observer-polyfill";
import DispatchModal from "ucf-apps/data/components/DispatchModal";
import FlowScheduler from "ucf-apps/data/components/FlowScheduler";
@withRouter
class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            editShow: false,
            editInfo: null,
            size: {},
            dispatchModalType: "",
            flowSchedulerData: { taskCode: "", taskId: "", visible: false },
        };
    }
    bodyRef = React.createRef();
    dispatchModalRef = React.createRef();
    componentDidMount() {
        this.props.ownerStore.getDataSource();
        this.resizeObserver = new ResizeObserver((entries) => {
            entries.forEach((entry) => {
                const { clientWidth, clientHeight } = entry.target;
                this.setState({ size: { width: clientWidth, height: clientHeight } });
            });
        });
        this.resizeObserver.observe(this.bodyRef.current);
    }

    changeEditClose = (editInfo = null) => {
        this.setState({
            editShow: false,
            editInfo,
        });
    };

    handleSave = async (data) => {
        let res = await this.props.ownerStore.save(data);
        if (res) {
            this.changeEditClose(null);
        }
    };

    handleAction = (action, record) => {
        let { ownerStore } = this.props;
        if (action === "delete") {
            Modal.confirm({
                fieldid: "************",
                title: lang.templateByUuid("UID:P_UBL-FE_18D8E09804180033", "确认要删除吗？") /* "确认要删除吗？" */,
                content: record.aliasname,
                onOk: ownerStore.timingActions.bind(null, action, record),
            });
        } else {
            ownerStore.timingActions(action, record);
        }
    };
    changeEditShow = async (editInfo = null) => {
        let treeId = "";
        const taskCode = editInfo?.jobname;
        if (editInfo.fromType === 3) {
            // 集成流
            this.setState({
                flowSchedulerData: {
                    taskCode,
                    taskId: editInfo?.id,
                    visible: true,
                },
            });
            return;
        }
        treeId = "GZTUBL"; // 数据集成treeNode code
        const baseUrl = "/iuap-apcom-supportcenter/ucf-wh/dispatch/bpaas-dispatch-fe/index.html#/";
        const iframeSrc = `${baseUrl}add/${editInfo?.id}?treeId=${treeId}&taskCode=${taskCode}&locale=${window.lang.lang || "zh_cn"}`;
        this.setState({
            dispatchModalType: "edit",
        });
        this.dispatchModalRef.current.openDispatchModal(iframeSrc, "edit");
    };
    handleDispatchModalClose = () => {
        this.props.ownerStore.getDataSource();
    };
    handleOpenDispatchDetail = (record) => {
        const baseUrl = "/iuap-apcom-supportcenter/ucf-wh/dispatch/bpaas-dispatch-fe/index.html#/detail/";
        const { jobname, id } = record;
        const iframeSrc = `${baseUrl}${id}-0?taskCode=${jobname}&locale=${window.lang.lang || "zh_cn"}`;
        this.setState({
            dispatchModalType: "detail",
        });
        this.dispatchModalRef.current.openDispatchModal(iframeSrc, "detail");
    };
    hoverContent = (record, index) => {
        let { status } = record;
        let idx = record.jobname.search(/(syncdata\|diwork)$/);
        return (
            <Space>
                <Button {...Grid.hoverButtonPorps} onClick={() => this.handleOpenDispatchDetail(record)}>
                    {lang.templateByUuid("UID:P_UBL-FE_1DE2F11404C80009", "查看") /* "查看" */}
                </Button>
                <Button
                    {...Grid.hoverButtonPorps}
                    fieldid="UCG-FE-routes-list-components-IndexView-index-3435054-GridAction"
                    onClick={this.changeEditShow.bind(null, record)}
                >
                    {lang.templateByUuid("UID:P_UBL-FE_18D8E0980418002A", "编辑") /* "编辑" */}
                </Button>

                <Button
                    {...Grid.hoverButtonPorps}
                    fieldid="UCG-FE-routes-list-components-IndexView-index-7671081-GridAction"
                    onClick={this.handleAction.bind(null, "exec", record)}
                >
                    {lang.templateByUuid("UID:P_UBL-FE_18D8E0980418002D", "立即执行") /* "立即执行" */}
                </Button>
                {status === "PAUSED" ? (
                    <Button
                        {...Grid.hoverButtonPorps}
                        fieldid="UCG-FE-routes-list-components-IndexView-index-3662724-GridAction"
                        onClick={this.handleAction.bind(null, "resume", record)}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_18D8E0980418002F", "恢复") /* "恢复" */}
                    </Button>
                ) : (
                    <Button
                        {...Grid.hoverButtonPorps}
                        fieldid="UCG-FE-routes-list-components-IndexView-index-4769666-GridAction"
                        onClick={this.handleAction.bind(null, "pause", record)}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_18D8E09804180032", "暂停") /* "暂停" */}
                    </Button>
                )}
                {idx == -1 ? (
                    <Button
                        {...Grid.hoverButtonPorps}
                        fieldid="UCG-FE-routes-list-components-IndexView-index-2677843-GridAction"
                        onClick={this.handleAction.bind(null, "delete", record)}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_18D8E0980418002B", "删除") /* "删除" */}
                    </Button>
                ) : null}
            </Space>
        );
    };

    renderStatus = (status) => {
        let statusStr = "";
        let color = "half-dark";
        switch (status) {
            case "NORMAL":
                statusStr = lang.templateByUuid("UID:P_UBL-FE_18D8E09804180037", "正常") /* "正常" */;
                status = "success";
                break;
            case "PAUSED":
                statusStr = lang.templateByUuid("UID:P_UBL-FE_18D8E09804180032", "暂停"); /* "暂停" */
                status = "pending";
                break;
            default:
                statusStr = "-";
                break;
        }
        return <CustomTag tagName={statusStr} status={status} />;
    };

    columns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_1DF51CA60488000A", "任务编码") /* "任务编码" */,
            width: 300,
            dataIndex: "jobname",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8E0980418002C", "任务名称") /* "任务名称" */,
            dataIndex: "aliasname",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8E09804180030", "任务状态") /* "任务状态" */,
            dataIndex: "status",
            width: 120,
            render: this.renderStatus,
        },
        // {
        //     title: lang.templateByUuid("UID:P_UBL-FE_18D8E09804180034", "时间表达式") /* "时间表达式" */,
        //     dataIndex: "cronexpression",
        // },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8E09804180035", "任务描述") /* "任务描述" */,
            dataIndex: "description",
        },
    ];
    handleFlowSchedulerClose = () => {
        this.setState({
            flowSchedulerData: { taskCode: "", taskId: "", visible: false },
        });
    };
    render() {
        let { editShow, editInfo, flowSchedulerData } = this.state;
        let { ownerState } = this.props;
        let { dataSource, pagination } = ownerState;
        return (
            <PageLayout
                header={
                    <Header
                        back
                        title={lang.templateByUuid("UID:P_UBL-FE_18D8E09804180031", "定时执行列表", undefined, {
                            returnStr: true,
                        })}
                    />
                }
                footer={
                    pagination ? (
                        <Pagination
                            style={{ backgroundColor: "white" }}
                            fieldid="1eeb01c2-b4b0-4312-b96e-7b44ac0c45d4"
                            {...pagination}
                            current={pagination.activePage}
                            onChange={(active, pageSize) => pagination.onPageChange({ pageNo: active, pageSize })}
                            onPageSizeChange={(active, pageSize) => pagination?.onPageChange({ pageSize, pageNo: 1 })}
                            showSizeChanger
                        />
                    ) : null
                }
            >
                <div ref={this.bodyRef} style={{ height: "calc(100% - 2px)" }}>
                    <Table
                        showRowNum={{ name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180075", "序号"), key: "id", width: "100px", base: 1 }}
                        fieldid="ublinker-routes-list-components-IndexView-index-9865043-Grid"
                        rowKey="id"
                        columns={this.columns}
                        data={dataSource.list}
                        pagination={false}
                        hoverContent={this.hoverContent}
                        scroll={{ y: this.state.size.height - 31 }}
                        bodyStyle={{ minHeight: this.state.size.height - 31 }}
                    />
                </div>

                <EditModal show={editShow} editInfo={editInfo} onCancel={this.changeEditClose} onOk={this.handleSave} />
                <DispatchModal ref={this.dispatchModalRef} onClose={this.handleDispatchModalClose} otherSource={this.props.otherSource} />

                {flowSchedulerData?.visible && <FlowScheduler {...flowSchedulerData} onCancel={this.handleFlowSchedulerClose} />}
            </PageLayout>
        );
    }
}

export default IndexView;
