import React, { Component } from "react";
import { FormControl, FormList } from "components/TinperBee";
import Modal from "components/TinperBee/Modal";
import { Error } from "utils/feedback";
import CronParser from "iuap-ip-commonui-fe/cron-parser";
import moment from "moment";
import CronParse from "components/CronEditor/parse-lib";
const FormItem = FormList.Item;
const initialCorn = "* * * * * ?";
const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 12 },
};
const dateMinute = "YYYY-MM-DD HH:mm";
class SqlWhereModal extends Component {
    constructor() {
        super();
        this.state = {
            cronexpression: initialCorn,
            description: "",
            show: "",
        };
        this.form = React.createRef();
    }

    static getDerivedStateFromProps(nextProps, prevState) {
        if (nextProps.show !== prevState.show) {
            let { cronexpression, description } = nextProps.editInfo || {};
            return {
                show: nextProps.show,
                cronexpression: nextProps.show ? cronexpression || initialCorn : initialCorn,
                description: nextProps.show ? description || "" : "",
            };
        }
        return null;
    }

    getExecTime = () => {
        let { cornValue } = this.state;
        let tempArr = [];
        const weekCron = cornValue.split(" ")[5];
        try {
            if (weekCron !== "?") {
                const interval = CronParser.parseExpression(String(cornValue).trim());
                for (let i = 0; i < 5; i++) {
                    const temp = moment(interval.next().toString()).format(dateMinute);
                    tempArr.push(temp);
                }
            } else {
                const cron = new CronParse();
                tempArr = cron.expressionChange(String(cornValue).trim());
            }
        } catch (error) {
            // console.log("error :", error);
            tempArr.push(lang.templateByUuid("UID:P_UBL-FE_18D8E09804180024", "暂无最新执行周期") /* "暂无最新执行周期" */);
        }
    };

    onChange = (field, value) => {
        this.setState({ [field]: value });
    };

    handleOk = () => {
        let { cronexpression, description } = this.state;
        if (cronexpression === "") {
            Error(
                lang.templateByUuid("UID:P_UBL-FE_18D8E09804180022", "请输入时间表达式", undefined, {
                    returnStr: true,
                }) /* "请输入时间表达式" */
            );
        } else {
            this.props.onOk({
                cronexpression,
                description,
                id: this.props.editInfo.id,
            });
        }
    };

    render() {
        let { show, cronexpression, description } = this.state;
        let { onCancel, editInfo } = this.props;
        if (show) {
            return (
                <Modal
                    fieldid="ublinker-routes-list-components-EditModal-index-4481812-Modal"
                    show={show}
                    title={lang.templateByUuid("UID:P_UBL-FE_18D8E09804180023", "设置时间表达式", undefined, {
                        returnStr: true,
                    })}
                    onCancel={onCancel}
                    onOk={this.handleOk}
                    // size={"sm"}
                >
                    {show && editInfo ? (
                        <FormList
                            fieldid="ublinker-routes-list-components-EditModal-index-5052138-FormList"
                            ref={this.form}
                            {...formItemLayout}
                            className="ucg-pad-20"
                        >
                            <FormItem
                                fieldid="ublinker-routes-list-components-EditModal-index-4426369-FormItem"
                                label={lang.templateByUuid("UID:P_UBL-FE_18D8E09804180020", "时间表达式") /* "时间表达式" */}
                                name="cronexpression"
                                initialValue={cronexpression}
                            >
                                <FormControl
                                    fieldid="ublinker-routes-list-components-EditModal-index-2254605-FormControl"
                                    // value={cronexpression}
                                    onChange={this.onChange.bind(null, "cronexpression")}
                                />
                            </FormItem>
                            <FormItem
                                fieldid="ublinker-routes-list-components-EditModal-index-677169-FormItem"
                                label={lang.templateByUuid("UID:P_UBL-FE_18D8E09804180021", "任务描述") /* "任务描述" */}
                                name="description"
                                initialValue={description}
                            >
                                <FormControl.TextArea
                                    rows={4}
                                    className="u-form-control"
                                    maxLength={100}
                                    placeholder={lang.templateByUuid("UID:P_UBL-FE_18D8E09804180021", "任务描述", undefined, {
                                        returnStr: true,
                                    })}
                                    // value={description}
                                    onChange={this.onChange.bind(null, "description")}
                                />
                            </FormItem>
                        </FormList>
                    ) : null}
                </Modal>
            );
        } else {
            return null;
        }
    }
}

export default SqlWhereModal;
