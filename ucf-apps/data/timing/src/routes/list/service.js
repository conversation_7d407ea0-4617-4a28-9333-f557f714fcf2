import { getInvokeService, getServicePath } from "utils/service";

/**
 * 获取数据交换定义列表
 * @param {Object} data
 * @param {String|Number} data.pageNo
 * @param {String|Number} data.pageSize
 * @return {*}
 */
export const timingTaskListService = function (data, header) {
    return getInvokeService(
        {
            method: "GET",
            path: "/mygwapp/schedule/page",
            header,
        },
        data
    );
};

/**
 * 保存定时任务信息
 * @param {Object} data
 * @param {String} data.id -timing.id
 * @param {String} data.cronexpression -时间表达式
 * @param {String} data.description -描述
 * @return {*}
 */
export const saveTimingTaskService = function (data, header) {
    return getInvokeService(
        {
            method: "POST",
            path: "/mygwapp/schedule/save",
            header,
        },
        data
    );
};

/**
 * 删除定时任务
 * @param {String} id
 * @return {*}
 */
export const deleteTimingTaskService = function (id, header) {
    return getInvokeService({
        method: "POST",
        path: "/mygwapp/schedule/delete/" + id,
        header,
    });
};

/**
 * 立即执行定时任务
 * @param {String} id
 * @return {*}
 */
export const execTimingTaskService = function (id, header) {
    return getInvokeService({
        method: "POST",
        path: "/mygwapp/schedule/runonce/" + id,
        header,
    });
};

/**
 * 暂停定时任务
 * @param {String} id
 * @return {*}
 */
export const pauseTimingTaskService = function (id, header) {
    return getInvokeService({
        method: "POST",
        path: "/mygwapp/schedule/pause/" + id,
        header,
    });
};

/**
 * 恢复定时任务
 * @param {String} id
 * @return {*}
 */
export const resumeTimingTaskService = function (id, header) {
    return getInvokeService({
        method: "POST",
        path: "/mygwapp/schedule/resume/" + id,
        header,
    });
};
