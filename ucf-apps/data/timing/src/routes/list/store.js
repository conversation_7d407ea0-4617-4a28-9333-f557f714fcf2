import { observable, action, toJS, reaction, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { defaultListMap } from "utils/pageListUtils";
import * as ownerService from "./service";
import { autoServiceMessage } from "utils/service";
import commonText from "constants/commonText";
const initState = {
    dataSource: { ...defaultListMap },
    serviceCodeDiwork: "kfljsjtbrw",
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    getDataSource = async (reqData) => {
        let requestData = { ...reqData };
        this.getPagesListFunc({
            service: ownerService.timingTaskListService,
            requestData,
            dataKey: "dataSource",
            header: { serviceCode: this.state.serviceCodeDiwork },
        });
    };

    save = async (data) => {
        let res = await autoServiceMessage({
            service: ownerService.saveTimingTaskService(data, { serviceCode: this.state.serviceCodeDiwork }),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8E09804180028", "保存成功", undefined, {
                returnStr: true,
            }) /* "保存成功" */,
        });
        if (res) {
            this.getDataSource();
        }
        return res;
    };

    timingActions = async (action, record) => {
        let service,
            successMsg = "";
        switch (action) {
            case "delete":
                service = ownerService.deleteTimingTaskService;
                successMsg = lang.templateByUuid("UID:P_UBL-FE_18D8E09804180027", "已删除", undefined, {
                    returnStr: true,
                }) /* "已删除" */;
                break;
            case "exec":
                service = ownerService.execTimingTaskService;
                successMsg = lang.templateByUuid("UID:P_UBL-FE_18D8E09804180029", "运行成功", undefined, {
                    returnStr: true,
                }) /* "运行成功" */;
                break;
            case "pause":
                service = ownerService.pauseTimingTaskService;
                successMsg = lang.templateByUuid("UID:P_UBL-FE_18D8E09804180025", "暂停成功", undefined, {
                    returnStr: true,
                }) /* "暂停成功" */;
                break;
            case "resume":
                service = ownerService.resumeTimingTaskService;
                successMsg = lang.templateByUuid("UID:P_UBL-FE_18D8E09804180026", "恢复成功", undefined, {
                    returnStr: true,
                }) /* "恢复成功" */;
                break;
        }
        let res = await autoServiceMessage({
            service: service(record.id, { serviceCode: this.state.serviceCodeDiwork }),
            success: successMsg,
        });
        if (res) {
            this.getDataSource();
        }
    };
}

export const storeKey = "dataTimingListStore";

export default Store;
