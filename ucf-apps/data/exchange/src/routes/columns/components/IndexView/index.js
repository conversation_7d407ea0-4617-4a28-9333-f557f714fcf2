import React, { Component, Fragment, useMemo } from "react";
import { Header } from "components/PageView";
import Grid from "components/TinperBee/Grid";
import { getInfoError } from "decorator/index";
import { Button, FormControl, Select, InputNumber, Modal } from "components/TinperBee";
import withRouter from "decorator/withRouter";
import { Form, Message } from "@tinper/next-ui";
import { GridActions, GridAction } from "components/TinperBee/Grid/GridActions";
import FormulaModal from "../../../../../../../integrate/programme-config/src/routes/applicationInfo/routes/MappingRelation/components/FormulaModal";
import EditCell from "ucfapps/integrate/programme-config/src/routes/applicationInfo/routes/MappingRelation/components/IndexView/EditCell.js";
import styles from "./index.modules.css";
import { Space } from "@tinper/next-ui";
import ResizeObserver from "resize-observer-polyfill";
export const FormContext = React.createContext(null);

@withRouter
@getInfoError
class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            selectedColumns: [],
            showFormulaModal: false,
            recordIndex: "",
            recodeItem: "",
            currentIndex: -1,
            size: {},
        };
    }
    formRef = React.createRef();
    bodyRef = React.createRef();
    componentDidMount() {
        this.props.ownerStore.getSelectData();
        this.props.ownerStore.getDataSource();

        this.resizeObserver = new ResizeObserver((entries) => {
            entries.forEach((entry) => {
                const { clientWidth, clientHeight } = entry.target;
                this.setState({ size: { width: clientWidth, height: clientHeight } });
            });
        });
        this.resizeObserver.observe(this.bodyRef.current);
    }
    componentWillUnmount() {
        this.props.ownerStore.init();
        this.resizeObserver?.disconnect();
    }

    renderAction = (value, record, index) => {
        return (
            <GridActions>
                <GridAction fieldid="UCG-FE-routes-columns-components-IndexView-index-4688958-GridAction" onClick={this.handleDelete.bind(null, record)}>
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803A9", "删除") /* "删除" */}
                </GridAction>
            </GridActions>
        );
    };

    handleDelete = (delList) => {
        Modal.confirm({
            fieldid: "************",
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803AF", "确认要删除吗？") /* "确认要删除吗？" */,
            onOk: async () => {
                let res = await this.props.ownerStore.deleteColumns(delList);
                this.setState({ selectedColumns: [] });
            },
        });
    };

    handleChange = async (index, field, value) => {
        // this.props.ownerStore.changeColumnsValue(index, "param", "");
        if (field === "translatorclass") {
            this.props.ownerStore.changeColumnsValue(index, "param", "");
        }
        this.props.ownerStore.changeColumnsValue(index, field, value);
    };

    renderText = (field, required, placeholder) => {
        return (value, record, index) => {
            let {
                ownerState: { columnsData },
            } = this.props;
            let rules = [];
            if (field === "fieldmapdesc" || field === "dcode") {
                rules.push({
                    required: true,
                    message: lang.templateByUuid("UID:P_UBL-FE_1B7F4D900548000C", "值不能为空") /* "值不能为空" */,
                });
            }
            if (field === "scode" || field === "dcode") {
                rules.push({
                    validator: async (rule, value) => {
                        const anotherField = field === "scode" ? "dcode" : "scode";
                        const foundIndex = columnsData.findIndex(
                            (item, curIndex) => index !== curIndex && item[field] === value && item[anotherField] === record[anotherField]
                        );
                        if (foundIndex > -1) {
                            return Promise.reject(
                                <div
                                    style={{ cursor: "pointer" }}
                                    onMouseDown={() => {
                                        this.setState({ currentIndex: foundIndex });
                                        setTimeout(() => {
                                            this.setState({ currentIndex: -1 });
                                        }, 100);
                                    }}
                                >
                                    {lang.templateByUuid("UID:P_UBL-FE_1CF09E9604100007", "重复行：") /* "重复行：" */}
                                    <span>{foundIndex + 1}</span>
                                </div>
                            );
                        }
                        return Promise.resolve();
                    },
                });
            }
            return (
                <EditCell
                    editCellType="input"
                    dataIndex={[index, field]}
                    from="exchange"
                    required={["fieldmapdesc", "dcode"].includes(field)}
                    showClose
                    placeholder={placeholder}
                    value={value}
                    onChange={this.handleChange.bind(null, index, field)}
                    rules={rules}
                />
            );
        };
    };

    renderNumber = (field) => {
        return (value, record, index) => {
            let {
                ownerState: { errors },
            } = this.props;
            let error = this.getInfoError(errors, `${index}.${field}`);
            return (
                <EditCell
                    editCellType="inputNumber"
                    dataIndex={[index, field]}
                    from="exchange"
                    iconStyle="one"
                    min={0}
                    value={value}
                    className="u-input-number-xs"
                    onChange={this.handleChange.bind(null, index, field)}
                />
            );
        };
    };

    isNullTypes = [
        {
            value: "Y",
            key: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803A3", "是") /* "是" */,
        },
        {
            value: "N",
            key: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803A4", "否") /* "否" */,
        },
    ];
    handleFormulaShow = () => {
        this.setState({
            showFormulaModal: !this.state.showFormulaModal,
        });
    };
    handlePop = (record, index) => {
        console.log(record, index); //公式
        this.handleFormulaShow();
        this.setState({
            recordIndex: index,
            recodeItem: record,
        });
    };
    // 确认增加
    handleFormulaOK = async (formulaExpression, codeString) => {
        const { recordIndex, recodeItem } = this.state;
        let value = JSON.stringify({
            formula: formulaExpression,
            field: codeString,
        });
        this.handleChange(recordIndex, "param", value);
        this.handleFormulaShow();
    };
    renderSelect = (field) => {
        return (value, record, index) => {
            let { ownerState } = this.props;
            let selectData = null;
            let customStyle = null;
            switch (field) {
                case "dnull":
                    selectData = this.isNullTypes;
                    break;
                case "translatorclass":
                    selectData = ownerState.translators;
                    break;
                case "param":
                    let translatorclass = record.translatorclass;
                    customStyle = { width: "260px" };

                    if (translatorclass) {
                        if (
                            translatorclass.endsWith("RefTranslator") ||
                            translatorclass.endsWith("ErpToCloudTranslator") ||
                            translatorclass.endsWith("CategoryProcessTranslator") ||
                            translatorclass.endsWith("ErpCodeToCloudPkTranslator") ||
                            translatorclass.endsWith("U8SystemPrefixTranslator") ||
                            translatorclass.endsWith("U8OrgCodePrefixTranslator")
                        ) {
                            selectData = ownerState.refTypes;
                        } else if (translatorclass.endsWith("SubEntityTranslator")) {
                            selectData = ownerState.voChangeTypes;
                        } else if (translatorclass.endsWith("EnumTranslator")) {
                            selectData = ownerState.enumTypes;
                        } else if (translatorclass.endsWith("FormulaTranslator")) {
                            selectData = "formula";
                        }
                    }
                    break;
            }
            if (selectData && selectData != "formula") {
                let {
                    ownerState: { errors },
                } = this.props;
                let error = this.getInfoError(errors, `${index}.${field}`);
                return (
                    <EditCell
                        style={customStyle}
                        fieldid="ublinker-routes-columns-components-IndexView-index-7280294-Select"
                        editCellType="select"
                        dataIndex={[index, field]}
                        from="exchange"
                        displayText={selectData.find((item) => item.value === value)?.key}
                        optionFilterProp="children"
                        value={value}
                        onChange={this.handleChange.bind(null, index, field)}
                        allowClear
                        showSearch
                    >
                        {selectData.map((item, index) => {
                            return <Select.Option value={item.value}>{item.key}</Select.Option>;
                        })}
                    </EditCell>
                );
            } else if (selectData == "formula") {
                return (
                    <EditCell
                        fieldid="ublinker-routes-MappingRelation-components-IndexView-index-3713291-FormControl"
                        editCellType="input"
                        dataIndex={[index, field]}
                        from="exchange"
                        style={customStyle}
                        readOnly
                        value={record.param && JSON.parse(record.param).formula}
                        suffix={
                            <i
                                className="ipaas iPS-list-public-line"
                                onMouseDown={(e) => {
                                    this.handlePop(record, index);
                                    return false;
                                }}
                            />
                        }
                    />
                );
            } else {
                return "-";
            }
        };
    };

    handleCancel = () => {
        const {
            ownerState: { dataChanged },
            navigate,
        } = this.props;
        Header.handleBackConfirm({
            back: navigate("/list"),
            backConfirm: dataChanged,
        });
    };

    getSelectedDataFunc = (selectedList) => {
        this.setState({ selectedColumns: selectedList });
    };

    handleOk = async () => {
        this.formRef.current
            .validateFields()
            .then(() => this.props.ownerStore.save())
            .catch((err) => Message.error(lang.templateByUuid("UID:P_UBL-FE_1B7F4D900548000D", "数据校验不通过") /* "数据校验不通过" */));
    };

    columns = [
        {
            key: "fieldmapdesc",
            title: lang.templateByUuid("UID:P_UBL-FE_1B7F4D900548000E", "字段名称") /* "字段名称" */,
            dataIndex: "fieldmapdesc",
            render: this.renderText("fieldmapdesc", true),
            required: true,
        },
        {
            key: "scode",
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803B6", "来源字段") /* "来源字段" */,
            dataIndex: "scode",
            render: this.renderText("scode", true),
        },
        {
            key: "stype",
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803A5", "来源字段类型") /* "来源字段类型" */,
            dataIndex: "stype",
            render: this.renderText("stype", true),
        },
        {
            key: "dcode",
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803AE", "目标字段") /* "目标字段" */,
            dataIndex: "dcode",
            render: this.renderText("dcode", true),
            required: true,
        },
        {
            key: "dtype",
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803B2", "目标字段类型") /* "目标字段类型" */,
            dataIndex: "dtype",
            render: this.renderText("dtype", true),
        },
        {
            key: "dnull",
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803B4", "目标字段是否为空") /* "目标字段是否为空" */,
            dataIndex: "dnull",
            render: this.renderSelect("dnull"),
        },
        {
            key: "dlength",
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803B5", "目标字段长度") /* "目标字段长度" */,
            dataIndex: "dlength",
            render: this.renderNumber("dlength"),
        },

        {
            key: "defaultvalue",
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803A7", "默认值") /* "默认值" */,
            dataIndex: "defaultvalue",
            render: this.renderText("defaultvalue"),
        },
        {
            key: "translatorclass",
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418050C", "选择翻译器"),
            dataIndex: "translatorclass",
            render: this.renderSelect("translatorclass"),
            width: 300,
        },
        {
            key: "param",
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418050F", "值转换规则"),
            dataIndex: "param",
            render: this.renderSelect("param"),
            width: 350,
        },
    ];

    hoverContent = (record) => {
        return (
            <Fragment>
                <Button
                    {...Grid.hoverButtonPorps}
                    fieldid="UCG-FE-routes-columns-components-IndexView-index-4688958-GridAction"
                    onClick={this.handleDelete.bind(null, record)}
                >
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803A9", "删除") /* "删除" */}
                </Button>
            </Fragment>
        );
    };
    handleAddRow = () => {
        this.props.ownerStore?.addColumn();
        this.setState({ currentIndex: this.props.ownerState?.columnsData?.length - 1 || 0 });
    };
    handleDropRow = (data) => {
        this.props.ownerStore.setColumnsData(data);
    };
    handleNavIndex = () => {
        this.props.navigate({
            pathname: "/",
        });
    };

    handleBack = () => {
        this.props.navigate("/list");
    };

    render() {
        let { selectedColumns, showFormulaModal, recodeItem } = this.state;
        let { ownerState, ownerStore } = this.props;
        let { columnsData, dataChanged } = ownerState;
        return (
            <div className={styles["adaptation-wrapper"]}>
                <Header
                    back={this.handleBack}
                    backConfirm={dataChanged}
                    title={lang.templateByUuid("UID:P_UBL-FE_1C2564240440000E", "编辑映射配置") /* "编辑映射配置" */}
                >
                    <Space size={8}>
                        <Button fieldid="ublinker-routes-columns-components-IndexView-index-1129307-Button" onClick={this.handleAddRow}>
                            {lang.templateByUuid("UID:P_UBL-FE_1CCD561604500006", "添加") /* "添加" */}
                        </Button>
                        <Button
                            fieldid="ublinker-routes-columns-components-IndexView-index-4056471-Button"
                            bordered
                            disabled={selectedColumns.length <= 0}
                            onClick={this.handleDelete.bind(null, selectedColumns)}
                        >
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803A9", "删除") /* "删除" */}
                        </Button>
                    </Space>
                </Header>

                <div className={styles["adaptation-table"]} ref={this.bodyRef}>
                    <Form ref={this.formRef}>
                        <FormContext.Provider value={this.formRef}>
                            <Grid
                                bordered
                                cacheId="exchangeTransformTable"
                                fieldid="ublinker-routes-columns-components-IndexView-index-1702559-Grid"
                                multiSelect
                                selectedList={selectedColumns}
                                getSelectedDataFunc={this.getSelectedDataFunc}
                                autoCheckedByClickRows={false}
                                rowKey={"pk_id"}
                                columns={this.columns}
                                data={columnsData}
                                hoverContent={this.hoverContent}
                                currentIndex={this.state.currentIndex}
                                bigData={true}
                                rowDraggAble
                                useDragHandle
                                onDropRow={this.handleDropRow}
                                scroll={{ y: this.state.size.height - 31 }}
                                bodyStyle={{ minHeight: this.state.size.height - 31 }}
                            />
                        </FormContext.Provider>
                    </Form>
                </div>
                <div className={styles.footer}>
                    <Space size={8}>
                        <Button fieldid="ublinker-routes-columns-components-IndexView-index-9380950-Button" colors="secondary" onClick={this.handleCancel}>
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803A6", "取消") /* "取消" */}
                        </Button>
                        <Button fieldid="ublinker-routes-columns-components-IndexView-index-6407401-Button" colors="primary" onClick={this.handleOk}>
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041803AA", "保存") /* "保存" */}
                        </Button>
                    </Space>
                </div>
                {showFormulaModal ? (
                    <FormulaModal
                        show={showFormulaModal}
                        contextData={columnsData}
                        nowRecord={recodeItem}
                        showExpression={recodeItem.param && JSON.parse(recodeItem.param).formula}
                        onCancel={this.handleFormulaShow}
                        onOk={this.handleFormulaOK}
                        from={"exchange"}
                        // ownerStore={ownerStore}
                        // ownerState={ownerState}
                    />
                ) : (
                    ""
                )}
            </div>
        );
    }
}

export default IndexView;
