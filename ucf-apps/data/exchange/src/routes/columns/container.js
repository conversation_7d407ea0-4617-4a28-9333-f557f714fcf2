/**
 * 数据交换定义 列定义
 * location = window.location || reactHistory.location
 * location.search.dataExId -数据交换行ID dataEx.pk_id
 * location.search.dataSrc -数据交换行ID dataEx.frombilltype
 * location.search.dataTar -数据交换行ID dataEx.tobilltype
 * */
import React, { Component } from "react";
import IndexView from "./components/IndexView";
import { inject, observer } from "mobx-react";
import Store, { storeKey } from "./store";
import { storeKey as commonStoreKey } from "../store";
import core from "core";
import { getPageParams } from "decorator/index";

core.addStore({
    storeKey: storeKey,
    store: new Store(),
});

@inject((rootStore) => {
    let ownerStore = rootStore[storeKey];
    let commonStore = rootStore[commonStoreKey];
    return {
        commonState: commonStore.toJS(),
        ownerState: ownerStore.toJS(),
        ownerStore: ownerStore,
    };
})
@observer
@getPageParams
class Container extends Component {
    constructor(props) {
        super(props);
        props.ownerStore.setPageParams(this.props.getPageParams());
    }

    render() {
        return <IndexView {...this.props} />;
    }
}
export default Container;
