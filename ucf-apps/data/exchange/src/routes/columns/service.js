import { getInvokeService, getServicePath } from "utils/service";

/**
 * 获取数据交换列定义数据
 * @param dataExId -数据交换定义项 pk_id
 * @return {*}
 */
export const dataExchangeColumnsService = function (dataExId, header = {}) {
    return getInvokeService({
        method: "GET",
        path: "/mygwapp/erpdata/vochange/get/" + dataExId,
        header,
    });
};

/**
 * 获取翻译器列表
 * @param {Object} data
 * @param {Object} data.app_src -来源应用
 * @param {Object} data.app_des -目标应用
 * @return {*}
 */
export const translatorsService = function (data, header = {}) {
    return getInvokeService(
        {
            method: "GET",
            path: "/mygwapp/select2/translator",
            header,
        },
        data
    );
};

/**
 * 获取参照类型列表
 * @param {Object} data
 * @param {Object} data.app_src -来源应用
 * @param {Object} data.app_des -目标应用
 * @return {*}
 */

export const refsService = function (data, header = {}) {
    return getInvokeService(
        {
            method: "GET",
            path: "/mygwapp/select2/dataview/ref",
            header,
        },
        data
    );
};

/**
 * 获取子实体类型列表
 * @param {Object} data
 * @param {Object} data.app_src -来源应用
 * @param {Object} data.app_des -目标应用
 * @return {*}
 */
export const voChangesService = function (data, header = {}) {
    return getInvokeService(
        {
            method: "GET",
            path: "/mygwapp/select2/vochange",
            header,
        },
        data
    );
};

/**
 * 获取枚举类型列表
 * @param {Object} data
 * @param {Object} data.app_src -来源应用
 * @param {Object} data.app_des -目标应用
 * @return {*}
 */
export const enumsService = function (data, header = {}) {
    return getInvokeService(
        {
            method: "GET",
            path: "/mygwapp/select2/enum",
            header,
        },
        data
    );
};

/***
 * 删除列定义
 * @param {Object} data
 * @param {String} data.dataExId
 * @param {Array} data.ids
 * @return {*}
 */
export const deleteColumnsService = function (data, header = {}) {
    let { dataExId, ids } = data;
    return getInvokeService(
        {
            method: "POST",
            path: `/mygwapp/erpdata/vochange/${dataExId}/coldelete`,
            header,
        },
        ids
    );
};

/***
 * 保存列定义
 * @param {Object} data
 * @param {String} data.dataExId
 * @param {Array} data.columnsData
 * @return {*}
 */
export const updateColumnsService = function (data, header = {}) {
    let { dataExId, columnsData } = data;
    return getInvokeService(
        {
            method: "POST",
            path: `/mygwapp/erpdata/vochange/${dataExId}/colupdate`,
            header,
        },
        columnsData
    );
};
