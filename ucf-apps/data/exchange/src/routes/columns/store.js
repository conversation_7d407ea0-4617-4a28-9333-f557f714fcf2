import { observable, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import * as ownerService from "./service";
import { autoServiceMessage } from "utils/service";
import { codeReg, codeMessage } from "utils/regExp";
import { getUuid } from "utils/index";
import { Error } from "utils/feedback";
import commonText from "constants/commonText";

const initState = {
    columnsData: [],
    queryParams: {
        dataExId: "", //数据交换pk_id
        dataSrc: "", //来源单据
        dataTar: "", //目标单据
    },
    translators: [],
    refTypes: [],
    voChangeTypes: [],
    enumTypes: [],

    dataChanged: false,

    errors: [],
    serviceCodeDiwork: "kfljsjjhdy",
};

const initColumnData = {
    pk_id: "",
    fieldmapdesc: "",
    scode: "",
    stype: "String",
    dcode: "",
    dtype: "String",
    dnull: "N",
    dlength: "0",
    defaultvalue: "",
    translatorclass: "",
    param: "",
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;

    setPageParams = (paramInfo) => {
        this.changeState(paramInfo);
    };

    init = () => {
        this.state = initState;
    };

    setColumnsData = (data) => {
        this.changeState({ ...this.state, columnsData: data });
    };
    getSelectData = async () => {
        let { dataSrc, dataTar } = this.state.queryParams;
        let requestData = {
            app_src: dataSrc,
            app_des: dataTar,
        };
        let results = await Promise.all([
            ownerService.translatorsService(requestData, { serviceCode: this.state.serviceCodeDiwork }),
            ownerService.refsService(requestData, { serviceCode: this.state.serviceCodeDiwork }),
            ownerService.voChangesService(requestData, { serviceCode: this.state.serviceCodeDiwork }),
            ownerService.enumsService(requestData, { serviceCode: this.state.serviceCodeDiwork }),
        ]);
        let dataKeys = ["translators", "refTypes", "voChangeTypes", "enumTypes"];
        let _data = {};
        results.forEach((res, index) => {
            let dataList = res.data;
            let dataKey = dataKeys[index];
            _data[dataKey] = dataList.map((dataItem) => {
                return {
                    value: dataItem.id,
                    key: dataItem.text,
                };
            });
        });
        this.changeState(_data);
    };

    getDataSource = async () => {
        let { dataExId } = this.state.queryParams;
        await this.dispatchService({
            service: ownerService.dataExchangeColumnsService(dataExId, { serviceCode: this.state.serviceCodeDiwork }),
            dataKey: "columnsData",
        });
        // this.state.columnsData.forEach((item) => {
        //     item.scode = item.scode.replace(/\s/g, "");
        // });
        this.state.dataChanged = false;
    };

    addColumn = () => {
        let column = {
            ...initColumnData,
            pk_id: getUuid(),
            isNew: true,
        };
        this.state.dataChanged = true;
        this.state.columnsData.push(column);
    };

    changeColumnsValue = (index, field, value) => {
        this.state.dataChanged = true;
        this.state.columnsData[index][field] = typeof value === "undefined" ? "" : value;
    };

    deleteColumns = async (delList) => {
        let _delList = null;
        if (Array.isArray(delList)) {
            _delList = delList;
        } else {
            _delList = [delList];
        }

        let ids = [],
            newIds = [];

        //区分出要删除的项中 原有的数据 和 新增未保存数据
        //原有数据调用删除接口 新增未保存数据本地删除
        _delList.forEach((item, index) => {
            let { pk_id } = item;
            if (item.isNew) {
                newIds.push(pk_id);
            } else {
                ids.push(pk_id);
            }
        });

        let delRes;

        if (ids.length > 0) {
            let requestData = {
                dataExId: this.state.queryParams.dataExId,
                ids: ids,
            };
            delRes = await autoServiceMessage({
                service: ownerService.deleteColumnsService(requestData, { serviceCode: this.state.serviceCodeDiwork }),
                success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180548", "删除成功！", undefined, {
                    returnStr: true,
                }) /* "删除成功！" */,
            });
        } else {
            delRes = true;
        }

        if (delRes) {
            let delIds = [...ids, ...newIds];
            let columnsData = this.toJS(this.state.columnsData);
            this.state.columnsData = columnsData.filter((item) => {
                return !delIds.includes(item.pk_id);
            });
        }
        return delRes;
    };

    validateFields = ["fieldmapdesc", "dcode", "dtype"];

    validate = (columnsData) => {
        let errors = [];
        columnsData.forEach((source, index) => {
            this.validateFields.forEach((field) => {
                let value = source[field].trim();
                let errorField = `${index}.${field}`;
                if (value === "") {
                    errors.push({
                        field: errorField,
                        message: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180546", "此项不能为空") /* "此项不能为空" */,
                    });
                } else {
                    // if ((field === 'dcode' || field === 'scode') && !codeReg.test(value)) {
                    //   errors.push({
                    //     field: `${index}.${field}`,
                    //     message: "此项" + codeMessage
                    //   })
                    // }
                }
            });
        });
        return errors;
    };

    save = async () => {
        let columnsData = this.toJS(this.state.columnsData);
        let errors = this.validate(columnsData);
        this.state.errors = errors;
        if (errors.length > 0) {
            Error(
                lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180549", "数据填写错误", undefined, {
                    returnStr: true,
                }) /* "数据填写错误" */
            );
        } else {
            columnsData.forEach((item) => {
                if (item.isNew) {
                    delete item.pk_id;
                    delete item.isNew;
                }
            });
            let res = await autoServiceMessage({
                service: ownerService.updateColumnsService(
                    {
                        dataExId: this.state.queryParams.dataExId,
                        columnsData,
                    },
                    { serviceCode: this.state.serviceCodeDiwork }
                ),
                success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180547", "保存成功", undefined, {
                    returnStr: true,
                }) /* "保存成功" */,
            });

            if (res) {
                this.getDataSource();
            }
        }
    };
}

export const storeKey = "dataExchangeColumnsStore";

export default Store;
