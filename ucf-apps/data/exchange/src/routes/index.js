import React, { createContext, useState } from "react";
import { RoutesRender } from "core";

import ListContainer from "./list/container";
import ColumnsContainer from "./columns/container";

import getConfigProvider from "components/ConfigProvider";
import config from "../config";

import CommonStore, { storeKey as commonStoreKey } from "./store";
import core from "core";

const commonStore = new CommonStore();
core.addStore({
    storeKey: commonStoreKey,
    store: commonStore,
});

const ConfigProvider = getConfigProvider(config);

const initQuery = { key: "" };

export const QueryContext = createContext({
    query: initQuery,
    updateQuery: () => {},
});

const routes = [
    {
        path: "/",
        exact: true,
        component: ListContainer,
    },
    {
        path: "/list",
        component: ListContainer,
    },
    {
        path: "/columns",
        component: ColumnsContainer,
    },
];

const Routes = () => {
    const [query, setQuery] = useState(initQuery);

    const updateQuery = (newQuery) => {
        setQuery(newQuery);
    };
    return (
        <QueryContext.Provider
            value={{
                query,
                updateQuery,
                initQuery,
            }}
        >
            <ConfigProvider fieldid="UCG-FE-data-exchange-src-routes-index-233483-ConfigProvider">
                <RoutesRender routes={routes} />
            </ConfigProvider>
        </QueryContext.Provider>
    );
};
export default Routes;
