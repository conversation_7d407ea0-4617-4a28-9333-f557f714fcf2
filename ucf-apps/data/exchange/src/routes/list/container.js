import React, { Component } from "react";
import IndexView from "./components/IndexView";
import { inject, observer } from "mobx-react";
import Store, { storeKey } from "./store";
import { storeKey as commonStoreKey } from "../store";
import core from "core";
import { QueryContext } from "../index.js";
core.addStore({
    storeKey: storeKey,
    store: new Store(),
});

@inject((rootStore) => {
    console.log("sssssssssss");
    console.log(rootStore);
    let ownerStore = rootStore[storeKey];
    let commonStore = rootStore[commonStoreKey];
    return {
        commonState: commonStore.toJS(),
        ownerState: ownerStore.toJS(),
        ownerStore: ownerStore,
        serviceCodeDiwork: rootStore.serviceCodeDiwork,
    };
})
@observer
class Container extends Component {
    constructor(props) {
        super(props);
        props.ownerStore.setServiceCodeDiwork(props.serviceCodeDiwork);
    }

    render() {
        return (
            <QueryContext.Consumer>
                {(context) => {
                    return <IndexView {...this.props} queryContext={context} />;
                }}
            </QueryContext.Consumer>
        );
    }
}
export default Container;
