/*
 * @Author: your name
 * @Date: 2021-04-13 15:18:36
 * @LastEditTime: 2021-06-22 20:19:57
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \ublinker-fe\ucf-apps\data\exchange\src\routes\list\components\Modal\addModal.js
 */

import React, { useEffect } from "react";
import Modal from "components/TinperBee/Modal";
import { FormControl, Select, FormList } from "components/TinperBee";
const FormItem = FormList.Item;
import "./index.css";
const AddModal = (props) => {
    const [form] = FormList.useForm();
    const { showModal, save, close, record, ymsMicro } = props;

    useEffect(() => {
        if (record) {
            setTimeout(() => {
                form.setFieldsValue(record);
            });
        }
    }, [record]);
    const handleSave = () => {
        form.validateFields()
            .then((values) => {
                values.dataversion = "0";
                values.frombilltype = "ublinker";
                values.tobilltype = "ublinker";
                save({ ...record, _id: record.pk_id, ...values });
            })
            .catch((errorInfo) => {
                console.log(errorInfo);
            });
    };

    const formItemLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 14 },
    };
    return (
        <Modal
            fieldid="ublinker-routes-list-components-Modal-addModal-2276450-Modal"
            show={showModal}
            backdropClosable={false}
            title={
                JSON.stringify(record) == "{}"
                    ? lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041800E1", "新增") /* "新增" */
                    : lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041800E2", "编辑") /* "编辑" */
            }
            onCancel={close}
            onOk={handleSave}
            size={"md"}
        >
            <FormList
                fieldid="ublinker-routes-list-components-Modal-addModal-6355180-FormList"
                layoutOpt={{ md: 12 }}
                form={form}
                {...formItemLayout}
                name="form122"
                className="config-action-form"
            >
                <FormItem
                    fieldid="ublinker-routes-list-components-Modal-addModal-8114997-FormItem"
                    label={lang.templateByUuid("UID:P_UBL-FE_1C3D24B605200009", "编码")}
                    name="billtype"
                    rules={[
                        {
                            required: true,
                            message: lang.templateByUuid("UID:P_UBL-FE_1C3D24B605200008", "请输入"),
                        },
                    ]}
                >
                    <FormControl placeholder={lang.templateByUuid("UID:P_UBL-FE_1C3D24B605200008", "请输入")} maxLength="128" />
                </FormItem>

                <FormItem
                    fieldid="ublinker-routes-list-components-Modal-addModal-999645-FormItem"
                    label={lang.templateByUuid("UID:P_UBL-FE_1C3D24B60520000A", "名称")}
                    name="note"
                >
                    <FormControl
                        placeholder={lang.templateByUuid("UID:P_UBL-FE_1C3D24B605200008", "请输入")}
                        fieldid="ublinker-routes-list-components-Modal-addModal-5015739-FormControl"
                    />
                </FormItem>
                {JSON.parse(localStorage.getItem("ublinkerEnv") || "{}").env === "test" ? (
                    <FormItem
                        label={lang.templateByUuid("UID:P_UBL-FE_20076E5804280033", "YMS微服务") /* "YMS微服务" */}
                        name="microServiceCode"
                        validateTrigger="onChange"
                    >
                        <Select
                            showSearch
                            allowClear
                            optionFilterProp="children"
                            placeholder={lang.templateByUuid("UID:P_UBL-FE_20076E5804280034", "请选择") /* "请选择" */}
                        >
                            {Object.entries(ymsMicro || {}).map(([key, value]) => (
                                <option key={value} value={value}>
                                    {key}
                                </option>
                            ))}
                        </Select>
                    </FormItem>
                ) : null}
            </FormList>
        </Modal>
    );
};
export default AddModal;
