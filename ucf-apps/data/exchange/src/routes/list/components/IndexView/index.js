import React, { Component, Fragment } from "react";
import query from "query-string";
import Grid from "components/TinperBee/Grid";
import withRouter from "decorator/withRouter";
import "./index.less";
import { Button, Icon } from "components/TinperBee";
import AddModal from "../Modal/addModal";
import ResizeObserver from "resize-observer-polyfill";
import styles from "./index.modules.css";
import { SearchForm } from "tne-tinpernextpro-fe";
import { Input, Space } from "@tinper/next-ui";

const Item = SearchForm.Item;
const formItemLayout = {
    labelCol: {
        xs: { span: 8 },
        sm: { span: 8 },
    },
    wrapperCol: {
        xs: { span: 16 },
        sm: { span: 16 },
    },
};
@withRouter
class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            hoverData: null,
            showModal: false,
            record: {},
            size: {},
            searchKey: "",
        };
    }
    bodyRef = React.createRef();
    async componentDidMount() {
        const { getDataSource, getYmsMicro } = this.props.ownerStore;
        const searchKey = this.props.queryContext?.query?.key || "";
        this.setState({ searchKey });
        getDataSource({ key: searchKey, pageNo: 1 });
        getYmsMicro();

        this.resizeObserver = new ResizeObserver((entries) => {
            entries.forEach((entry) => {
                const { clientWidth, clientHeight } = entry.target;
                this.setState({ size: { width: clientWidth, height: clientHeight } });
            });
        });
        this.resizeObserver.observe(this.bodyRef.current);
    }
    componentWillUnmount() {
        this.resizeObserver?.disconnect();
    }
    appRender = (value, record) => {
        let {
            commonState: { appMap },
        } = this.props;
        return value ? appMap[value] || value : "-";
    };

    navToColumns = (record) => {
        this.props.navigate({
            pathname: "/columns",
            search:
                "?" +
                query.stringify({
                    dataExId: record.pk_id,
                    dataSrc: record.frombilltype,
                    dataTar: record.tobilltype,
                }),
        });
    };

    hoverContent = (record) => {
        return (
            <Fragment>
                <Button
                    fieldid="ublinker-routes-list-components-IndexView-index-3173125-Button"
                    {...Grid.hoverButtonPorps}
                    onClick={this.navToColumns.bind(null, record)}
                >
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418053F", "映射配置") /* "映射配置" */}
                </Button>
                {record.tenantid ? (
                    <Button
                        fieldid="ublinker-routes-list-components-IndexView-index-8810421-Button"
                        {...Grid.hoverButtonPorps}
                        onClick={this.handleEdit.bind(null, record)}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180544", "编辑") /* "编辑" */}
                    </Button>
                ) : null}
            </Fragment>
        );
    };

    handleSearch = (value) => {
        let { ownerStore, queryContext } = this.props;
        queryContext.updateQuery({ key: value });
        ownerStore.getDataSource({ key: value, pageNo: 1 });
    };
    handleAdd = async () => {
        this.setState({
            showModal: true,
            record: {},
        });
    };
    handleEdit = (record) => {
        this.setState({
            showModal: true,
            record,
        });
    };
    columns = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_1C3D237E05200009", "编码"),
            dataIndex: "billtype",
            width: 240,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_1C3D237E0520000A", "名称"),
            dataIndex: "note",
            width: 350,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180545", "来源单据") /* "来源单据" */,
            dataIndex: "frombilltype",
            width: 120,
            render: this.appRender,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418053E", "目标单据") /* "目标单据" */,
            dataIndex: "tobilltype",
            width: 120,
            render: this.appRender,
        },
    ];

    close = () => {
        this.setState({
            showModal: false,
        });
    };
    save = async (param) => {
        let { addExchangeListService, getDataSource } = this.props.ownerStore;
        const res = await addExchangeListService(param, this.close);
        if (res) {
            getDataSource({ key: this.props.queryContext?.query?.key || "", pageNo: 1 });
        }
    };
    render() {
        let { ownerState } = this.props;
        let { dataSource, pagination, ymsMicro } = ownerState;
        let { showModal, record } = this.state;
        return (
            <div className={styles["exchange-list"]}>
                <div style={{ display: "flex", justifyContent: "flex-end", padding: "10px", background: "#fff" }}>
                    <Space size={8}>
                        <Input
                            fieldid="e7fbe95d-9b49-40f2-acc3-f8976f223af3"
                            placeholder={lang.templateByUuid("UID:P_UBL-FE_1C3D237E0520000B", "请输入编码/名称")}
                            type="search"
                            allowClear
                            value={this.state.searchKey}
                            onChange={(value) => this.setState({ searchKey: value })}
                            onSearch={this.handleSearch}
                            style={{ width: "300px" }}
                        />
                        <Button fieldid="ublinker-routes-list-components-IndexView-index-66836-Button" colors="primary" onClick={this.handleAdd}>
                            <Icon fieldid="ublinker-routes-list-components-IndexView-index-5972991-Icon" type="uf-plus" />
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF604180543", "新增") /* "新增" */}
                        </Button>
                    </Space>
                </div>

                <div className={styles["exchange-table"]} ref={this.bodyRef}>
                    <Grid
                        fieldid="ublinker-routes-list-components-IndexView-index-1249628-Grid"
                        rowKey={"pk_id"}
                        columnFilterAble
                        columns={this.columns}
                        data={dataSource.list}
                        pagination={pagination}
                        hoverContent={this.hoverContent}
                        scroll={{ y: this.state.size.height - 62 }}
                        bodyStyle={{ minHeight: this.state.size.height - 62 }}
                    />
                </div>
                {showModal ? <AddModal showModal={showModal} close={this.close} save={this.save} record={record} ymsMicro={ymsMicro} /> : ""}
            </div>
        );
    }
}

export default IndexView;
