/*
 * @Author: your name
 * @Date: 2021-04-08 14:13:25
 * @LastEditTime: 2021-04-23 11:25:00
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \ublinker-fe\ucf-apps\data\exchange\src\routes\list\store.js
 */
import { observable, action, toJS, reaction, makeObservable } from "mobx";
import DefaultStore from "utils/defaultStore";
import { defaultListMap } from "utils/pageListUtils";
import { autoServiceMessage } from "utils/service";
import * as ownerService from "./service";
const initState = {
    dataSource: { ...defaultListMap },
    searchKey: "",
    serviceCodeDiwork: "kfljsjjhdy",
    ymsMicro: {},
};

class Store extends DefaultStore {
    constructor(props) {
        super(props);
        makeObservable(this);
    }
    @observable state = initState;
    setServiceCodeDiwork = (str = "kfljsjjhdy") => {
        this.state.serviceCodeDiwork = str;
    };
    getDataSource = async (reqData) => {
        let requestData = {
            ...reqData,
        };
        this.getPagesListFunc({
            service: ownerService.dataExchangeListService,
            requestData,
            dataKey: "dataSource",
            header: { serviceCode: this.state.serviceCodeDiwork },
        });
    };
    addExchangeListService = async (param, cb) => {
        let res = await autoServiceMessage({
            service: ownerService.addExchangeList(param, { serviceCode: this.state.serviceCodeDiwork }),
            success: lang.templateByUuid("UID:P_UBL-FE_18D8CEF60418002D", "操作成功", undefined, {
                returnStr: true,
            }) /* "操作成功" */,
        });

        if (res) {
            cb && cb();
            return res;
        }
    };
    // 获取YMS微服务列表
    getYmsMicro = async () => {
        let res = await autoServiceMessage({
            service: ownerService.getYmsMicroService(),
        });
        this.changeState({
            ymsMicro: res.data,
        });
    };
}

export const storeKey = "dataExchangeListStore";

export default Store;
