/*
 * @Author: your name
 * @Date: 2021-04-08 14:13:25
 * @LastEditTime: 2021-04-15 10:17:12
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \ublinker-fe\ucf-apps\data\exchange\src\routes\list\service.js
 */

import { getInvokeService, getServicePath, defineService } from "utils/service";

/**
 * 获取数据交换定义列表
 * @param {Object} data
 * @param {String|Number} data.pageNo
 * @param {String|Number} data.pageSize
 * @return {*}
 */
export const dataExchangeListService = function (data, header = {}) {
    return getInvokeService(
        {
            method: "GET",
            path: "/mygwapp/erpdata/vochange/page",
            header,
        },
        data
    );
};

export const addExchangeList = function (data, header = {}) {
    let service = defineService({
        method: "POST",
        path: "/gwmanage/gwportal/mygwapp/erpdata/vochange/add",
        header,
    });

    return service.invoke(data);
};
// 获取yms微服务
export const getYmsMicroService = function (header) {
    return getInvokeService({
        method: "GET",
        path: "/gwmanage/gwportal/diwork/msCode/listWithNoDomainPrefix",
        header,
    });
};
