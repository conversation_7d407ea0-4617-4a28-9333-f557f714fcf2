// mock ipaas-common
jest.mock(
    "iuap-ip-commonui-fe/hooks",
    () => {
        return {
            __esModule: true,
            useSize: () => ({ width: 0, height: 0 }),
        };
    },
    { virtual: true }
);

jest.mock(
    "iuap-ip-commonui-fe/TimeChoice",
    () => {
        return {
            __esModule: true,
            default: () => null,
        };
    },
    { virtual: true }
);

jest.mock(
    "iuap-ip-commonui-fe/htmldiff-js",
    () => {
        return {
            execute: () => "",
        };
    },
    { virtual: true }
);

jest.mock(
    "iuap-ip-commonui-fe/@antv/g6-react-node",
    () => {
        return {
            __esModule: true,
            Rect: () => null,
            Text: () => null,
            Image: () => null,
            Group: () => null,
            createNodeFromReact: () => null,
            appenAutoShapeListener: () => null,
        };
    },
    { virtual: true }
);

jest.mock(
    "iuap-ip-commonui-fe/jsonc-parser",
    () => {
        return {
            format: jest.fn((str) => str),
            applyEdits: jest.fn((str) => JSON.stringify(JSON.parse(str), null, 1)),
        };
    },
    { virtual: true }
);

jest.mock(
    "iuap-ip-commonui-fe",
    () => {
        return {
            SvgIconLib: {
                getIconComponent: () => null,
            },
        };
    },
    { virtual: true }
);

jest.mock("decorator/index", () => ({
    navTo: jest.fn((target) => (Component) => Component),
    withIframeSize: jest.fn((Component) => Component),
    getPageParams: jest.fn((Component) => Component),
}));

jest.mock("decorator/withRouter", () => {
    return jest.fn((Component) => Component);
});

jest.mock("resize-observer-polyfill", () => {
    return jest.fn().mockImplementation(() => ({
        observe: jest.fn(),
        unobserve: jest.fn(),
        disconnect: jest.fn(),
    }));
});
