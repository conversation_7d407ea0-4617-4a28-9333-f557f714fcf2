import React, { FC, PropsWith<PERSON><PERSON>dren, ReactElement } from "react";
import { render } from "@testing-library/react";
import { MemoryRouter as Router } from 'react-router';
import { Provider } from "mobx-react";
import { ConfigProvider } from "@tinper/next-ui";
import "@testing-library/dom";
import "@testing-library/jest-dom";
import userEvent from "@testing-library/user-event";

type Options = {
    store?: Record<string, any>;
};

const AllTheProviders = (options?: Options) => {
    return ({ children }: { children: React.ReactNode }) => {
        return (
            <Router>
                <Provider {...options?.store}>
                    <ConfigProvider>{children}</ConfigProvider>
                </Provider>
            </Router>
        );
    };
};

const customRender = (ui: ReactElement, options?: Options) => {
    return render(ui, { wrapper: AllTheProviders(options), ...options });
};

export * from "@testing-library/react";
export * from "@testing-library/user-event";

export { customRender as render };

export const pureRender = render;

export const user = userEvent.setup();
