
jest.mock("components/AsyncComponent/withMultiLang", () => {
    return {
        __esModule: true,
        default: (Component) => (props) => <Component {...props} />,
    };
});


jest.mock(
    'iuap-ip-commonui-fe/react-codemirror',
    () => {
        return {
            Controlled: () => null,
        };
    },
    { virtual: true },
);

jest.mock(
    'iuap-ip-commonui-fe/react-diff-viewer',
    () => {
        return <div />;
    },
    { virtual: true },
);

jest.mock(
    'iuap-ip-commonui-fe/cron-parser',
    () => {
        return {
            parseExpression: () => ({
                next: () => new Date(),
                prev: () => new Date(),
            }),
        };
    },
    { virtual: true },
);
