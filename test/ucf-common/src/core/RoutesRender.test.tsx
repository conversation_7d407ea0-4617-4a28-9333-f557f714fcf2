import RoutersRender from "ucf-common/src/core/RoutesRender";
import React from "react";
import { screen, render } from "@test/utils";

describe("RoutesRender", () => {
    it("should render without crashing", () => {
        const routes = [
            {
                path: "/",
                component: () => <div>Home</div>,
                componentProps: {},
            },
        ];
        const { container } = render(<RoutersRender routes={routes} />);
        expect(container).toBeDefined();
        expect(screen.getByText("Home")).toBeInTheDocument();
    });
});
