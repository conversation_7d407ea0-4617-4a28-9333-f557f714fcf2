import type { FC, PropsWithChildren, ReactElement } from "react";
import React from "react";
import { MemoryRouter as Router } from "react-router";
import { render } from "@testing-library/react";
import { Provider } from "mobx-react";
import { ConfigProvider } from "@tinper/next-ui";
import "@testing-library/dom";
import "@testing-library/jest-dom";
import userEvent from "@testing-library/user-event";

type Options = {
    store?: Record<string, any>;
};

const AllTheProviders: FC<PropsWithChildren<Options>> = ({ children, store }) => {
    return (
        <Router>
            <Provider {...store}>
                <ConfigProvider>{children}</ConfigProvider>
            </Provider>
        </Router>
    );
};

const customRender = (ui: ReactElement, options?: Options) => {
    return render(ui, { wrapper: AllTheProviders, ...options });
};

export * from "@testing-library/react";
export * from "@testing-library/user-event";

export { customRender as render };

export const pureRender = render;

export const user = userEvent.setup();

export * as uiSelect from "./select";
