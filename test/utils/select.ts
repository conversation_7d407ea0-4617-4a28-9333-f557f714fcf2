import { fireEvent, screen, act } from "@testing-library/react";

export const open = async (select: HTMLElement) => {
    await act(async () => {
        fireEvent.click(select.querySelector(".wui-select-selector"));
    });
};

export const change = async (select: HTMLElement, index: number) => {
    await open(select);
    await act(async () => {
        fireEvent.click(await screen.findByTestId(`${select.getAttribute("fieldid")}_option_${index}`));
    });
};

export const input = async (select: HTMLElement, value: string) => {
    await act(async () => {
        fireEvent.change(select.querySelector(".wui-select-selector input"), { target: { value } });
    });
};

export const clear = async (select: HTMLElement) => {
    await act(async () => {
        fireEvent.mouseDown(select.querySelector(".wui-select-clear"));
    });
};
