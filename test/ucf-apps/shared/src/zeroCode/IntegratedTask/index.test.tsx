import React from "react";
import IntegratedTask from "ucf-apps/shared/src/zeroCode/IntegratedTask";
import { pureRender, screen } from "@test/utils";
import Routers from "ucf-apps/data/sync-task/src/routes";

jest.mock("ucf-apps/data/sync-task/src/routes", () => {
    return {
        __esModule: true,
        default: jest.fn(() => <div>Routes</div>),
    };
});

describe("IntegratedTask", () => {
    it("renders without crashing", async () => {
        pureRender(<IntegratedTask source={3} parentId="2278889014312304648" isRoot />);
        const dom = await screen.findByText("Routes");
        expect(dom).toBeInTheDocument();
        expect(Routers).toHaveBeenCalledWith(
            {
                otherSource: {
                    isRoot: true,
                    parentId: "2278889014312304648",
                    source: 3,
                },
            },
            {}
        );
    });
});
