// store 测试
import React from "react";
import { screen, fireEvent, render, waitFor, act } from "@test/utils";
import Store from "@data/map-relations/src/routes/list/store.js";
import * as ownerService from "@data/map-relations/src/routes/list/service";
import { autoServiceMessage } from "utils/service";

// 模拟依赖模块
jest.mock("@data/map-relations/src/routes/list/service", () => ({
  getList: jest.fn(),
  updateDataTypeService: jest.fn()
}));

jest.mock("utils/service", () => ({
  autoServiceMessage: jest.fn()
}));

describe("MapRelations List Store 测试", () => {
  let store;

  beforeEach(() => {
    // 重置所有模拟
    jest.clearAllMocks();
    // 创建新的 store 实例
    store = new Store();
  });

  // 测试构造函数
  it("constructor 方法应该正确初始化 store", () => {
    // 验证 state 是否正确初始化
    expect(store.state).toBeDefined();
    expect(store.state.dataSource).toBeDefined();
    expect(store.state.selectedData).toEqual({});
  });

  // 测试 setSelectedData 方法
  it("setSelectedData 方法应该正确更新 selectedData", () => {
    // 准备测试数据
    const testData = { id: 1, name: "测试数据" };
    
    // 调用被测试方法
    store.setSelectedData(testData);
    
    // 验证结果
    expect(store.state.selectedData).toEqual(testData);
  });

  // 测试 getDataSource 方法
  it("getDataSource 方法应该调用 getPagesListFunc 方法", async () => {
    // 模拟 getPagesListFunc 方法
    store.getPagesListFunc = jest.fn();
    
    // 准备测试数据
    const requestData = { page: 1, pageSize: 10 };
    
    // 调用被测试方法
    await store.getDataSource(requestData);
    
    // 验证结果
    expect(store.getPagesListFunc).toHaveBeenCalledWith({
      service: ownerService.getList,
      requestData,
      dataKey: "dataSource"
    });
  });

  // 测试 updateDataType 方法
  it("updateDataType 方法应该调用服务并在成功时刷新数据", async () => {
    // 模拟 autoServiceMessage 返回成功
    (autoServiceMessage as jest.Mock).mockResolvedValue(true);
    
    // 模拟 getDataSource 方法
    store.getDataSource = jest.fn();
    
    // 准备测试数据
    const updateData = { id: 1, type: "新类型" };
    const callback = jest.fn();
    
    // 调用被测试方法
    await store.updateDataType(updateData, callback);
    
    // 验证结果
    expect(ownerService.updateDataTypeService).toHaveBeenCalledWith(updateData);
    expect(autoServiceMessage).toHaveBeenCalled();
    expect(callback).toHaveBeenCalled();
    expect(store.getDataSource).toHaveBeenCalled();
  });

  it("updateDataType 方法在服务失败时不应调用回调和刷新数据", async () => {
    // 模拟 autoServiceMessage 返回失败
    (autoServiceMessage as jest.Mock).mockResolvedValue(false);
    
    // 模拟 getDataSource 方法
    store.getDataSource = jest.fn();
    
    // 准备测试数据
    const updateData = { id: 1, type: "新类型" };
    const callback = jest.fn();
    
    // 调用被测试方法
    await store.updateDataType(updateData, callback);
    
    // 验证结果
    expect(ownerService.updateDataTypeService).toHaveBeenCalledWith(updateData);
    expect(autoServiceMessage).toHaveBeenCalled();
    expect(callback).not.toHaveBeenCalled();
    expect(store.getDataSource).not.toHaveBeenCalled();
  });
});
