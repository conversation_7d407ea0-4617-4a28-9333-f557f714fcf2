import React from "react";
import { screen, fireEvent, render, waitFor, act } from "@test/utils";
import IndexView from "@data/map-relations/src/routes/list/components/IndexView/index.js";
import OwnerStore from "@data/map-relations/src/routes/list/store.js";
import * as ownerService from "@data/map-relations/src/routes/list/service";

jest.mock("iuap-ip-commonui-fe", () => {
    return {
        PageTopHelp: jest.fn(({ children }) => <div>{children}</div>),
        PageLayout: jest.fn(({ children }) => <div>{children}</div>),
        StatusTag: jest.fn(({ children }) => <div>{children}</div>),
    };
});

jest.mock("@data/map-relations/src/routes/list/components/Modal/addModal", () => {
    return jest.fn(({ visible, onCancel, onOk, record }) => (
        <div fieldid="add-modal" style={{ display: visible ? "block" : "none" }}>
            <div fieldid="modal-content">{record && JSON.stringify(record)}</div>
            <button fieldid="modal-cancel" onClick={onCancel}>
                取消
            </button>
            <button fieldid="modal-save" onClick={() => onOk({ id: "test-id", name: "test-name" })}>
                保存
            </button>
        </div>
    ));
});

// 模拟数据
const mockDataSource = {
    list: [
        {
            id: "1",
            code: "MAP001",
            name: "测试映射1",
            description: "这是测试映射1的描述",
            creator: "admin",
            createTime: "2023-01-01 10:00:00",
            dataRecordTime: "2023-01-01 10:00:00",
        },
        {
            id: "2",
            code: "MAP002",
            name: "测试映射2",
            description: "这是测试映射2的描述",
            creator: "user",
            createTime: "2023-01-02 10:00:00",
            dataRecordTime: "2023-01-02 10:00:00",
        },
    ],
};

const mockPagination = {
    pageNo: 1,
    total: 10,
    pageSize: 10,
};

// 模拟服务
jest.mock("@data/map-relations/src/routes/list/service", () => ({
    getList: jest.fn(() => Promise.resolve({ data: mockDataSource })),
    addMapRelations: jest.fn(() => Promise.resolve({ data: { success: true } })),
    getYmsMicroService: jest.fn(() => Promise.resolve({ data: {} })),
    downloadExcelTempService: jest.fn(() => Promise.resolve({ data: "file-content" })),
}));

// 模拟工具函数
jest.mock("utils", () => ({
    downloadFile: jest.fn(),
}));

describe("MapRelations IndexView 组件测试", () => {
    const mockOwnerStore = new OwnerStore();
    mockOwnerStore.changeState({
        dataSource: mockDataSource,
        pagination: mockPagination,
    });

    beforeEach(() => {
        jest.clearAllMocks();
    });

    test("组件渲染", async () => {
        render(<IndexView ownerStore={mockOwnerStore} ownerState={mockOwnerStore.state} />);

        await waitFor(() => expect(ownerService.getList).toHaveBeenCalled());
    });
});
