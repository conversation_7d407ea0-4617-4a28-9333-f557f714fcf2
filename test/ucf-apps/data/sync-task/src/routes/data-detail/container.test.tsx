import React from "react";
import { render, screen, waitFor } from "@test/utils";
import Container from "@data/sync-task/src/routes/data-detail/container.js";
import Store from "@data/sync-task/src/routes/data-detail/store.js";
import ListStore from "@data/sync-task/src/routes/list/store.js";
import mixCore from "core";
import * as ownerService from "@data/sync-task/src/routes/data-detail/service";

// 模拟 store 实例
const mockOwnerStore = new Store();
const mockListStore = new ListStore();
// 模拟 mixCore
jest.mock("core", () => ({
  addStore: jest.fn(),
  getStore: jest.fn(() => ({
    "dataSyncTaskDataDetailStore": mockOwnerStore,
   "dataSyncTaskListStore": mockListStore
  }))
}));

// 模拟 getPageParams 装饰器
jest.mock("decorator", () => ({
  getPageParams: (Component) => {
    Component.prototype.getPageParams = () => ({
      taskId: "test-task-id",
      taskName: "测试任务",
      dataTypeName: "测试数据类型"
    });
    return Component;
  }
}));

// 模拟 mobx-react
jest.mock("mobx-react", () => ({
    inject: (children) => {
        return function(Component) {
            return <Component {...{
                'dataSyncTaskDataDetailStore': { toJS: () => ({}) },
                'dataSyncTaskListStore': { toJS: () => ({}) }
              }}/>
        }
    },
//   inject: jest.fn((children) => (Component) => {
//     const mockOwnerStore = new Store();
//     const mockListStore = new ListStore();
//     return children({
//         'dataSyncTaskDataDetailStore': mockOwnerStore,
//         'dataSyncTaskListStore': mockListStore
//       },Component)
//   }),
  observer: jest.fn((Component) => {
    console.log("啊啊啊222==", Component);
    return <div />
  })
}));

// 模拟 IndexView 组件
jest.mock("@data/sync-task/src/routes/data-detail/components/IndexView", () => {
  return jest.fn((props) => (
    <div fieldid="index-view">
      <div fieldid="task-id">{props.ownerState.queryParams.taskId}</div>
      <div fieldid="task-name">{props.ownerState.queryParams.taskName}</div>
      <div fieldid="data-type-name">{props.ownerState.queryParams.dataTypeName}</div>
    </div>
  ));
});

describe("数据同步任务详情容器组件测试", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // 重置 store 状态
    mockOwnerStore.init();
  });

  test("组件渲染测试", async () => {
    render(<Container />);
    await waitFor(() => {
      expect(screen.getByTestId("index-view")).toBeInTheDocument();
    });
  });
});
