import Routers from "ucf-apps/data/sync-task/src/routes/index.js";
import { render, screen } from "@test/utils";
import React from "react";
import CommonStore, { storeKey as commonStoreKey } from "ucf-apps/data/sync-task/src/routes/store";

jest.mock("ucf-apps/data/sync-task/src/routes/list/container", () => {
    return {
        __esModule: true,
        default: jest.fn(() => <div>Sync Task List</div>),
    };
})

describe("Sync Task Routes", () => {
    let store;
    const renderComponent = () => {
        return render(<Routers />, {
            store: {
                [commonStoreKey]: store,
            },
        });
    };
    beforeEach(() => {
        store = new CommonStore();
    })
    it("renders the Sync Task page", () => {
        renderComponent();
        expect(screen.getByText("Sync Task List")).toBeInTheDocument();
    });
});