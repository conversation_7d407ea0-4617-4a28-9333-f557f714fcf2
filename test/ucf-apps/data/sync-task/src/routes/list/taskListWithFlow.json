{"status": 1, "data": {"itemCount": 801, "pageIndex": 1, "pageSize": 20, "pageCount": 41, "hasPre": false, "hasNext": true, "items": [{"pk_view": null, "gatewayid": "", "tenantid": "0000L6YQ8AVLFUZPXD0000", "nctime": 0, "lastsuctime": "1900-01-01 00:00:00", "updateSuctime": true, "lastexectime": null, "lastexecend": null, "taskstatus": 0, "expression": null, "sqlwhere": null, "creationtime": null, "dataversion": 0, "dataview": {"dataversion": -1, "dataenable": -1, "partialObject": false, "empty": false, "schemeCode": "flow1748331632865", "sqlview": null, "tableview": "flow1748331632865", "app_src": null, "appcode": null, "commondoccode": null, "datatype": {"typetag": "flow1748331632865", "typename": "新建流程2025-05-27 15:40:32"}}, "batchexectime": null, "increment": true, "rank": 0, "maxRank": 0, "treeIndex": 0, "exec": 0, "excluded": false, "timestamp": 0.0, "needRetry": false, "needFailWarningMail": false, "modifior": "默认用户", "modifiedTime": "2025-05-27 20:03:35", "traceId": null, "enable": true, "connectId": null, "fromType": 3, "conditionalConflict": false, "startSchemeId": "2277463377239343104", "startSchemeCode": "flow1748331632865", "startSchemeName": "新建流程2025-05-27 15:40:32", "isSavelog": 0, "errorType": null, "syncExecTypeInt": null, "startTypeInt": 11, "startTypeName": "HttpListener触发", "getDataWay": null, "pkIntegratedId": "2277463377239343104", "changeConnect": true, "backUpTime": null, "delete": false, "dataTypeTag": "flow1748331632865", "dataTypeName": "新建流程2025-05-27 15:40:32", "schemeCode": "flow1748331632865", "schemeName": null, "appSrc": null, "tableView": "flow1748331632865", "sqlView": null, "appCode": null, "commondoccode": null, "lastFailLogId": null, "fromBillType": null, "toBillType": null, "voChangeRegId": null, "taskParam": null, "integrateScheme": null, "jobName": "0000L6YQ8AVLFUZPXD0000|flow|2277463377239343104", "taskId": null, "pk_id": "2277598952188280836"}, {"pk_view": null, "gatewayid": "", "tenantid": "0000L6YQ8AVLFUZPXD0000", "nctime": 0, "lastsuctime": "1900-01-01 00:00:00", "updateSuctime": true, "lastexectime": null, "lastexecend": null, "taskstatus": 0, "expression": null, "sqlwhere": null, "creationtime": null, "dataversion": 0, "dataview": {"dataversion": -1, "dataenable": -1, "partialObject": false, "empty": false, "schemeCode": "flow1748309988294", "sqlview": null, "tableview": "flow1748309988294", "app_src": null, "appcode": null, "commondoccode": null, "datatype": {"typetag": "flow1748309988294", "typename": "新建流程2025-05-27 09:39:48"}}, "batchexectime": null, "increment": true, "rank": 0, "maxRank": 0, "treeIndex": 0, "exec": 0, "excluded": false, "timestamp": 0.0, "needRetry": false, "needFailWarningMail": false, "modifior": "默认用户", "modifiedTime": "2025-05-27 09:47:55", "traceId": null, "enable": true, "connectId": null, "fromType": 3, "conditionalConflict": false, "startSchemeId": "2277277456695033857", "startSchemeCode": "flow1748309988294", "startSchemeName": "新建流程2025-05-27 09:39:48", "isSavelog": 0, "errorType": null, "syncExecTypeInt": null, "startTypeInt": 11, "startTypeName": "HttpListener触发", "getDataWay": null, "pkIntegratedId": "2277277456695033857", "changeConnect": true, "backUpTime": null, "delete": false, "dataTypeTag": "flow1748309988294", "dataTypeName": "新建流程2025-05-27 09:39:48", "schemeCode": "flow1748309988294", "schemeName": null, "appSrc": null, "tableView": "flow1748309988294", "sqlView": null, "appCode": null, "commondoccode": null, "lastFailLogId": null, "fromBillType": null, "toBillType": null, "voChangeRegId": null, "taskParam": null, "integrateScheme": null, "jobName": "0000L6YQ8AVLFUZPXD0000|flow|2277277456695033857", "taskId": null, "pk_id": "2277281639975092229"}, {"pk_view": null, "gatewayid": "", "tenantid": "0000L6YQ8AVLFUZPXD0000", "nctime": 0, "lastsuctime": "1900-01-01 00:00:00", "updateSuctime": true, "lastexectime": null, "lastexecend": null, "taskstatus": 0, "expression": null, "sqlwhere": null, "creationtime": null, "dataversion": 0, "dataview": {"dataversion": -1, "dataenable": -1, "partialObject": false, "empty": false, "schemeCode": "flow1748260307537", "sqlview": null, "tableview": "flow1748260307537", "app_src": null, "appcode": null, "commondoccode": null, "datatype": {"typetag": "flow1748260307537", "typename": "xxmmdm"}}, "batchexectime": null, "increment": true, "rank": 0, "maxRank": 0, "treeIndex": 0, "exec": 0, "excluded": false, "timestamp": 0.0, "needRetry": false, "needFailWarningMail": false, "modifior": "默认用户", "modifiedTime": "2025-05-26 19:52:51", "traceId": null, "enable": true, "connectId": null, "fromType": 3, "conditionalConflict": false, "startSchemeId": "2276850700145655815", "startSchemeCode": "flow1748260307537", "startSchemeName": "xxmmdm", "isSavelog": 0, "errorType": null, "syncExecTypeInt": null, "startTypeInt": 11, "startTypeName": "HttpListener触发", "getDataWay": null, "pkIntegratedId": "2276850700145655815", "changeConnect": true, "backUpTime": null, "delete": false, "dataTypeTag": "flow1748260307537", "dataTypeName": "xxmmdm", "schemeCode": "flow1748260307537", "schemeName": null, "appSrc": null, "tableView": "flow1748260307537", "sqlView": null, "appCode": null, "commondoccode": null, "lastFailLogId": null, "fromBillType": null, "toBillType": null, "voChangeRegId": null, "taskParam": null, "integrateScheme": null, "jobName": "0000L6YQ8AVLFUZPXD0000|flow|2276850700145655815", "taskId": null, "pk_id": "2276851249892294665"}, {"pk_view": null, "gatewayid": "", "tenantid": "0000L6YQ8AVLFUZPXD0000", "nctime": 0, "lastsuctime": "1900-01-01 00:00:00", "updateSuctime": true, "lastexectime": null, "lastexecend": null, "taskstatus": 0, "expression": null, "sqlwhere": null, "creationtime": null, "dataversion": 0, "dataview": {"dataversion": -1, "dataenable": -1, "partialObject": false, "empty": false, "schemeCode": "flow1746683785799", "sqlview": null, "tableview": "flow1746683785799", "app_src": null, "appcode": null, "commondoccode": null, "datatype": {"typetag": "flow1746683785799", "typename": "guohhwtest"}}, "batchexectime": null, "increment": true, "rank": 0, "maxRank": 0, "treeIndex": 0, "exec": 0, "excluded": false, "timestamp": 0.0, "needRetry": false, "needFailWarningMail": false, "modifior": "默认用户", "modifiedTime": "2025-05-26 09:11:52", "traceId": null, "enable": true, "connectId": null, "fromType": 3, "conditionalConflict": false, "startSchemeId": "2263308479272845313", "startSchemeCode": "flow1746683785799", "startSchemeName": "guohhwtest", "isSavelog": 0, "errorType": null, "syncExecTypeInt": null, "startTypeInt": 11, "startTypeName": "HttpListener触发", "getDataWay": null, "pkIntegratedId": "2263308479272845313", "changeConnect": true, "backUpTime": null, "delete": false, "dataTypeTag": "flow1746683785799", "dataTypeName": "guohhwtest", "schemeCode": "flow1746683785799", "schemeName": null, "appSrc": null, "tableView": "flow1746683785799", "sqlView": null, "appCode": null, "commondoccode": null, "lastFailLogId": null, "fromBillType": null, "toBillType": null, "voChangeRegId": null, "taskParam": null, "integrateScheme": null, "jobName": "0000L6YQ8AVLFUZPXD0000|flow|2263308479272845313", "taskId": null, "pk_id": "2276520889597820932"}, {"pk_view": null, "gatewayid": "", "tenantid": "0000L6YQ8AVLFUZPXD0000", "nctime": 0, "lastsuctime": "1900-01-01 00:00:00", "updateSuctime": true, "lastexectime": null, "lastexecend": null, "taskstatus": 0, "expression": null, "sqlwhere": null, "creationtime": null, "dataversion": 0, "dataview": {"dataversion": -1, "dataenable": -1, "partialObject": false, "empty": false, "schemeCode": "flow1748017586440", "sqlview": null, "tableview": "flow1748017586440", "app_src": null, "appcode": null, "commondoccode": null, "datatype": {"typetag": "flow1748017586440", "typename": "映射关系组件测试验证-分页"}}, "batchexectime": null, "increment": true, "rank": 0, "maxRank": 0, "treeIndex": 0, "exec": 0, "excluded": false, "timestamp": 0.0, "needRetry": false, "needFailWarningMail": false, "modifior": "默认用户", "modifiedTime": "2025-05-24 00:41:36", "traceId": null, "enable": true, "connectId": null, "fromType": 3, "conditionalConflict": false, "startSchemeId": "2274765742621065220", "startSchemeCode": "flow1748017586440", "startSchemeName": "映射关系组件测试验证-分页", "isSavelog": 0, "errorType": null, "syncExecTypeInt": null, "startTypeInt": 10, "startTypeName": "API触发", "getDataWay": null, "pkIntegratedId": "2274765742621065220", "changeConnect": true, "backUpTime": null, "delete": false, "dataTypeTag": "flow1748017586440", "dataTypeName": "映射关系组件测试验证-分页", "schemeCode": "flow1748017586440", "schemeName": null, "appSrc": null, "tableView": "flow1748017586440", "sqlView": null, "appCode": null, "commondoccode": null, "lastFailLogId": null, "fromBillType": null, "toBillType": null, "voChangeRegId": null, "taskParam": null, "integrateScheme": null, "jobName": "0000L6YQ8AVLFUZPXD0000|flow|2274765742621065220", "taskId": null, "pk_id": "2274773559462854662"}, {"pk_view": null, "gatewayid": "", "tenantid": "0000L6YQ8AVLFUZPXD0000", "nctime": 0, "lastsuctime": "2025-05-23 21:26:42", "updateSuctime": true, "lastexectime": "2025-05-23 21:26:42", "lastexecend": "2025-05-23 21:26:43", "taskstatus": 2, "expression": null, "sqlwhere": null, "creationtime": null, "dataversion": 0, "dataview": {"dataversion": -1, "dataenable": -1, "partialObject": false, "empty": false, "schemeCode": "flow1747981422528", "sqlview": null, "tableview": "flow1747981422528", "app_src": null, "appcode": null, "commondoccode": null, "datatype": {"typetag": "flow1747981422528", "typename": "新建流程2025-05-23 14:23:42"}}, "batchexectime": null, "increment": true, "rank": 0, "maxRank": 0, "treeIndex": 0, "exec": 0, "excluded": false, "timestamp": 0.0, "needRetry": false, "needFailWarningMail": false, "modifior": "默认用户", "modifiedTime": "2025-05-23 14:25:39", "traceId": null, "enable": true, "connectId": null, "fromType": 3, "conditionalConflict": false, "startSchemeId": "2274455096226480129", "startSchemeCode": "flow1747981422528", "startSchemeName": "新建流程2025-05-23 14:23:42", "isSavelog": 0, "errorType": null, "syncExecTypeInt": null, "startTypeInt": 10, "startTypeName": "API触发", "getDataWay": null, "pkIntegratedId": "2274455096226480129", "changeConnect": true, "backUpTime": null, "delete": false, "dataTypeTag": "flow1747981422528", "dataTypeName": "新建流程2025-05-23 14:23:42", "schemeCode": "flow1747981422528", "schemeName": null, "appSrc": null, "tableView": "flow1747981422528", "sqlView": null, "appCode": null, "commondoccode": null, "lastFailLogId": null, "fromBillType": null, "toBillType": null, "voChangeRegId": null, "taskParam": null, "integrateScheme": null, "jobName": "0000L6YQ8AVLFUZPXD0000|flow|2274455096226480129", "taskId": null, "pk_id": "2274456101250138117"}, {"pk_view": null, "gatewayid": "", "tenantid": "0000L6YQ8AVLFUZPXD0000", "nctime": 0, "lastsuctime": "1900-01-01 00:00:00", "updateSuctime": true, "lastexectime": null, "lastexecend": null, "taskstatus": 0, "expression": null, "sqlwhere": null, "creationtime": null, "dataversion": 0, "dataview": {"dataversion": -1, "dataenable": -1, "partialObject": false, "empty": false, "schemeCode": "flow1747969118753", "sqlview": null, "tableview": "flow1747969118753", "app_src": null, "appcode": null, "commondoccode": null, "datatype": {"typetag": "flow1747969118753", "typename": "ccbobe_002"}}, "batchexectime": null, "increment": true, "rank": 0, "maxRank": 0, "treeIndex": 0, "exec": 0, "excluded": false, "timestamp": 0.0, "needRetry": false, "needFailWarningMail": false, "modifior": "默认用户", "modifiedTime": "2025-05-23 11:00:27", "traceId": null, "enable": true, "connectId": null, "fromType": 3, "conditionalConflict": false, "startSchemeId": "2274349405691707396", "startSchemeCode": "flow1747969118753", "startSchemeName": "ccbobe_002", "isSavelog": 0, "errorType": null, "syncExecTypeInt": null, "startTypeInt": 11, "startTypeName": "HttpListener触发", "getDataWay": null, "pkIntegratedId": "2274349405691707396", "changeConnect": true, "backUpTime": null, "delete": false, "dataTypeTag": "flow1747969118753", "dataTypeName": "ccbobe_002", "schemeCode": "flow1747969118753", "schemeName": null, "appSrc": null, "tableView": "flow1747969118753", "sqlView": null, "appCode": null, "commondoccode": null, "lastFailLogId": null, "fromBillType": null, "toBillType": null, "voChangeRegId": null, "taskParam": null, "integrateScheme": null, "jobName": "0000L6YQ8AVLFUZPXD0000|flow|2274349405691707396", "taskId": null, "pk_id": "2274350341983305735"}, {"pk_view": null, "gatewayid": "", "tenantid": "0000L6YQ8AVLFUZPXD0000", "nctime": 0, "lastsuctime": "1900-01-01 00:00:00", "updateSuctime": true, "lastexectime": null, "lastexecend": null, "taskstatus": 0, "expression": null, "sqlwhere": null, "creationtime": null, "dataversion": 0, "dataview": {"dataversion": -1, "dataenable": -1, "partialObject": false, "empty": false, "schemeCode": "flow1747897025162", "sqlview": null, "tableview": "flow1747897025162", "app_src": null, "appcode": null, "commondoccode": null, "datatype": {"typetag": "flow1747897025162", "typename": "新建流程2025-05-22 14:57:05"}}, "batchexectime": null, "increment": true, "rank": 0, "maxRank": 0, "treeIndex": 0, "exec": 0, "excluded": false, "timestamp": 0.0, "needRetry": false, "needFailWarningMail": false, "modifior": "默认用户", "modifiedTime": "2025-05-23 10:22:28", "traceId": null, "enable": true, "connectId": null, "fromType": 3, "conditionalConflict": false, "startSchemeId": "2273730131537166340", "startSchemeCode": "flow1747897025162", "startSchemeName": "新建流程2025-05-22 14:57:05", "isSavelog": 0, "errorType": null, "syncExecTypeInt": null, "startTypeInt": 11, "startTypeName": "HttpListener触发", "getDataWay": null, "pkIntegratedId": "2273730131537166340", "changeConnect": true, "backUpTime": null, "delete": false, "dataTypeTag": "flow1747897025162", "dataTypeName": "新建流程2025-05-22 14:57:05", "schemeCode": "flow1747897025162", "schemeName": null, "appSrc": null, "tableView": "flow1747897025162", "sqlView": null, "appCode": null, "commondoccode": null, "lastFailLogId": null, "fromBillType": null, "toBillType": null, "voChangeRegId": null, "taskParam": null, "integrateScheme": null, "jobName": "0000L6YQ8AVLFUZPXD0000|flow|2273730131537166340", "taskId": null, "pk_id": "2274330765522370560"}, {"pk_view": null, "gatewayid": "", "tenantid": "0000L6YQ8AVLFUZPXD0000", "nctime": 0, "lastsuctime": "1900-01-01 00:00:00", "updateSuctime": true, "lastexectime": null, "lastexecend": null, "taskstatus": 0, "expression": null, "sqlwhere": null, "creationtime": null, "dataversion": 0, "dataview": {"dataversion": -1, "dataenable": -1, "partialObject": false, "empty": false, "schemeCode": "flow1747914816957", "sqlview": null, "tableview": "flow1747914816957", "app_src": null, "appcode": null, "commondoccode": null, "datatype": {"typetag": "flow1747914816957", "typename": "新建流程2025-05-22 19:53:36"}}, "batchexectime": null, "increment": true, "rank": 0, "maxRank": 0, "treeIndex": 0, "exec": 0, "excluded": false, "timestamp": 0.0, "needRetry": false, "needFailWarningMail": false, "modifior": "默认用户", "modifiedTime": "2025-05-22 19:54:06", "traceId": null, "enable": true, "connectId": null, "fromType": 3, "conditionalConflict": false, "startSchemeId": "2273882955063492613", "startSchemeCode": "flow1747914816957", "startSchemeName": "新建流程2025-05-22 19:53:36", "isSavelog": 0, "errorType": null, "syncExecTypeInt": null, "startTypeInt": 10, "startTypeName": "API触发", "getDataWay": null, "pkIntegratedId": "2273882955063492613", "changeConnect": true, "backUpTime": null, "delete": false, "dataTypeTag": "flow1747914816957", "dataTypeName": "新建流程2025-05-22 19:53:36", "schemeCode": "flow1747914816957", "schemeName": null, "appSrc": null, "tableView": "flow1747914816957", "sqlView": null, "appCode": null, "commondoccode": null, "lastFailLogId": null, "fromBillType": null, "toBillType": null, "voChangeRegId": null, "taskParam": null, "integrateScheme": null, "jobName": "0000L6YQ8AVLFUZPXD0000|flow|2273882955063492613", "taskId": null, "pk_id": "2273883212750258186"}, {"pk_view": null, "gatewayid": "", "tenantid": "0000L6YQ8AVLFUZPXD0000", "nctime": 0, "lastsuctime": "2025-05-22 17:22:28", "updateSuctime": true, "lastexectime": "2025-05-22 17:22:28", "lastexecend": "2025-05-22 17:22:28", "taskstatus": 2, "expression": null, "sqlwhere": null, "creationtime": null, "dataversion": 0, "dataview": {"dataversion": -1, "dataenable": -1, "partialObject": false, "empty": false, "schemeCode": "flow1747905227165", "sqlview": null, "tableview": "flow1747905227165", "app_src": null, "appcode": null, "commondoccode": null, "datatype": {"typetag": "flow1747905227165", "typename": "新建流程2025-05-22 17:13:47"}}, "batchexectime": null, "increment": true, "rank": 0, "maxRank": 0, "treeIndex": 0, "exec": 0, "excluded": false, "timestamp": 0.0, "needRetry": false, "needFailWarningMail": false, "modifior": "默认用户", "modifiedTime": "2025-05-22 17:14:32", "traceId": null, "enable": true, "connectId": null, "fromType": 3, "conditionalConflict": false, "startSchemeId": "2273800586180689928", "startSchemeCode": "flow1747905227165", "startSchemeName": "新建流程2025-05-22 17:13:47", "isSavelog": 0, "errorType": null, "syncExecTypeInt": null, "startTypeInt": 10, "startTypeName": "API触发", "getDataWay": null, "pkIntegratedId": "2273800586180689928", "changeConnect": true, "backUpTime": null, "delete": false, "dataTypeTag": "flow1747905227165", "dataTypeName": "新建流程2025-05-22 17:13:47", "schemeCode": "flow1747905227165", "schemeName": null, "appSrc": null, "tableView": "flow1747905227165", "sqlView": null, "appCode": null, "commondoccode": null, "lastFailLogId": null, "fromBillType": null, "toBillType": null, "voChangeRegId": null, "taskParam": null, "integrateScheme": null, "jobName": "0000L6YQ8AVLFUZPXD0000|flow|2273800586180689928", "taskId": null, "pk_id": "2273800972722241536"}, {"pk_view": null, "gatewayid": "", "tenantid": "0000L6YQ8AVLFUZPXD0000", "nctime": 0, "lastsuctime": "2025-05-22 16:26:09", "updateSuctime": true, "lastexectime": "2025-05-22 16:26:09", "lastexecend": "2025-05-22 16:26:09", "taskstatus": 2, "expression": null, "sqlwhere": null, "creationtime": null, "dataversion": 0, "dataview": {"dataversion": -1, "dataenable": -1, "partialObject": false, "empty": false, "schemeCode": "flow1747902260398", "sqlview": null, "tableview": "flow1747902260398", "app_src": null, "appcode": null, "commondoccode": null, "datatype": {"typetag": "flow1747902260398", "typename": "新建流程2025-05-22 16:24:20"}}, "batchexectime": null, "increment": true, "rank": 0, "maxRank": 0, "treeIndex": 0, "exec": 0, "excluded": false, "timestamp": 0.0, "needRetry": false, "needFailWarningMail": false, "modifior": "默认用户", "modifiedTime": "2025-05-22 16:25:52", "traceId": null, "enable": true, "connectId": null, "fromType": 3, "conditionalConflict": false, "startSchemeId": "2273775099844755462", "startSchemeCode": "flow1747902260398", "startSchemeName": "新建流程2025-05-22 16:24:20", "isSavelog": 0, "errorType": null, "syncExecTypeInt": null, "startTypeInt": 10, "startTypeName": "API触发", "getDataWay": null, "pkIntegratedId": "2273775099844755462", "changeConnect": true, "backUpTime": null, "delete": false, "dataTypeTag": "flow1747902260398", "dataTypeName": "新建流程2025-05-22 16:24:20", "schemeCode": "flow1747902260398", "schemeName": null, "appSrc": null, "tableView": "flow1747902260398", "sqlView": null, "appCode": null, "commondoccode": null, "lastFailLogId": null, "fromBillType": null, "toBillType": null, "voChangeRegId": null, "taskParam": null, "integrateScheme": null, "jobName": "0000L6YQ8AVLFUZPXD0000|flow|2273775099844755462", "taskId": null, "pk_id": "2273775890113232902"}, {"pk_view": null, "gatewayid": "", "tenantid": "0000L6YQ8AVLFUZPXD0000", "nctime": 0, "lastsuctime": "2025-05-22 16:20:50", "updateSuctime": true, "lastexectime": "2025-05-22 16:20:50", "lastexecend": "2025-05-22 16:20:50", "taskstatus": 2, "expression": null, "sqlwhere": null, "creationtime": null, "dataversion": 0, "dataview": {"dataversion": -1, "dataenable": -1, "partialObject": false, "empty": false, "schemeCode": "flow1747901968992", "sqlview": null, "tableview": "flow1747901968992", "app_src": null, "appcode": null, "commondoccode": null, "datatype": {"typetag": "flow1747901968992", "typename": "新建流程2025-05-22 16:19:28"}}, "batchexectime": null, "increment": true, "rank": 0, "maxRank": 0, "treeIndex": 0, "exec": 0, "excluded": false, "timestamp": 0.0, "needRetry": false, "needFailWarningMail": false, "modifior": "默认用户", "modifiedTime": "2025-05-22 16:20:15", "traceId": null, "enable": true, "connectId": null, "fromType": 3, "conditionalConflict": false, "startSchemeId": "2273772591583854592", "startSchemeCode": "flow1747901968992", "startSchemeName": "新建流程2025-05-22 16:19:28", "isSavelog": 0, "errorType": null, "syncExecTypeInt": null, "startTypeInt": 10, "startTypeName": "API触发", "getDataWay": null, "pkIntegratedId": "2273772591583854592", "changeConnect": true, "backUpTime": null, "delete": false, "dataTypeTag": "flow1747901968992", "dataTypeName": "新建流程2025-05-22 16:19:28", "schemeCode": "flow1747901968992", "schemeName": null, "appSrc": null, "tableView": "flow1747901968992", "sqlView": null, "appCode": null, "commondoccode": null, "lastFailLogId": null, "fromBillType": null, "toBillType": null, "voChangeRegId": null, "taskParam": null, "integrateScheme": null, "jobName": "0000L6YQ8AVLFUZPXD0000|flow|2273772591583854592", "taskId": null, "pk_id": "2273772995305275393"}, {"pk_view": null, "gatewayid": "", "tenantid": "0000L6YQ8AVLFUZPXD0000", "nctime": 0, "lastsuctime": "1900-01-01 00:00:00", "updateSuctime": true, "lastexectime": null, "lastexecend": null, "taskstatus": 0, "expression": null, "sqlwhere": null, "creationtime": null, "dataversion": 0, "dataview": {"dataversion": -1, "dataenable": -1, "partialObject": false, "empty": false, "schemeCode": "flow1747898452537", "sqlview": null, "tableview": "flow1747898452537", "app_src": null, "appcode": null, "commondoccode": null, "datatype": {"typetag": "flow1747898452537", "typename": "新建流程2025-05-22 15:20:52"}}, "batchexectime": null, "increment": true, "rank": 0, "maxRank": 0, "treeIndex": 0, "exec": 0, "excluded": false, "timestamp": 0.0, "needRetry": false, "needFailWarningMail": false, "modifior": "默认用户", "modifiedTime": "2025-05-22 15:24:45", "traceId": null, "enable": true, "connectId": null, "fromType": 3, "conditionalConflict": false, "startSchemeId": "2273742389373829120", "startSchemeCode": "flow1747898452537", "startSchemeName": "新建流程2025-05-22 15:20:52", "isSavelog": 0, "errorType": null, "syncExecTypeInt": null, "startTypeInt": 10, "startTypeName": "API触发", "getDataWay": null, "pkIntegratedId": "2273742389373829120", "changeConnect": true, "backUpTime": null, "delete": false, "dataTypeTag": "flow1747898452537", "dataTypeName": "新建流程2025-05-22 15:20:52", "schemeCode": "flow1747898452537", "schemeName": null, "appSrc": null, "tableView": "flow1747898452537", "sqlView": null, "appCode": null, "commondoccode": null, "lastFailLogId": null, "fromBillType": null, "toBillType": null, "voChangeRegId": null, "taskParam": null, "integrateScheme": null, "jobName": "0000L6YQ8AVLFUZPXD0000|flow|2273742389373829120", "taskId": null, "pk_id": "2273744390825181189"}, {"pk_view": null, "gatewayid": "", "tenantid": "0000L6YQ8AVLFUZPXD0000", "nctime": 0, "lastsuctime": "2025-05-22 14:47:36", "updateSuctime": true, "lastexectime": "2025-05-22 14:47:36", "lastexecend": "2025-05-22 14:47:36", "taskstatus": 2, "expression": null, "sqlwhere": null, "creationtime": null, "dataversion": 0, "dataview": {"dataversion": -1, "dataenable": -1, "partialObject": false, "empty": false, "schemeCode": "flow1747896365005", "sqlview": null, "tableview": "flow1747896365005", "app_src": null, "appcode": null, "commondoccode": null, "datatype": {"typetag": "flow1747896365005", "typename": "新建流程2025-05-22 14:46:05"}}, "batchexectime": null, "increment": true, "rank": 0, "maxRank": 0, "treeIndex": 0, "exec": 0, "excluded": false, "timestamp": 0.0, "needRetry": false, "needFailWarningMail": false, "modifior": "默认用户", "modifiedTime": "2025-05-22 14:47:06", "traceId": null, "enable": true, "connectId": null, "fromType": 3, "conditionalConflict": false, "startSchemeId": "2273724462180335618", "startSchemeCode": "flow1747896365005", "startSchemeName": "新建流程2025-05-22 14:46:05", "isSavelog": 0, "errorType": null, "syncExecTypeInt": null, "startTypeInt": 10, "startTypeName": "API触发", "getDataWay": null, "pkIntegratedId": "2273724462180335618", "changeConnect": true, "backUpTime": null, "delete": false, "dataTypeTag": "flow1747896365005", "dataTypeName": "新建流程2025-05-22 14:46:05", "schemeCode": "flow1747896365005", "schemeName": null, "appSrc": null, "tableView": "flow1747896365005", "sqlView": null, "appCode": null, "commondoccode": null, "lastFailLogId": null, "fromBillType": null, "toBillType": null, "voChangeRegId": null, "taskParam": null, "integrateScheme": null, "jobName": "0000L6YQ8AVLFUZPXD0000|flow|2273724462180335618", "taskId": null, "pk_id": "2273724986162937858"}, {"pk_view": "2272978520836407306", "gatewayid": "当前YonBIP连接", "tenantid": "0000L6YQ8AVLFUZPXD0000", "nctime": 0, "lastsuctime": "1900-01-01 00:00:00", "updateSuctime": true, "lastexectime": null, "lastexecend": null, "taskstatus": 0, "expression": null, "sqlwhere": null, "creationtime": "2025-05-21 14:38:46", "dataversion": 0, "dataview": {"dataversion": -1, "dataenable": -1, "partialObject": false, "empty": false, "schemeCode": "bip_pricedecisiondetail_u9_price_lapse_1_dlmtest06071735", "sqlview": null, "tableview": "bip_pricedecisiondetail_u9_price_lapse_1_dlmtest06071735", "app_src": "", "appcode": "bip_pricedecisiondetail_u9_price_lapse_1_dlmtest06071735", "commondoccode": null, "datatype": {"typetag": "bip_pricedecisiondetail_u9_price_lapse_1_dlmtest06071735", "typename": "定标单撤回失效u9c厂商价目表_1_复制1"}}, "batchexectime": null, "increment": true, "rank": 0, "maxRank": 0, "treeIndex": 0, "exec": 0, "excluded": false, "timestamp": 0.0, "needRetry": false, "needFailWarningMail": false, "modifior": null, "modifiedTime": "", "traceId": null, "enable": true, "connectId": "2051046814643126275", "fromType": 2, "conditionalConflict": false, "startSchemeId": null, "startSchemeCode": "bip_pricedecisiondetail_u9_price_lapse_1_dlmtest06071735", "startSchemeName": "定标单撤回失效u9c厂商价目表_1_复制1", "isSavelog": 0, "errorType": null, "syncExecTypeInt": null, "startTypeInt": 10, "startTypeName": "API触发", "getDataWay": 0, "pkIntegratedId": "2272978486476668932", "changeConnect": true, "backUpTime": null, "delete": false, "dataTypeTag": "bip_pricedecisiondetail_u9_price_lapse_1_dlmtest06071735", "dataTypeName": "定标单撤回失效u9c厂商价目表_1_复制1", "schemeCode": "bip_pricedecisiondetail_u9_price_lapse_1_dlmtest06071735", "schemeName": null, "appSrc": "", "tableView": "bip_pricedecisiondetail_u9_price_lapse_1_dlmtest06071735", "sqlView": null, "appCode": "bip_pricedecisiondetail_u9_price_lapse_1_dlmtest06071735", "commondoccode": null, "lastFailLogId": null, "fromBillType": "ublinker", "toBillType": "ublinker", "voChangeRegId": "2272978520836407341", "taskParam": null, "integrateScheme": {"sourceConnectConfigId": "2051046814643126275", "targetConnectConfigId": "2263910573575241735", "applicationCode": "BIPToU9CApp_1", "applicationName": "段李敏专用-BIP旗舰版到U9C集成_1", "targetConnectorCode": "yonyou-erp-u9c", "targetConnectorName": "用友U9C连接器", "sourceConnectorCode": "yonyou-erp-yonbip", "sourceConnectorName": "用友YonBIP旗舰版连接器", "sourceCategoryId": "1778823790308884487", "targetCategoryId": "1778742529527644577", "sourceCategoryName": "寻源中心", "targetCategoryName": "用友U9C连接器", "pageExecRate": "0", "incrementFlag": true, "fromType": 2, "updateFlag": false, "longTSFlag": false, "upgradeBaseObjectAndApi": 0, "passiveForcedPush": false, "tenantId": "0000L6YQ8AVLFUZPXD0000", "ytenantId": "0000L6YQ8AVLFUZPXD0000", "sourceSystemCode": "", "sourceSystemId": "", "sourceSystemName": "", "sourceObjectCode": "priceDecision", "sourceObjectName": "定标单", "targetObjectCode": "U9OrderCommonDTO", "targetObjectName": "U9通用查询对象", "targetSystemCode": "", "targetSystemId": "", "targetSystemName": "", "errorType": 1, "schemeCode": "bip_pricedecisiondetail_u9_price_lapse_1_dlmtest06071735", "schemeName": "定标单撤回失效u9c厂商价目表_1_复制1", "status": 0, "transformationRule": "0", "orderConditions": [], "dataparentname": "", "treeDoc": "0", "targetOpreation": ["purPriceCancel"], "increment": "", "totalcountURI": "", "datapkname": "cpuPricedecisionId", "getDataWay": "0", "parseReturnValue": 0, "openPlatformId": "", "yonBipConnectorUpgradeFlag": false, "genId": "0", "dataversion": 0, "dataenable": 0, "creator": "99ea7655-00a2-4bda-b23c-19ade37ea574", "creationtime": "2025-05-21 14:38:42", "modifier": "99ea7655-00a2-4bda-b23c-19ade37ea574", "partialObject": false, "empty": false, "ccreationtime": "2025-05-21 14:38:42", "supportFeatures": "0", "_id": "2272978486476668932", "cmodifier": "99ea7655-00a2-4bda-b23c-19ade37ea574", "ccreator": "99ea7655-00a2-4bda-b23c-19ade37ea574", "id": "2272978486476668932"}, "jobName": "0000L6YQ8AVLFUZPXD0000|syncdata||2272978520836407344|bip_pricedecisiondetail_u9_price_lapse_1_dlmtest06071735", "taskId": null, "pk_id": "2272978520836407344"}, {"pk_view": "2272978477886734345", "gatewayid": "当前YonBIP连接", "tenantid": "0000L6YQ8AVLFUZPXD0000", "nctime": 0, "lastsuctime": "1900-01-01 00:00:00", "updateSuctime": true, "lastexectime": null, "lastexecend": null, "taskstatus": 0, "expression": null, "sqlwhere": null, "creationtime": "2025-05-21 14:38:42", "dataversion": 0, "dataview": {"dataversion": -1, "dataenable": -1, "partialObject": false, "empty": false, "schemeCode": "bip_pricedecisiondetail_u9_price_lapse_1_dlmtest060222", "sqlview": null, "tableview": "bip_pricedecisiondetail_u9_price_lapse_1_dlmtest060222", "app_src": "", "appcode": "bip_pricedecisiondetail_u9_price_lapse_1_dlmtest060222", "commondoccode": null, "datatype": {"typetag": "bip_pricedecisiondetail_u9_price_lapse_1_dlmtest060222", "typename": "定标单撤回失效u9c厂商价目表_1_复制1"}}, "batchexectime": null, "increment": true, "rank": 0, "maxRank": 0, "treeIndex": 0, "exec": 0, "excluded": false, "timestamp": 0.0, "needRetry": false, "needFailWarningMail": false, "modifior": null, "modifiedTime": "", "traceId": null, "enable": true, "connectId": "2051046814643126275", "fromType": 2, "conditionalConflict": false, "startSchemeId": null, "startSchemeCode": "bip_pricedecisiondetail_u9_price_lapse_1_dlmtest060222", "startSchemeName": "定标单撤回失效u9c厂商价目表_1_复制1", "isSavelog": 0, "errorType": null, "syncExecTypeInt": null, "startTypeInt": 10, "startTypeName": "API触发", "getDataWay": 0, "pkIntegratedId": "2272978452116930562", "changeConnect": true, "backUpTime": null, "delete": false, "dataTypeTag": "bip_pricedecisiondetail_u9_price_lapse_1_dlmtest060222", "dataTypeName": "定标单撤回失效u9c厂商价目表_1_复制1", "schemeCode": "bip_pricedecisiondetail_u9_price_lapse_1_dlmtest060222", "schemeName": null, "appSrc": "", "tableView": "bip_pricedecisiondetail_u9_price_lapse_1_dlmtest060222", "sqlView": null, "appCode": "bip_pricedecisiondetail_u9_price_lapse_1_dlmtest060222", "commondoccode": null, "lastFailLogId": null, "fromBillType": "ublinker", "toBillType": "ublinker", "voChangeRegId": "2272978477886734380", "taskParam": null, "integrateScheme": {"sourceConnectConfigId": "2051046814643126275", "targetConnectConfigId": "2263910573575241735", "applicationCode": "BIPToU9CApp_1", "applicationName": "段李敏专用-BIP旗舰版到U9C集成_1", "targetConnectorCode": "yonyou-erp-u9c", "targetConnectorName": "用友U9C连接器", "sourceConnectorCode": "yonyou-erp-yonbip", "sourceConnectorName": "YonBIP连接器", "sourceCategoryId": "1778823790308884487", "targetCategoryId": "1778742529527644577", "sourceCategoryName": "寻源中心", "targetCategoryName": "用友U9C连接器", "pageExecRate": "0", "incrementFlag": true, "fromType": 2, "updateFlag": false, "longTSFlag": false, "upgradeBaseObjectAndApi": 0, "passiveForcedPush": false, "tenantId": "0000L6YQ8AVLFUZPXD0000", "ytenantId": "0000L6YQ8AVLFUZPXD0000", "sourceSystemCode": "", "sourceSystemId": "", "sourceSystemName": "", "sourceObjectCode": "priceDecision", "sourceObjectName": "定标单", "targetObjectCode": "U9OrderCommonDTO", "targetObjectName": "U9通用查询对象", "targetSystemCode": "", "targetSystemId": "", "targetSystemName": "", "errorType": 1, "schemeCode": "bip_pricedecisiondetail_u9_price_lapse_1_dlmtest060222", "schemeName": "定标单撤回失效u9c厂商价目表_1_复制1", "status": 0, "transformationRule": "0", "orderConditions": [], "dataparentname": "", "treeDoc": "0", "targetOpreation": ["purPriceCancel"], "increment": "", "totalcountURI": "", "datapkname": "cpuPricedecisionId", "getDataWay": "0", "parseReturnValue": 0, "openPlatformId": "", "yonBipConnectorUpgradeFlag": false, "genId": "0", "dataversion": 0, "dataenable": 0, "creator": "99ea7655-00a2-4bda-b23c-19ade37ea574", "creationtime": "2025-05-21 14:38:38", "modifier": "99ea7655-00a2-4bda-b23c-19ade37ea574", "partialObject": false, "empty": false, "ccreationtime": "2025-05-21 14:38:38", "supportFeatures": "0", "_id": "2272978452116930562", "cmodifier": "99ea7655-00a2-4bda-b23c-19ade37ea574", "ccreator": "99ea7655-00a2-4bda-b23c-19ade37ea574", "id": "2272978452116930562"}, "jobName": "0000L6YQ8AVLFUZPXD0000|syncdata||2272978486476668930|bip_pricedecisiondetail_u9_price_lapse_1_dlmtest060222", "taskId": null, "pk_id": "2272978486476668930"}, {"pk_view": "2272978443526995975", "gatewayid": "当前YonBIP连接", "tenantid": "0000L6YQ8AVLFUZPXD0000", "nctime": 0, "lastsuctime": "1900-01-01 00:00:00", "updateSuctime": true, "lastexectime": null, "lastexecend": null, "taskstatus": 0, "expression": null, "sqlwhere": null, "creationtime": "2025-05-21 14:38:37", "dataversion": 0, "dataview": {"dataversion": -1, "dataenable": -1, "partialObject": false, "empty": false, "schemeCode": "bip_pricedecisiondetail_u9_price_lapse_1", "sqlview": null, "tableview": "bip_pricedecisiondetail_u9_price_lapse_1", "app_src": "", "appcode": "bip_pricedecisiondetail_u9_price_lapse_1", "commondoccode": null, "datatype": {"typetag": "bip_pricedecisiondetail_u9_price_lapse_1", "typename": "定标单撤回失效u9c厂商价目表_1"}}, "batchexectime": null, "increment": true, "rank": 0, "maxRank": 0, "treeIndex": 0, "exec": 0, "excluded": false, "timestamp": 0.0, "needRetry": false, "needFailWarningMail": false, "modifior": null, "modifiedTime": "", "traceId": null, "enable": true, "connectId": "2051046814643126275", "fromType": 2, "conditionalConflict": false, "startSchemeId": null, "startSchemeCode": "bip_pricedecisiondetail_u9_price_lapse_1", "startSchemeName": "定标单撤回失效u9c厂商价目表_1", "isSavelog": 0, "errorType": null, "syncExecTypeInt": null, "startTypeInt": 10, "startTypeName": "API触发", "getDataWay": 0, "pkIntegratedId": "2272978400577323011", "changeConnect": true, "backUpTime": null, "delete": false, "dataTypeTag": "bip_pricedecisiondetail_u9_price_lapse_1", "dataTypeName": "定标单撤回失效u9c厂商价目表_1", "schemeCode": "bip_pricedecisiondetail_u9_price_lapse_1", "schemeName": null, "appSrc": "", "tableView": "bip_pricedecisiondetail_u9_price_lapse_1", "sqlView": null, "appCode": "bip_pricedecisiondetail_u9_price_lapse_1", "commondoccode": null, "lastFailLogId": null, "fromBillType": "ublinker", "toBillType": "ublinker", "voChangeRegId": "2272978443526996010", "taskParam": null, "integrateScheme": {"sourceConnectConfigId": "2051046814643126275", "targetConnectConfigId": "2263910573575241735", "applicationCode": "BIPToU9CApp_1", "applicationName": "段李敏专用-BIP旗舰版到U9C集成_1", "targetConnectorCode": "yonyou-erp-u9c", "targetConnectorName": "用友U9C", "sourceConnectorCode": "yonyou-erp-yonbip", "sourceConnectorName": "当前租户默认连接配置", "sourceCategoryId": "1778823790308884487", "targetCategoryId": "1778742529527644577", "sourceCategoryName": "寻源中心", "targetCategoryName": "用友U9C", "pageExecRate": "0", "queryCriteria": {"coreRequest": {}}, "incrementFlag": true, "fromType": 2, "updateFlag": false, "longTSFlag": false, "upgradeBaseObjectAndApi": 0, "passiveForcedPush": false, "tenantId": "0000L6YQ8AVLFUZPXD0000", "ytenantId": "0000L6YQ8AVLFUZPXD0000", "sourceSystemCode": "", "sourceSystemId": "", "sourceSystemName": "", "sourceObjectCode": "priceDecision", "sourceObjectName": "定标单", "targetObjectCode": "U9OrderCommonDTO", "targetObjectName": "U9通用查询对象", "targetSystemCode": "", "targetSystemId": "", "targetSystemName": "", "errorType": 1, "schemeCode": "bip_pricedecisiondetail_u9_price_lapse_1", "schemeName": "定标单撤回失效u9c厂商价目表_1", "status": 0, "transformationRule": "0", "orderConditions": [], "dataparentname": "", "treeDoc": "0", "targetOpreation": ["purPriceCancel"], "increment": "", "totalcountURI": "", "datapkname": "cpuPricedecisionId", "getDataWay": "0", "parseReturnValue": 0, "openPlatformId": "", "yonBipConnectorUpgradeFlag": false, "genId": "0", "dataversion": 0, "dataenable": 0, "creator": "99ea7655-00a2-4bda-b23c-19ade37ea574", "creationtime": "2025-05-21 14:38:32", "modifier": "99ea7655-00a2-4bda-b23c-19ade37ea574", "partialObject": false, "empty": false, "ccreationtime": "2025-05-21 14:38:32", "supportFeatures": "0", "_id": "2272978400577323011", "cmodifier": "99ea7655-00a2-4bda-b23c-19ade37ea574", "ccreator": "99ea7655-00a2-4bda-b23c-19ade37ea574", "id": "2272978400577323011"}, "jobName": "0000L6YQ8AVLFUZPXD0000|syncdata||2272978443526996013|bip_pricedecisiondetail_u9_price_lapse_1", "taskId": null, "pk_id": "2272978443526996013"}, {"pk_view": "2272978391987388416", "gatewayid": "当前YonBIP连接", "tenantid": "0000L6YQ8AVLFUZPXD0000", "nctime": 0, "lastsuctime": "1900-01-01 00:00:00", "updateSuctime": true, "lastexectime": null, "lastexecend": null, "taskstatus": 0, "expression": null, "sqlwhere": null, "creationtime": "2025-05-21 14:38:31", "dataversion": 0, "dataview": {"dataversion": -1, "dataenable": -1, "partialObject": false, "empty": false, "schemeCode": "BIPVendor_U9CSupplier_1_dlm06121233", "sqlview": null, "tableview": "BIPVendor_U9CSupplier_1_dlm06121233", "app_src": "", "appcode": "BIPVendor_U9CSupplier_1_dlm06121233", "commondoccode": null, "datatype": {"typetag": "BIPVendor_U9CSupplier_1_dlm06121233", "typename": "BIP供应商同步到U9C供应商_1_复制1_复制1"}}, "batchexectime": null, "increment": false, "rank": 0, "maxRank": 0, "treeIndex": 0, "exec": 0, "excluded": false, "timestamp": 0.0, "needRetry": false, "needFailWarningMail": false, "modifior": null, "modifiedTime": "", "traceId": null, "enable": true, "connectId": "2051046814643126275", "fromType": 2, "conditionalConflict": false, "startSchemeId": null, "startSchemeCode": "BIPVendor_U9CSupplier_1_dlm06121233", "startSchemeName": "BIP供应商同步到U9C供应商_1_复制1_复制1", "isSavelog": 0, "errorType": null, "syncExecTypeInt": null, "startTypeInt": 1, "startTypeName": "定时触发", "getDataWay": 1, "pkIntegratedId": "2272978254548434944", "changeConnect": true, "backUpTime": null, "delete": false, "dataTypeTag": "BIPVendor_U9CSupplier_1_dlm06121233", "dataTypeName": "BIP供应商同步到U9C供应商_1_复制1_复制1", "schemeCode": "BIPVendor_U9CSupplier_1_dlm06121233", "schemeName": null, "appSrc": "", "tableView": "BIPVendor_U9CSupplier_1_dlm06121233", "sqlView": null, "appCode": "BIPVendor_U9CSupplier_1_dlm06121233", "commondoccode": null, "lastFailLogId": null, "fromBillType": "ublinker", "toBillType": "ublinker", "voChangeRegId": "2272978391987388525", "taskParam": null, "integrateScheme": {"sourceConnectConfigId": "2051046814643126275", "targetConnectConfigId": "2263910573575241735", "applicationCode": "BIPToU9CApp_1", "applicationName": "段李敏专用-BIP旗舰版到U9C集成_1", "targetConnectorCode": "yonyou-erp-u9c", "targetConnectorName": "用友U9C连接器", "sourceConnectorCode": "yonyou-erp-yonbip", "sourceConnectorName": "用友YonBIP旗舰版连接器", "sourceCategoryId": "1673949656818122769", "targetCategoryId": "1752791933382558125", "sourceCategoryName": "供应商档案", "targetCategoryName": "用友U9C连接器", "pageExecRate": "0", "queryCriteria": {"coreRequest": {}, "tenantId": "", "vendororg": "", "code": "", "helpcode": "", "vendorclass": "", "shipvia": "", "correspondingcust": "", "stopstatus": "", "person": "", "org": "", "deliveryvendor": "", "invoicevendor": "", "currency": "", "taxrate": "", "parentVendor": "", "retailInvestors": "", "vendorApplyRange_isCreator": "", "queryChildrenTable": "", "onlyManageOrg": "", "custom_queryDetailData": "", "yl_apiType": ""}, "incrementFlag": false, "fromType": 2, "updateFlag": false, "longTSFlag": false, "upgradeBaseObjectAndApi": 0, "passiveForcedPush": false, "tenantId": "0000L6YQ8AVLFUZPXD0000", "ytenantId": "0000L6YQ8AVLFUZPXD0000", "sourceSystemCode": "", "sourceSystemId": "", "sourceSystemName": "", "sourceObjectCode": "vendor", "sourceObjectName": "供应商档案", "targetObjectCode": "supplier", "targetObjectName": "供应商档案", "targetSystemCode": "", "targetSystemId": "", "targetSystemName": "", "errorType": 2, "schemeCode": "BIPVendor_U9CSupplier_1_dlm06121233", "schemeName": "BIP供应商同步到U9C供应商_1_复制1_复制1", "status": 0, "transformationRule": "1", "preTask": ["BIPVendor_U9CSupplier", "BIPVendor_U9CSupplier", "bip_pricedecisiondetail_u9_price_lapse", "BIPVendor_U9CSupplier_1", "bip_pricedecisiondetail_u9_price_lapse_1"], "orderConditions": [], "pageSize": 50, "dataparentname": "", "treeDoc": "0", "sourceOpreation": ["queryVendorList"], "targetOpreation": ["batchSaveSupplier"], "increment": "simple.pubts", "totalcountURI": "totalCount", "pageBegin": 1, "datapkname": "id", "pageType": 1, "getDataWay": "1", "parseReturnValue": 1, "openPlatformId": "", "yonBipConnectorUpgradeFlag": false, "genId": "1", "dataversion": 0, "dataenable": 0, "creator": "99ea7655-00a2-4bda-b23c-19ade37ea574", "creationtime": "2025-05-21 14:38:15", "modifier": "99ea7655-00a2-4bda-b23c-19ade37ea574", "partialObject": false, "empty": false, "ccreationtime": "2025-05-21 14:38:15", "supportFeatures": "1", "_id": "2272978254548434944", "cmodifier": "99ea7655-00a2-4bda-b23c-19ade37ea574", "ccreator": "99ea7655-00a2-4bda-b23c-19ade37ea574", "id": "2272978254548434944"}, "jobName": "0000L6YQ8AVLFUZPXD0000|syncdata||2272978391987388553|BIPVendor_U9CSupplier_1_dlm06121233", "taskId": null, "pk_id": "2272978391987388553"}, {"pk_view": "2272978237368565778", "gatewayid": "当前YonBIP连接", "tenantid": "0000L6YQ8AVLFUZPXD0000", "nctime": 0, "lastsuctime": "1900-01-01 00:00:00", "updateSuctime": true, "lastexectime": null, "lastexecend": null, "taskstatus": 0, "expression": null, "sqlwhere": null, "creationtime": "2025-05-21 14:38:14", "dataversion": 0, "dataview": {"dataversion": -1, "dataenable": -1, "partialObject": false, "empty": false, "schemeCode": "BIPVendor_U9CSupplier_1_dlm060221", "sqlview": null, "tableview": "BIPVendor_U9CSupplier_1_dlm060221", "app_src": "", "appcode": "BIPVendor_U9CSupplier_1_dlm060221", "commondoccode": null, "datatype": {"typetag": "BIPVendor_U9CSupplier_1_dlm060221", "typename": "BIP供应商同步到U9C供应商_1_复制1"}}, "batchexectime": null, "increment": false, "rank": 0, "maxRank": 0, "treeIndex": 0, "exec": 0, "excluded": false, "timestamp": 0.0, "needRetry": false, "needFailWarningMail": false, "modifior": null, "modifiedTime": "", "traceId": null, "enable": true, "connectId": "2051046814643126275", "fromType": 2, "conditionalConflict": false, "startSchemeId": null, "startSchemeCode": "BIPVendor_U9CSupplier_1_dlm060221", "startSchemeName": "BIP供应商同步到U9C供应商_1_复制1", "isSavelog": 0, "errorType": null, "syncExecTypeInt": null, "startTypeInt": 1, "startTypeName": "定时触发", "getDataWay": 1, "pkIntegratedId": "2272978099929612291", "changeConnect": true, "backUpTime": null, "delete": false, "dataTypeTag": "BIPVendor_U9CSupplier_1_dlm060221", "dataTypeName": "BIP供应商同步到U9C供应商_1_复制1", "schemeCode": "BIPVendor_U9CSupplier_1_dlm060221", "schemeName": null, "appSrc": "", "tableView": "BIPVendor_U9CSupplier_1_dlm060221", "sqlView": null, "appCode": "BIPVendor_U9CSupplier_1_dlm060221", "commondoccode": null, "lastFailLogId": null, "fromBillType": "ublinker", "toBillType": "ublinker", "voChangeRegId": "2272978245958500464", "taskParam": null, "integrateScheme": {"sourceConnectConfigId": "2051046814643126275", "targetConnectConfigId": "2263910573575241735", "applicationCode": "BIPToU9CApp_1", "applicationName": "段李敏专用-BIP旗舰版到U9C集成_1", "targetConnectorCode": "yonyou-erp-u9c", "targetConnectorName": "用友U9C连接器", "sourceConnectorCode": "yonyou-erp-yonbip", "sourceConnectorName": "YonBIP连接器", "sourceCategoryId": "1673949656818122769", "targetCategoryId": "1752791933382558125", "sourceCategoryName": "供应商档案", "targetCategoryName": "用友U9C连接器", "pageExecRate": "0", "queryCriteria": {"coreRequest": {}, "tenantId": "", "vendororg": "", "code": "", "helpcode": "", "vendorclass": "", "shipvia": "", "correspondingcust": "", "stopstatus": "", "person": "", "org": "", "deliveryvendor": "", "invoicevendor": "", "currency": "", "taxrate": "", "parentVendor": "", "retailInvestors": "", "vendorApplyRange_isCreator": "", "queryChildrenTable": "", "onlyManageOrg": "", "custom_queryDetailData": "", "yl_apiType": ""}, "incrementFlag": false, "fromType": 2, "updateFlag": false, "longTSFlag": false, "upgradeBaseObjectAndApi": 0, "passiveForcedPush": false, "tenantId": "0000L6YQ8AVLFUZPXD0000", "ytenantId": "0000L6YQ8AVLFUZPXD0000", "sourceSystemCode": "", "sourceSystemId": "", "sourceSystemName": "", "sourceObjectCode": "vendor", "sourceObjectName": "供应商档案", "targetObjectCode": "supplier", "targetObjectName": "供应商档案", "targetSystemCode": "", "targetSystemId": "", "targetSystemName": "", "errorType": 2, "schemeCode": "BIPVendor_U9CSupplier_1_dlm060221", "schemeName": "BIP供应商同步到U9C供应商_1_复制1", "status": 0, "transformationRule": "1", "preTask": ["BIPVendor_U9CSupplier", "bip_pricedecisiondetail_u9_price_lapse", "BIPVendor_U9CSupplier_1"], "orderConditions": [], "pageSize": 50, "dataparentname": "", "treeDoc": "0", "sourceOpreation": ["queryVendorList"], "targetOpreation": ["batchSaveSupplier"], "increment": "simple.pubts", "totalcountURI": "totalCount", "pageBegin": 1, "datapkname": "id", "pageType": 1, "getDataWay": "1", "parseReturnValue": 1, "openPlatformId": "", "yonBipConnectorUpgradeFlag": false, "genId": "1", "dataversion": 0, "dataenable": 0, "creator": "99ea7655-00a2-4bda-b23c-19ade37ea574", "creationtime": "2025-05-21 14:37:58", "modifier": "99ea7655-00a2-4bda-b23c-19ade37ea574", "partialObject": false, "empty": false, "ccreationtime": "2025-05-21 14:37:58", "supportFeatures": "1", "_id": "2272978099929612291", "cmodifier": "99ea7655-00a2-4bda-b23c-19ade37ea574", "ccreator": "99ea7655-00a2-4bda-b23c-19ade37ea574", "id": "2272978099929612291"}, "jobName": "0000L6YQ8AVLFUZPXD0000|syncdata||2272978245958500492|BIPVendor_U9CSupplier_1_dlm060221", "taskId": null, "pk_id": "2272978245958500492"}, {"pk_view": "2272978091339677705", "gatewayid": "当前YonBIP连接", "tenantid": "0000L6YQ8AVLFUZPXD0000", "nctime": 0, "lastsuctime": "1900-01-01 00:00:00", "updateSuctime": true, "lastexectime": null, "lastexecend": null, "taskstatus": 0, "expression": null, "sqlwhere": null, "creationtime": "2025-05-21 14:37:56", "dataversion": 0, "dataview": {"dataversion": -1, "dataenable": -1, "partialObject": false, "empty": false, "schemeCode": "BIPVendor_U9CSupplier_1", "sqlview": null, "tableview": "BIPVendor_U9CSupplier_1", "app_src": "", "appcode": "BIPVendor_U9CSupplier_1", "commondoccode": null, "datatype": {"typetag": "BIPVendor_U9CSupplier_1", "typename": "BIP供应商同步到U9C供应商_1"}}, "batchexectime": null, "increment": false, "rank": 0, "maxRank": 0, "treeIndex": 0, "exec": 0, "excluded": false, "timestamp": 0.0, "needRetry": false, "needFailWarningMail": false, "modifior": null, "modifiedTime": "", "traceId": null, "enable": true, "connectId": "2051046814643126275", "fromType": 2, "conditionalConflict": false, "startSchemeId": null, "startSchemeCode": "BIPVendor_U9CSupplier_1", "startSchemeName": "BIP供应商同步到U9C供应商_1", "isSavelog": 0, "errorType": null, "syncExecTypeInt": null, "startTypeInt": 1, "startTypeName": "定时触发", "getDataWay": 1, "pkIntegratedId": "2272977971080593411", "changeConnect": true, "backUpTime": null, "delete": false, "dataTypeTag": "BIPVendor_U9CSupplier_1", "dataTypeName": "BIP供应商同步到U9C供应商_1", "schemeCode": "BIPVendor_U9CSupplier_1", "schemeName": null, "appSrc": "", "tableView": "BIPVendor_U9CSupplier_1", "sqlView": null, "appCode": "BIPVendor_U9CSupplier_1", "commondoccode": null, "lastFailLogId": null, "fromBillType": "ublinker", "toBillType": "ublinker", "voChangeRegId": "2272978091339677808", "taskParam": null, "integrateScheme": {"sourceConnectConfigId": "2051046814643126275", "targetConnectConfigId": "2263910573575241735", "applicationCode": "BIPToU9CApp_1", "applicationName": "段李敏专用-BIP旗舰版到U9C集成_1", "targetConnectorCode": "yonyou-erp-u9c", "targetConnectorName": "用友U9C", "sourceConnectorCode": "yonyou-erp-yonbip", "sourceConnectorName": "当前租户默认连接配置", "sourceCategoryId": "1673949656818122769", "targetCategoryId": "1752791933382558125", "sourceCategoryName": "供应商档案", "targetCategoryName": "用友U9C", "pageExecRate": "0", "queryCriteria": {"taxrate": "", "onlyManageOrg": "", "queryChildrenTable": "true", "vendorApplyRange_isCreator": "1", "vendororg": "", "code": "", "helpcode": "", "vendorclass": "", "shipvia": "", "correspondingcust": "", "stopstatus": "", "person": "", "org": "", "deliveryvendor": "", "invoicevendor": "", "currency": "", "parentVendor": "", "retailInvestors": ""}, "incrementFlag": false, "fromType": 2, "updateFlag": false, "longTSFlag": false, "upgradeBaseObjectAndApi": 0, "passiveForcedPush": false, "tenantId": "0000L6YQ8AVLFUZPXD0000", "ytenantId": "0000L6YQ8AVLFUZPXD0000", "sourceSystemCode": "", "sourceSystemId": "", "sourceSystemName": "", "sourceObjectCode": "vendor", "sourceObjectName": "供应商档案", "targetObjectCode": "supplier", "targetObjectName": "供应商档案", "targetSystemCode": "", "targetSystemId": "", "targetSystemName": "", "errorType": 2, "schemeCode": "BIPVendor_U9CSupplier_1", "schemeName": "BIP供应商同步到U9C供应商_1", "status": 0, "transformationRule": "1", "preTask": ["u9c_base_bank_bip", "u9c_base_country_bip", "u9ce_adminorg_syn", "u9ce_dept_syn", "u9ce_supplierclass_syn_upc", "u9c_taxrate_syn", "u9ce_person_syn", "u9c_base_bankcategory_bip", "u9ce_currtype_sync_diwork"], "orderConditions": [], "pageSize": 50, "dataparentname": "", "treeDoc": "0", "sourceOpreation": ["queryVendorList"], "targetOpreation": ["batchSaveSupplier"], "increment": "simple.pubts", "totalcountURI": "totalCount", "pageBegin": 1, "datapkname": "id", "pageType": 1, "getDataWay": "1", "parseReturnValue": 1, "openPlatformId": "", "yonBipConnectorUpgradeFlag": false, "genId": "1", "dataversion": 0, "dataenable": 0, "creator": "99ea7655-00a2-4bda-b23c-19ade37ea574", "creationtime": "2025-05-21 14:37:43", "modifier": "99ea7655-00a2-4bda-b23c-19ade37ea574", "partialObject": false, "empty": false, "ccreationtime": "2025-05-21 14:37:43", "supportFeatures": "1", "_id": "2272977971080593411", "cmodifier": "99ea7655-00a2-4bda-b23c-19ade37ea574", "ccreator": "99ea7655-00a2-4bda-b23c-19ade37ea574", "id": "2272977971080593411"}, "jobName": "0000L6YQ8AVLFUZPXD0000|syncdata||2272978091339677836|BIPVendor_U9CSupplier_1", "taskId": null, "pk_id": "2272978091339677836"}]}}