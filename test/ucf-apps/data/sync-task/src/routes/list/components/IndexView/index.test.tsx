import IndexView from "ucf-apps/data/sync-task/src/routes/list/components/IndexView/index.js";
import { render, screen } from "@test/utils";
import React from "react";
import CommonStore from "ucf-apps/data/sync-task/src/routes/store";
import Store from "ucf-apps/data/sync-task/src/routes/list/store";

jest.mock("ucf-apps/data/sync-task/src/routes/list/components/IndexView/SearchForm.jsx", () => {
    return {
        __esModule: true,
        default: () => <div>Search Form</div>,
    };
});

jest.mock("ucf-apps/data/sync-task/src/routes/list/service.js", () => {
    return {
        __esModule: true,
        getTreeDataService: jest.fn(() =>
            Promise.resolve({
                data: [
                    {
                        id: "2278889014312304648",
                        code: "test0529",
                        name: "test0529",
                        isSchemeApp: false,
                        children: [
                            {
                                id: "2282496357345263621",
                                code: "aaa",
                                name: "bbb",
                                parentId: "2278889014312304648",
                                appId: "2278889014312304648",
                                children: [],
                            },
                        ],
                    },
                ],
            })
        ),
        getConnectorListService: jest.fn(() =>
            Promise.resolve({
                data: [
                    {
                        value: "公有云BIP",
                        key: "1840326613784854537",
                    },
                    {
                        value: "当前YonBIP连接",
                        key: "2051046814643126275",
                    },
                ],
            })
        ),
        getTaskListService: jest.fn(() =>
            Promise.resolve({
                data: {
                    itemCount: 0,
                    pageIndex: 0,
                    pageSize: 20,
                    pageCount: 0,
                    hasPre: false,
                    hasNext: false,
                    items: [],
                },
            })
        ),
    };
});

describe("Sync Task List", () => {
    let store;
    let commonStore = new CommonStore();
    beforeEach(() => {
        store = new Store();
    });

    it("renders the Sync Task List", () => {
        render(
            <IndexView
                commonState={commonStore.toJS()}
                ownerState={store.toJS()}
                ownerStore={store}
                queryContext={{
                    initData: {},
                }}
            />
        );
        expect(screen.getByText("数据同步任务")).toBeInTheDocument();
        expect(screen.getByText("暂时没有数据哦~")).toBeInTheDocument();
    });
});
