import React from "react";
import { screen, fireEvent, waitFor, act } from "@testing-library/react";
import "@testing-library/jest-dom";
import SqlWhereModal from "@data/sync-task/src/routes/list/components/SqlWhereModal/index2";
import { render } from "@test/utils";

// 模拟feedback工具
jest.mock("utils/feedback", () => ({
  Success: jest.fn(),
  Error: jest.fn()
}));

// 模拟store对象
const mockOwnerStore = {
  getDataColumns: jest.fn(() => Promise.resolve()),
  setSqlWhere: jest.fn(() => Promise.resolve())
};

// 模拟selectedTask
const mockSelectedTask = {
    "pk_view": "6287297496e47c3a5763ff82",
    "gatewayid": "[wangtest002]旧-王豪阳高级版测试",
    "tenantid": "0000L6YQ8AVLFUZPXD0000",
    "nctime": 0,
    "lastsuctime": "1900-01-01 00:00:00",
    "updateSuctime": true,
    "lastexectime": null,
    "lastexecend": null,
    "taskstatus": 0,
    "expression": null,
    "sqlwhere": null,
    "creationtime": "2025-05-19 16:17:16",
    "dataversion": 0,
    "dataview": {
        "dataversion": -1,
        "dataenable": -1,
        "partialObject": false,
        "empty": false,
        "schemeCode": null,
        "sqlview": "SELECT \tcertificatenum AS certno, \tworkname  AS title, workunitname AS ratingorg, \tworkdate AS getdate, \tworkdate AS begindate, \tremark as memo, CASE \t\t \t\tWHEN istop = 'Y' THEN \t\t1 ELSE 0  \tEND AS isprefs, CASE \t\t \t\tWHEN lastflag = 'Y' THEN \t\t1 ELSE 0  \tEND AS lastflag, \tpk_psndoc AS staff_id, \tpk_psndoc_sub AS id  FROM \thi_psndoc_nationduty  WHERE \tpk_psndoc IN ( \tSELECT \t\tpsndoc.pk_psndoc  \tFROM \t\thi_psnjob psnjob \t\tINNER JOIN hi_psnorg psnorg ON psnorg.pk_psnorg = psnjob.pk_psnorg \t\tINNER JOIN bd_psndoc psndoc ON psndoc.pk_psndoc = psnjob.pk_psndoc  \t\tAND psndoc.pk_org = psnjob.pk_org  \tWHERE \t\tpsnorg.lastflag = 'Y'  \t\tAND psnorg.indocflag = 'Y'  \t\tAND psnjob.lastflag = 'Y'  \t\tAND psnjob.endflag = 'N'  \t\tAND psnjob.ismainjob = 'Y'  \t\tAND psndoc.mobile IS NOT NULL  \t)  \tAND ts > @lastupdatetime  ORDER BY \tstaff_id",
        "tableview": "bip_psndoc_nationduty_hr_diwork",
        "app_src": "diwork",
        "appcode": "yonbip_pro",
        "commondoccode": "",
        "datatype": {
            "typetag": "nationduty_hr",
            "typename": "职业资格"
        }
    },
    "batchexectime": null,
    "increment": false,
    "rank": 0,
    "maxRank": 0,
    "treeIndex": 0,
    "exec": 0,
    "excluded": true,
    "timestamp": 0,
    "needRetry": false,
    "needFailWarningMail": false,
    "modifior": null,
    "modifiedTime": "",
    "traceId": null,
    "enable": true,
    "connectId": null,
    "fromType": null,
    "conditionalConflict": false,
    "startSchemeId": null,
    "startSchemeCode": "bip_psndoc_nationduty_hr_diwork",
    "startSchemeName": "职业资格",
    "isSavelog": 0,
    "errorType": null,
    "syncExecTypeInt": null,
    "startTypeInt": 1,
    "startTypeName": "定时触发",
    "getDataWay": 1,
    "pkIntegratedId": null,
    "changeConnect": true,
    "backUpTime": null,
    "delete": false,
    "dataTypeTag": "nationduty_hr",
    "dataTypeName": "职业资格",
    "schemeCode": null,
    "schemeName": null,
    "appSrc": "diwork",
    "tableView": "bip_psndoc_nationduty_hr_diwork",
    "sqlView": "SELECT \tcertificatenum AS certno, \tworkname  AS title, workunitname AS ratingorg, \tworkdate AS getdate, \tworkdate AS begindate, \tremark as memo, CASE \t\t \t\tWHEN istop = 'Y' THEN \t\t1 ELSE 0  \tEND AS isprefs, CASE \t\t \t\tWHEN lastflag = 'Y' THEN \t\t1 ELSE 0  \tEND AS lastflag, \tpk_psndoc AS staff_id, \tpk_psndoc_sub AS id  FROM \thi_psndoc_nationduty  WHERE \tpk_psndoc IN ( \tSELECT \t\tpsndoc.pk_psndoc  \tFROM \t\thi_psnjob psnjob \t\tINNER JOIN hi_psnorg psnorg ON psnorg.pk_psnorg = psnjob.pk_psnorg \t\tINNER JOIN bd_psndoc psndoc ON psndoc.pk_psndoc = psnjob.pk_psndoc  \t\tAND psndoc.pk_org = psnjob.pk_org  \tWHERE \t\tpsnorg.lastflag = 'Y'  \t\tAND psnorg.indocflag = 'Y'  \t\tAND psnjob.lastflag = 'Y'  \t\tAND psnjob.endflag = 'N'  \t\tAND psnjob.ismainjob = 'Y'  \t\tAND psndoc.mobile IS NOT NULL  \t)  \tAND ts > @lastupdatetime  ORDER BY \tstaff_id",
    "appCode": "yonbip_pro",
    "commondoccode": "",
    "lastFailLogId": null,
    "fromBillType": null,
    "toBillType": null,
    "voChangeRegId": null,
    "taskParam": null,
    "integrateScheme": null,
    "jobName": "0000L6YQ8AVLFUZPXD0000|syncdata|diwork|2271544981012086793|bip_psndoc_nationduty_hr_diwork",
    "taskId": null,
    "pk_id": "2271544981012086793"
};

// 模拟props
const mockProps = {
  ownerStore: mockOwnerStore,
  ownerState: {
    resultArr2:{
        "begindate": {
            "name": "begindate"
        },
        "getdate": {
            "name": "getdate"
        },
        "lastflag": {
            "name": "lastflag"
        },
        "memo": {
            "name": "memo"
        },
        "title": {
            "name": "title"
        },
        "isprefs": {
            "name": "isprefs"
        },
        "ratingorg": {
            "name": "ratingorg"
        },
        "certno": {
            "name": "certno"
        },
        "staff_id": {
            "name": "staff_id"
        },
        "name": {
            "name": "名称"
        },
        "id": {
            "name": "id"
        },
        "selectData": [
            {
                "value": "begindate",
                "key": "begindate",
                "data": {}
            },
            {
                "value": "getdate",
                "key": "getdate",
                "data": {}
            },
            {
                "value": "lastflag",
                "key": "lastflag",
                "data": {}
            },
            {
                "value": "memo",
                "key": "memo",
                "data": {}
            },
            {
                "value": "title",
                "key": "title",
                "data": {}
            },
            {
                "value": "isprefs",
                "key": "isprefs",
                "data": {}
            },
            {
                "value": "ratingorg",
                "key": "ratingorg",
                "data": {}
            },
            {
                "value": "certno",
                "key": "certno",
                "data": {}
            },
            {
                "value": "staff_id",
                "key": "staff_id",
                "data": {}
            },
            {
                "value": "name",
                "key": "名称",
                "data": {}
            },
            {
                "value": "id",
                "key": "id",
                "data": {}
            }
        ]
    },
    getRealColumnContent: "workdate",
    selectedTask: mockSelectedTask,
  },
};

describe("SqlWhereModal 组件测试", () => {
  // 在每个测试前重置所有模拟
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test("组件应正确渲染", async () => {
    render(<SqlWhereModal ownerState={mockProps.ownerState} ownerStore={mockProps.ownerStore} />);
    const layout = screen.getByTestId('ublinker-routes-list-components-SqlWhereModal-index2-63628-Checkbox');
    expect(layout).toBeInTheDocument();
  });
});
