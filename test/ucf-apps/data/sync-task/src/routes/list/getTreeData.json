{"code": 200, "displayCode": null, "message": "操作成功", "data": [{"id": "2276567189348155398", "code": "nhtest001", "name": "nhtest001", "isSchemeApp": false, "children": []}, {"id": "2272176470051258377", "code": "AT1F8856D405D80008", "name": "王海春的ynf应用", "isSchemeApp": false, "children": []}, {"id": "2272124707105406983", "code": "sadas", "name": "asdasd", "isSchemeApp": false, "children": []}, {"id": "2272113737758933001", "code": "test0520", "name": "test0520", "isSchemeApp": false, "children": []}, {"id": "2268418536877588482", "code": "ccbobe_test", "name": "ccbobe_test", "isSchemeApp": false, "children": []}, {"id": "2264117350468943873", "code": "test20250509", "name": "test20250509", "isSchemeApp": false, "children": []}, {"id": "2263408852658552840", "code": "wtftest", "name": "wtftest", "isSchemeApp": false, "children": []}, {"id": "2261832290450538502", "code": "xiu0506", "name": "xiu0506", "isSchemeApp": false, "children": [{"id": "2273012433912070152", "code": "hh", "name": "hh", "parentId": "2261832290450538502", "appId": "2261832290450538502", "children": []}, {"id": "2272258263408443401", "code": "sd", "name": "sd", "parentId": "2261832290450538502", "appId": "2261832290450538502", "children": [{"id": "2273012365192593414", "code": "gg", "name": "gg", "parentId": "2272258263408443401", "appId": "2261832290450538502", "children": []}]}]}, {"id": "2261731582057381889", "code": "<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "isSchemeApp": false, "children": []}, {"id": "2247787438156546057", "code": "YANJQTEST418", "name": "YANJQTEST418", "isSchemeApp": false, "children": []}, {"id": "2245595553119338497", "code": "myzd", "name": "没有自动生产编码", "isSchemeApp": false, "children": [{"id": "2245595922486525952", "code": "xiaji", "name": "新增下级分类", "parentId": "2245595553119338497", "appId": "2245595553119338497", "children": []}, {"id": "2245596498012143619", "code": "xzadaa", "name": "嘻嘻嘻嘻嘻嘻", "parentId": "2245595553119338497", "appId": "2245595553119338497", "children": []}]}, {"id": "2245391988867727364", "code": "nhtest", "name": "nhtest", "isSchemeApp": false, "children": []}, {"id": "2242578501799510025", "code": "maxit_test", "name": "maxit_test", "isSchemeApp": false, "children": []}, {"id": "2242566956920602632", "code": "qqq", "name": "qqq", "isSchemeApp": false, "desc": "qqq", "children": []}, {"id": "2241948206768324615", "code": "<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "isSchemeApp": false, "desc": "<PERSON><PERSON><PERSON><PERSON>", "children": [{"id": "2241948696394596353", "code": "huhufenlei1", "name": "huhufenlei1", "parentId": "2241948206768324615", "appId": "2241948206768324615", "children": []}]}, {"id": "2241691633995743232", "code": "zero", "name": "低代码集成", "isSchemeApp": false, "children": []}, {"id": "2241205778704236552", "code": "demoapp20250408", "name": "demoapp20250408", "isSchemeApp": false, "children": [{"id": "2241206019222405122", "code": "democategory040819", "name": "democategory040819", "parentId": "2241205778704236552", "appId": "2241205778704236552", "children": []}]}, {"id": "2240416483975364609", "code": "ccbobe_20250407", "name": "ccbobe_20250407", "isSchemeApp": false, "children": [{"id": "2240416896305856517", "code": "A1", "name": "A1", "parentId": "2240416483975364609", "appId": "2240416483975364609", "children": []}]}, {"id": "2235341335892066306", "code": "demoapp20250401", "name": "demoapp20250401", "isSchemeApp": false, "children": [{"id": "2235341731029057542", "code": "demo0331", "name": "demo0331", "parentId": "2235341335892066306", "appId": "2235341335892066306", "children": [{"id": "2235341945777422345", "code": "dlmdemo", "name": "dlmdemo", "parentId": "2235341731029057542", "appId": "2235341335892066306", "children": []}]}, {"id": "2235342186295590915", "code": "demo0401", "name": "demo0401", "parentId": "2235341335892066306", "appId": "2235341335892066306", "children": [{"id": "2235344067491266564", "code": "testdemo", "name": "testdemo", "parentId": "2235342186295590915", "appId": "2235341335892066306", "children": []}]}]}, {"id": "2235119535189917696", "code": "transform_demo", "name": "Transform演示勿动", "isSchemeApp": false, "children": []}, {"id": "2233615961622577161", "code": "YANJQTEST", "name": "YANJQTEST", "isSchemeApp": false, "desc": "2025-3-29日测试验证", "children": []}, {"id": "2232228541151838210", "code": "xiu0327", "name": "xiu0327", "isSchemeApp": false, "children": []}, {"id": "2232227149582434313", "code": "xxm", "name": "xxm", "isSchemeApp": false, "children": []}, {"id": "2232225440185450496", "code": "testbizhong", "name": "测试币种", "isSchemeApp": false, "children": []}, {"id": "2222352951058366467", "code": "huhu0314", "name": "huhu0314", "isSchemeApp": false, "desc": "huhu0314", "children": [{"id": "2222353097087254534", "code": "huhu0314fl", "name": "huhu0314fl", "parentId": "2222352951058366467", "appId": "2222352951058366467", "children": []}]}, {"id": "2221639350832070656", "code": "huhu0313zlc", "name": "huhu0313zlc", "isSchemeApp": false, "desc": "huhu0313zlc", "children": [{"id": "2221639479681089538", "code": "huhu0313fl", "name": "huhu0313fl11", "parentId": "2221639350832070656", "appId": "2221639350832070656", "children": []}]}, {"id": "2221058439309164544", "code": "huhu0312yy", "name": "huhu0312应用123", "isSchemeApp": false, "desc": "huhu0312应用111", "children": []}, {"id": "2221039326698405888", "code": "xiua", "name": "xiua", "isSchemeApp": false, "children": []}, {"id": "2220284202660659208", "code": "guohhw", "name": "guohhw", "isSchemeApp": false, "desc": "aabbcc", "children": [{"id": "2221610497241776133", "code": "huhu0313", "name": "huhu0313111", "parentId": "2220284202660659208", "appId": "2220284202660659208", "children": [{"id": "2225351387971059720", "code": "cc", "name": "cc", "parentId": "2221610497241776133", "appId": "2220284202660659208", "children": []}]}]}, {"id": "2220143963481833473", "code": "mutest005", "name": "mutest005", "isSchemeApp": false, "children": []}, {"id": "2214402932225343495", "code": "xiu0303", "name": "xiu030340", "isSchemeApp": false, "desc": "123", "children": [{"id": "2255931598092369921", "code": "b", "name": "b", "parentId": "2214402932225343495", "appId": "2214402932225343495", "children": []}]}, {"id": "2211995018926751751", "code": "ai", "name": "集成小助手", "isSchemeApp": false, "children": []}, {"id": "2210784009978380289", "code": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "isSchemeApp": false, "desc": "<PERSON><PERSON><PERSON>", "children": []}, {"id": "2210686196371685380", "code": "ceshi1", "name": "ceshi1", "isSchemeApp": false, "desc": "ceshi1", "children": []}, {"id": "2178756507555332096", "code": "zouzou1", "name": "走走1", "isSchemeApp": false, "children": []}, {"id": "2173570016727793667", "code": "mutest004", "name": "mutest004", "isSchemeApp": false, "children": []}, {"id": "2170539238687244297", "code": "mutest003", "name": "mutest003", "isSchemeApp": false, "children": [{"id": "2170577309277356035", "code": "ziwen001", "name": "ziwen001", "parentId": "2170539238687244297", "appId": "2170539238687244297", "children": []}, {"id": "2170586603586584582", "code": "wenjian006", "name": "wenjian006", "parentId": "2170539238687244297", "appId": "2170539238687244297", "children": []}, {"id": "2170577463896178693", "code": "ziwen003", "name": "ziwen003", "parentId": "2170539238687244297", "appId": "2170539238687244297", "children": []}, {"id": "2170577489665982467", "code": "ziwen003", "name": "ziwen003", "parentId": "2170539238687244297", "appId": "2170539238687244297", "children": [{"id": "2170577481076047873", "code": "ziwen003", "name": "ziwen003", "parentId": "2170577489665982467", "appId": "2170539238687244297", "children": []}, {"id": "2170577498255917064", "code": "ziwen003", "name": "ziwen003", "parentId": "2170577489665982467", "appId": "2170539238687244297", "children": []}, {"id": "2170577489665982465", "code": "ziwen003", "name": "ziwen003", "parentId": "2170577489665982467", "appId": "2170539238687244297", "children": []}, {"id": "2170577498255917066", "code": "ziwen003", "name": "ziwen003", "parentId": "2170577489665982467", "appId": "2170539238687244297", "children": []}]}, {"id": "2170551101386915842", "code": "wenjian004", "name": "wenjian004", "parentId": "2170539238687244297", "appId": "2170539238687244297", "children": [{"id": "2170551032667439107", "code": "wenjian003", "name": "wenjian003", "parentId": "2170551101386915842", "appId": "2170539238687244297", "children": []}]}, {"id": "2170589077475164169", "code": "test004", "name": "test004", "parentId": "2170539238687244297", "appId": "2170539238687244297", "children": []}]}, {"id": "2169704589098483713", "code": "mutest002", "name": "mutest002", "isSchemeApp": false, "children": [{"id": "2169705267703316488", "code": "zi001", "name": "zi001", "parentId": "2169704589098483713", "appId": "2169704589098483713", "children": []}, {"id": "2169705353602662406", "code": "zi002", "name": "zi002", "parentId": "2169704589098483713", "appId": "2169704589098483713", "children": []}, {"id": "2169704992825409541", "code": "liu003", "name": "liu003", "parentId": "2169704589098483713", "appId": "2169704589098483713", "children": []}, {"id": "2169705439502008326", "code": "zi003", "name": "zi003", "parentId": "2169704589098483713", "appId": "2169704589098483713", "children": []}, {"id": "2169705164624101382", "code": "liu005", "name": "liu005", "parentId": "2169704589098483713", "appId": "2169704589098483713", "children": [{"id": "2169705087314690053", "code": "liu004", "name": "liu004", "parentId": "2169705164624101382", "appId": "2169704589098483713", "children": []}]}, {"id": "2169705533991288839", "code": "zi004", "name": "zi004", "parentId": "2169704589098483713", "appId": "2169704589098483713", "children": []}]}, {"id": "2155038426835451910", "code": "aaa", "name": "aaa", "isSchemeApp": false, "children": []}, {"id": "2155010638397046793", "code": "testTree", "name": "测试树节点拖拽问题", "isSchemeApp": false, "children": []}, {"id": "2146669124702437377", "code": "zxjctest", "name": "中信集成测试", "isSchemeApp": false, "desc": "中信集成平台，通过集成总线实现", "children": []}, {"id": "2145338750005673991", "code": "hwyTest", "name": "数据转换Test", "isSchemeApp": false, "children": []}, {"id": "2144801320747401219", "code": "wms_erp_sxjy", "name": "WMS山西晋云推ERP系统", "isSchemeApp": false, "desc": "", "children": []}, {"id": "2138785926908739589", "code": "batchImport1", "name": "batchImport1", "isSchemeApp": false, "children": []}, {"id": "2138784363540643844", "code": "batchImport2", "name": "batchImport2", "isSchemeApp": false, "desc": "", "children": []}, {"id": "2117134077737304064", "code": "aitest", "name": "aitest", "isSchemeApp": false, "desc": "aitest", "children": []}, {"id": "2093253398147629059", "code": "idoc_ysb_20240921", "name": "电子档案专属测试0921_勿动", "isSchemeApp": false, "desc": "", "children": []}, {"id": "2086777197350617089", "code": "ceshi_qina", "name": "ceshi_qina", "isSchemeApp": false, "children": []}, {"id": "2070454371706994693", "code": "yzj_test001", "name": "扬子江测试集成流001", "isSchemeApp": false, "desc": "", "children": []}, {"id": "2069484825777537030", "code": "laquSAP", "name": "laquSAP", "isSchemeApp": false, "desc": "", "children": []}, {"id": "2068758375002800133", "code": "yzj_002", "name": "yzj_002", "isSchemeApp": false, "desc": "", "children": []}, {"id": "2068743437106544646", "code": "yzj_001", "name": "yzj_001", "isSchemeApp": false, "desc": "", "children": []}, {"id": "2061441039249440773", "code": "yangzijiang_mu001", "name": "yangzijiang_mu001", "isSchemeApp": false, "desc": "", "children": []}, {"id": "2055515702339567625", "code": "mu_test001", "name": "mu_test001", "isSchemeApp": false, "desc": "mu_test001", "children": [{"id": "2096384996242096134", "code": "wenjian001", "name": "wenjian001", "parentId": "2055515702339567625", "appId": "2055515702339567625", "children": []}, {"id": "2097807764293156872", "code": "wenjian_zi", "name": "wenjian_zi", "parentId": "2055515702339567625", "appId": "2055515702339567625", "children": []}]}, {"id": "2054066717835395075", "code": "q1111111111111", "name": "111111111111", "isSchemeApp": false, "children": []}, {"id": "2054058334059233286", "code": "eds", "name": "你好730", "isSchemeApp": false, "children": []}, {"id": "2053550084809293833", "code": "A00001", "name": "A00001", "isSchemeApp": false, "children": []}, {"id": "2002132110727446529", "code": "test001", "name": "test001", "isSchemeApp": false, "children": []}, {"id": "1977587712663748617", "code": "huhu1", "name": "huhu1", "isSchemeApp": false, "desc": "1", "children": []}, {"id": "1975497326292631555", "code": "openapi_webservice", "name": "openapi_webservice", "isSchemeApp": false, "desc": "", "children": []}, {"id": "1970888912308535298", "code": "ddx", "name": "测试集成应用", "isSchemeApp": false, "children": []}, {"id": "1965895875385884677", "code": "simpleRequest", "name": "simpleRequest", "isSchemeApp": false, "desc": "", "children": [{"id": "1977433076665417735", "code": "wenjian01", "name": "文件01", "parentId": "1965895875385884677", "appId": "1965895875385884677", "children": []}]}, {"id": "1910127554688712705", "code": "app006", "name": "app006", "isSchemeApp": false, "children": []}, {"id": "1910127279810805768", "code": "app003", "name": "app003", "isSchemeApp": false, "children": []}, {"id": "1910127073652375554", "code": "app002", "name": "app002", "isSchemeApp": false, "children": []}, {"id": "1910051525177638918", "code": "app001", "name": "app001", "isSchemeApp": false, "children": []}, {"id": "1882495023609544706", "code": "a2312", "name": "1231123", "isSchemeApp": false, "children": []}, {"id": "1879479303735869444", "code": "test", "name": "test", "isSchemeApp": false, "children": []}, {"id": "1836474887120093187", "code": "ptest", "name": "ptest", "isSchemeApp": false, "children": []}, {"id": "1736572616346959876", "code": "automobileExhibitionChange", "name": "地区车展变更", "isSchemeApp": false, "children": []}, {"id": "1731189467566309382", "code": "JCYY", "name": "集成应用", "isSchemeApp": false, "children": []}, {"id": "1731181891243999233", "code": "n123", "name": "123", "isSchemeApp": false, "children": []}, {"id": "1727523154615074823", "code": "part", "name": "零件信息", "isSchemeApp": false, "children": []}, {"id": "2267050710076751872", "code": "BIPToU9CArchives", "name": "U9C档案", "applicationType": 1, "connectId": "2232787548319514633", "connectIdTwo": "2263910573575241735", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "U9PlatformHandler", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "U9C连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "u9c", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2267050710076751872", "isSchemeApp": true}, {"id": "2232139970343337991", "code": "HRY", "name": "BIP简版人力云同步U9C简版人力", "applicationType": 1, "connectId": "2232787548319514633", "connectIdTwo": "2263910573575241735", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "U9PlatformHandler", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "U9C连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "u9c", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2232139970343337991", "isSchemeApp": true}, {"id": "2263415879233175554", "code": "mdm_bip_outbox", "name": "主数据&BIP开箱模型", "applicationType": 1, "connectId": "2263417047455105029", "connectIdTwo": "2051046814643126275", "connectTypeCode": "mdm", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "主数据连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "mdm", "logo2": "yonbip", "memo": "主数据与BIP预置开箱档案模型集成资产。", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2263415879233175554", "isSchemeApp": true}, {"id": "2256542308081336325", "code": "xiu0429", "name": "xiu0429", "applicationType": 1, "connectId": "2051046814643126275", "connectIdTwo": "2051046814643126275", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "yonbip", "memo": "1", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2256542308081336325", "isSchemeApp": true}, {"id": "2180187856949477380", "code": "BIP_test", "name": "收入云-连接测试", "applicationType": 1, "connectId": "2051046814643126275", "connectIdTwo": "2051046814643126275", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2180187856949477380", "isSchemeApp": true}, {"id": "2206266108088156169", "code": "gaojibdaobizhong", "name": "高级版到币种", "applicationType": 1, "connectId": "1785974154830288606", "connectIdTwo": "2051046814643126275", "connectTypeCode": "yonbippremium-connector", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "YonBIP高级版连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbippremium", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2206266108088156169", "isSchemeApp": true}, {"id": "2228457946424803336", "code": "duodianfievent", "name": "多点fievent", "applicationType": 1, "connectId": "2051046814643126275", "connectIdTwo": "2228457096017870849", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2228457946424803336", "isSchemeApp": true}, {"id": "2226235412508835840", "code": "huhu5", "name": "huhu5", "applicationType": 1, "connectId": "1965548189176037379", "connectIdTwo": "1840325960949825545", "connectTypeCode": "mdm", "connectTypeCodeTwo": "mdm", "connectTypeName": "主数据连接器", "connectTypeNameTwo": "主数据连接器", "dataenable": 0, "dataversion": 0, "logo1": "mdm", "logo2": "mdm", "memo": "huhu51111", "microServiceCode": "yonbip-hr-otd", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2226235412508835840", "isSchemeApp": true}, {"id": "2110282402297806857", "code": "revenue_source_transaction", "name": "收入云-原始数据池&交易事项池-数据集成", "applicationType": 1, "connectId": "2195696365365362694", "connectIdTwo": "2195696923711111170", "connectTypeCode": "yonyou-erp-revenue_rawdatapool", "connectTypeCodeTwo": "yonyou-erp-revenue_transactions", "connectTypeName": "用友收入稽核清分原始数据池连接器", "connectTypeNameTwo": "用友收入稽核清分交易事项连接器_废弃", "dataenable": 0, "dataversion": 0, "logo1": "default", "logo2": "bipicon", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2110282402297806857", "isSchemeApp": true}, {"id": "2219623662534000649", "code": "bip_sapecc_proc", "name": "BIP和SAPECC_采购", "applicationType": 1, "connectId": "2051046814643126275", "connectIdTwo": "2219625174374809601", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "sap-erp-ecc", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "SAP-ECC连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "https://yonbip-test-oss.diwork.com/ipaasbase/icon/0a4e6f87-2cae-4474-9101-f4be56392d6c_sap", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2219623662534000649", "isSchemeApp": true}, {"id": "2197262413549535235", "code": "bip_saps4hana_efi", "name": "BIP和SAPS4HANA_智能会计", "applicationType": 1, "connectId": "2051046814643126275", "connectIdTwo": "2197264054195060744", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "sap-erp-s4hana-v2", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "SAP-S4-HANA连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "default", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2197262413549535235", "isSchemeApp": true}, {"id": "2177961131165351940", "code": "duodianbip", "name": "duodianbip", "applicationType": 1, "connectId": "2051046814643126275", "connectIdTwo": "2178007516804284422", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2177961131165351940", "isSchemeApp": true}, {"id": "65d5947ffc62004638852ff3", "code": "BIPToBIPBasicData", "name": "BIP基础数据", "applicationType": 1, "connectId": "2051046814643126275", "connectIdTwo": "1852928984139431940", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "yonbip", "microServiceCode": "https://devcenter-test.yyuap.com", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "65d5947ffc62004638852ff3", "isSchemeApp": true}, {"id": "652ce8e71ff93e5efb1c29b7", "code": "public_bip_mdm_basedoc", "name": "主数据&BIP旗舰版基础档案", "applicationType": 1, "connectId": "1969338892968525824", "connectIdTwo": "1840326613784854537", "connectTypeCode": "mdm", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "主数据连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "mdm", "logo2": "yonbip", "memo": "主数据系统和公有云BIP之间基础档案集成", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "652ce8e71ff93e5efb1c29b7", "isSchemeApp": true}, {"id": "647ca3531c125e67a9b6d599", "code": "MenuBIPPremToBIP", "name": "BIP高级版到BIP旗舰版菜单互相同步", "applicationType": 1, "connectId": "2106816200402010115", "connectIdTwo": "2051046814643126275", "connectTypeCode": "yonbippremium-connector", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "YonBIP高级版连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbippremium", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": true, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "647ca3531c125e67a9b6d599", "isSchemeApp": true}, {"id": "2091919939647045636", "code": "public_bip_mdm_user", "name": "主数据&BIP旗舰版用户", "applicationType": 1, "connectId": "1919739133150887944", "connectIdTwo": "1969338892968525824", "connectTypeCode": "api-gateway-connector", "connectTypeCodeTwo": "mdm", "connectTypeName": "API网关连接器", "connectTypeNameTwo": "主数据连接器", "dataenable": 0, "dataversion": 0, "logo1": "api", "logo2": "mdm", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2091919939647045636", "isSchemeApp": true}, {"id": "2085811388031172618", "code": "bip_oraclefusion_proc", "name": "BIP和OracleFusion_采购", "applicationType": 1, "connectId": "2051046814643126275", "connectIdTwo": "2086069300825686019", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "oracle-erp-fusion", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "Oracle-Fusion连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "https://yonbip-test-oss.diwork.com/ipaasbase/icon/Oracle", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2085811388031172618", "isSchemeApp": true}, {"id": "2080813351789658113", "code": "BIPERPNC65_1", "name": "采购云标准采购集成NC65(NC65到BIP-基于主数据翻译)", "applicationType": 1, "connectId": "1871520514573860866", "connectIdTwo": "1840326613784854537", "connectTypeCode": "yonyou-erp-nc65", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "用友NC65连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "https://yonbip-test-oss.diwork.com/ipaasbase/icon/92360e9aaf3805b73174c5e2408d9857", "logo2": "yonbip", "memo": "采购云标准采购集成NC65(NC65到BIP-基于主数据翻译)", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2080813351789658113", "isSchemeApp": true}, {"id": "2061588244938096643", "code": "bip_dingtalk_attendance", "name": "BIP和钉钉考勤打卡", "applicationType": 1, "connectId": "1919739133150887944", "connectIdTwo": "2058339694879440899", "connectTypeCode": "api-gateway-connector", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "API网关连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "api", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2061588244938096643", "isSchemeApp": true}, {"id": "2058338286139080707", "code": "dingt<PERSON>_thirdevent", "name": "钉钉三方事件", "applicationType": 1, "connectId": "1919739133150887944", "connectIdTwo": "2058339694879440899", "connectTypeCode": "api-gateway-connector", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "API网关连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "api", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2058338286139080707", "isSchemeApp": true}, {"id": "63a3f2f6af7db02d4c509864", "code": "saperps4_yonbip", "name": "SAP_BIP集成应用", "applicationType": 1, "connectId": "1969338892968525824", "connectIdTwo": "2051046814643126275", "connectName": "测试环境主数据", "connectNameTwo": "当前租户默认连接配置", "connectTypeCode": "mdm", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "主数据连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "integrateSchemeSize": 1, "logo1": "mdm", "logo2": "yonbip", "memo": "123", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "63a3f2f6af7db02d4c509864", "isSchemeApp": true}, {"id": "2034841499316256773", "code": "bip_dingtalk_basedoc", "name": "BIP和钉钉", "applicationType": 1, "connectId": "1840326613784854537", "connectIdTwo": "2034845278873845765", "connectName": "公有云BIP", "connectNameTwo": "钉钉集成测试", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "ali-o<PERSON>-<PERSON><PERSON><PERSON>", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "钉钉连接器v2", "dataenable": 0, "dataversion": 0, "integrateSchemeSize": 15, "logo1": "yonbip", "logo2": "default", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2034841499316256773", "isSchemeApp": true}, {"id": "665751f3fc62004638e518ce", "code": "yonlinker_base_test", "name": "集成方案功能测试", "applicationType": 1, "connectId": "2034845278873845765", "connectIdTwo": "1840326613784854537", "connectName": "钉钉集成测试", "connectNameTwo": "公有云BIP", "connectTypeCode": "ali-o<PERSON>-<PERSON><PERSON><PERSON>", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "钉钉连接器v2", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "integrateSchemeSize": 1, "logo1": "default", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "665751f3fc62004638e518ce", "isSchemeApp": true}, {"id": "6464a48c309c09312caaaa36", "code": "hahaha003", "name": "hahaha003", "applicationType": 1, "connectId": "1727581643480760325", "connectIdTwo": "1727584057252380678", "connectName": "hahaha002", "connectNameTwo": "hahaha002", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "yonyou-ncc-connector", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "用友NCC2105连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "default", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "6464a48c309c09312caaaa36", "isSchemeApp": true}, {"id": "647ca34e1c125e67a9b6cf04", "code": "bip_factoryorg_syn_application", "name": "工厂组织_集成应用", "applicationType": 1, "connectId": "2051046814643126275", "connectIdTwo": "2051046814643126275", "connectName": "当前租户默认连接配置", "connectNameTwo": "当前租户默认连接配置", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": true, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "647ca34e1c125e67a9b6cf04", "isSchemeApp": true}, {"id": "647ca3521c125e67a9b6d4cf", "code": "hhhh_application", "name": "项目档案集成_集成应用", "applicationType": 1, "connectId": "1785974154830288095", "connectIdTwo": "2051046814643126275", "connectName": "友工程集成", "connectNameTwo": "当前租户默认连接配置", "connectTypeCode": "yonbippremium-connector", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "YonBIP高级版连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbippremium", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": true, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "647ca3521c125e67a9b6d4cf", "isSchemeApp": true}, {"id": "65095a69f50c8039131cfbe4", "code": "wangtest001", "name": "王豪阳高级版连接测试", "applicationType": 1, "connectId": "1820297583538995207", "connectIdTwo": "1820297583538995207", "connectName": "新-王豪阳高级版测试", "connectNameTwo": "新-王豪阳高级版测试", "connectTypeCode": "yonbippremium-connector", "connectTypeCodeTwo": "yonbippremium-connector", "connectTypeName": "YonBIP高级版连接器", "connectTypeNameTwo": "YonBIP高级版连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbippremium", "logo2": "yonbippremium", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "65095a69f50c8039131cfbe4", "isSchemeApp": true}, {"id": "6523b1f51e024c46ba160639", "code": "11_2", "name": "11_2", "applicationType": 1, "connectId": "1587389822154047494", "connectIdTwo": "1588710997031387137", "connectName": "234324", "connectNameTwo": "好好好", "connectTypeCode": "u8open", "connectTypeCodeTwo": "u8open", "dataenable": 0, "dataversion": 0, "logo1": "u8", "logo2": "u8", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "6523b1f51e024c46ba160639", "isSchemeApp": true}, {"id": "6523b2058f821f4ef1146854", "code": "11_3", "name": "11_3", "applicationType": 1, "connectId": "1587389822154047494", "connectIdTwo": "1588710997031387137", "connectName": "234324", "connectNameTwo": "好好好", "connectTypeCode": "u8open", "connectTypeCodeTwo": "u8open", "dataenable": 0, "dataversion": 0, "logo1": "u8", "logo2": "u8", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "6523b2058f821f4ef1146854", "isSchemeApp": true}, {"id": "652e457a6a823707c3aa355c", "code": "ptest", "name": "ptest", "applicationType": 1, "connectId": "1820297583538995207", "connectIdTwo": "1780979451476574210", "connectName": "新-王豪阳高级版测试", "connectNameTwo": "阿斯蒂芬", "connectTypeCode": "yonbippremium-connector", "connectTypeCodeTwo": "api-gateway-connector", "connectTypeName": "YonBIP高级版连接器", "connectTypeNameTwo": "API网关连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbippremium", "logo2": "api", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "652e457a6a823707c3aa355c", "isSchemeApp": true}, {"id": "6564530057431a0ae3d5dfb5", "code": "BIPToNC65Voucher", "name": "nc65凭证", "applicationType": 1, "connectId": "2051046814643126275", "connectIdTwo": "6564530057431a0ae3d5dfb4", "connectName": "当前租户默认连接配置", "connectNameTwo": "nc65", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "<PERSON><PERSON><PERSON><PERSON>", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "NC连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "nc", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "6564530057431a0ae3d5dfb5", "isSchemeApp": true}, {"id": "65df20071f6abe293d10d8b7", "code": "01", "name": "01", "applicationType": 1, "connectId": "1823433296217374729", "connectIdTwo": "1840325960949825545", "connectName": "双良日常环境集成租户", "connectNameTwo": "双良主数据", "connectTypeCode": "mdm", "connectTypeCodeTwo": "mdm", "connectTypeName": "主数据连接器", "connectTypeNameTwo": "主数据连接器", "dataenable": 0, "dataversion": 0, "logo1": "mdm", "logo2": "mdm", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "65df20071f6abe293d10d8b7", "isSchemeApp": true}, {"id": "66173d950baaa45c38baf417", "code": "test111", "name": "test111", "applicationType": 1, "connectId": "1785974154830288309", "connectIdTwo": "1919739133150887942", "connectName": "YonBIP高级版连接器", "connectNameTwo": "当前租户默认连接配置", "connectTypeCode": "yonbippremium-connector", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "YonBIP高级版连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbippremium", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "66173d950baaa45c38baf417", "isSchemeApp": true}, {"id": "6624cd40875d6838d2cb98af", "code": "duanguozTest1", "name": "duanguozTest1", "applicationType": 1, "connectId": "1919739133150887942", "connectIdTwo": "1979874172859318275", "connectName": "当前租户默认连接配置", "connectNameTwo": "duanguoz高级版连接配置测试1", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "yonbippremium-connector", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "YonBIP高级版连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "yonbippremium", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "6624cd40875d6838d2cb98af", "isSchemeApp": true}, {"id": "662872e70d588d661dc7425b", "code": "123456", "name": "测试用", "applicationType": 1, "connectId": "1981476092580462594", "connectIdTwo": "1970751902838161414", "connectName": "测试环境主数据0423", "connectNameTwo": "测试环境主数据_0409", "connectTypeCode": "mdm", "connectTypeCodeTwo": "mdm", "connectTypeName": "主数据连接器", "connectTypeNameTwo": "主数据连接器", "dataenable": 0, "dataversion": 0, "logo1": "mdm", "logo2": "mdm", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "662872e70d588d661dc7425b", "isSchemeApp": true}, {"id": "664721c7e937f3428d10d205", "code": "nnn", "name": "nnn", "applicationType": 1, "connectId": "1981476092580462594", "connectIdTwo": "1785974154830288095", "connectName": "测试环境主数据0423", "connectNameTwo": "友工程集成", "connectTypeCode": "mdm", "connectTypeCodeTwo": "yonbippremium-connector", "connectTypeName": "主数据连接器", "connectTypeNameTwo": "YonBIP高级版连接器", "dataenable": 0, "dataversion": 0, "logo1": "mdm", "logo2": "yonbippremium", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "664721c7e937f3428d10d205", "isSchemeApp": true}, {"id": "65e06d5c641a7f66ad9eeda6", "code": "wcy", "name": "wcy", "applicationType": 1, "connectId": "1823433296217374729", "connectIdTwo": "1785974154830288095", "connectName": "双良日常环境集成租户", "connectNameTwo": "友工程集成", "connectTypeCode": "mdm", "connectTypeCodeTwo": "yonbippremium-connector", "connectTypeName": "主数据连接器", "connectTypeNameTwo": "YonBIP高级版连接器", "dataenable": 0, "dataversion": 0, "integrateSchemeSize": 0, "logo1": "mdm", "logo2": "yonbippremium", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "65e06d5c641a7f66ad9eeda6", "isSchemeApp": true}, {"id": "659cfd31fc6200463884c3ed", "code": "BIPPremiumToBIPCGY", "name": "NCC到BIP旗舰版采购云", "applicationType": 1, "connectId": "6397e7c4c959870b2b3f3bb5", "connectIdTwo": "1825587926700916738", "connectName": "NCC连接器", "connectNameTwo": "aiService", "connectTypeCode": "NC<PERSON><PERSON><PERSON>", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "NCC连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "ncc", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "659cfd31fc6200463884c3ed", "isSchemeApp": true}, {"id": "6549d5f7a3af23693a9ceb4f", "code": "BIPToNCC2111YFK", "name": "BIP旗舰版到NCC2111友费控单据集成", "applicationType": 1, "connectId": "63b51d9b6e0cfd1c5884bd70", "connectIdTwo": "653f144cdb11634d23f0c517", "connectName": "当前租户默认连接配置", "connectNameTwo": "2111新的", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "yonbippremium-connector", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "YonBIP高级版连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "yonbippremium", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "6549d5f7a3af23693a9ceb4f", "isSchemeApp": true}, {"id": "64d22ba6af7db02d4c149598", "code": "bip_bip_basedoc", "name": "基础档案同步（采购云&新能源）", "applicationType": 1, "connectId": "1789244806784876544", "connectIdTwo": "1789245201921867784", "connectName": "wangtest001", "connectNameTwo": "wangtest002", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "integrateSchemeSize": 3, "logo1": "yonbip", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "64d22ba6af7db02d4c149598", "isSchemeApp": true}, {"id": "63b51b25af7db02d4ccf6aee", "code": "sync_prem_plus", "name": "YonBIP高级版旗舰版数据同步", "applicationType": 1, "connectTypeCode": "yonbip-premium-connector", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "用友YonBIP高级版-已废弃", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "integrateSchemeSize": 1, "logo1": "https://yonbip-daily-oss.yyuap.com/image/01256452-13b8-4c3a-9839-5686486c4db4_YonBIP开放平台连接器", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "63b51b25af7db02d4ccf6aee", "isSchemeApp": true}, {"id": "2029443395454763009", "code": "1122", "name": "1122", "applicationType": 1, "connectId": "1919739133150887942", "connectIdTwo": "1919739133150887942", "connectName": "当前租户默认连接配置", "connectNameTwo": "当前租户默认连接配置", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2029443395454763009", "isSchemeApp": true}, {"id": "2032638928430301184", "code": "dlmtestncc1909", "name": "dlmtestncc1909", "applicationType": 1, "connectId": "2029722585496813577", "connectIdTwo": "1919739133150887942", "connectName": "ncc1909", "connectNameTwo": "当前租户默认连接配置", "connectTypeCode": "NC<PERSON><PERSON><PERSON>", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "NCC连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "ncc", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2032638928430301184", "isSchemeApp": true}, {"id": "2039982841442861064", "code": "BIP_NC65_voucher", "name": "BIP事项分录传NC65凭证", "applicationType": 1, "connectId": "", "connectIdTwo": "", "connectName": "aaaaa", "connectNameTwo": "收入确认二期", "connectTypeCode": "api-gateway-connector", "connectTypeCodeTwo": "<PERSON><PERSON><PERSON><PERSON>", "connectTypeName": "API网关连接器", "connectTypeNameTwo": "NC连接器", "dataenable": 0, "dataversion": 0, "integrateSchemeSize": 5, "logo1": "api", "logo2": "nc", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2039982841442861064", "isSchemeApp": true}, {"id": "2039982867212664848", "code": "BipToNc65_voucher_reverse", "name": "BIP事项分录删除nc65总账凭证", "applicationType": 1, "connectId": "", "connectIdTwo": "", "connectName": "BIP收入合同连接器", "connectNameTwo": "NC65应收单连接器", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "<PERSON><PERSON><PERSON><PERSON>", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "NC连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "nc", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2039982867212664848", "isSchemeApp": true}, {"id": "2045929867932336134", "code": "dlmtestmicrosoftecode0719", "name": "dlmtestmicrosoftecode0719", "applicationType": 1, "connectId": "2038470841179045891", "connectIdTwo": "1919739133150887942", "connectName": "高级版测试", "connectNameTwo": "当前租户默认连接配置", "connectTypeCode": "yonbippremium-connector", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "YonBIP高级版连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbippremium", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2045929867932336134", "isSchemeApp": true}, {"id": "2049650383338864649", "code": "11111", "name": "11", "applicationType": 1, "connectId": "1919739133150887944", "connectIdTwo": "1919739133150887942", "connectName": "当前租户默认连接配置", "connectNameTwo": "当前租户默认连接配置", "connectTypeCode": "api-gateway-connector", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "API网关连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "api", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2049650383338864649", "isSchemeApp": true}, {"id": "2049651044763828233", "code": "555", "name": "555", "applicationType": 1, "connectId": "1919739133150887944", "connectIdTwo": "1919739133150887942", "connectName": "当前租户默认连接配置", "connectNameTwo": "当前租户默认连接配置", "connectTypeCode": "api-gateway-connector", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "API网关连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "api", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2049651044763828233", "isSchemeApp": true}, {"id": "2054053875888422919", "code": "bnn", "name": "nbnk", "applicationType": 1, "connectId": "1840325960949825545", "connectIdTwo": "2051046814643126275", "connectName": "双良主数据", "connectNameTwo": "当前租户默认连接配置", "connectTypeCode": "mdm", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "主数据连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "mdm", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2054053875888422919", "isSchemeApp": true}, {"id": "2055505506098216964", "code": "ncc2111_to_yb_efi_yy", "name": "NCC2111到BIP旗舰版财务", "applicationType": 1, "connectId": "2055505506094546948", "connectIdTwo": "1871520514573860868", "connectName": "ncc2111连接器", "connectNameTwo": "NCC2111升迁bip旗舰版连接器", "connectTypeCode": "yonyou-erp-ncc2111", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "用友NCC2111连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "integrateSchemeSize": 7, "logo1": "default", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2055505506098216964", "isSchemeApp": true}, {"id": "2055505514688151553", "code": "NCC2111ToBIPYGC", "name": "NCC2111到BIP旗舰版友工程", "applicationType": 1, "connectId": "2055505514688151552", "connectIdTwo": "2051046814643126275", "connectName": "2111", "connectNameTwo": "当前租户默认连接配置", "connectTypeCode": "NC<PERSON><PERSON><PERSON>", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "NCC连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "ncc", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2055505514688151553", "isSchemeApp": true}, {"id": "2057053644240977926", "code": "saperps4_yonbip_2", "name": "SAP_BIP集成应用_2", "applicationType": 1, "connectId": "1969338892968525824", "connectIdTwo": "2051046814643126275", "connectName": "测试环境主数据", "connectNameTwo": "当前租户默认连接配置", "connectTypeCode": "mdm", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "主数据连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "integrateSchemeSize": 1, "logo1": "mdm", "logo2": "yonbip", "memo": "123", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2057053644240977926", "isSchemeApp": true}, {"id": "2057650722004533255", "code": "test2323", "name": "托尔斯泰", "applicationType": 1, "connectId": "2051046814643126275", "connectIdTwo": "1919739133150887944", "connectName": "当前租户默认连接配置", "connectNameTwo": "当前租户默认连接配置", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "api-gateway-connector", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "API网关连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "api", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2057650722004533255", "isSchemeApp": true}, {"id": "2057796862561746945", "code": "bnbnbnbn", "name": "bnbnnbn", "applicationType": 1, "connectId": "1840325960949825545", "connectIdTwo": "2051046814643126275", "connectName": "双良主数据", "connectNameTwo": "当前租户默认连接配置", "connectTypeCode": "mdm", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "主数据连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "mdm", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2057796862561746945", "isSchemeApp": true}, {"id": "2057860505387139077", "code": "bhjj", "name": "gvhhgghhghg", "applicationType": 1, "connectId": "2051046814643126275", "connectIdTwo": "1840325960949825545", "connectName": "当前租户默认连接配置", "connectNameTwo": "双良主数据", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "mdm", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "主数据连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "mdm", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2057860505387139077", "isSchemeApp": true}, {"id": "2059838492437905410", "code": "11_4", "name": "11_4", "applicationType": 1, "connectId": "1587389822154047494", "connectIdTwo": "1588710997031387137", "connectName": "234324", "connectNameTwo": "好好好", "connectTypeCode": "u8open", "connectTypeCodeTwo": "u8open", "dataenable": 0, "dataversion": 0, "integrateSchemeSize": 2, "logo1": "u8", "logo2": "u8", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2059838492437905410", "isSchemeApp": true}, {"id": "2059839918367047688", "code": "ccbobe_001", "name": "ccbobe_001", "applicationType": 1, "connectId": "2051046814643126275", "connectIdTwo": "1785974154830288309", "connectName": "当前租户默认连接配置", "connectNameTwo": "YonBIP高级版连接器", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "yonbippremium-connector", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "YonBIP高级版连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "yonbippremium", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2059839918367047688", "isSchemeApp": true}, {"id": "2059966156069404675", "code": "BIPToBIPPremiumYFK", "name": "BIP旗舰版到BIP高级版友费控单据集成", "applicationType": 1, "connectId": "632817632990fb683f67d064", "connectIdTwo": "1785974154830288586", "connectName": "当前YonBIP连接", "connectNameTwo": "高级版连接器", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "yonbippremium-connector", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "YonBIP高级版连接器", "dataenable": 0, "dataversion": 0, "integrateSchemeSize": 11, "logo1": "yonbip", "logo2": "yonbippremium", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2059966156069404675", "isSchemeApp": true}, {"id": "2061436048495345667", "code": "BIPToBIPPremiumArchives", "name": "BIP旗舰版到BIP高级版云融合档案", "applicationType": 1, "connectId": "2060732695964549128", "connectIdTwo": "2038470841179045891", "connectName": "ccbobe_事件验证", "connectNameTwo": "高级版测试", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "yonbippremium-connector", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "YonBIP高级版连接器", "dataenable": 0, "dataversion": 0, "integrateSchemeSize": 17, "logo1": "yonbip", "logo2": "yonbippremium", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2061436048495345667", "isSchemeApp": true}, {"id": "2062886415983181827", "code": "BIPToU8Voucher", "name": "u8凭证", "applicationType": 1, "connectId": "2060732695964549128", "connectIdTwo": "661f547c5202db06c24cd5de", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "U8NativeSQL", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "U8连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "u8", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2062886415983181827", "isSchemeApp": true}, {"id": "2062898974467555337", "code": "0810ccc", "name": "0810ccc", "applicationType": 1, "connectId": "2061441039249440775", "connectIdTwo": "661f547c5202db06c24cd5de", "connectTypeCode": "mdm", "connectTypeCodeTwo": "U8NativeSQL", "connectTypeName": "主数据连接器", "connectTypeNameTwo": "U8连接器", "dataenable": 0, "dataversion": 0, "logo1": "mdm", "logo2": "u8", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2062898974467555337", "isSchemeApp": true}, {"id": "2071100463617933318", "code": "NC65ToBIPFI", "name": "NC65迁移到BIP旗舰版财务", "applicationType": 1, "connectId": "1871520514573860866", "connectIdTwo": "2051046814643126275", "connectTypeCode": "yonyou-erp-nc65", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "用友NC65连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "integrateSchemeSize": 1, "logo1": "https://yonbip-test-oss.diwork.com/ipaasbase/icon/92360e9aaf3805b73174c5e2408d9857", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2071100463617933318", "isSchemeApp": true}, {"id": "2075597019506802689", "code": "casc", "name": "casc", "applicationType": 1, "connectId": "1969338892968525824", "connectIdTwo": "2051046814643126275", "connectTypeCode": "mdm", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "主数据连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "mdm", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2075597019506802689", "isSchemeApp": true}, {"id": "2078746587751251970", "code": "111", "name": "11", "applicationType": 1, "connectId": "1969338892968525824", "connectIdTwo": "2051046814643126275", "connectTypeCode": "mdm", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "主数据连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "mdm", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2078746587751251970", "isSchemeApp": true}, {"id": "2084508406640541705", "code": "13", "name": "123", "applicationType": 1, "connectId": "1969338892968525824", "connectIdTwo": "1969338892968525824", "connectTypeCode": "mdm", "connectTypeCodeTwo": "mdm", "connectTypeName": "主数据连接器", "connectTypeNameTwo": "主数据连接器", "dataenable": 0, "dataversion": 0, "logo1": "mdm", "logo2": "mdm", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2084508406640541705", "isSchemeApp": true}, {"id": "2091892640824950788", "code": "BIPToBIPPremiumYXY", "name": "BIP旗舰版到BIP高级版采购营销单据同步", "applicationType": 1, "connectId": "2051046814643126275", "connectIdTwo": "1785974154830288309", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "yonbippremium-connector", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "YonBIP高级版连接器", "dataenable": 0, "dataversion": 0, "integrateSchemeSize": 20, "logo1": "yonbip", "logo2": "yonbippremium", "memo": "该集成应用支持高级版2311及以前版本的连接配置", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": true, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2091892640824950788", "isSchemeApp": true}, {"id": "2096979256511299586", "code": "qqq", "name": "ttt", "applicationType": 1, "connectId": "2051046814643126275", "connectIdTwo": "1969338892968525824", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "mdm", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "主数据连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "mdm", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2096979256511299586", "isSchemeApp": true}, {"id": "2099363238219087874", "code": "msotoling", "name": "BIP主数据系统同步到零代码档案", "applicationType": 1, "connectId": "", "connectIdTwo": "", "connectName": "当前租户默认连接配置", "connectNameTwo": "当前租户默认连接配置", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "integrateSchemeSize": 0, "logo1": "yonbip", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2099363238219087874", "isSchemeApp": true}, {"id": "2099370187476172803", "code": "a", "name": "a", "applicationType": 1, "connectId": "1969338892968525824", "connectIdTwo": "2051046814643126275", "connectTypeCode": "mdm", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "主数据连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "mdm", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2099370187476172803", "isSchemeApp": true}, {"id": "2117841424492265477", "code": "22", "name": "22", "applicationType": 1, "connectId": "1781735451621064706", "connectIdTwo": "1785974154830288606", "connectTypeCode": "mdm", "connectTypeCodeTwo": "yonbippremium-connector", "connectTypeName": "主数据连接器", "connectTypeNameTwo": "YonBIP高级版连接器", "dataenable": 0, "dataversion": 0, "logo1": "mdm", "logo2": "yonbippremium", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2117841424492265477", "isSchemeApp": true}, {"id": "2123684529923162117", "code": "JCCGPT_TEST", "name": "测试", "applicationType": 1, "connectId": "2051046814643126275", "connectIdTwo": "2055505506094546948", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "yonyou-erp-ncc2111", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "用友NCC2111连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "default", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2123684529923162117", "isSchemeApp": true}, {"id": "2124359363761012741", "code": "revenue_connector", "name": "收入稽核清分导入", "applicationType": 1, "connectId": "", "connectIdTwo": "", "connectName": "收入云原始数据池", "connectNameTwo": "收入稽核清分业务交易池", "connectTypeCode": "yonyou-erp-revenue_rawdatapool", "connectTypeCodeTwo": "yonyou-erp-revenue_transactions", "connectTypeName": "用友收入稽核清分原始数据池连接器", "connectTypeNameTwo": "用友收入稽核清分交易事项连接器_废弃", "dataenable": 0, "dataversion": 0, "logo1": "default", "logo2": "bipicon", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2124359363761012741", "isSchemeApp": true}, {"id": "2134089723847114752", "code": "MuTest001", "name": "主要是测试一次数据转换", "applicationType": 1, "connectId": "2051046814643126275", "connectIdTwo": "2028118655731499011", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "<PERSON><PERSON><PERSON><PERSON>", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "NC连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "nc", "memo": "测试使用", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2134089723847114752", "isSchemeApp": true}, {"id": "2174351211125800968", "code": "bip_weaver_eoffice_base", "name": "BIP和泛微EOffice", "applicationType": 1, "connectId": "2174336221692035075", "connectIdTwo": "2174349398651699201", "connectTypeCode": "weaver-oa-eoffice", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "泛微EOffice连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "default", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2174351211125800968", "isSchemeApp": true}, {"id": "2175622779173863424", "code": "BIP2BIP_BaseDOC", "name": "BIP旗舰版基础数据自混", "applicationType": 1, "connectId": "2051046814643126275", "connectIdTwo": "2051046814643126275", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "yonbip", "memo": "BIP旗舰版基础数据到BIP旗舰版【自混】", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2175622779173863424", "isSchemeApp": true}, {"id": "2178772819831685121", "code": "test1614", "name": "test1614", "applicationType": 1, "connectId": "2034845278873845765", "connectIdTwo": "2034845278873845765", "connectTypeCode": "ali-o<PERSON>-<PERSON><PERSON><PERSON>", "connectTypeCodeTwo": "ali-o<PERSON>-<PERSON><PERSON><PERSON>", "connectTypeName": "钉钉连接器v2", "connectTypeNameTwo": "钉钉连接器v2", "dataenable": 0, "dataversion": 0, "logo1": "default", "logo2": "default", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2178772819831685121", "isSchemeApp": true}, {"id": "2181041945556025353", "code": "cs", "name": "测试", "applicationType": 1, "connectId": "", "connectIdTwo": "", "connectTypeCode": "yonyou-erp-revenue_rawdatapool", "connectTypeCodeTwo": "yonyou-erp-revenue_transactions", "connectTypeName": "用友收入稽核清分原始数据池连接器", "connectTypeNameTwo": "用友收入稽核清分交易事项连接器_废弃", "dataenable": 0, "dataversion": 0, "logo1": "default", "logo2": "bipicon", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2181041945556025353", "isSchemeApp": true}, {"id": "2198759364271013889", "code": "bip_weaver_base", "name": "BIP和泛微", "applicationType": 1, "connectId": "", "connectIdTwo": "", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "weaver-oa-oa", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "泛微ECology连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "https://yonbip-test-oss.diwork.com/ipaasbase/icon/%E6%B3%9B%E5%BE%AE", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2198759364271013889", "isSchemeApp": true}, {"id": "2201716663986421765", "code": "apigateway_connector_test", "name": "API网关连接器测试", "applicationType": 1, "connectId": "1919739133150887944", "connectIdTwo": "2051046814643126275", "connectTypeCode": "api-gateway-connector", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "API网关连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "api", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2201716663986421765", "isSchemeApp": true}, {"id": "2231409937579507718", "code": "123", "name": "单接口自动化专用", "applicationType": 1, "connectId": "", "connectIdTwo": "", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2231409937579507718", "isSchemeApp": true}, {"id": "2235214118937952265", "code": "testbase2data", "name": "BIP到BIP组织部门数据", "applicationType": 1, "connectId": "", "connectIdTwo": "", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "yonbip", "memo": "BIP到BIP组织部门数据", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2235214118937952265", "isSchemeApp": true}, {"id": "2235760696492294150", "code": "aaaaaaaaaa", "name": "aaaaaaaaaa", "applicationType": 1, "connectId": "2233696749964754950", "connectIdTwo": "2051046814643126275", "connectTypeCode": "yonbippremium-connector", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "YonBIP高级版连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbippremium", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2235760696492294150", "isSchemeApp": true}, {"id": "2235851681079492609", "code": "renyuan1203", "name": "核心人力人员", "applicationType": 1, "connectId": "", "connectIdTwo": "", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2235851681079492609", "isSchemeApp": true}, {"id": "2241674583004151814", "code": "BIPPremiumToBIPArchives", "name": "BIP高级版到BIP旗舰版云融合档案", "applicationType": 1, "connectId": "1785974154830288585", "connectIdTwo": "2051046814643126275", "connectTypeCode": "yonbippremium-connector", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "YonBIP高级版连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbippremium", "logo2": "yonbip", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2241674583004151814", "isSchemeApp": true}, {"id": "2250588504150769673", "code": "test250421", "name": "test250421导入导出初始化", "applicationType": 1, "connectId": "2051046814643126275", "connectIdTwo": "2051046814643126275", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "yonbip", "memo": "集成方案后置任务，使用数据转化规则 vochangereg ，vochangeitem", "microServiceCode": "iuap-ipaas-dataintegration", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2250588504150769673", "isSchemeApp": true}, {"id": "2262426567893581831", "code": "BIPPremiumToBIPYXY_02", "name": "BIP高级版到BIP旗舰版营销单据同步", "applicationType": 1, "connectId": "2246981676500516868", "connectIdTwo": "2051046814643126275", "connectTypeCode": "yonbippremium-connector", "connectTypeCodeTwo": "yonyou-erp-yonbip", "connectTypeName": "YonBIP高级版连接器", "connectTypeNameTwo": "YonBIP连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbippremium", "logo2": "yonbip", "memo": "", "microServiceCode": "yonbip-mkt-mkc2b", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2262426567893581831", "isSchemeApp": true}, {"id": "2266139670182887430", "code": "BIPToU9CYFK", "name": "U9C费控云", "applicationType": 1, "connectId": "2051046814643126275", "connectIdTwo": "2263910573575241735", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "U9PlatformHandler", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "U9C连接器", "dataenable": 0, "dataversion": 0, "logo1": "yonbip", "logo2": "u9c", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2266139670182887430", "isSchemeApp": true}, {"id": "2272976716950142984", "code": "BIPToU9CApp", "name": "BIP旗舰版到U9C集成", "applicationType": 1, "connectId": "2051046814643126275", "connectIdTwo": "2263910573575241735", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "yonyou-erp-u9c", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "用友U9C连接器", "dataenable": 0, "dataversion": 0, "integrateSchemeSize": 2, "logo1": "yonbip", "logo2": "u9c", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2272976716950142984", "isSchemeApp": true}, {"id": "2272977275295891465", "code": "znbzToU9C", "name": "旗舰版费控服务传U9C", "applicationType": 1, "connectId": "1919739133150887944", "connectIdTwo": "2263910573575241735", "connectTypeCode": "api-gateway-connector", "connectTypeCodeTwo": "U9PlatformHandler", "connectTypeName": "API网关连接器", "connectTypeNameTwo": "U9C连接器", "dataenable": 0, "dataversion": 0, "integrateSchemeSize": 1, "logo1": "api", "logo2": "u9c", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2272977275295891465", "isSchemeApp": true}, {"id": "2272977971080593410", "code": "BIPToU9CApp_1", "name": "段李敏专用-BIP旗舰版到U9C集成_1", "applicationType": 1, "connectId": "2051046814643126275", "connectIdTwo": "2263910573575241735", "connectTypeCode": "yonyou-erp-yonbip", "connectTypeCodeTwo": "yonyou-erp-u9c", "connectTypeName": "YonBIP连接器", "connectTypeNameTwo": "用友U9C连接器", "dataenable": 0, "dataversion": 0, "integrateSchemeSize": 2, "logo1": "yonbip", "logo2": "u9c", "tenantId": "0000L6YQ8AVLFUZPXD0000", "updateFlag": false, "ytenantId": "0000L6YQ8AVLFUZPXD0000", "_id": "2272977971080593410", "isSchemeApp": true}], "info": null, "logOperType": null, "operResult": true}