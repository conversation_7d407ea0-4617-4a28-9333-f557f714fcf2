import React from "react";
import { screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import IndexView from "@data/sync-task/src/routes/list/components/IndexView/index";
import SyncTaskListStore, { storeKey } from "@data/sync-task/src/routes/list/store";
import { render } from "@test/utils";
import { fireEvent, waitFor } from "@testing-library/react";

const syncTaskListStore = new SyncTaskListStore();

describe("IndexView Component", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });
    const indexVIewProps = {
        ownerStore: syncTaskListStore,
        ownerState: syncTaskListStore,
        getPageParams: jest.fn(() => ({queryParams: {back: false}})),
        navigate: jest.fn(),
        queryContext: { initialData: undefined },
    };
    // 测试构造函数
    describe("constructor", () => {
        test("应该正确初始化组件状态", () => {
            render(<IndexView {...indexVIewProps} />);
            const layout = screen.getByTestId('f730889b-781c-47d5-a103-b3dcff1afe7a');
            expect(layout).toBeInTheDocument();
            
        });
        
    });

});

