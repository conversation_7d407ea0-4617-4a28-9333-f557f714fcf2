import React from "react";
import { screen, fireEvent, waitFor, act } from "@testing-library/react";
import "@testing-library/jest-dom";
import FlowScheduler from "@data/components/FlowScheduler/index";
import { getScheduleDetailService, updateScheduleService } from "@data/components/service";
import { autoServiceMessage } from "utils/service";
import { render } from "@test/utils";

jest.mock("iuap-ip-commonui-fe/Scheduler", () => {
  return jest.fn(({ timeData, taskCode, onSave, onCancel }) => (
    <div fieldid="scheduler-component">
      <div fieldid="scheduler-timedata">{JSON.stringify(timeData)}</div>
      <div fieldid="scheduler-taskcode">{taskCode}</div>
      <button fieldid="scheduler-save-btn" onClick={() => onSave()}>保存</button>
      <button fieldid="scheduler-cancel-btn" onClick={onCancel}>取消</button>
    </div>
  ));
});

jest.mock("utils/service", () => ({
  autoServiceMessage: jest.fn()
}));

jest.mock("@data/components/service", () => ({
  getScheduleDetailService: jest.fn(() => Promise.resolve({ data: {} })),
  updateScheduleService: jest.fn(() => Promise.resolve({ data: {} }))
}));

const handleCancelScheduler = jest.fn();


describe("FlowScheduler 组件测试", () => {
  // 在每个测试前重置所有模拟
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test("组件渲染", async () => {
    console.log("FlowScheduler 组件测试",FlowScheduler);
    render(
      <FlowScheduler 
        taskId="task-123" 
        taskCode="task-code-123" 
        onCancel={handleCancelScheduler} 
      />
    );
    await waitFor(() => expect(getScheduleDetailService).toHaveBeenCalledWith({taskId: "task-123", taskCode: "task-code-123"}));
    // 验证Modal组件是否被渲染
    const modal = screen.getByTestId("a86197e9-d735-400b-b9fd-ab4bd30c8921");
    expect(modal).toBeInTheDocument();
    expect(modal).toHaveTextContent("定时任务");
    
    // 验证Scheduler组件是否被渲染
    expect(screen.getByTestId("scheduler-component")).toBeInTheDocument();
    expect(screen.getByTestId("scheduler-timedata")).toHaveTextContent(JSON.stringify({}));
    expect(screen.getByTestId("scheduler-taskcode")).toHaveTextContent("task-code-123");
    expect(screen.getByTestId("scheduler-save-btn")).toBeInTheDocument();
    expect(screen.getByTestId("scheduler-cancel-btn")).toBeInTheDocument();
    
    // 模拟保存按钮点击
    fireEvent.click(screen.getByTestId("scheduler-save-btn"));
    await waitFor(() => expect(updateScheduleService).toHaveBeenCalledWith({taskId: "task-123", taskCode: "task-code-123", timeData: {}}));
    expect(updateScheduleService).toHaveBeenCalled();

    // 模拟取消按钮点击
    fireEvent.click(screen.getByTestId("scheduler-cancel-btn"));
    expect(handleCancelScheduler).toHaveBeenCalled();
  });
});
