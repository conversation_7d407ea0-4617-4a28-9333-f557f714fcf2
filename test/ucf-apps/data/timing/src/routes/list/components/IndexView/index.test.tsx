import React from "react";
import { screen, fireEvent, render, waitFor, act } from "@test/utils";
import IndexView from "@data/timing/src/routes/list/components/IndexView/index.js";
import OwnerStore from "@data/timing/src/routes/list/store.js";
import { timingTaskListService } from "@data/timing/src/routes/list/service";

// 模拟组件依赖
jest.mock("components/PageView", () => ({
    Header: jest.fn(({ title, back }) => (
        <div fieldid="header-component">
            {back && <div fieldid="header-back-button"></div>}
            <div fieldid="header-title">{title}</div>
        </div>
    )),
}));

jest.mock("components/TinperBee/Grid", () => {
    return {}
});

jest.mock("components/PageLayout", () => {
    return jest.fn(({ children, header, footer }) => (
        <div fieldid="page-layout">
            <div fieldid="page-header">{header}</div>
            <div fieldid="page-content">{children}</div>
            {footer && <div fieldid="page-footer">{footer}</div>}
        </div>
    ));
});

jest.mock("components/CustomTag", () => {
    return jest.fn(({ tagName, status }) => (
        <div fieldid="custom-tag" data-status={status}>
            {tagName}
        </div>
    ));
});

jest.mock("@data/timing/src/routes/list/components/EditModal", () => {
    return jest.fn(({ show, editInfo, onCancel, onOk }) => (
        <div fieldid="edit-modal" style={{ display: show ? "block" : "none" }}>
            <div fieldid="modal-content">{editInfo && JSON.stringify(editInfo)}</div>
            <button fieldid="modal-cancel" onClick={onCancel}>
                取消
            </button>
            <button fieldid="modal-save" onClick={() => onOk({ id: "test-id", name: "test-name" })}>
                保存
            </button>
        </div>
    ));
});

jest.mock("ucf-apps/data/components/DispatchModal", () => {
    const { forwardRef, useImperativeHandle } = jest.requireActual('react');
    return forwardRef((props, ref) => {
        useImperativeHandle(ref, () => ({
            openDispatchModal: (uri, type) => {
                console.log("openDispatchModal", uri, type);
                // 可能需要在这里设置一些状态或模拟更多行为
            },
            closeDispatchModal: jest.fn(() => {
                console.log("closeDispatchModal");
            }),
        }));
        return <div fieldid="dispatch-modal" data-testid="dispatch-modal"></div>;
    });
});

jest.mock("ucf-apps/data/components/FlowScheduler", () => {
    return jest.fn(({ taskId, taskCode, visible, onCancel }) => (
        <div fieldid="flow-scheduler" style={{ display: visible ? "block" : "none" }}>
            <div fieldid="scheduler-task-id">{taskId}</div>
            <div fieldid="scheduler-task-code">{taskCode}</div>
            <button fieldid="scheduler-cancel" onClick={onCancel}>
                取消
            </button>
        </div>
    ));
});

jest.mock("react-router-dom", () => ({
    ...jest.requireActual("react-router-dom"),
    withRouter: (Component) => (props) => <Component {...props} />, // 透传 props，不实际注入路由
}));



// 模拟数据
const mockDataSource = {
    list: [
        {
            id: "1",
            jobname: "task-001",
            aliasname: "测试任务1",
            status: "NORMAL",
            description: "这是测试任务1的描述",
        },
        {
            id: "2",
            jobname: "syncdata|diwork",
            aliasname: "测试任务2",
            status: "PAUSED",
            description: "这是测试任务2的描述",
        },
    ],
};

const mockPagination = {
    activePage: 1,
    total: 10,
    pageSize: 10,
    onPageChange: jest.fn(),
};

jest.mock("@data/timing/src/routes/list/service", () => ({
  timingTaskListService: jest.fn(() => Promise.resolve({ data: mockDataSource })),
}));

describe("IndexView 组件测试", () => {
    const mockOwnerStore = new OwnerStore();
    mockOwnerStore.changeState({
        dataSource: mockDataSource,
        pagination: mockPagination,
    });
    // 在每个测试前重置所有模拟
    beforeEach(() => {
        jest.clearAllMocks();
    });

    test("组件渲染", async () => {
        render(<IndexView ownerStore={mockOwnerStore} ownerState={mockOwnerStore.state} />);

        await waitFor(() => expect(timingTaskListService).toHaveBeenCalled());
        // hover表格
        // await waitFor(() => expect(screen.getByText("task-001")).toBeInTheDocument());
        // fireEvent.mouseOver(screen.getByText("task-001"));
        // // 编辑
        // const editButton = screen.getByTestId("UCG-FE-routes-list-components-IndexView-index-3435054-GridAction");
        // await waitFor(() => expect(editButton).toBeInTheDocument());
        // await act(async () => {
        //     fireEvent.click(screen.getByText("编辑"));
        //     await Promise.resolve(); // 等待状态更新完成
        // });
        // const dispatchModal = screen.getByTestId("dispatch-modal");
        // expect(dispatchModal).toBeInTheDocument();
    });
});
